# Print Manager Backup System

Independent database backup system with abstraction layer, supporting multiple database and storage types.

## 🚀 New Features

- ✅ **Abstraction Layer**: Easy to add new databases and storage providers
- ✅ **Connection String**: Use connection string instead of multiple env variables
- ✅ **Multi-Database Support**: PostgreSQL, MySQL
- ✅ **Multi-Storage Support**: AWS S3, Google Cloud Storage, Local
- ✅ **MySQL Support**: Using [mysqldump library](https://github.com/bradzacher/mysqldump)

## 🗄️ Database Dump & Restore

### PostgreSQL Dump & Restore

**Dump Format Restore:**
```bash
# Restore from custom dump file
pg_restore -d "postgresql://username:password@host:port/database_name" backup.dump

# Restore from compressed custom dump
gunzip -c backup.dump.gz | pg_restore -d "postgresql://username:password@host:port/database_name"

# Example with connection string
gunzip -c postgres_backup_2025-08-12.dump.gz | pg_restore -d "postgresql://testuser:testpass@localhost:5433/testdb"
```

**Common pg_restore flags:**
```bash
--no-owner          # Skip ownership restoration (useful for different users)
--no-privileges     # Skip privilege restoration (GRANT/REVOKE)
--jobs=4            # Use 4 parallel jobs for faster restore
--verbose           # Show detailed progress
--clean             # Drop objects before recreating them
--if-exists         # Use IF EXISTS when dropping objects
--single-transaction # Wrap restore in single transaction
```

## Quick Deploy (3 steps)

### Step 1: Copy directory
```bash
cp -r backup-system /path/to/new/project/
cd /path/to/new/project/backup-system
```

### Step 2: Configure Database and Storage
Edit `pm2.backup.config.js` or add to env.sh:

```javascript
env: {
  // Database Configuration (connection string)
  // If postgresql please add version mapping with your postgresql version
  BACKUP_POSTGRES_VERSION: 15

  DB_BACKUP_CONNECTION_STRING: "postgresql://user:password@localhost:5432/database_name",
  // or MySQL: "mysql://user:password@localhost:3306/database_name"
  
  // Storage Configuration
  BACKUP_STORAGE_TYPE: "s3", // "s3", "gcs", or "local"
  BACKUP_STORAGE_BUCKET: "your-backup-bucket",
  BACKUP_STORAGE_PREFIX: "db-backups",
  
  
  // AWS S3 Configuration
  BACKUP_AWS_ACCESS_KEY_ID: "your-access-key",
  BACKUP_AWS_SECRET_ACCESS_KEY: "your-secret-key",
  BACKUP_AWS_DEFAULT_REGION: "eu-west-1",

  // another one using s3 api
    BACKUP_ENDPOINT:"backup_endpoint"
  
  // Google Cloud Storage Configuration
  BACKUP_GCS_KEY_FILENAME: "/path/to/gcloud-privatekey.json"
}
```

### Step 3: Deploy
- add this code to end of deploy.sh
```

if [ -d "$PROJECT_DIR/backup-system" ]; then
    echo "Deploying backup system..."
    cd "$PROJECT_DIR/backup-system"
    echo "Current directory: $(pwd)"
    echo "Checking for .nvmrc file..."
    if [ -f ".nvmrc" ]; then
        echo "✓ .nvmrc file found"
        chmod +x deploy-backup.sh
        ./deploy-backup.sh
    else
        echo "❌ .nvmrc file not found in backup-system directory"
        echo "Files in backup-system:"
        ls -la
        exit 1
    fi
    cd "$PROJECT_DIR"
elif [ -f "$PROJECT_DIR/deployments/auto_backup_db.sh" ]; then
    echo "Running legacy database backup setup..."
    chmod +x "$PROJECT_DIR/deployments/auto_backup_db.sh"
    "$PROJECT_DIR/deployments/auto_backup_db.sh"
fi
```

## 📋 System Requirements

- **Node.js** (v18+) - will be auto-installed via .nvmrc
- **NVM** (Node Version Manager) - to manage Node.js version
- **PM2** (will be auto-installed)
- **PostgreSQL client** (pg_dump, psql) - only needed for PostgreSQL backups
  - **Version compatibility**: Set `BACKUP_POSTGRES_VERSION` to match your server version
  - **MySQL**: No client needed (uses `mysqldump` library)

### Install NVM and Node.js:
```bash
# Install NVM
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.bashrc

# Node.js will be auto-installed via .nvmrc when running script
```

### Install PostgreSQL client (only needed for PostgreSQL):
```bash
# Ubuntu/Debian
sudo apt-get install postgresql-client-15

# CentOS/RHEL
sudo yum install postgresql15

# macOS  
brew install postgresql@15
```

## 🔧 Management

```bash
# Check status
pm2 status

# View logs
pm2 logs backup-system

# Restart
pm2 restart backup-system

# Stop
pm2 stop backup-system

# Delete
pm2 delete backup-system
```

## ⚙️ Advanced Configuration

### Connection String Format:

**PostgreSQL:**
```
postgresql://username:password@host:port/database_name
postgresql://user:pass@localhost:5432/mydb
```

**MySQL:**
```
mysql://username:password@host:port/database_name
mysql://user:pass@localhost:3306/mydb
```

### Storage Configuration:

**AWS S3:**
```javascript
BACKUP_STORAGE_TYPE: "s3",
BACKUP_STORAGE_BUCKET: "my-backup-bucket",
BACKUP_AWS_ACCESS_KEY_ID: "your-key",
BACKUP_AWS_SECRET_ACCESS_KEY: "your-secret"
```

**Google Cloud Storage:**
```javascript
BACKUP_STORAGE_TYPE: "gcs",
BACKUP_STORAGE_BUCKET: "my-backup-bucket",
GCS_KEY_FILENAME: "/path/to/service-account-key.json"
```

**Local Storage:**
```javascript
BACKUP_STORAGE_TYPE: "local"
// Files will be kept in /tmp/db-backups
```

### Change backup schedule:
```javascript
DB_BACKUP_SCHEDULE: "0 2 * * *"  // 2 AM daily
DB_BACKUP_SCHEDULE: "0 */6 * * *" // Every 6 hours
DB_BACKUP_SCHEDULE: "0 2,14 * * *" // 2 AM and 2 PM
```

### Change retention period:
```javascript
DB_BACKUP_RETENTION_DAYS: '7'  // Keep 7 days
```

## Troubleshooting

### Error "No .nvmrc file found":
```bash
# Check .nvmrc file
ls -la .nvmrc
cat .nvmrc

# If missing or has extra characters, recreate
echo -n "18" > .nvmrc
```

### Error "Node.js version too old":
```bash
# Check current Node.js version
node --version

# Install Node.js 18 via NVM
nvm install 18
nvm use 18
```

### Database connection failed:
```bash
# Test PostgreSQL connection
PGPASSWORD="password" psql -h localhost -p 5432 -U user -d database -c "SELECT 1"

# Test MySQL connection (if mysql client available)
mysql -h localhost -P 3306 -u user -p database -e "SELECT 1"
```

### Storage upload failed:
- Check credentials (AWS keys, GCS key file)
- Check bucket permissions
- Check network connectivity

### PM2 process not starting:
```bash
pm2 logs backup-system --lines 50
```

## 📁 Directory Structure
```
backup-system/
├── pm2.backup.config.js         # PM2 configuration
├── package.json                 # Dependencies
├── .nvmrc                       # Node.js version
├── index.ts                     # Entry point
└── src/
    ├── types/
    │   └── backup.ts            # Interface definitions
    ├── providers/
    │   ├── PostgreSQLBackupProvider.ts
    │   ├── MySQLBackupProvider.ts
    │   ├── S3StorageProvider.ts
    │   └── GCSStorageProvider.ts
    ├── factories/
    │   └── BackupFactory.ts     # Factory pattern
    ├── backupDatabaseDaily.ts   # Main logic
    └── indexCronBackup.ts       # Cron wrapper
```