#!/bin/bash

echo "=== Deploying Backup System ==="

cd "$(dirname "$0")"
BACKUP_DIR="$(pwd)"

echo "Backup directory: $BACKUP_DIR"

if [ -f /env.sh ]; then
    . /env.sh
fi

echo "Setting up environment..."
source ~/.profile

# Setup Node.js version from .nvmrc
if [ -f ".nvmrc" ]; then
    echo "Setting up Node.js version from .nvmrc..."
    echo "Current directory: $(pwd)"
    echo ".nvmrc content: $(cat .nvmrc)"
    
    if command -v nvm &> /dev/null; then
        # Use absolute path to .nvmrc
        NVM_RC_PATH="$(pwd)/.nvmrc"
        echo "Using .nvmrc at: $NVM_RC_PATH"
        
        # Install and use Node.js version from .nvmrc
        nvm install $(cat .nvmrc)
        nvm use $(cat .nvmrc)
        echo "✓ Node.js version set to $(node --version)"
    else
        echo "⚠️  NVM not found. Please install NVM first:"
        echo "   curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash"
        exit 1
    fi
else
    echo "❌ .nvmrc file not found in $(pwd)"
    echo "Current directory: $(pwd)"
    echo "Files in current directory:"
    ls -la
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 16 ]; then
    echo "❌ Node.js version too old. Please install Node.js 16+ or use NVM with .nvmrc"
    exit 1
fi

echo "Installing Node.js dependencies..."
npm install

echo "Setting up backup system..."
chmod +x setup-backup.sh
./setup-backup.sh

echo "Stopping existing backup process..."
pm2 delete backup-system || echo "No existing backup process found"

echo "Starting backup system with PM2..."
pm2 start pm2.backup.config.js

echo "Saving PM2 configuration..."
pm2 save

echo "=== Backup System Deployed Successfully ==="
echo "Check status with: pm2 status"
echo "View logs with: pm2 logs backup-system" 