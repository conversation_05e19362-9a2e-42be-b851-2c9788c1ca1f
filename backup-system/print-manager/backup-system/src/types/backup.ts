export interface DatabaseConfig {
  connectionString: string;
  type: 'postgresql' | 'mysql';
}

export interface StorageConfig {
  type: 's3' | 'gcs' | 'local';
  bucket: string;
  prefix?: string;
  credentials?: {
    accessKeyId?: string;
    secretAccessKey?: string;
    keyFilename?: string;
  };
  region?: string;
}

export interface BackupResult {
  filePath: string;
  fileName: string;
  size: number;
  compressed: boolean;
}

export interface DatabaseBackupProvider {
  backup(): Promise<BackupResult>;
  testConnection(): Promise<boolean>;
}

export interface StorageProvider {
  upload(filePath: string, fileName: string): Promise<string>;
  cleanupOldFiles(retentionDays: number): Promise<void>;
} 