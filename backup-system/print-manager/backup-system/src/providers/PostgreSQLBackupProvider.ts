import { exec } from 'child_process';
import * as path from 'path';
import * as fs from 'fs';
import { DatabaseBackupProvider, BackupResult, DatabaseConfig } from '../types/backup';

export class PostgreSQLBackupProvider implements DatabaseBackupProvider {
  private config: DatabaseConfig;
  private backupDir: string;

  constructor(config: DatabaseConfig) {
    this.config = config;
    this.backupDir = '/tmp/db-backups';
    
    if (!fs.existsSync(this.backupDir)) {
      fs.mkdirSync(this.backupDir, { recursive: true });
    }
  }

  private parseConnectionString(): { host: string; port: string; user: string; password: string; database: string } {
    const url = new URL(this.config.connectionString);
    
    return {
      host: url.hostname,
      port: url.port || '5432',
      user: url.username,
      password: url.password,
      database: url.pathname.slice(1)
    };
  }

  async testConnection(): Promise<boolean> {
    return new Promise((resolve) => {
      const { host, port, user, password, database } = this.parseConnectionString();
      
      const testCommand = `PGPASSWORD="${password}" psql -h ${host} -p ${port} -U ${user} -d ${database} -c "SELECT 1" 2>/dev/null`;
      
      exec(testCommand, { env: { ...process.env, PGPASSWORD: password } }, (error) => {
        if (error) {
          console.error('PostgreSQL connection test failed:', error);
          resolve(false);
        } else {
          console.log('PostgreSQL connection successful');
          resolve(true);
        }
      });
    });
  }

  async backup(): Promise<BackupResult> {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      const { host, port, user, password, database } = this.parseConnectionString();
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `postgres_${database}_${timestamp}.dump.gz`;
      const filePath = path.join(this.backupDir, fileName);
      const tempDumpFile = filePath.replace('.gz', '');
      
      const pgDumpCommand = `PGPASSWORD="${password}" pg_dump -h ${host} -p ${port} -U ${user} -d ${database} -Fc --verbose -f "${tempDumpFile}"`;
      
      console.log(`Creating PostgreSQL backup: ${fileName}`);
      
      exec(pgDumpCommand, { 
        env: { ...process.env, PGPASSWORD: password }
      }, (error, stdout, stderr) => {
        if (error) {
          const endTime = Date.now();
          const duration = endTime - startTime;
          const durationSeconds = Math.round(duration / 1000);
          const durationMinutes = Math.round(duration / 60000);
          
          console.error('PostgreSQL pg_dump error:', error);
          console.error('stderr:', stderr);
          console.error(`Backup failed after ${durationSeconds}s (${durationMinutes}m)`);
          
          if (fs.existsSync(tempDumpFile)) {
            fs.unlinkSync(tempDumpFile);
          }
          
          reject(error);
          return;
        }
        
        if (stderr) {
          console.log('PostgreSQL backup info:', stderr);
        }
        
        if (!fs.existsSync(tempDumpFile)) {
          const endTime = Date.now();
          const duration = endTime - startTime;
          const durationSeconds = Math.round(duration / 1000);
          const durationMinutes = Math.round(duration / 60000);
          
          console.error(`Backup file was not created: ${tempDumpFile}`);
          console.error(`Backup failed after ${durationSeconds}s (${durationMinutes}m)`);
          reject(new Error(`Backup file was not created: ${tempDumpFile}`));
          return;
        }
        
        const tempStats = fs.statSync(tempDumpFile);
        
        if (tempStats.size === 0) {
          fs.unlinkSync(tempDumpFile);
          reject(new Error(`Temp dump file is empty: ${tempDumpFile}`));
          return;
        }
        
        const gzipCommand = `gzip "${tempDumpFile}"`;
        exec(gzipCommand, (gzipError) => {
          if (gzipError) {
            console.error('Compression error:', gzipError);
            if (fs.existsSync(tempDumpFile)) {
              fs.unlinkSync(tempDumpFile);
            }
            
            reject(gzipError);
            return;
          }
          
          if (!fs.existsSync(filePath)) {
            console.error(`Compressed file was not created: ${filePath}`);
            
            if (fs.existsSync(tempDumpFile)) {
              fs.unlinkSync(tempDumpFile);
            }
            
            reject(new Error(`Compressed file was not created: ${filePath}`));
            return;
          }
          
          const stats = fs.statSync(filePath);
          
          console.log(`PostgreSQL backup completed: ${fileName} (${stats.size} bytes)`);
          
          resolve({
            filePath,
            fileName,
            size: stats.size,
            compressed: true
          });
        });
      });
    });
  }
} 