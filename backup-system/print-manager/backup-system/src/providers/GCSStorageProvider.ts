import { Storage } from '@google-cloud/storage';
import * as path from 'path';
import * as fs from 'fs';
import { StorageProvider, StorageConfig } from '../types/backup';

export class GCSStorageProvider implements StorageProvider {
  private config: StorageConfig;
  private storage: Storage;

  constructor(config: StorageConfig) {
    this.config = config;
    
    this.storage = new Storage({
      keyFilename: config.credentials?.keyFilename
    });
  }

  async upload(filePath: string, fileName: string): Promise<string> {
    if (!this.config.bucket) {
      throw new Error('GCS bucket not configured');
    }

    const destination = `${this.config.prefix || 'db-backups'}/${fileName}`;
    
    console.log(`Uploading backup to GCS: gs://${this.config.bucket}/${destination}`);
    
    try {
      await this.storage.bucket(this.config.bucket!).upload(filePath, {
        destination,
        metadata: {
          cacheControl: 'public, max-age=31536000',
        },
      });
      
      const url = `https://storage.googleapis.com/${this.config.bucket}/${destination}`;
      console.log(`Backup uploaded to GCS successfully: ${url}`);
      return url;
    } catch (error) {
      console.error('GCS upload error:', error);
      throw error;
    }
  }

  async cleanupOldFiles(retentionDays: number): Promise<void> {
    if (!this.config.bucket) {
      return;
    }

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);
    
    console.log(`Cleaning up backups older than ${retentionDays} days (before ${cutoffDate.toISOString()})`);
    
    try {
      const [files] = await this.storage.bucket(this.config.bucket!).getFiles({
        prefix: (this.config.prefix || 'db-backups') + '/'
      });

      if (files.length === 0) {
        console.log('No backup files found in GCS');
        return;
      }

      const filesToDelete = files.filter(file => {
        return file.metadata.timeCreated && new Date(file.metadata.timeCreated) < cutoffDate;
      });

      if (filesToDelete.length === 0) {
        console.log('No old backup files to clean up');
        return;
      }

      console.log(`Found ${filesToDelete.length} old backup files to delete`);

      await Promise.all(filesToDelete.map(file => file.delete()));
      
      console.log(`Successfully deleted ${filesToDelete.length} old backup files`);
    } catch (error) {
      console.error('GCS cleanup error:', error);
    }
  }
} 