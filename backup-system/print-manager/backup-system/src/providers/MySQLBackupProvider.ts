import * as path from 'path';
import * as fs from 'fs';
import mysqldump from 'mysqldump';
import { DatabaseBackupProvider, BackupResult, DatabaseConfig } from '../types/backup';

export class MySQLBackupProvider implements DatabaseBackupProvider {
  private config: DatabaseConfig;
  private backupDir: string;

  constructor(config: DatabaseConfig) {
    this.config = config;
    this.backupDir = '/tmp/db-backups';
    
    if (!fs.existsSync(this.backupDir)) {
      fs.mkdirSync(this.backupDir, { recursive: true });
    }
  }

  private parseConnectionString(): { host: string; port: number; user: string; password: string; database: string } {
    const url = new URL(this.config.connectionString);
    
    return {
      host: url.hostname,
      port: parseInt(url.port) || 3306,
      user: url.username,
      password: url.password,
      database: url.pathname.slice(1)
    };
  }

  async testConnection(): Promise<boolean> {
    try {
      const connection = this.parseConnectionString();
      
      await mysqldump({
        connection: {
          host: connection.host,
          port: connection.port,
          user: connection.user,
          password: connection.password,
          database: connection.database
        },
        dump: {
          schema: false,
          data: false
        }
      });
      
      console.log('MySQL connection successful');
      return true;
    } catch (error) {
      console.error('MySQL connection test failed:', error);
      return false;
    }
  }

  async backup(): Promise<BackupResult> {
    const connection = this.parseConnectionString();
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const fileName = `mysql_${connection.database}_${timestamp}.sql`;
    const filePath = path.join(this.backupDir, fileName);
    
    console.log(`Creating MySQL backup: ${fileName}`);
    
    try {
      if (!fs.existsSync(this.backupDir)) {
        fs.mkdirSync(this.backupDir, { recursive: true });
        console.log(`Created backup directory: ${this.backupDir}`);
      }
      
      const testFile = path.join(this.backupDir, 'test-write.tmp');
      try {
        fs.writeFileSync(testFile, 'test');
        fs.unlinkSync(testFile);
        console.log('Write permissions OK');
      } catch (writeError) {
        console.error('Write permission test failed:', writeError);
        throw new Error(`Cannot write to backup directory: ${this.backupDir}`);
      }
      
      const result = await mysqldump({
        connection: {
          host: connection.host,
          port: connection.port,
          user: connection.user,
          password: connection.password,
          database: connection.database
        },
        dump: {
          schema: {},
          data: {}
        }
      });
      
      const schemaPart = result.dump.schema || '';
      const dataPart = result.dump.data || '';
      const triggerPart = result.dump.trigger || '';
      
      const dumpContent = [schemaPart, dataPart, triggerPart]
        .filter(part => part !== null && part !== '')
        .join('\n\n');
      
      fs.writeFileSync(filePath, dumpContent);
      
      const stats = fs.statSync(filePath);
      console.log(`MySQL backup created: ${fileName} (${stats.size} bytes)`);
      
      return {
        filePath,
        fileName,
        size: stats.size,
        compressed: false
      };
    } catch (error) {
      console.error('MySQL backup error:', error);
      throw error;
    }
  }
} 