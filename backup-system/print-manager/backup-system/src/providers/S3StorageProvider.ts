import * as AWS from 'aws-sdk';
import * as path from 'path';
import * as fs from 'fs';
import { StorageProvider, StorageConfig } from '../types/backup';

export class S3StorageProvider implements StorageProvider {
  private config: StorageConfig;
  private s3: AWS.S3;

  constructor(config: StorageConfig) {
    this.config = config;
    
    const usingAWS = !process.env.BACKUP_ENDPOINT;
    
    if (usingAWS) {
      AWS.config.update({
        accessKeyId: config.credentials?.accessKeyId || process.env.BACKUP_AWS_ACCESS_KEY_ID?.trim(),
        secretAccessKey: config.credentials?.secretAccessKey || process.env.BACKUP_AWS_SECRET_ACCESS_KEY?.trim(),
        region: config.region || process.env.BACKUP_AWS_REGION?.trim() || 'eu-west-1'
      });
    } else {
      AWS.config.update({
        accessKeyId: config.credentials?.accessKeyId || process.env.BACKUP_AWS_ACCESS_KEY_ID?.trim(),
        secretAccessKey: config.credentials?.secretAccessKey || process.env.BACKUP_AWS_SECRET_ACCESS_KEY?.trim(),
        region: config.region || process.env.BACKUP_AWS_REGION?.trim() || 'eu-west-1',
        // @ts-ignore
        endpoint: new AWS.Endpoint(process.env.BACKUP_ENDPOINT!),
        signatureVersion: 'v4'
      });
    }
    
    this.s3 = new AWS.S3();
  }

  async upload(filePath: string, fileName: string): Promise<string> {
    if (!this.config.bucket) {
      throw new Error('S3 bucket not configured');
    }

    const s3Key = `${this.config.prefix || 'db-backups'}/${fileName}`;
    
    console.log(`Uploading backup to S3: s3://${this.config.bucket}/${s3Key}`);
    
    const fileStream = fs.createReadStream(filePath);
    
    const uploadParams = {
      Bucket: this.config.bucket!,
      Key: s3Key,
      Body: fileStream,
      ContentType: 'application/gzip',
      ServerSideEncryption: 'AES256'
    };

    return new Promise((resolve, reject) => {
      this.s3.upload(uploadParams, (error: any, data: any) => {
        if (error) {
          console.error('S3 upload error:', error);
          reject(error);
          return;
        }
        
        console.log(`Backup uploaded to S3 successfully: ${data.Location}`);
        resolve(data.Location);
      });
    });
  }

  async cleanupOldFiles(retentionDays: number): Promise<void> {
    if (!this.config.bucket) {
      return;
    }

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);
    
    console.log(`Cleaning up backups older than ${retentionDays} days (before ${cutoffDate.toISOString()})`);
    
    const listParams = {
      Bucket: this.config.bucket,
      Prefix: (this.config.prefix || 'db-backups') + '/'
    };

    return new Promise((resolve) => {
      this.s3.listObjectsV2(listParams, (error: any, data: any) => {
        if (error) {
          console.error('S3 list objects error:', error);
          resolve();
          return;
        }

        if (!data.Contents || data.Contents.length === 0) {
          console.log('No backup files found in S3');
          resolve();
          return;
        }

        const objectsToDelete = data.Contents.filter((obj: any) => {
          return new Date(obj.LastModified) < cutoffDate;
        });

        if (objectsToDelete.length === 0) {
          console.log('No old backup files to clean up');
          resolve();
          return;
        }

        console.log(`Found ${objectsToDelete.length} old backup files to delete`);

        const deleteParams = {
          Bucket: this.config.bucket!,
          Delete: {
            Objects: objectsToDelete.map((obj: any) => ({ Key: obj.Key }))
          }
        };

        this.s3.deleteObjects(deleteParams, (deleteError: any, deleteData: any) => {
          if (deleteError) {
            console.error('S3 delete objects error:', deleteError);
          } else {
            console.log(`Successfully deleted ${deleteData.Deleted?.length || 0} old backup files`);
          }
          resolve();
        });
      });
    });
  }
} 