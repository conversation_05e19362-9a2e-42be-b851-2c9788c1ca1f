var CronJob = require('cron').CronJob;
import * as fs from 'fs';
import { BackupFactory } from './factories/BackupFactory';
import { DatabaseConfig, StorageConfig } from './types/backup';

export const execute = async () => {
  const isEnabled = process.env.DB_BACKUP_ENABLED === 'true';
  
  if (!isEnabled) {
    console.log('Database backup is disabled');
    return;
  }

  try {
    console.log('Starting database backup...');
    
    const dbFile = await backup();
    if (dbFile) {
      await uploadToFileStorage(dbFile);
    }
    
    console.log('Database backup completed successfully');
  } catch (error) {
    console.error('Database backup failed:', error);
  }
};

const backup = async () => {
  const connectionString = process.env.DB_BACKUP_CONNECTION_STRING;
  
  if (!connectionString) {
    console.log('Database connection string not configured, skipping backup');
    return null;
  }

  const dbType = connectionString.split('://')[0];
  console.log(`Detected database type: ${dbType}`);
  
  switch (dbType) {
    case 'postgresql':
      return await backupPostgreSQL();
    case 'mysql':
      return await backupMySQL();
    default:
      console.log(`Unsupported database type: ${dbType}`);
      return null;
  }
};

const backupPostgreSQL = async () => {
  const connectionString = process.env.DB_BACKUP_CONNECTION_STRING;
  if (!connectionString) {
    console.log('PostgreSQL connection string not configured');
    return null;
  }

  const config: DatabaseConfig = {
    connectionString,
    type: 'postgresql'
  };

  const provider = BackupFactory.createDatabaseProvider(config);
  
  const isConnected = await provider.testConnection();
  if (!isConnected) {
    console.log('PostgreSQL connection failed, skipping backup');
    return null;
  }

  return await provider.backup();
};

const backupMySQL = async () => {
  const connectionString = process.env.DB_BACKUP_CONNECTION_STRING;
  if (!connectionString) {
    console.log('MySQL connection string not configured');
    return null;
  }

  const config: DatabaseConfig = {
    connectionString,
    type: 'mysql'
  };

  const provider = BackupFactory.createDatabaseProvider(config);
  
  const isConnected = await provider.testConnection();
  if (!isConnected) {
    console.log('MySQL connection failed, skipping backup');
    return null;
  }

  return await provider.backup();
};

const uploadToFileStorage = async (backupResult: any) => {
  const storageType = process.env.BACKUP_STORAGE_TYPE || 's3';
  
  if (storageType === 'local') {
    console.log('Keeping local backup file');
    return;
  }

  const bucket = process.env.BACKUP_STORAGE_BUCKET;
  if (!bucket) {
    console.log('Storage bucket not configured, keeping local backup file');
    return;
  }

  const config: StorageConfig = {
    type: storageType as 's3' | 'gcs',
    bucket,
    prefix: process.env.BACKUP_STORAGE_PREFIX || 'db-backups',
    credentials: {
      accessKeyId: process.env.BACKUP_AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.BACKUP_AWS_SECRET_ACCESS_KEY,
      keyFilename: process.env.BACKUP_GCS_KEY_FILENAME
    },
    region: process.env.BACKUP_AWS_DEFAULT_REGION || 'eu-west-1'
  };

  try {
    const provider = BackupFactory.createStorageProvider(config);
    const url = await provider.upload(backupResult.filePath, backupResult.fileName);
    
    if (fs.existsSync(backupResult.filePath)) {
      fs.unlinkSync(backupResult.filePath);
      console.log('Local backup file cleaned up after upload');
    }
    
    return url;
  } catch (error) {
    console.error('Storage upload failed:', error);
    console.log('Keeping local backup file due to upload failure');
  }
};

const cleanupOldBackups = async (): Promise<void> => {
  const storageType = process.env.BACKUP_STORAGE_TYPE || 's3';
  
  if (storageType === 'local') {
    console.log('Skipping cleanup for local storage');
    return;
  }

  const bucket = process.env.BACKUP_STORAGE_BUCKET;
  if (!bucket) {
    console.log('Storage bucket not configured, skipping cleanup');
    return;
  }

  const config: StorageConfig = {
    type: storageType as 's3' | 'gcs',
    bucket,
    prefix: process.env.BACKUP_STORAGE_PREFIX || 'db-backups',
    credentials: {
      accessKeyId: process.env.BACKUP_AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.BACKUP_AWS_SECRET_ACCESS_KEY,
      keyFilename: process.env.BACKUP_GCS_KEY_FILENAME
    },
    region: process.env.BACKUP_AWS_DEFAULT_REGION || 'eu-west-1'
  };

  try {
    const provider = BackupFactory.createStorageProvider(config);
    const retentionDays = parseInt(process.env.DB_BACKUP_RETENTION_DAYS || '30');
    await provider.cleanupOldFiles(retentionDays);
  } catch (error) {
    console.error('Cleanup failed:', error);
  }
};

export const backupDatabaseDaily = () => {
  const schedule = process.env.DB_BACKUP_SCHEDULE || '0 2,14 * * *'; // Default: 2 AM and 2 PM daily
  
  console.log(`Setting up database backup cron job with schedule: ${schedule}`);
  
  var job = new CronJob(schedule, function() {
    console.log('Running scheduled database backup...');
    execute().then(() => {
      cleanupOldBackups();
    });
  }, null, false, 'Europe/London');
  job.start();
  
  // Run initial backup after 5 seconds
  // setTimeout(() => {
  //   console.log('Running initial database backup check...');
  //   execute().then(() => {
  //     cleanupOldBackups();
  //   });
  // }, 5000);
  
  console.log('Database backup scheduled: twice daily (2 AM and 2 PM), retention: 30 days');
};