import { DatabaseBackupProvider, StorageProvider, DatabaseConfig, StorageConfig } from '../types/backup';
import { PostgreSQLBackupProvider } from '../providers/PostgreSQLBackupProvider';
import { MySQLBackupProvider } from '../providers/MySQLBackupProvider';
import { S3StorageProvider } from '../providers/S3StorageProvider';
import { GCSStorageProvider } from '../providers/GCSStorageProvider';

export class BackupFactory {
  static createDatabaseProvider(config: DatabaseConfig): DatabaseBackupProvider {
    const dbType = config.connectionString.split('://')[0];
    
    switch (dbType) {
      case 'postgresql':
        return new PostgreSQLBackupProvider(config);
      case 'mysql':
        return new MySQLBackupProvider(config);
      default:
        throw new Error(`Unsupported database type: ${dbType}`);
    }
  }

  static createStorageProvider(config: StorageConfig): StorageProvider {
    // Validate bucket is provided for S3 and GCS
    if ((config.type === 's3' || config.type === 'gcs') && !config.bucket) {
      throw new Error(`${config.type.toUpperCase()} bucket is required`);
    }

    switch (config.type) {
      case 's3':
        return new S3StorageProvider(config);
      case 'gcs':
        return new GCSStorageProvider(config);
      case 'local':
        throw new Error('Local storage provider not implemented yet');
      default:
        throw new Error(`Unsupported storage type: ${config.type}`);
    }
  }
} 