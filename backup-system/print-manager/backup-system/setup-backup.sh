#!/bin/bash

echo "=== PostgreSQL Complete Cleanup and Reinstall ==="

# Stop any running PostgreSQL services
echo "Stopping PostgreSQL services..."
systemctl stop postgresql* 2>/dev/null || true
service postgresql stop 2>/dev/null || true

# Remove all PostgreSQL packages (SUPPRESS verbose output)
echo "Removing all PostgreSQL packages..."
if command -v apt-get &> /dev/null; then
    echo "Using apt-get to remove PostgreSQL..."
    # Hide the long list of "not installed" packages
    DEBIAN_FRONTEND=noninteractive apt-get remove --purge -y postgresql* >/dev/null 2>&1 || true
    DEBIAN_FRONTEND=noninteractive apt-get remove --purge -y libpq* >/dev/null 2>&1 || true
    DEBIAN_FRONTEND=noninteractive apt-get autoremove -y >/dev/null 2>&1 || true
    DEBIAN_FRONTEND=noninteractive apt-get autoclean >/dev/null 2>&1 || true
    echo "✓ PostgreSQL packages removed"
fi

# Remove PostgreSQL directories
echo "Removing PostgreSQL directories and files..."
rm -rf /usr/lib/postgresql/ 2>/dev/null || true
rm -rf /etc/postgresql/ 2>/dev/null || true
rm -rf /var/lib/postgresql/ 2>/dev/null || true
rm -rf /usr/share/postgresql/ 2>/dev/null || true
rm -rf /var/log/postgresql/ 2>/dev/null || true
echo "✓ PostgreSQL directories removed"

# Remove PostgreSQL binaries from common locations
echo "Removing PostgreSQL binaries..."
rm -f /usr/bin/pg_dump /usr/bin/pg_restore /usr/bin/psql 2>/dev/null || true
rm -f /usr/local/bin/pg_dump /usr/local/bin/pg_restore /usr/local/bin/psql 2>/dev/null || true
rm -f /bin/pg_dump /bin/pg_restore /bin/psql 2>/dev/null || true
echo "✓ PostgreSQL binaries removed"

# Remove update-alternatives
echo "Removing update-alternatives..."
update-alternatives --remove-all pg_dump >/dev/null 2>&1 || true
update-alternatives --remove-all pg_restore >/dev/null 2>&1 || true
update-alternatives --remove-all psql >/dev/null 2>&1 || true
echo "✓ Update-alternatives cleaned"

# Clean environment variables
echo "Cleaning environment variables..."
if [ -f /etc/environment ]; then
    sed -i 's|/usr/lib/postgresql/[0-9]*/bin:||g' /etc/environment 2>/dev/null || true
    sed -i 's|:/usr/lib/postgresql/[0-9]*/bin||g' /etc/environment 2>/dev/null || true
fi
rm -f /etc/profile.d/postgresql*.sh 2>/dev/null || true

# Clean current shell environment
unset PGVERSION 2>/dev/null || true
export PATH=$(echo $PATH | sed 's|/usr/lib/postgresql/[0-9]*/bin:||g' | sed 's|:/usr/lib/postgresql/[0-9]*/bin||g')
echo "✓ Environment variables cleaned"

# Remove PostgreSQL repository files
echo "Removing PostgreSQL repository..."
rm -f /etc/apt/sources.list.d/pgdg.list 2>/dev/null || true
rm -rf /etc/apt/keyrings/postgresql.gpg* 2>/dev/null || true
echo "✓ Repository files removed"

echo "=== PostgreSQL cleanup completed ==="

# Set PostgreSQL version
BACKUP_POSTGRES_VERSION=${BACKUP_POSTGRES_VERSION:-16}
echo ""
echo "Installing PostgreSQL client version $BACKUP_POSTGRES_VERSION..."

if command -v apt-get &> /dev/null; then
    echo "Setting up installation environment..."
    
    # Update and install dependencies (hide verbose output)
    DEBIAN_FRONTEND=noninteractive apt-get update >/dev/null 2>&1
    DEBIAN_FRONTEND=noninteractive apt-get install -y wget ca-certificates gnupg lsb-release >/dev/null 2>&1
    
    # Get Ubuntu codename
    if command -v lsb_release &> /dev/null; then
        UBUNTU_CODENAME=$(lsb_release -cs)
    else
        UBUNTU_CODENAME="jammy"
    fi
    echo "✓ Ubuntu codename: $UBUNTU_CODENAME"
    
    # Set up PostgreSQL repository
    echo "Setting up PostgreSQL repository..."
    mkdir -p /etc/apt/keyrings
    chmod 0755 /etc/apt/keyrings
    
    # Download GPG key (hide verbose output)
    wget --quiet --output-document=- https://www.postgresql.org/media/keys/ACCC4CF8.asc | gpg --dearmor --output=/etc/apt/keyrings/postgresql.gpg --yes >/dev/null 2>&1 || {
        rm -f /etc/apt/keyrings/postgresql.gpg
        wget -qO- https://www.postgresql.org/media/keys/ACCC4CF8.asc > /tmp/postgresql.asc
        gpg --dearmor < /tmp/postgresql.asc > /etc/apt/keyrings/postgresql.gpg
        rm -f /tmp/postgresql.asc
    }
    
    chmod 0644 /etc/apt/keyrings/postgresql.gpg
    echo "deb [signed-by=/etc/apt/keyrings/postgresql.gpg] http://apt.postgresql.org/pub/repos/apt $UBUNTU_CODENAME-pgdg main" > /etc/apt/sources.list.d/pgdg.list
    echo "✓ Repository configured"
    
    # Update package lists
    echo "Updating package lists..."
    DEBIAN_FRONTEND=noninteractive apt-get update >/dev/null 2>&1
    echo "✓ Package lists updated"
    
    # Install PostgreSQL client (hide verbose output)
    echo "Installing postgresql-client-$BACKUP_POSTGRES_VERSION..."
    DEBIAN_FRONTEND=noninteractive apt-get install -y postgresql-client-$BACKUP_POSTGRES_VERSION >/dev/null 2>&1
    
    # Verify installation
    PG_BIN_DIR="/usr/lib/postgresql/$BACKUP_POSTGRES_VERSION/bin"
    
    if [ -x "$PG_BIN_DIR/pg_dump" ]; then
        echo "✓ PostgreSQL $BACKUP_POSTGRES_VERSION installed successfully"
        
        # Get version
        VERSION_OUTPUT=$($PG_BIN_DIR/pg_dump --version)
        echo "✓ Version: $VERSION_OUTPUT"
        
        # Set up system-wide defaults
        echo "Setting up system-wide defaults..."
        
        # Remove any existing alternatives first (hide output)
        update-alternatives --remove-all pg_dump >/dev/null 2>&1 || true
        update-alternatives --remove-all pg_restore >/dev/null 2>&1 || true
        update-alternatives --remove-all psql >/dev/null 2>&1 || true
        
        # Install new alternatives (hide output)
        update-alternatives --install /usr/bin/pg_dump pg_dump $PG_BIN_DIR/pg_dump 100 >/dev/null 2>&1
        update-alternatives --install /usr/bin/pg_restore pg_restore $PG_BIN_DIR/pg_restore 100 >/dev/null 2>&1
        update-alternatives --install /usr/bin/psql psql $PG_BIN_DIR/psql 100 >/dev/null 2>&1
        
        # Set as default
        update-alternatives --set pg_dump $PG_BIN_DIR/pg_dump >/dev/null 2>&1
        update-alternatives --set pg_restore $PG_BIN_DIR/pg_restore >/dev/null 2>&1
        update-alternatives --set psql $PG_BIN_DIR/psql >/dev/null 2>&1
        
        # Create profile script
        cat > /etc/profile.d/postgresql-$BACKUP_POSTGRES_VERSION.sh << EOF
#!/bin/bash
# PostgreSQL $BACKUP_POSTGRES_VERSION priority PATH
export PATH="$PG_BIN_DIR:\$PATH"
EOF
        chmod +x /etc/profile.d/postgresql-$BACKUP_POSTGRES_VERSION.sh
        
        # Update current PATH
        export PATH="$PG_BIN_DIR:$PATH"
        
        echo "✓ System configuration completed"
        
        # Clear bash hash table
        hash -r
        
        # Final verification
        echo ""
        echo "=== Installation Complete ==="
        
        # Test the installation
        FINAL_VERSION=$(pg_dump --version 2>/dev/null || echo "ERROR")
        echo "Final version check: $FINAL_VERSION"
        echo "pg_dump location: $(which pg_dump 2>/dev/null || echo 'NOT FOUND')"
        
        # Check if correct version
        if echo "$FINAL_VERSION" | grep -q "$BACKUP_POSTGRES_VERSION"; then
            echo "✅ SUCCESS: PostgreSQL $BACKUP_POSTGRES_VERSION is now the default version"
        else
            echo "⚠️  WARNING: Default version might not be $BACKUP_POSTGRES_VERSION"
            echo "Current default: $FINAL_VERSION"
        fi
        
    else
        echo "❌ Installation failed - binary not found at $PG_BIN_DIR/pg_dump"
        exit 1
    fi
    
else
    echo "❌ Unsupported package manager"
    exit 1
fi

echo ""
echo "=== INSTALLATION COMPLETE ==="
echo "✅ PostgreSQL $BACKUP_POSTGRES_VERSION installed and configured"
echo "✅ Run 'hash -r' if pg_dump command not found"
echo "✅ Or restart shell with: exec bash"
