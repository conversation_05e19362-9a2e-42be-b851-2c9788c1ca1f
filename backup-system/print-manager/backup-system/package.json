{"name": "backup-system", "version": "1.0.0", "description": "Database backup system for print-manager", "main": "index.js", "scripts": {"start": "ts-node index.ts", "build": "tsc", "dev": "ts-node-dev index.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"aws-sdk": "^2.1531.0", "cron": "^3.1.6", "dotenv": "^16.3.1", "mysqldump": "^3.2.0", "@google-cloud/storage": "^7.7.0"}, "devDependencies": {"@types/node": "^18.19.0", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "typescript": "^4.9.5"}, "keywords": ["backup", "database", "postgresql", "aws-s3"], "author": "", "license": "MIT"}