import boto3
import json
import logging
import os
import sys
from datetime import datetime

# from https://gist.github.com/niranjv/fb95e716151642e8ca553b0e38dd152e
logger = logging.getLogger()
for h in logger.handlers:
    logger.removeHandler(h)
h = logging.StreamHandler(sys.stdout)
FORMAT = '[%(levelname)s] %(message)s'
h.setFormatter(logging.Formatter(FORMAT))
logger.addHandler(h)
logger.setLevel(logging.INFO)

S3_BUCKET_NAME = os.environ['S3_BUCKET_NAME']
LOCAL_RENDER_FILE = '/tmp/render_file.blend'
CACHE_PATH = '/mnt/personify-efs'

def handler(event, context):
    try:
        received_body = event['body']
        record = json.loads(received_body)

        file_name = record['file_name']
        frame = record['frame']
        support_files = record['support_files']
        angle = record['angle'] or 0
        record_type = record['type']

        if record_type == 'prepare-s3':
            retrieve_files_from_s3(file_name, support_files)
            copy_files_to_cache_path(file_name, support_files)
        else:
            logger.info(f'Received message for file: {file_name} and frame: {frame} and angle: {angle}')
            retrieve_files_from_s3(file_name, support_files)
            frame_str = str(frame).zfill(4)
            output_file = f'/tmp/rendered_{frame_str}.png'
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            s3_uploaded_key = f'bg/2d-render/rendered_{frame_str}_{angle}_{timestamp}.png'
            render_frame(frame, output_file, angle)
            upload_file_to_s3(output_file, s3_uploaded_key)
            logger.info(f'Rendered frame: {frame} and uploaded file to S3')

        logger.info('Done.')
        response = {
            "statusCode": 200,
            "headers": {
                "content-type": "application/json"
            },
            "body": json.dumps({
                "ukey": s3_uploaded_key if record_type != 'prepare-s3' else None
            })
        }
        return response
    except Exception as e:
        logger.exception(e)
        raise e

def render_frame(frame, output_file, angle):
    logger.info(f'Rendering frame: {frame}')

    os.system(f"blender -b -P render_frame.py -- {LOCAL_RENDER_FILE} {output_file} {frame} {angle}")

    logger.info(f'Rendering frame: {frame} done')


def copy_files_to_cache_path(file_name, support_files):
    logger.info(f'Moving files to cache path: {CACHE_PATH}')

    blender_cache_path = f'{CACHE_PATH}/{gen_name_from_url(file_name)}'
    os.system(f'cp {LOCAL_RENDER_FILE} {blender_cache_path}')

    for file in support_files:
        destination_path = f'/tmp/{os.path.basename(file)}'
        os.system(f'cp {destination_path} {CACHE_PATH}')

    logger.info(f'Moving files to cache path: {CACHE_PATH} done')


def retrieve_files_from_s3(file_name, support_files):
    logger.info(f'Retrieving file: {file_name} from S3 bucket: {S3_BUCKET_NAME}')

    s3 = boto3.resource('s3')
    bucket = s3.Bucket(S3_BUCKET_NAME)

    blender_cache_path = f'{CACHE_PATH}/{gen_name_from_url(file_name)}'
    if os.path.exists(blender_cache_path):
        logger.info(f'File: {file_name} already exists in cache path: {blender_cache_path}')
        os.system(f'cp {blender_cache_path} {LOCAL_RENDER_FILE}')
    else:
        bucket.download_file(file_name, LOCAL_RENDER_FILE)
        logger.info(f'Retrieving file: {file_name} from S3 bucket: {S3_BUCKET_NAME} done')

    # help me check files in cache path first, only download if not exist
    for file in support_files:
        destination_path = f'/tmp/{os.path.basename(file)}'
        if os.path.exists(f'{CACHE_PATH}/{os.path.basename(file)}'):
            logger.info(f'File: {file} already exists in cache path: {CACHE_PATH}')
            # copy to destination_path
            os.system(f'cp {CACHE_PATH}/{os.path.basename(file)} {destination_path}')
            continue
        logger.info(f'Retrieving file: {file} from S3 bucket: {S3_BUCKET_NAME}')
        bucket.download_file(file, destination_path)
        logger.info(f'Retrieving file: {file} from S3 bucket: {S3_BUCKET_NAME} done')


def upload_file_to_s3(file_name, s3_uploaded_key):
    logger.info(f'Uploading file: {file_name} to S3 bucket: {S3_BUCKET_NAME}')
    
    s3 = boto3.resource('s3')
    bucket = s3.Bucket(S3_BUCKET_NAME)
    bucket.upload_file(file_name, s3_uploaded_key)

    logger.info(f'Uploading file: {file_name} to S3 bucket: {S3_BUCKET_NAME} done')


# the url will have the ending like "/bg/2d-render/blend-files/1701953143376/HW-16.blend" I need to return 1701953143376_HW-16.blend
def gen_name_from_url(url):
    return url.split('/')[-2] + '_' + url.split('/')[-1]
    

    
