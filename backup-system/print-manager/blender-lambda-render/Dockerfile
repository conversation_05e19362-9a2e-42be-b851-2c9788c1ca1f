FROM nvidia/cudagl:11.4.2-base-ubuntu20.04

# Install dependencies
RUN apt-get update && apt-get install -y \
	wget \ 
	libopenexr-dev \ 
	bzip2 \ 
	build-essential \ 
	zlib1g-dev \ 
	libxmu-dev \ 
	libxi-dev \ 
	libxxf86vm-dev \ 
	libfontconfig1 \ 
	libxrender1 \ 
	libgl1-mesa-glx \ 
	xz-utils

# Download and install Blender
RUN wget https://print-manager-media.s3.eu-west-1.amazonaws.com/bg/2d-render/blender-3.6.5-linux-x64.tar.xz \
	&& tar -xvf blender-3.6.5-linux-x64.tar.xz --strip-components=1 -C /bin \
	&& rm -rf blender-3.6.5-linux-x64.tar.xz \
	&& rm -rf blender-3.6.5-linux-x64
ARG DEBIAN_FRONTEND=noninteractive
RUN apt install software-properties-common -y
RUN apt-get install -y build-essential zlib1g-dev libncurses5-dev libgdbm-dev libnss3-dev libssl-dev libreadline-dev libffi-dev wget

# Environment variables
ENV DEBIAN_FRONTEND noninteractive
ENV LC_ALL C.UTF-8
ENV LANG C.UTF-8
ENV PATH "$PATH:/bin/3.6/python/bin"
ENV BLENDER_PATH "/bin/3.6"
ENV BLENDERPIP "/bin/3.6/bin/python/site-packages/pip"
ENV BLENDERPY "/bin/3.6/python/bin/python3.10"
ENV HW="GPU"

# Download the Python source since it is not bundled with Blender
# RUN wget https://www.python.org/ftp/python/3.10.5/Python-3.10.5.tgz \
# 	&& tar -xzf Python-3.10.5.tgz \
# 	&& mkdir -p $BLENDER_PATH/python/include/python3.10 \
# 	&& cp -r Python-3.10.5/Include/* $BLENDER_PATH/python/include/python3.10/ \
# 	&& rm -rf Python-3.10.5.tgz \
# 	&& rm -rf Python-3.10.5

# RUN apt install software-properties-common -y
# RUN add-apt-repository ppa:deadsnakes/ppa -y
# RUN apt install python3.10 -y

COPY ./Python-3.10.5.tgz .
RUN tar -xzf Python-3.10.5.tgz \
	&& cd Python-3.10.5 \
	&& ./configure --enable-optimizations \
	&& make -j $(nproc) \
	&& make altinstall

RUN Python-3.10.5/python -m pip install --upgrade pip

RUN mkdir -p $BLENDER_PATH/python/include/python3.10
RUN cp -r Python-3.10.5/Include/* $BLENDER_PATH/python/include/python3.10/
RUN cp -r /usr/local/lib/python3.10/* $BLENDER_PATH/python/lib/python3.10

# Blender comes with a super outdated version of numpy (which is needed for matplotlib / opencv) so override it with a modern one
# RUN rm -rf ${BLENDER_PATH}/python/lib/python3.10/site-packages/numpy

# Must first ensurepip to install Blender pip3 and then new numpy
RUN pip3.10 install numpy

ARG FUNCTION_DIR="/home/<USER>"
RUN mkdir -p $FUNCTION_DIR
WORKDIR ${FUNCTION_DIR}

RUN pip3.10 install boto3
RUN pip3.10 install awslambdaric --target ${FUNCTION_DIR}

RUN apt-get install libxkbcommon-x11-0 -y

# ENTRYPOINT [ "/bin/3.6/python/bin/python3.10", "-m", "awslambdaric" ]
ENTRYPOINT [ "/usr/local/bin/python3.10", "-m", "awslambdaric" ]
CMD [ "consumer_function.handler" ]

# ENTRYPOINT [ "/bin/sh"]

COPY *.py ${FUNCTION_DIR}/
RUN chmod 755 $(find . -type d) && \
    chmod 644 $(find . -type f)