docker run --platform linux/amd64 --rm -v ~/.aws-lambda-rie:/aws-lambda -p 9000:8080 \
-e AWS_ACCESS_KEY_ID='********************' \
-e AWS_SECRET_ACCESS_KEY='KpHHeDHu102sKq8bR6kbLUtDIuRLBvUGdTl9AK6z' \
-e AWS_REGION='eu-west-1' \
-e AWS_DEFAULT_REGION='eu-west-1' \
-e S3_BUCKET_NAME='print-manager-media' \
  --entrypoint /aws-lambda/aws-lambda-rie \
  blender-lambda-consumer:latest \
    /usr/local/bin/python3.10 -m awslambdaric consumer_function.handler