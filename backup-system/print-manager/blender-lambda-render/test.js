const path = require('path');
const fs = require('fs');
const axios = require('axios');

// console.log(path.parse('https://s3.com/s4zip.zip'));
const scanFiles = (dir) => {
    const files = [];
    const doingScan = (dir) => {
        fs.readdirSync(dir).forEach(file => {
            let fullPath = path.join(dir, file);
            if (fs.lstatSync(fullPath).isDirectory()) {
                doingScan(fullPath);
            } else {
                files.push(fullPath);
            }
        });
    };
    doingScan(dir);
    return files;
}

// console.log(scanFiles(process.argv[2]));
/*
$: node test src            
[
  'src/AWSHelper.js',
  'src/Artwork.png',
  'src/index.js',
  'src/render.js',
  'src/render_frame.py',
  'src/runCLI.js'
]
*/


axios.request({
  // url: 'http://localhost:4433/gen-blender',
  // url: 'https://2d-render.personify.tech/gen-blender',
//   url: 'https://mbwh0zzse4.execute-api.eu-west-1.amazonaws.com/default/blender-node-bg',
  // url: 'https://g8mm24lajd.execute-api.eu-west-1.amazonaws.com/default/blender-node-bg',
  url: 'http://localhost:9000/2015-03-31/functions/function/invocations',
  method: 'post',
  headers: {
    'Content-Type': 'application/json',
  },
  data: JSON.stringify({
    body: JSON.stringify({
        "file_name": "bg/2d-render/Glass-10.blend",
        "frame": "2",
        "support_files": ["bg/2d-render/small_empty_room_3_4k.exr", "bg/2d-render/Artwork.png"]
    })
  }),
}).then(res => {
  console.log(res.data);
})