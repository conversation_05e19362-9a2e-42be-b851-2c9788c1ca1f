const AWS = require('aws-sdk');
const fs = require('fs');
const mime = require('mime');
const axios = require('axios');


class AWSHelper {
    constructor() {
      this.init();
      this.checkIfAWSWorking();
    }
  
    s3Info = {
      access_key: '********************',
      access_secret: 'KpHHeDHu102sKq8bR6kbLUtDIuRLBvUGdTl9AK6z',
      bucket: 'print-manager-media',
      region: 'eu-west-1'
    };
    s3
  
    init() {
      AWS.config.update({
        region: 'eu-west-1',
        accessKeyId: this.s3Info.access_key,
        secretAccessKey: this.s3Info.access_secret,
      });
      this.s3 = new AWS.S3();
    }
  
    checkIfAWSWorking = () => {
      const sts = new AWS.STS();
      sts.getCallerIdentity({}, function (err, data) {
        if (err) {
          console.log("Error", err);
        } else {
          console.log("Successfully get the identity");
          console.log(data);
        }
      });
    }
  
    getSignedUrl(key) {
      const url = this.s3.getSignedUrl('getObject', {
        Bucket: this.s3Info.bucket,
        Key: key,
        Expires: 60 * 15,
      })
      return url;
    }
  
    upload = async ({ filePath, key }, shouldLog = false) => {
      const objectParams = {
        ACL: 'public-read',
        Bucket: this.s3Info.bucket,
        Key: key,
        Body: fs.createReadStream(filePath),
        ContentType: mime.getType(filePath),
      };
      const res = await this.s3.putObject(objectParams).promise();
      if (shouldLog) console.log(res);
      return `https://${this.s3Info.bucket}.s3.eu-west-1.amazonaws.com/${key}`;
    }
  
    uploadRaw = async ({ data, key, contentType }, shouldLog = false) => {
      const objectParams = {
        ACL: 'public-read',
        Bucket: this.s3Info.bucket,
        Key: key,
        Body: data,
        ContentType: contentType,
      };
      const res = await this.s3.putObject(objectParams).promise();
      if (shouldLog) console.log(res);
      return `https://${this.s3Info.bucket}.s3.eu-west-1.amazonaws.com/${key}`;
    }
  
    uploadFromURL = async ({ url, key, isPrivate = false }, shouldLog = false) => {
      const response = await axios.get(url, { responseType: 'arraybuffer' });
      const fileBuffer = Buffer.from(response.data, 'binary');
      const fileType = mime.getExtension(response.headers['content-type']);
      const contentType = ['jpeg', 'png', 'webp', 'gif'].includes(fileType)
        ? `image/${fileType}`
        : 'application/octet-stream'
  
      const objectParams = {
        ACL: isPrivate ? 'private' : 'public-read',
        Bucket: this.s3Info.bucket,
        Key: key,
        Body: fileBuffer,
        ContentType: contentType,
      };
  
      const res = await this.s3.putObject(objectParams).promise();
      if (shouldLog) console.log(res);
      return `https://${this.s3Info.bucket}.s3.eu-west-1.amazonaws.com/${key}`;
    }
  
    getSignedUrlForUpload(key, contentType) {
      const url = this.s3.getSignedUrl('putObject', {
        Bucket: this.s3Info.bucket,
        Key: key,
        Expires: 60 * 15,
        ACL: 'public-read',
        ContentType: contentType,
      })
      return url;
    }
  }
  
module.exports = new AWSHelper();