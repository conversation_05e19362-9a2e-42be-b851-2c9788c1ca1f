{"name": "nodejs-server", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "docker:build": "docker buildx build --platform linux/amd64 -t blender-render:latest .", "docker:build:runpod": "docker buildx build --platform linux/amd64 -t blender-render:runpod-serverless-202408202154 .", "docker:login:aws": "aws ecr get-login-password --region eu-west-1 | docker login --username AWS --password-stdin 680042302044.dkr.ecr.eu-west-1.amazonaws.com", "docker:login:gitlab": "TOKEN=\"**************************\" docker login gitlab.personify.tech:4567 -u le.huy --password-stdin", "docker:push:runpod:aws": "docker tag blender-render:runpod-serverless 680042302044.dkr.ecr.eu-west-1.amazonaws.com/blender-render-with-angle:runpod-serverless && docker push 680042302044.dkr.ecr.eu-west-1.amazonaws.com/blender-render-with-angle:runpod-serverless", "docker:push:runpod:dockerhub": "docker tag blender-render:runpod-serverless-202408202154 lequanghuylc/blender-print-on-demand-preview:runpod-serverless-202408202154 && docker push lequanghuylc/blender-print-on-demand-preview:runpod-serverless-202408202154", "docker:run:bash": "docker run -it --entrypoint \"bash\" --rm -e DEVICE_TYPE=NONE blender-render:latest", "docker:run:runpod": "docker run -it --entrypoint \"bash\" --rm -e DEVICE_TYPE=NONE blender-render:runpod-serverless-202408202154"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"async": "^3.2.5", "aws-sdk": "^2.1545.0", "axios": "^1.6.7", "body-parser": "^1.20.2", "dotenv": "^16.4.5", "express": "^4.18.2", "mime": "2.6.0"}}