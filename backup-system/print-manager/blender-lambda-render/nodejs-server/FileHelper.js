const fs = require('fs');
const axios = require('axios');
const path = require('path');
class FileHelper {

  fileToBase64(filePath) {
    const buffer = fs.readFileSync(filePath);
    const base64 = Buffer.from(buffer).toString('base64');
    return base64;
  }

  base64ToFile(base64Data, filePath) {
    let b = base64Data.includes(';base64,') ? base64Data.split(';base64,').pop() : base64Data;
    fs.writeFileSync(filePath, b, 'base64');
  }

  getFileFromUrlOrBase64(url, base64Data, filePath) {
    if (!url) return this.base64ToFile(base64Data, filePath);
    return this.downloadFile(url, filePath);
  }

  async downloadFile(fileUrl, outputLocationPath, otherAxiosOptions = {}) {
    const writer = fs.createWriteStream(outputLocationPath);

    return new Promise((resolve, reject) => {
      axios({
        method: 'get',
        url: fileUrl,
        responseType: 'stream',
        ...otherAxiosOptions,
      }).then(response => {
        response.data.pipe(writer);
        let error = null;
        writer.on('error', err => {
          error = err;
          writer.close();
          reject(err);
        });
        writer.on('close', () => {
          if (!error) {
            resolve(true);
          }
        });
      }).catch(err => {
        reject(err);
      });
    });
  }
}

module.exports = new FileHelper();