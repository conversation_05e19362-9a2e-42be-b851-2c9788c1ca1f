import sys
import bpy
import math
import os

def enable_gpus(device_type, use_cpus=False):
    preferences = bpy.context.preferences
    cycles_preferences = preferences.addons["cycles"].preferences
    cycles_preferences.refresh_devices()
    devices = cycles_preferences.devices

    if not devices:
        raise RuntimeError("Unsupported device type")

    activated_gpus = []
    for device in devices:
        if device.type == "CPU":
            device.use = use_cpus
        else:
            device.use = True
            activated_gpus.append(device.name)
            print('activated gpu', device.name)
            print('activated gpu', device.type)

    cycles_preferences.compute_device_type = device_type
    bpy.context.scene.cycles.device = "GPU"

    return activated_gpus

# read from env var
device_type = os.environ.get('DEVICE_TYPE', 'CUDA')
enable_gpus(device_type)

argv = sys.argv
argv = argv[argv.index("--") + 1:]

input_file = argv[0]
output_file = argv[1]
frame_number = int(argv[2])
angle = int(argv[3] or '0')
rotation_angle = math.radians(angle)
rotation_layer = argv[4]
resolution_x = int(argv[5] or '840')
resolution_y = int(argv[6] or '840')
artwork_layer = argv[7]

bpy.ops.wm.open_mainfile(filepath=input_file, load_ui=False)

rotate_only_one = False
# loop through all object, and if Artwork-And-Product exist then rotate_only_one = True
for ob in bpy.data.objects:
    if ob.name == 'Artwork-And-Product':
        rotate_only_one = True
        break

# also rotate rotation_layer, check if rotation_layer != 'none'
if rotation_layer != 'none' and rotate_only_one == False:
    ob = bpy.data.objects.get(rotation_layer)
    if ob is not None:
        ob.rotation_mode = 'XYZ'
        ob.rotation_euler = (0, 0, rotation_angle)
        bpy.context.view_layer.update()
    else:
        print(f"Object '{rotation_layer}' not found.")

object_name = artwork_layer
# if None then use a default value
if object_name is None:
    object_name = 'Artwork-Model'
ob = bpy.data.objects.get(object_name)

if ob is not None and rotate_only_one == False:
    if angle != 0:
        ob.rotation_euler = (0, 0, rotation_angle)
        ob.rotation_mode = 'XYZ'
        bpy.context.view_layer.update()
else:
    print(f"Object '{object_name}' not found.")

if rotate_only_one == True:
    for ob in bpy.data.objects:
        if ob.name == 'Artwork-And-Product':
            ob.rotation_euler = (0, 0, rotation_angle)
            ob.rotation_mode = 'XYZ'
            bpy.context.view_layer.update()
            break
    
r = bpy.context.scene.render
r.resolution_x = resolution_x
r.resolution_y = resolution_y

bpy.context.scene.frame_set(frame_number)
bpy.context.scene.render.filepath = output_file

bpy.ops.render.render(write_still = True)
