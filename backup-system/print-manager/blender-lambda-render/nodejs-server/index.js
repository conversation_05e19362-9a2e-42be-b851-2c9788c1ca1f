require('dotenv').config()
const FileHelper = require('./FileHelper');
const AWSHelper = require('./AWSHelper');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const startWebServer = require('./webserver');

const FILES_PATH = path.join(__dirname, 'files');
if (!fs.existsSync(FILES_PATH)) {
  fs.mkdirSync(FILES_PATH);
}
const RENDER_PYTHON = path.join(__dirname, 'render_frame_gpu.py');

const handleRender = async (body) => {
  const {
    artworkBase64, artworkUrl, blend, support, angle, debug,
    additionRotationLayer, resolution_x, resolution_y,
    mainArtworkRotationLayer, type, ouput,
  } = body;
  console.log('NEW JOB', {
    artworkUrl, blend, support, angle, debug, additionRotationLayer,
    mainArtworkRotationLayer
  })
  const [blendFolderName, blendFileName] = blend.slice(blend.indexOf('blend-files') + 'blend-files/'.length, blend.length).split('/');
  console.log(blendFolderName, blendFileName);
  const [supportFolderName, supportFileName] = support.slice(support.indexOf('blend-files') + 'blend-files/'.length, support.length).split('/');
  console.log(supportFolderName, supportFileName);

  const blendFolderPath = path.join(FILES_PATH, blendFolderName);
  if (!fs.existsSync(blendFolderPath)) fs.mkdirSync(blendFolderPath);
  const supportFolderPath = path.join(FILES_PATH, supportFolderName);
  if (!fs.existsSync(supportFolderPath)) fs.mkdirSync(supportFolderPath);

  const blendFilePath = path.join(blendFolderPath, blendFileName);
  const supportFilePath = path.join(supportFolderPath, supportFileName);
  console.log('type', type);
  if (type === 'prepare-s3') {
    // blend and support is s3 url, download it to local
    if (!fs.existsSync(blendFilePath)) {
      await FileHelper.downloadFile(blend, blendFilePath);
    }
    if (!fs.existsSync(supportFilePath)) {
      await FileHelper.downloadFile(support, supportFilePath);
    }
    return '';
  }

  // create a random folder and save the blend and support files there
  const folder = 'render_' + Math.random().toString(36).substring(7);
  const folderPath = path.join(FILES_PATH, folder);
  if (!fs.existsSync(folderPath)) fs.mkdirSync(folderPath);
  console.log('folderPath: ', folderPath);

  const artworkWorkingPath = path.join(folderPath, 'Artwork.png');
  const blendWorkingPath = path.join(folderPath, blendFileName);
  const supportWorkingPath = path.join(folderPath, supportFileName);
  await Promise.all([
    FileHelper.getFileFromUrlOrBase64(artworkUrl, artworkBase64, artworkWorkingPath),
    (async () => {
      if (fs.existsSync(blendFilePath)) {
        console.log('blend file exists, copying...');
        fs.copyFileSync(blendFilePath, blendWorkingPath);
      } else {
        await FileHelper.downloadFile(blend, blendWorkingPath);
        fs.copyFile(blendWorkingPath, blendFilePath, () => {});
      }
    })(),
    (async () => {
      if (fs.existsSync(supportFilePath)) {
        console.log('support file exists, copying...');
        fs.copyFileSync(supportFilePath, supportWorkingPath);
      } else {
        await FileHelper.downloadFile(support, supportWorkingPath);
        fs.copyFile(supportWorkingPath, supportFilePath, () => {});
      }
    })(),
  ]);

  // run command: blender -b -P render_frame.py -- {LOCAL_RENDER_FILE} {output_file} {frame} {angle}
  fs.copyFileSync(RENDER_PYTHON, path.join(folderPath, 'render_frame_gpu.py'));
  const blenderPath = process.env.BLENDER_PATH || 'blender';
  let command = `DEVICE_TYPE=${process.env.DEVICE_TYPE} ${blenderPath} -b -P render_frame_gpu.py -- ${blendFileName} render0002.png 2 ${angle} ${additionRotationLayer || 'none'} ${resolution_x || 840} ${resolution_y || 840} ${mainArtworkRotationLayer || 'Artwork-Model'}`;
  console.log('running command: ', command);
  // run exec command

  const runCommand = () => new Promise((resolve, reject) => {
    exec(command, {
      cwd: folderPath,
    }, (err, stdout, stderr) => {
      if (err) {
        console.log('error: ', err);
      }
      console.log('stdout: ', stdout);
      console.log('stderr: ', stderr);
      resolve();
    });
  });
  await runCommand();
  const outputPath = path.join(folderPath, 'render0002.png');
  // const compressedOutputPath = path.join(folderPath, 'render0002_compressed.jpg');
  // await sharp(outputPath)
  //   .resize(1000)
  //   .jpeg({ quality: 100 })
  //   .toFile(compressedOutputPath);
  const s3_upload_key = ouput || `bg/2d-render/rendered_0002_${angle}_${new Date().getTime()}.png`;
  const s3Url = await AWSHelper.upload({
    filePath: outputPath,
    key: s3_upload_key,
  });
  console.log('s3Url', s3Url);
  // remove folder
  if (!debug) {
    fs.rmdirSync(folderPath, { recursive: true });
  }
  return s3_upload_key;
}

if (process.argv[2]) {
  const artworkUrl = process.argv[2];
  const blend = process.argv[3];
  const support = process.argv[4];
  const angle = process.argv[5];
  const ouput = process.argv[6];
  handleRender({
    artworkUrl, blend, support, angle,
    type: '',
    debug: true,
    ouput,
  }).then((s3_upload_key) => {
    // console.log(s3_upload_key);
    fs.writeFileSync('output.txt', s3_upload_key);
    process.exit(0);
  });
} else {
  startWebServer(handleRender, { FILES_PATH, RENDER_PYTHON });
}