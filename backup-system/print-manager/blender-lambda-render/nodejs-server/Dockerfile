FROM nvidia/cudagl:11.4.2-base-ubuntu20.04

# Install dependencies
RUN apt-get update && apt-get install -y \
	wget \ 
	libopenexr-dev \ 
	bzip2 \ 
	build-essential \ 
	zlib1g-dev \ 
	libxmu-dev \ 
	libxi-dev \ 
	libxxf86vm-dev \ 
	libfontconfig1 \ 
	libxrender1 \ 
	libgl1-mesa-glx \ 
	xz-utils

ENV DEBIAN_FRONTEND=noninteractive
RUN apt-get update && apt-get install -y --no-install-recommends \
	wget \
	ca-certificates \
	libgl1-mesa-glx \
	libx11-6 \
	libxi6 \
	libxrender1 \
	libxrandr2 \
	libxcursor1 \
	libxinerama1 \
	libxxf86vm1 \
	libglu1-mesa \
	python3 \
	python3-pip \
	&& rm -rf /var/lib/apt/lists/*

RUN pip3 install numpy
RUN apt-get update
RUN apt install software-properties-common -y
RUN apt-get install -y build-essential zlib1g-dev libncurses5-dev libgdbm-dev libnss3-dev libssl-dev libreadline-dev libffi-dev wget

RUN wget https://download.blender.org/release/Blender4.2/blender-4.2.0-linux-x64.tar.xz -O /tmp/blender.tar.xz \
    && tar -xf /tmp/blender.tar.xz -C /opt/ \
    && rm /tmp/blender.tar.xz

# Create a symlink to make Blender accessible globally
RUN ln -s /opt/blender-4.2.0-linux-x64/blender /usr/local/bin/blender

ENV BLENDER_PATH="/usr/local/bin/blender"
ENV HW="GPU"

ARG FUNCTION_DIR="/home/<USER>"
RUN mkdir -p $FUNCTION_DIR
WORKDIR ${FUNCTION_DIR}
RUN apt-get install libxkbcommon-x11-0 -y

RUN rm /bin/sh && ln -s /bin/bash /bin/sh
ENV NVM_DIR /usr/local/nvm

RUN apt-get update && apt-get install -y -q --no-install-recommends \
  apt-transport-https \
  build-essential \
  ca-certificates \
  curl \
  git \
  libssl-dev \
  wget \
  && rm -rf /var/lib/apt/lists/*

RUN mkdir -p "$NVM_DIR"; \
    curl -o- \
        "https://raw.githubusercontent.com/nvm-sh/nvm/master/install.sh" | \
        bash \
    ; \
    source $NVM_DIR/nvm.sh; \
    nvm install 18.19.0 && ln -s "$NVM_DIR/versions/node/$(nvm version)/bin/node" "/usr/local/bin/node" && ln -s "$NVM_DIR/versions/node/$(nvm version)/bin/npm" "/usr/local/bin/npm" && ln -s "$NVM_DIR/versions/node/$(nvm version)/bin/pm2" "/usr/local/bin/pm2" && ln -s "$NVM_DIR/versions/node/$(nvm version)/bin/yarn" "/usr/local/bin/yarn"
RUN npm i -g yarn
WORKDIR /tmp
RUN mkdir -p /tmp/install-dep
COPY package.json yarn.lock /tmp/install-dep/
RUN cd /tmp/install-dep && yarn install

WORKDIR ${FUNCTION_DIR}
COPY . ${FUNCTION_DIR}/
RUN cp -r /tmp/install-dep/node_modules ${FUNCTION_DIR}/

RUN pip3 install --no-cache-dir runpod
COPY rp_handler.py .

# FOR Runpod serverless, use this
ENTRYPOINT ["python3", "-u", "rp_handler.py"]

# FOR AWS Batch, use this
# ENTRYPOINT [ "node",  "index.js" ]
