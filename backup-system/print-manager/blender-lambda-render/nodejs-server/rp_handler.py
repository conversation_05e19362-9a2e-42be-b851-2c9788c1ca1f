import runpod
import subprocess
import asyncio
import os
import time

# ---------------------------------------------------------------------------- #
#                                RunPod Handler                                #
# ---------------------------------------------------------------------------- #
async def handler(event):
    """
    This is the handler function that will be called by <PERSON><PERSON><PERSON> serverless.
    """
    # return process_input(event['input'])
    # return {
    #   "output": ""
    # }
    input = event['input']
    artworkUrl = input['artworkUrl']
    blend = input['blend']
    support = input['support']
    angle = input['angle']
    fileoutput = input['ouput']
    # command = f"node index.js {artworkUrl} {blend} {support} {angle} {output}"
    process = await asyncio.create_subprocess_exec(
        'node', 'index.js', artworkUrl, blend, support, angle, fileoutput,
        stdout=asyncio.subprocess.PIPE,
        stderr=asyncio.subprocess.PIPE
    )

    stdout, stderr = await process.communicate()
    print(f'[{process.pid}] stdout: {stdout.decode()}')

    if process.returncode == 0:
      output_file = 'output.txt'
      if os.path.exists(output_file):
        with open(output_file, 'r') as file:
          s3_key = file.read()
          # remove output file
          os.remove(output_file)
          return s3_key
    else:
      return ""  

if __name__ == '__main__':
    runpod.serverless.start({'handler': handler})