const express = require('express');
const bodyParser = require('body-parser');
const async = require('async');
const concurrency = 1;

module.exports = (handleRender, { FILES_PATH, RENDER_PYTHON }) => {
  const app = express();
  let q = async.queue(async function (task, callback) {
    // Here, write the CPU intensive task
    // When the task is done, call the callback
    const { req, res } = task;
    const s3_upload_key = await handleRender(req.body);
    res.json({ ukey: s3_upload_key });
    callback();
  }, concurrency);


  q.drain(function () {
    console.log('All tasks have been processed');
  });

  app.use(bodyParser.json({ limit: '50mb' }));

  app.listen(4387, () => console.log('Server running on port 4387'));

  app.get('/', (req, res) => {
    res.json({ message: 'Hello World' });
  })

  
  // set FILES_Path static
  app.use('/files', express.static(FILES_PATH));

  app.post('/', async (req, res) => {
    q.push(({ req, res }), function (err) {
      console.log('err', err);
    });
  })
}