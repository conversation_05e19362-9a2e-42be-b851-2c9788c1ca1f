### readme

This is a docker image, push to AWS ECR

run commands:
- make sure `Python-3.10.5.tgz` in folder (download if not)
- `build.sh`
- `push.sh`

to test lambda locally:
- `build.sh`
- `run.sh`
- `node test.js`

### CURL

```
curl --location 'https://msubg5vjtjs3n23niqk4vwfwre0tunct.lambda-url.eu-west-1.on.aws/' \
--header 'Cache-Control: no-cache' \
--header 'Content-Type: application/json' \
--data '{
    "file_name": "bg/2d-render/Glass-10.blend",
    "frame": "2",
    "support_files": ["bg/2d-render/small_empty_room_3_4k.exr", "bg/2d-render/Artwork.png"]
}'
```