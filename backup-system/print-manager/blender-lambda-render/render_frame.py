import sys
import bpy
import math

argv = sys.argv
argv = argv[argv.index("--") + 1:]

input_file = argv[0]
output_file = argv[1]
frame_number = int(argv[2])
angle = int(argv[3] or '0')
rotation_angle = math.radians(angle)

bpy.ops.wm.open_mainfile(filepath=input_file, load_ui=False)

object_name = 'Artwork-Model'
ob = bpy.data.objects.get(object_name)

if ob is not None:
    if angle != 0:
        ob.rotation_euler = (0, 0, rotation_angle)
        bpy.context.view_layer.update()
else:
    print(f"Object '{object_name}' not found.")

bpy.context.scene.frame_set(frame_number)
bpy.context.scene.render.filepath = output_file

bpy.ops.render.render(write_still = True)
