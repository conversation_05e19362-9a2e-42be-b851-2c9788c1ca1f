import { IScreen } from 'type';
import React, { useState, useRef, useEffect } from 'react';
import { SCREEN } from 'const';
import { useNavFunc } from 'navigation';
import {
  Animated,
  useWindowDimensions,
} from 'react-native';
import AcceptInviteAnimated from './AcceptInviteAnimated';

const FORM_WIDTH = 320;
const FORM_HEIGHT = 500;

const HEADER_LOGIN_WIDTH = 269 * 0.7;
const HEADER_LOGIN_HEIGHT = 79 * 0.7;

const LOGO_HEIGHT = { BEFORE: 120, AFTER: HEADER_LOGIN_HEIGHT };

const calcFormInCenterPosition = (w, h) => {
  return {
    x: (w - FORM_WIDTH) / 2,
    y: (h - FORM_HEIGHT) / 2,
  }
};

const animate = (
  value: Animated.Value | Animated.ValueXY,
  toValue: Animated.TimingAnimationConfig['toValue'],
  duration: Animated.TimingAnimationConfig['duration'],
  delay: Animated.TimingAnimationConfig['delay'] = 0,
) => new Promise((resolve) => {
  Animated.timing(value, {
    toValue,
    duration,
    delay,
    useNativeDriver: false,
  }).start(({ finished }) => {
    resolve(finished);
  });
});

const AcceptInvite: IScreen = () => {
  const { reset, route } = useNavFunc();
  const { email, code }: any = route.params || {};
  const { width: innerWidth, height: innerHeight } = useWindowDimensions();
  const [showWelcomeMessage, setShowWelcomeMessage] = useState(false);
  const [showAfterLoginLayout, setShowAfterLoginLayout] = useState(false);

  // anim params
  const position = useRef(new Animated.ValueXY(calcFormInCenterPosition(innerWidth, innerHeight))).current;
  useEffect(() => {
    Animated.timing(position, {
      toValue: calcFormInCenterPosition(innerWidth, innerHeight),
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [innerWidth, innerHeight]);

  const logoHeight = useRef(new Animated.Value(LOGO_HEIGHT.BEFORE)).current;
  const containerWidth = useRef(new Animated.Value(FORM_WIDTH)).current;
  const containerHeight = useRef(new Animated.Value(FORM_HEIGHT)).current;
  const inputOpacity = useRef(new Animated.Value(1)).current;

  const doRedirect = () => {
    reset(SCREEN.Home);
  }

  const onLoginSuccess = async () => {
    if (innerWidth < 480) return doRedirect();

    await animate(inputOpacity, 0, 300, 300);
    setShowWelcomeMessage(true);

    await animate(containerHeight, 120, 300, 4000);
    setShowWelcomeMessage(false);

    await Promise.all([
      animate(position, { x: 0, y: 0 }, 500, 0),
      animate(containerHeight, HEADER_LOGIN_HEIGHT, 300, 200),
      animate(containerWidth, HEADER_LOGIN_WIDTH, 300, 200),
      animate(logoHeight, LOGO_HEIGHT.AFTER, 300, 200),
    ]);
    setShowAfterLoginLayout(true);

    setTimeout(() => {
      doRedirect();
    }, 500);
  }

  return (
    <AcceptInviteAnimated
      logoHeight={logoHeight}
      width={containerWidth}
      height={containerHeight}
      position={position}
      inputOpacity={inputOpacity}
      onLoginSuccess={onLoginSuccess}
      showWelcomeMessage={showWelcomeMessage}
      showAfterLoginLayout={showAfterLoginLayout}
      email={email}
      code={code}
    />
  )
}

AcceptInvite.routeInfo = {
  title: 'Accept Invite',
  path: '/invite',
}

export default AcceptInvite;
