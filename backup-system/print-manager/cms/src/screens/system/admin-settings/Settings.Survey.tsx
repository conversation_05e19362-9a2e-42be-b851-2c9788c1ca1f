import React, { forwardRef, useEffect, useImperative<PERSON><PERSON>le, useMemo, useRef, useState } from "react";
import { Col, Grid, Row, showPopupMessage, Text, TextArea } from "components";
import { COLORS } from "const";
import apiClient from "store/api-client";
import { Survey } from 'survey-react-ui';
import { Model } from 'survey-core';
import { debounce } from 'lodash';
import { ActivityIndicator } from 'react-native';
import { Tabs, TabsProps, Button } from 'antd';
import ReactJson from 'react-json-view'
import ElementForm from "./ElementForm";

interface IProps { }

const FORM_ID = 'initial-question';
const TEMPLATE_MODEL = {
  "pages": [{ "elements": [{ "name": "What will you mainly use the platform for?", "title": "What will you mainly use the platform for?", "description": "You can choose multiple ", "type": "checkbox", "choices": [{ "value": "Sell Print On Demand Products Online", "text": "Sell Print On Demand Products Online" }, { "value": "Order for my physical store", "text": "Order for my physical store" }, { "value": "Order for myself", "text": "Order for myself" }], "multiSelect": false, "isRequired": true, "showOtherItem": true }] }, { "elements": [{ "type": "radiogroup", "name": "Have you used a print on demand platform before?", "title": "Have you used a print on demand platform before?", "description": "Select one", "multiSelect": false, "isRequired": true, "choices": [{ "value": "I'm new, help me", "text": "I'm new, help me" }, { "value": "Just starting out ", "text": "Just starting out " }, { "value": "Yes and I am currently selling via my store", "text": "Yes and I am currently selling via my store" }, { "value": "I am a Pro", "text": "I am a Pro" }] }] }, { "elements": [{ "name": "Where would you like to sell?", "title": "Where would you like to sell?", "description": "You can choose multiple ", "type": "checkbox", "choices": [{ "value": "United Kingdom", "text": "United Kingdom" }, { "value": "Europe", "text": "Europe" }, { "value": "USA", "text": "USA" }, { "value": "Rest of the world ", "text": "Rest of the world " }], "multiSelect": false, "isRequired": true, "showOtherItem": false }] }, { "elements": [{ "type": "checkbox", "name": "What best describes you?", "title": "What best describes you?", "description": "You can choose multiple ", "isRequired": true, "choices": [{ "value": "I am an artist and sell my designs online", "text": "I am an artist and sell my designs online" }, { "value": "I am an individual running a side business", "text": "I am an individual running a side business" }, { "value": "This is my full time business", "text": "This is my full time business" }, { "value": "I want to buy something for myself", "text": "I want to buy something for myself" }], "showLabel": true, "multiSelect": false, "showOtherItem": true }] }, { "elements": [{ "type": "checkbox", "name": "What product are you most interested in?", "title": "What product are you most interested in?", "description": "You can choose multiple ", "choices": [{ "value": "Bottles", "text": "Bottles" }, { "value": "Glassware", "text": "Glassware" }, { "value": "Homeware", "text": "Homeware" }, { "value": "Candles", "text": "Candles" }], "multiSelect": false, "isRequired": true, "showOtherItem": true }] }]
}

const SurveySettings = (props: IProps, ref) => {
  const [initialQuestion, setInitialQuestion] = useState<any>({});
  const [loading, setLoading] = useState(true);
  const [survey, setSurvey] = useState<Model | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [pageIdx, setPageIdx] = useState(0);
  const pageIdxRef = useRef(0);

  const updateSurvey = async (value: any) => {
    try {
      // Create preview model with only current page
      const previewModel = {
        pages: [value.pages?.[pageIdxRef.current]]
      };
      setSurvey(new Model(previewModel));
      setError(null);
    } catch (error) {
      setSurvey(null);
      setError(Object.keys(value).length > 0 ? "Invalid form data" : null);
    }
    setLoading(false);
  };

  const submitUpdateSurvey = async () => {
    const res = await apiClient.Api.Survey.updateSurveyForm({
      type: FORM_ID,
      data: JSON.stringify(initialQuestion),
    });
    if (res.data.data) {
      showPopupMessage({
        title: '',
        content: 'Success',
        buttonOkText: 'OK',

        typeHighlight: 'success',
        contentHighlight: 'Success'

      });
    }
    return res;
  }

  const debouncedUpdateSurvey = useMemo(
    () => debounce(updateSurvey, 500),
    []
  );

  useEffect(() => {
    setLoading(true)
    debouncedUpdateSurvey(initialQuestion);
  }, [initialQuestion, pageIdx]);

  const getData = async () => {
    setLoading(true)
    const res = await apiClient.Api.Survey.getSurveyForm({
      type: FORM_ID,
    })
    if (res.data.data.data) {
      setInitialQuestion(JSON.parse(res.data.data.data));
    } else {
      // setInitialQuestion(TEMPLATE_MODEL);
    }
  }

  useEffect(() => {
    pageIdxRef.current = pageIdx;
  }, [pageIdx]);

  useEffect(() => {
    getData();
  }, []);

  useImperativeHandle(ref, () => ({
    getData,
    updateSurvey: submitUpdateSurvey,
  }));

  const items: TabsProps['items'] = useMemo(() => {
    return initialQuestion?.pages?.map((page, idx) => ({
      key: String(idx),
      label: `Page ${idx + 1}`,
    }))
  }, [initialQuestion])

  const addNewPage = () => {
    setInitialQuestion(prev => ({
      ...prev,
      pages: [
        ...(prev.pages || []),
        {
          elements: [{
            type: "radiogroup",
            name: "",
            title: "",
            description: "",
            choices: [],
            multiSelect: false,
            isRequired: false
          }]
        }
      ]
    }));
  };

  const removePage = () => {
    if (initialQuestion.pages.length <= 1) return;
    setInitialQuestion(prev => ({
      ...prev,
      pages: prev.pages.filter((_, idx) => idx !== pageIdxRef.current)
    }));
    setPageIdx(Math.max(0, pageIdxRef.current - 1));
  };

  const handleElementChange = (elementData: any) => {
    setInitialQuestion(prev => ({
      ...prev,
      pages: prev.pages.map((page, idx) => {
        if (idx === pageIdx) {
          return {
            ...page,
            elements: [elementData]
          };
        }
        return page;
      })
    }));
  };

  return (
    <Col flex1>
      <Grid xs='100%' lg='6:4' alignItems="flex-start">
        <Col padding={10}>
          <Text fontSize={16} fontWeight="600" mb1>
            Form model
          </Text>
          <Row alignItems="flex-start" marginTop={12} gap={8}>
            <Button type="primary" onClick={addNewPage}>Add Page</Button>
            <Col width={10} />
            <Button type="primary" danger onClick={removePage} disabled={initialQuestion?.pages?.length <= 1}>Remove Page</Button>
          </Row>
          <Col alignItems="flex-start">
            <Tabs
              tabIndex={pageIdx}
              items={items}
              onChange={(key) => setPageIdx(Number(key))}
              style={{ outline: 'none' }}
            />
          </Col>
          {!!initialQuestion?.pages?.[pageIdx]?.elements?.[0] && (
            <ElementForm
              key={pageIdx}
              data={initialQuestion?.pages?.[pageIdx]?.elements?.[0]}
              onChange={handleElementChange}
            />
          )}
          {/* <ReactJson
            src={initialQuestion}
            enableClipboard={false}
            onEdit={(edit) => {
              setInitialQuestion(edit.updated_src);
            }}
          /> */}
          {/* <TextArea
            value={initialQuestion}
            onChangeText={setInitialQuestion}
          /> */}
        </Col>
        <Col padding={10}>
          <Text fontSize={16} fontWeight="600" mb1>
            Preview
          </Text>
          {loading ? (
            <ActivityIndicator size="large" color={COLORS.BLUE_LIGHT} />
          ) : (
            <>
              {survey && <Survey model={survey} />}
              {!!error && (
                <Text color="red">{error}</Text>
              )}
            </>
          )}
        </Col>
      </Grid>
    </Col>
  );
};

export default forwardRef(SurveySettings);
