'use client'
import "survey-core/survey-core.css";
import "survey-creator-core/survey-creator-core.css";
import React, { forwardRef, useImperativeHandle, useState } from "react";
import { Col } from "components";
import { ICreatorOptions } from "survey-creator-core";
import { SurveyCreator } from "survey-creator-react";
import { SurveyCreatorComponent } from "survey-creator-react";

interface IProps { }

const defaultCreatorOptions: ICreatorOptions = {
  autoSaveEnabled: true,
  collapseOnDrag: true
};


const SurveySettings = (props: IProps, ref) => {
  let [creator, setCreator] = useState<SurveyCreator>();

  if (!creator) {
    creator = new SurveyCreator(defaultCreatorOptions);
    setCreator(creator);
  }

  useImperativeHandle(ref, () => ({
    getData: () => {
    },
  }));

  creator.saveSurveyFunc = (saveNo: number, callback: (num: number, status: boolean) => void) => {
    // If you use localStorage:
    window.localStorage.setItem("survey-json", creator.text);
    callback(saveNo, true);

    // If you use a web service:
    // saveSurveyJson(
    //   "https://your-web-service.com/",
    //   creator.JSON,
    //   saveNo,
    //   callback
    // );

    console.log("saveSurveyFunc", creator.text);
  };

  // function saveSurveyJson (url: string, json: object, saveNo: number, callback: (num: number, status: boolean) => void) {
  //   fetch(url, {
  //     method: 'POST',
  //     headers: {
  //       'Content-Type': 'application/json;charset=UTF-8'
  //     },
  //     body: JSON.stringify(json)
  //   })
  //   .then(response => {
  //     if (response.ok) {
  //       callback(saveNo, true);
  //     } else {
  //       callback(saveNo, false);
  //     }
  //   })
  //   .catch(error => {
  //     callback(saveNo, false);
  //   });
  // }
  return (
    <Col flex1>
      <SurveyCreatorComponent style={{ height: '100%' }} creator={creator} />
    </Col>
  );
};

export default forwardRef(SurveySettings);
