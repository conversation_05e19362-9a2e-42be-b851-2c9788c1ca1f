.container{
  padding-top: 30px;
}
.title-setting-email{
  padding-left: 40px;
}
strong{
  font-size: 16px;
}
.email-setting-container {
  width: 50%;
  margin: 40px;
  margin-top: 0;
  display: flex;
  flex-direction: column;
  border: 1px solid #d8dae5;
  border-radius: 5px;
}
.setting-item {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  font-size: 14px;
}
.setting-item:first-child {
  border-bottom: 1px solid #d8dae5;
}
.setting-item:last-child {
  border-top: 1px solid #d8dae5;
}
.setting-item p {
  margin: 0;
  padding: 0;
  flex:1;
}
.toggle-btn {
  margin: 0;
  margin-left: 5px;
  padding: 0;
  background-color: rgb(185, 185, 185);
  border: 1px solid #aaa;
  border-radius: 99px;
  width: 40px;
  height: 23px;
  transition: background-color 0.1s ease, border-color 0.2s ease;
  cursor: pointer;
  /* box-shadow: 1px 1px 10px rgba(0, 0, 0, 0.748); */
  position: relative;
}

.toggle-btn .email-setting-thumb {
  height: 15px;
  width: 15px;
  background-color: #fff;
  border-radius: 99px;
  transform: translateX(0);
  transition: left 0.15s ease;
  position: absolute;
  left: 3px;
  top: 50%;
  transform: translateY(-50%);
}
.toggle-btn:focus {
  outline: none;
}
.toggle-btn.toggledStep1 {
  background-color: rgb(34, 51, 99);
}
.toggle-btn.toggledStep1 .email-setting-thumb {
  left: 20px;
}
.toggle-btn.toggledStep2 {
  background-color: rgb(34, 51, 99);
}
.toggle-btn.toggledStep2 .email-setting-thumb {
  left: 20px;
}
.toggle-btn.toggledStep3 {
  background-color: rgb(34, 51, 99);
}
.toggle-btn.toggledStep3 .email-setting-thumb {
  left: 20px;
}
@media screen and (max-width: 1024px){
  .title-setting-email{
    padding: 0;
  }
  .email-setting-container{
    width:100%;
    margin:0;
  }
}

