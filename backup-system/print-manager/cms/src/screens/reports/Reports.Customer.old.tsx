import React, { useMemo, useState, useEffect, useCallback } from 'react';
import { Button, Col, Input, Row, Select, Text, TouchField } from 'components';
import { AntDesign, Feather, FontAwesome } from '@expo/vector-icons';
import { COLOR } from 'const';
import DatePicker from 'react-datepicker';
const moment = require('moment');
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { saveAs } from 'file-saver';
import Store from 'store';
import { TUser } from 'type';
import { ValHelper } from 'helpers';
import { useUserStore } from 'store/User.Store';

let XLSX;

interface Props {
  customers: Array<{
    client: string,
    email: string,
    first_name?: string,
    last_name?: string,
    products: Array<{
      name: string,
      product_id: number,
      variant_id: number,
      timestamp: string,
      quantity: number,
      order_number: number,
    }>
  }>,
  resellerId: string,
  listReseller: Array<TUser>,
}

const clients = [
  { id: '440478626329', name: 'Bottled Goose', slug: 'bg' },
  { id: '665847158329', name: 'Partner In Wine', slug: 'piw' },
  { id: '578953627077', name: 'Great Harbour Gifts', slug: 'ghg' },
];

const ReportsCustomer = ({ customers, resellerId, listReseller }: Props) => {
  const OrderStore = Store.useOrderStore();
  const { user } = useUserStore();
  const [activeCustomerEmail, setActiveCustomerEmail] = useState('');
  const [filterClientId, setFilterClientId] = useState<string>();
  const [filterMonth, setFilterMonth] = useState(new Date());
  const [searchKeyword, setSearchKeyword] = useState('');
  const [tab, setTab] = useState<'customer' | 'product' | 'order'>('customer');
  const [clientReportData, setClientReportData] = useState({
    customers: [],
    orders: [],
    products: [],
  });

  useEffect(() => {
    if (!XLSX) {
      import('xlsx').then(xlsx => {
        XLSX = xlsx;
      });
    }
  }, []);

  const clientOptions = useMemo(() => {
    const combinedClients = [...clients, ...listReseller.map(i => ({
      id: i.id,
      name: [i.firstName, i.lastName].join(' '),
      slug: 'bg',
    }))]
    return combinedClients.map((c, index) => ({
      label: c.name,
      value: c.id,
      slug: c.slug,
      index,
    }));
  }, [listReseller]);


  const TABS = [
    { key: 'customer', label: 'Customer' },
    { key: 'product', label: 'Product' },
    { key: 'order', label: 'Order' },
  ]

  const getReportData = async ({
    clientId = filterClientId,
    month = filterMonth,
  }) => {
    const _client = clientId
      ? clientOptions.find(i => i.value === clientId)
      : clientOptions.find(i => i.slug === resellerId || i.value === String(resellerId));

    const filterClientId = resellerId === 'all' ? _client?.value : (user.resellerId || user.id);
    const res = await OrderStore.orderReportClientByMonth(_client?.slug, moment(month).format('MM-YYYY'), filterClientId)
    setClientReportData(res || {});
  };

  const onChangeFilterClient = (d) => {
    setFilterClientId(d.value);
    getReportData({ clientId: d.value });
  }

  const onChangeFilterMonth = (date) => {
    setFilterMonth(date);
    getReportData({ month: date });
  }

  useEffect(() => {
    getReportData({});
  }, []);

  const renderMonthContent = (month, shortMonth, longMonth) => {
    const tooltipText = `Tooltip for month: ${longMonth}`;
    return <span title={tooltipText}>{shortMonth}</span>;
  };

  const downloadExcel = () => {
    var wb = XLSX.utils.book_new();

    const customer = clientReportData.customers.map(v => ({
      Client: v.clientName,
      Products: v.products?.length || 0,
      'Customer Name': `${v.first_name || ''} ${v.last_name || ''}`,
      'Customer Email': v.email,
    }));

    XLSX.utils.book_append_sheet(wb, XLSX.utils.json_to_sheet(customer), "Customers");

    const products = clientReportData.products.map(v => ({
      Client: v.clientName,
      'Product Name': v.name,
      'Product ID - Variant ID': `${v.product_id} - ${v.variant_id}`,
      'Total Quantity': v.totalQuantity,
      'Total Customer': v.customers.length,
    }));
    XLSX.utils.book_append_sheet(wb, XLSX.utils.json_to_sheet(products), "Products");

    let orderProducts = [];
    clientReportData.orders.forEach(v => {
      const _customer = v['Raw Data']?.customer;
      const products = v['Raw Data']?.line_items || [];

      products.forEach(p => {
        orderProducts.push({
          'Order Number': `#${v['Order Number']}`,
          Client: v['Client Name'] || v['Client ID'],
          'Product Name': p.name,
          'Product ID - Variant ID': `${p.product_id} - ${p.variant_id}`,
          'Quantity': p.quantity,
          'Customer': `${_customer?.first_name} ${_customer?.last_name} (${_customer?.email})`,
        })
      })
    });
    XLSX.utils.book_append_sheet(wb, XLSX.utils.json_to_sheet(orderProducts), "Orders");

    var wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
    saveAs(new Blob([wbout], { type: "application/octet-stream" }), `export--${moment(filterMonth).format('MM/yyyy')}.xlsx`);
  };

  const displayCustomers = useMemo(() => {
    if (!clientReportData.customers?.length) return [];
    if (!searchKeyword.trim()) return clientReportData.customers;
    return clientReportData.customers.filter(i =>
      ValHelper.isKeywordInFields([
        i.clientName, i.email, i.first_name, i.last_name, `${i.first_name || ''} ${i.last_name || ''}`,
      ], searchKeyword.trim())
    )
  }, [clientReportData.customers, searchKeyword]);

  const renderCustomer = () => {
    return (
      <Col mb2>
        <Row p0 m0 mt2>
          <Col width={50}></Col>
          <Col flex1><Text bold>Brand</Text></Col>
          <Col flex1><Text bold>Customer Name</Text></Col>
          <Col flex1><Text bold>Total</Text></Col>
          <Col flex1><Text bold>Customer Email</Text></Col>
          <Col flex1><Text bold>Date</Text></Col>
          <Col flex1><Text bold>Products</Text></Col>
        </Row>
        {displayCustomers.map((v, vI) => {
          const customerKey = v.client + v.email;
          const totalProductsValue = v.products?.reduce((t, lineItem) => (
            t + Number(lineItem.price || 0) * Number(lineItem.quantity || 0)
          ), 0);

          return (
            <Col key={`customer${customerKey}${vI}`}>
              <Row p0 m0 borderTopWidth={1} borderTopColor={COLOR.GREY_LIGHT} backgroundColor={vI % 2 === 0 ? COLOR.GREY_LIGHT : 'transparent'}>
                <Col width={50}><Text caption>{vI + 1}</Text></Col>
                <Col flex1><Text bold>{v.clientName}</Text></Col>
                <Col flex1><Text>{v.first_name || ''} {v.last_name || ''}</Text></Col>
                <Col flex1><Text>£{ValHelper.formatMoney(totalProductsValue)}</Text></Col>
                <Col flex1><Text>{v.email}</Text></Col>
                <Col flex1><Text>{moment(v.createdAt).format("DD-MM-YYYY")}</Text></Col>
                <Row flex1>
                  <Text mr0>Total: {v.products?.length || 0}</Text>
                  <TouchField cirle middle onPress={() => {
                    setActiveCustomerEmail(a => a === customerKey ? '' : customerKey);
                  }}>
                    <FontAwesome name="expand" size={24} color="black" />
                  </TouchField>
                </Row>
              </Row>
              {activeCustomerEmail === customerKey && (
                <Col p2 round1 borderWidth={1} borderColor={COLOR.BORDER}>
                  <Row p0 m0>
                    <Col flex={2}><Text bold>Product Name</Text></Col>
                    <Col flex1><Text bold>Product ID</Text></Col>
                    <Col flex1><Text bold>Variant ID</Text></Col>
                    <Col flex1 middle><Text bold>Quantity</Text></Col>
                    <Col flex1><Text bold>Date</Text></Col>
                  </Row>
                  {v.products?.map((p, pI) => {
                    return (
                      <Row key={'p-' + p.variant_id + '-' + pI} p0 m0 borderTopWidth={1} borderTopColor={COLOR.GREY_LIGHT} backgroundColor={pI % 2 === 0 ? COLOR.GREY_LIGHT : 'transparent'}>
                        <Col flex={2}><Text>{p.name}</Text></Col>
                        <Col flex1><Text>{p.product_id}</Text></Col>
                        <Col flex1><Text>{p.variant_id}</Text></Col>
                        <Col flex1 middle><Text>{p.quantity}</Text></Col>
                        <Col flex1><Text>{p.createdAt ? moment(p.createdAt).format('DD-MM-YYYY') : ""}</Text></Col>
                      </Row>
                    );
                  })}
                </Col>
              )}
            </Col>
          );
        })}
      </Col>
    )
  }

  const displayProducts = useMemo(() => {
    if (!clientReportData.products?.length) return [];
    if (!searchKeyword) return clientReportData.products;
    return clientReportData.products.filter(i =>
      ValHelper.isKeywordInFields([
        i.clientName, i.name, i.product_id, i.variant_id
      ], searchKeyword)
    )
  }, [clientReportData.products, searchKeyword]);

  const renderProduct = () => {
    return (
      <Col mb2>
        <Row p0 m0 mt2>
          <Col width={50}></Col>
          <Col flex1><Text bold>Brand</Text></Col>
          <Col flex={2}><Text bold>Product Name</Text></Col>
          <Col flex={2}><Text bold>Product ID - Variant ID</Text></Col>
          <Col flex1><Text bold>Date</Text></Col>
          <Col flex1 middle><Text bold>Total Quantity</Text></Col>
          <Col flex1><Text bold>Total Customer</Text></Col>
        </Row>
        {displayProducts.map((v, vI) => {
          return (
            <Col key={'product' + v.client + v.email + vI}>
              <Row p0 m0 borderTopWidth={1} borderTopColor={COLOR.GREY_LIGHT} backgroundColor={vI % 2 === 0 ? COLOR.GREY_LIGHT : 'transparent'}>
                <Col width={50}><Text caption>{vI + 1}</Text></Col>
                <Col flex1><Text bold>{v.clientName}</Text></Col>
                <Col flex={2}><Text>{v.name}</Text></Col>
                <Col flex={2}><Text>{v.product_id} - {v.variant_id}</Text></Col>
                <Col flex1 middle><Text>{moment(v.createdAt).format("DD-MM-YYYY")}</Text></Col>
                <Col flex1 middle><Text>{v.totalQuantity}</Text></Col>
                <Row flex1>
                  <Text mr0>{v.customers.length}</Text>
                </Row>
              </Row>
            </Col>
          );
        })}
      </Col>
    )
  }

  const displayOrders = useMemo(() => {
    if (!clientReportData.orders?.length) return [];
    if (!searchKeyword) return clientReportData.orders;
    return clientReportData.orders.filter(i => {
      const _customer = i['Raw Data']?.customer;
      return ValHelper.isKeywordInFields([
        i['Order Number'], i['Client Name'], i.product_id, i.variant_id, i.name,
        _customer?.first_name, _customer?.last_name, _customer?.email,
      ], searchKeyword)
    })
  }, [clientReportData.orders, searchKeyword]);

  const renderListOrder = () => {
    return (
      <Col mb2>
        <Row p0 m0 mt2>
          <Col width={100}><Text bold>Order#</Text></Col>
          <Col flex1><Text bold>Brand</Text></Col>
          <Col flex={1.5}><Text bold>Product Name</Text></Col>
          <Col flex={1.5}><Text bold>Product ID - Variant ID</Text></Col>
          <Col width={90} middle><Text bold>Quantity</Text></Col>
          <Col width={100} middle><Text bold>Time</Text></Col>
          <Col flex1><Text bold>Customer</Text></Col>
        </Row>
        {displayOrders.map((v, vI) => {
          const products = v['Raw Data']?.line_items || [];
          const _customer = v['Raw Data']?.customer;
          return (
            <Col key={`v${vI}`}>
              {products.map((p, pI) => {
                return (
                  <Col key={`p${vI}${pI}`}>
                    <Row p0 m0 borderTopWidth={1} borderTopColor={COLOR.GREY_LIGHT} backgroundColor={vI % 2 === 0 ? COLOR.GREY_LIGHT : 'transparent'}>
                      <Col width={100}><Text caption>#{v['Order Number']}</Text></Col>
                      <Col flex1><Text bold>{v['Client Name'] || v['Client ID']}</Text></Col>
                      <Col flex={1.5}><Text>{p.name}</Text></Col>
                      <Col flex={1.5}><Text>{p.product_id} - {p.variant_id}</Text></Col>
                      <Col width={90} middle><Text>{p.quantity}</Text></Col>
                      <Col width={100} middle><Text>{moment(v.created_at).format('DD/MM/YYYY')}</Text></Col>
                      <Col flex1><Text>{_customer?.first_name} {_customer?.last_name} ({_customer?.email})</Text></Col>
                    </Row>
                  </Col>
                )
              })}
            </Col>
          )
        })}
      </Col>
    );
  }



  return (
    <Col>
      <Row mb2 marginLeft={-10}>
        {resellerId === 'all' && (
          <Select
            placeholder='Filter by Client'
            options={clientOptions}
            value={clientOptions.find(i => i.value === filterClientId)}
            onChange={onChangeFilterClient}
            width={200}
            ml2
          />
        )}
      </Row>
      <Row zIndex={100} p1>
        <Text>Filter by month:</Text>
        <Col ml2>
          <DatePicker
            selected={filterMonth}
            onChange={onChangeFilterMonth}
            renderMonthContent={renderMonthContent}
            showMonthYearPicker
            dateFormat="MM/yyyy"
          />
        </Col>
        <Text ml2>Total customers: {clientReportData.customers?.length || 0}</Text>
        <Text ml2>Total orders: {clientReportData.orders?.length || 0}</Text>

        <Row
          ml2
          flex1
          height={30}
          borderRadius={15}
          backgroundColor={COLOR.GREY_LIGHT}
        >
          <Input
            height={30}
            borderColor="transparent"
            value={searchKeyword}
            flex1
            onChange={setSearchKeyword}
            inputProps={{
              style: {
                // @ts-ignore
                outline: "none",
                fontSize: 12,
              },
              placeholderTextColor: '#939496',
            }}
            placeholder={"Search for Client name, Order no. or Product"}
          />
          <Col
            absolute
            top={5}
            right={10}
          >
            <Feather name="search" size={18} color={COLOR.BLACK} />
          </Col>
        </Row>
      </Row>
      <Row>
        <Row flex1>
          {TABS.map((v, vI) => {
            return (
              <Button
                key={v.key}
                outline={v.key !== tab}
                text={v.label}
                m0 borderRadius={30} height={30} width={100}
                onPress={() => setTab(v.key as any)}
              />
            )
          })}
        </Row>
        <Button
          outline
          iconLeft={(
            <MaterialCommunityIcons name="microsoft-excel" size={24} color={COLOR.MAIN} style={{ marginRight: 5 }} />
          )}
          text={'Excel'}
          m0 borderRadius={30} height={30} width={100}
          onPress={downloadExcel}
        />
      </Row>

      {tab === 'customer' ? renderCustomer() :
        tab === 'product' ? renderProduct() : renderListOrder()}
    </Col>
  )
}

export default ReportsCustomer;
