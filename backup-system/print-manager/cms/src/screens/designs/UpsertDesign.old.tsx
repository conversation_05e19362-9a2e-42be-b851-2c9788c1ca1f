import React, { useEffect, useState, useRef, useMemo } from 'react';
import { IScreen, TProduct } from 'type';
import { CMSLayout, Col, Row, Text, Button, ShimmerLoading, TouchField, Input02, Select01, RatioCol, RatioCol2, UploadFile, useUIState, useRefState, Grid, UseMemo } from 'components';
import { useNavFunc } from 'navigation';
import { useDynamicResponsiveValue } from 'quickly-react';
import { COLOR, SCREEN } from 'const';
import Store from 'store';
import { Animated, Image, LayoutAnimation, View } from 'react-native';
import { Entypo, AntDesign, FontAwesome5, Feather } from '@expo/vector-icons';
import { AnimHelper } from 'helpers';

const TAB = {
  INFO: 'INFO',
  EDITOR: 'EDITOR',
  items: [
    { title: 'Design Info', key: 'INFO' },
    { title: 'Editor', key: 'EDITOR' },
  ],
}

type TSelectResellerOption = {
  label: string,
  value: string,
  data?: any,
}

const UpsertDesign: IScreen = () => {
  const UserStore = Store.useUserStore();
  const { user } = UserStore;
  const DesignStore = Store.useDesignStore();
  const ProductStore = Store.useProductStore();
  const { navigation, route } = useNavFunc();
  // @ts-ignore
  const { id, onDemand } = route.params || {};
  const { design, setDesign, uiState } = DesignStore.useDesign(id);
  const canEdit = (user?.role === 'admin')
    || (!!design?.createdByUserId && design?.createdByUserId === user?.id );
  const [listProducts, setListProducts] = useState([]);

  const [listResellers, setListResellers] = useState<Array<TSelectResellerOption>>([]);
  const [selectedResellers, setSelectedResellers] = useState<Array<TSelectResellerOption>>([]);

  const [expand, setExpand] = useState(false);
  const [expandUI, setExpandUI] = useState(false);

  const [curTab, setCurTab] = useState(TAB.INFO);

  // const [{ loading: removing }, setRemoveUI] = useUIState();
  // const [{ loading: submitting }, setSubmitUI] = useUIState();

  const [selectedProduct, setSelectedProduct] = useState<any>();

  const titleXY = useRef(new Animated.ValueXY({ x: 0, y: 0 })).current;
  const titleScale = useRef(new Animated.Value(1)).current;

  const rV = useDynamicResponsiveValue(['xs', 'md']);
  const breakpoint = rV({ xs: 'xs', md: 'md' });

  useEffect(() => {
    if (!user) return;
    (async () => {
      const resellerId = UserStore.getResellerId(user);
      const { list, error } = await ProductStore.getList(1, resellerId);
      if (!error && !!list) {
        console.log('list', list);
        setListProducts(list);
      }
    })();

  }, [user]);

  useEffect(() => {
    console.log('onDemand', onDemand);
    setDesign({
      ...design,
      isCustomizable: !!onDemand ? false : true,
    });
  }, [onDemand]);

  useEffect(() => {
    console.log('design?.availableForResellerIds', design?.availableForResellerIds)
    setListResellers([
      { label: 'All resellers', value: 'all' },
    ])
    if (design?.availableForResellerIds && !!design?.availableForResellerIds['all']) {
      setSelectedResellers([
        { label: 'All resellers', value: 'all' },
      ]);
    }
  }, [design?.availableForResellerIds]);

  useEffect(() => {
    console.log('should run here', selectedProduct);
    if (!design?.productId) return;
    if (listProducts.length === 0) return;
    if (!!selectedProduct && selectedProduct.value === design?.productId) return;
    const find = listProducts.find(val => val.id === design?.productId);
    if (!find) return;
    console.log(find, selectedProduct)
    if (!!selectedProduct && find.name === selectedProduct.name) return;
    setSelectedProduct({
      value: design?.productId,
      label: find.name,
      data: find,
    })
  }, [design?.productId, listProducts, selectedProduct]);

  const animExpand = bool => {
    setExpand(bool);
    if (bool) {
      Promise.all([
        AnimHelper.animate(titleXY, {
          x: -15,
          y: 0,
        }, 300),
        AnimHelper.animate(titleScale, 0.7, 300)
      ])
    } else {
      Promise.all([
        AnimHelper.animate(titleXY, {
          x: 0,
          y: 0,
        }, 300),
        AnimHelper.animate(titleScale, 1, 300)
      ])
    }
    setTimeout(() => {
      setExpandUI(bool);
    }, 400);
  }

  const addToPrintJobs = async () => {
    const res = await Store.Api.PrintJob.upsert({
      clientId: 'admin',
      artworkUrls: design?.data?.artworkUrls || [],
      previewUrl: design?.image || undefined,
      isPDFDownloaded: false,
      isPrinted: false,
      isRePrinted: false,
      productId: design?.productId,
      productName: 'Design: ' + design?.name,
      designId: design?.id,
      quantity: 1,
      data: {
        product: {
          physicalWidth: design?.width,
          physicalHeight: design?.height,
          printAreas: design?.printAreas,
        }
      }
    });
    if (res.data.error) {
      return alert(res.data.error)
    }
    alert('Successfully added a print job');
  };

  const handleSave = async () => {
    if (!selectedProduct || !selectedProduct.value) {
      alert('Please select product');
      return;
    }
    const product: TProduct = selectedProduct.data;

    const availableForResellerIds = {};
    selectedResellers.forEach(({ value }) => {
      availableForResellerIds[value] = true;
    })


    const res = await Store.Api.Design.upsert({
      id: id === 'new' ? undefined : id,
      productId: selectedProduct.value,
      name: design.name,
      width: product.physicalWidth,
      height: product.physicalHeight,
      printAreas: product.printAreas,
      availableForResellerIds: user.role === 'admin' ? availableForResellerIds : undefined,
      isCustomizable: design.isCustomizable,
    });
    if (res.data.error) {
      alert(res.data.error);
      return;
    }
    navigation.reset({
      index: 0,
      routes: [{ name: SCREEN.UpsertDesign, params: { id: res.data.data.id } }],
    });
  };

  const isCustomizableOptions = [
    { label: 'YES', value: true },
    { label: 'NO', value: false },
  ]

  const renderReady = () => {
    if (breakpoint === 'xs') return (
      <Col flex1 middle>
        <Text>Please use bigger screen to see this page.</Text>
      </Col>
    )

    return curTab === TAB.INFO ? (
      <Col flex1>
        <Grid xs='100%' md='50%' p1>
          <Col m1>
            <Text subtitle1 mb1>Select product:</Text>
            <Select01
              value={selectedProduct}
              onChange={(newData) => {
                if (!!design?.data && Object.keys(design?.data).length > 0) {
                  alert('The product is attached to the design. If you wish to use a different product, please create a new design.');
                  return;
                }
                setSelectedProduct(newData)
              }}
              options={listProducts.map(val => ({
                label: val.name,
                value: val.id,
                data: val,
              }))}
              mb1
            />

            <Text subtitle1 mb1>Design Name</Text>
            <Input02
              height={35}
              value={design?.name || ''}
              onChange={(v) => setDesign({ ...design, name: v })}
              mb2
            />

            {user?.role === 'admin' ? (
              <>
                <Text subtitle1 mb1>Available for Resellers:</Text>
                <Text caption mb1>* If blank, it will be hidden from all resellers</Text>
                <Select01
                  value={selectedResellers}
                  isMulti
                  onChange={setSelectedResellers}
                  options={listResellers}
                  mb1
                />

                <Text subtitle1 mb1>Is Customizable:</Text>
                <Text caption mb1>* If NO, it will be a preset (<Text bold>Print on Demand design</Text>) that resellers can only print</Text>
                <Select01
                  value={design?.isCustomizable ? {
                    label: 'YES', value: true
                  } : {
                    label: 'NO', value: false
                  }}
                  isClearable={false}
                  onChange={(newVal) => {
                    console.log('newVal', newVal);
                    setDesign({
                      ...design,
                      isCustomizable: Boolean(newVal?.value),
                    });
                  }}
                  options={isCustomizableOptions}
                  mb1
                />
              </>
            ) : !design?.isCustomizable ? (
              <Text caption mb1>This Design is not Customizable.</Text>
            ) : null}

            <Row>
              {canEdit && (
                <Button onPress={handleSave} height={30} width={75} borderRadius={15} text="Save" />
              )}
              {Boolean(!!design?.data && Object.keys(design?.data).length > 0) && (
                <Button
                  outline
                  onPress={addToPrintJobs}
                  height={30}
                  width={150}
                  ml1
                  borderRadius={15}
                  text="Add to print jobs"
                />
              )}
            </Row>
          </Col>
          <Col m1>

          </Col>
        </Grid>

      </Col>
    ) : (
      <View
        style={{
          width: '100%',
          height: '100%',
          margin: 0,
        }}
      >
        <iframe
          src={window.location.href.includes('iframe_dev=1') ? `http://localhost:3009/design/${id}` : `https://bg-editor.personify.tech/design/${id}`}
          style={{
            width: '100%',
            height: '100%',
            border: 'none',
            backgroundColor: COLOR.MAIN,
          }}
          onLoad={Store.Client.editor.initOnLoad}
        />
      </View>
    );
  }

  return (
    <CMSLayout requireAuthen expanded={expand}>
      <Row zIndex={2} m2={expandUI ? false : true} marginBottom={0} justifyContent={'space-between'}>
        <Animated.View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            transform: [
              { translateX: titleXY.x },
              { translateY: titleXY.y },
              { scale: titleScale },
            ],
            zIndex: 10,
            ...(expandUI ? {
              position: 'absolute',
              top: 0,
              left: 0,
            } : {})
          }}
        >
          {curTab !== TAB.EDITOR ? null : (
            !expandUI ? (
              <TouchField width={40} height={40} borderRadius={20} middle onPress={() => animExpand(true)}>
                <FontAwesome5 name="expand" size={20} color={COLOR.GREY} />
              </TouchField>
            ) : (
              <TouchField bgHovered='rgba(255,255,255,0.2)' width={40} height={40} borderRadius={20} middle onPress={() => animExpand(false)}>
                <Feather name="minimize" size={20} color={'white'} />
              </TouchField>
            )
          )}
          <Text h3 ml0 color={expandUI ? 'white' : COLOR.FONT}>{id === 'new' ? 'Create new design' : design?.name}</Text>
        </Animated.View>
        <Row>
          {expandUI ? null : TAB.items.map((val, i) => (
            <Button
              key={val.key}
              text={val.title}
              outline={val.key !== curTab}
              height={30}
              borderRadius={15}
              width={100}
              m0
              onPress={() => {
                if (val.key === curTab) return;
                if (val.key === TAB.EDITOR && id === 'new') {
                  alert('Please save Design info and continue');
                  return;
                }
                setCurTab(val.key)
              }}
              bgHovered={val.key !== curTab ? COLOR.GREY_BG : undefined}
            />
          ))}
        </Row>
      </Row>
      <Col
        flex1
        zIndex={1}
        style={!expandUI ? {
          margin: 20,
          marginVertical: 10,
          borderRadius: 10,
          backgroundColor: 'white',
          borderColor: 'white',
          borderWidth: 1,
        } : {}}
      >
        {useMemo(() => {
          return uiState.errorMes ? (
            <Col flex1 middle>
              <Text color="red" subtitle1>{uiState.errorMes}</Text>
            </Col>
          ) : (
            uiState.fetching ? (
              <Row height={50} stretch>
                <ShimmerLoading round1 flex={1} m1 />
                <ShimmerLoading round1 flex={1} m1 />
                <ShimmerLoading round1 flex={1} m1 />
                <ShimmerLoading round1 flex={1} m1 />
              </Row>
            ) : (
              renderReady()
            )
          );
        }, [uiState, breakpoint, curTab, listProducts, design, listResellers, selectedResellers, selectedProduct])}
      </Col>
    </CMSLayout>
  );
};

UpsertDesign.routeInfo = {
  title: 'Design - Bottled Goose',
  path: '/design/:id',
};

export default UpsertDesign;
