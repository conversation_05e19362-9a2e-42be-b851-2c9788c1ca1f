import React, { useState } from 'react';
import { Row, Text, Col, Button } from 'components';
import { COLOR } from 'const';

const TABS = [
  { title: 'Custom Product', key: 'custom-product' },
  { title: 'Print on Demand', key: 'print-on-demand' },
  { title: 'Wholesale', key: 'wholesale' },
];


export const useDesignTabs = () => {
  const [tab, setTab] = useState<'custom-product' | 'wholesale' | 'print-on-demand'>('custom-product');
  return [tab, setTab];
}

export const ListDesignsTabs = ({ activeTab, onChangeTab, ...props }) => {
  return (
    <Row {...props}>
      {TABS.map((v, vI) => {
        const isActive = !activeTab ? false : activeTab === v.key || activeTab?.toLowerCase() === v.key?.toLowerCase();
        return (
          <Button
            key={v.key}
            solid={isActive}
            outline={!isActive}
            width={130}
            height={30}
            text={v.title}
            mr1
            onPress={() => {
              if (!isActive) onChangeTab(v.key)
            }}
            {...(!isActive ? {
              backgroundColor: '#D1D1D1',
              borderColor: '#D1D1D1',
              bgHovered: COLOR.GREY,
            } : {})}
          />
        )
      })}
    </Row>
  )
};