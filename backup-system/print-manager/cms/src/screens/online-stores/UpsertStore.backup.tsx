import React, { useEffect } from 'react';
import { IScreen } from 'type';
import { CMSLayout, Col, Row, Text, Button, Input02, Select01, useUIState, Grid, CalendlyButton, showPopupMessage } from 'components';
import { useNavFunc } from 'navigation';
import { COLOR, SCREEN } from 'const';
import Store from 'store';
import ShopifyCodeSnippet from './UpsertStore.ShopifyCodeSnippet';
import { Image } from 'react-native';
import { ASSETS } from 'assets';

const UpsertStore: IScreen = () => {

  const { navigation, route } = useNavFunc();
  // @ts-ignore
  const { id, storetype } = route.params || {};

  const ShopStore = Store.useShopStore();

  const { shop, setShop } = ShopStore.useShop(id);

  const [{ loading: submitting }, setSubmitUI] = useUIState();

  const onChangeField = (label) => (newValue) => {
    setShop({
      ...shop,
      [label]: newValue,
    })
  }

  useEffect(() => {
    if (storetype === 'shopify' && id === 'new') {
      setShop({
        ...shop,
        type: 'shopify',
      });
    }
  }, [storetype]);

  const submit = async () => {
    setSubmitUI({ loading: true });
    try {
      const cleanedUrl = shop?.url?.endsWith('/') ? shop?.url.slice(0, shop?.url.length - 1) : shop?.url
      const res = await Store.Api.Shop.upsert({
        id: id === 'new' ? undefined : id,
        name: shop?.name,
        data: shop?.data,
        url: cleanedUrl,
        type: shop?.type,
      });
      if (res.data.error) {
        showPopupMessage({
          title: '',
          content: String(res.data.error),
          buttonOkText: 'OK',
          // 
          typeHighlight: 'danger',
          contentHighlight: 'Error'
  
        });
        // alert(res.data.error);
        return;
      }
      ShopStore.resellerHealthCheck();
      navigation.reset({
        index: 0,
        routes: [{ name: SCREEN.UpsertStore, params: { id: res.data.data.id } }],
      });
    } catch (err) {
      showPopupMessage({
        title: '',
        content: String(err),
        buttonOkText: 'OK',
        // 
        typeHighlight: 'danger',
        contentHighlight: 'Error'

      });
      // alert(String(err));
    }
    setSubmitUI({ loading: false });
  }

  const createDummyShopifyProduct = async () => {

    try {
      const res = await Store.Api.Shop.testShopifyConnection({
        url: shop?.url,
        token: shop?.data?.shopifyAccessToken,
      });
      if (res.data.error) {
        showPopupMessage({
          title: '',
          content: String(res.data.error),
          buttonOkText: 'OK',
          // 
          typeHighlight: 'danger',
          contentHighlight: 'Error'
  
        });
        // alert(res.data.error)
      } else if (res.data.data.id) {
        showPopupMessage({
          title: '',
          content: `Successfully create a dummy product: ${res.data.data.id}, please check it in the Shopify admin`,
          buttonOkText: 'OK',
          // 
          typeHighlight: 'success',
          contentHighlight: 'Success'
  
        });
        // alert(`Successfully create a dummy product: ${res.data.data.id}, please check it in the Shopify admin`);
      }
    } catch (err) {
      showPopupMessage({
        title: '',
        content: 'ERROR: ' + String(err),
        buttonOkText: 'OK',
        // 
        typeHighlight: 'danger',
        contentHighlight: 'Error'

      });
      // alert('ERROR: ' + String(err))
    }

  }

  return (
    <CMSLayout requireAuthen>
      <Row m2 marginBottom={0} justifyContent={'space-between'}>
        <Text h3>{id === 'new' ? 'New Store Connection' : `${shop?.name}`}</Text>
        <Row>
          <CalendlyButton />
          <Button
            ml1
            isLoading={submitting}
            text={id === 'new' ? "Connect" : "Save"}
            width={100} height={40} borderRadius={20}
            onPress={submit}
          />
        </Row>
      </Row>
      <Col flex1 m2 mv1 p1 round1 bgWhite overflow={'scroll'}>
        <Col flex1>
          <Grid xs='100%' md='50%' alignItems={'flex-start'} mb2>
            <Col m1>
              <Text subtitle1 mb1>Store Name</Text>
              <Input02
                height={35}
                value={shop?.name || ''}
                onChange={onChangeField('name')}
                mb2
              />
              <Text subtitle1 mb1>Store type:</Text>
              <Select01
                value={shop?.type === 'shopify' ? {
                  label: 'Shopify', value: 'shopify'
                } : shop?.type === 'wordpress' ? {
                  label: 'WooCommerce', value: 'wordpress'
                } : undefined}
                onChange={(newVal) => {
                  setShop({
                    ...shop,
                    type: newVal.value,
                  })
                }}
                options={[
                  { label: 'Shopify', value: 'shopify' },
                  { label: 'WooCommerce', value: 'wordpress' },
                ]}
                mb2
              />

              <Text subtitle1 mb1>Store URL</Text>
              {shop?.type === 'shopify' && (
                <Text caption mb1>* It should by {`https://[your-store-slug].myshopify.com`}</Text>
              )}
              <Input02
                height={35}
                value={shop?.url || ''}
                onChange={onChangeField('url')}
                mb2
              />

              {shop?.type === 'shopify' && (
                <Col>
                  <Text subtitle1 mb1>Shopify Access Token</Text>
                  <Text caption mb1>* You can get Access Token by creating new custom app in Shopify Admin</Text>
                  <Text caption mb1>* Please check the manual for more information</Text>
                  <Input02
                    height={35}
                    value={shop?.data?.shopifyAccessToken || ''}
                    onChange={newVal => {
                      setShop({
                        ...shop,
                        data: {
                          ...shop.data,
                          shopifyAccessToken: newVal,
                        }
                      })
                    }}
                    mb2
                  />
                  {shop?.url && shop?.data?.shopifyAccessToken && (
                    <Button
                      text='Test connection (create dummy product)'
                      outline
                      borderRadius={15}
                      height={30}
                      width={320}
                      mb2
                      onPress={createDummyShopifyProduct}
                    />
                  )}

                </Col>
              )}

              {shop?.type === 'wordpress' && (
                <Col>
                  <Text subtitle1 mb1>Wordpress integration coming soon</Text>
                </Col>
              )}

            </Col>
            <Col m1>
              {shop?.type === 'shopify' ? (
                <Col>
                  <Text subtitle1 mb1>Tickboxes needed for access token</Text>
                  <Text caption mb1>* {`Settings -> Apps and sales channels -> Develop custom apps -> {app name} -> Configuration -> Admin API access scopes`}</Text>
                  <Col backgroundColor={COLOR.GREY_LIGHT} round1 p2 mb2>
                    <Text color='#800000'>write_products</Text>
                    <Text color='#800000'>read_products</Text>
                    <Text color='#800000'>write_publications</Text>
                    <Text color='#800000'>read_publications</Text>
                    <Text color='#800000'>write_orders</Text>
                    <Text color='#800000'>read_orders</Text>
                    <Text color='#800000'>write_discounts</Text>
                    <Text color='#800000'>read_discounts</Text>
                    <Text color='#800000'>write_fulfillments</Text>
                    <Text color='#800000'>read_fulfillments</Text>
                    <Text color='#800000'>write_custom_fulfillment_services</Text>
                    <Text color='#800000'>read_custom_fulfillment_services</Text>
                    <Text color='#800000'>write_third_party_fulfillment_orders</Text>
                    <Text color='#800000'>read_third_party_fulfillment_orders</Text>
                    <Text color='#800000'>read_inventory</Text>
                    <Text color='#800000'>write_inventory</Text>
                  </Col>

                  <ShopifyCodeSnippet />
                </Col>
              ) : null}

            </Col>
          </Grid>
        </Col>
        {shop?.type === 'shopify' &&
          <Text h4 m1>Shopify App coming soon 'Easy self connection'</Text>
        }
      </Col>
      {shop?.type === 'shopify' &&
        <Col position={'fixed'} bottom={20} right={30}>
          <Image
            source={ASSETS.SHOPIFY_TEXT}
            style={{ height: 60, aspectRatio: 40 / 12 }}
            resizeMode='contain'
          />
        </Col>
      }
    </CMSLayout>
  );
};

UpsertStore.routeInfo = {
  title: 'My Store - Bottled Goose',
  path: '/my-stores/:id'
};

export default UpsertStore;
