import React, { useEffect, useState, useMemo } from 'react';
import { Col, Row, Text, Input02, modal } from 'components';
import { Image, useWindowDimensions } from 'react-native';
import { ASSETS } from 'assets';
import { COLOR } from 'const';
import { AntDesign } from '@expo/vector-icons';

const ShopifyAdminStoreUrl = ({ shopUrl, onChange, hideInstructions } : any) => {
  const [adminUrl, setAdminUrl] = useState(''); 
  const { width } = useWindowDimensions();

  const showGrabAdminScreenshot = () => {
    modal.show((
      <Col width={width * 0.8} ml0>
        <Image source={ASSETS.GRAB_ADMIN_URL} style={{ width: width * 0.8, height: (width * 0.8) * 270/620 }} resizeMode='contain' />
      </Col>
    ))
  }

  const { isValid, siteUrl } = useMemo(() => { 
    if (adminUrl.indexOf('https://') !== 0) return { isValid: false, siteUrl: '' };
    const a : HTMLAnchorElement = document.createElement("a");
    a.href = adminUrl;
    const pathname = a.pathname;
    const hostname = a.hostname;
    const slug = hostname.includes('.myshopify.com') ? hostname.replace('.myshopify.com', '') : pathname.replace('/store/', '');
    return {
      isValid: true,
      siteUrl: `https://${slug}.myshopify.com`,
    }
  }, [adminUrl]);

  useEffect(() => {
    if (!isValid) {
      if (!shopUrl) return;
      const slug = shopUrl.replace('.myshopify.com', '').replace('https://', '');
      setAdminUrl(`https://admin.shopify.com/store/${slug}`)
      return;
    }
    if (siteUrl === shopUrl) return;
    onChange(siteUrl);
  }, [isValid, siteUrl, shopUrl]);

  return (
    <Col mb1>
      <Row>
        <Col flex1>
          <Text subtitle1 mb1>Admin Store URL</Text>
          {!hideInstructions && (
            <Text caption mb1>* Grab your admin url in address bar <Text onPress={showGrabAdminScreenshot} bold color={COLOR.MAIN}>(see screenshot)</Text> and paste here</Text>
          )}
        </Col>
      </Row>
      
      <Input02
        height={35}
        value={adminUrl}
        onChange={setAdminUrl}
        mb2
      />
      {!!isValid && !!shopUrl && (
        <>
          <Row mb0>
            <AntDesign name="checkcircle" size={16} color={COLOR.MAIN} />
            <Text ml0>Your technical site URL:</Text>
          </Row>
          <Text mb0 bold color={COLOR.MAIN}>{shopUrl}</Text>
        </>
      )}
      {!isValid && !!adminUrl && (
        <Text color="red"> Invalid URL</Text>
      )}
    </Col>
  );
};

export default ShopifyAdminStoreUrl;