import React, { useEffect, useState } from 'react';
import { IScreen, TDesign, TOnlineStore, TProduct, TProductInstance } from 'type';
import { CMSLayout, Col, Row, Text, Button, useUIState, Grid, TouchField, ShimmerLoading, useRefState, showPopupMessage } from 'components';
import { useNavFunc } from 'navigation';
import { COLOR, SCREEN } from 'const';
import { ASSETS, SVG } from 'assets';
import { TimeHelper } from 'helpers';
import Store from 'store';
import { Image } from 'react-native';
import { Entypo } from '@expo/vector-icons';

const ListStore: IScreen = () => {
  const { navigation, navigate } = useNavFunc();

  const UserStore = Store.useUserStore();
  const ShopStore = Store.useShopStore();
  const [{ fetching, errorMes, loading: btnLoading }, setUI] = useUIState({ fetching: false });
  const [pagination, setPagination] = useState({ hasNext: false, total: 1 });
  const [shops, getShops, setShops] = useRefState<Array<TOnlineStore>>([])

  const getData = async (p) => {
    try {
      const u = await UserStore.onReady();
      const resellerId = UserStore.getResellerId(u);
      if (getShops().length === 0) {
        setUI({ fetching: true, errorMes: '' });
      }
      await TimeHelper.wait(500);
      const { list, hasNext, total, error } = await ShopStore.getList(p, resellerId);
      console.log({ list, hasNext, total, error });
      if (error) return setUI({ fetching: false, errorMes: error });
      setPagination({ hasNext, total });
      setShops(list);
      setUI({ fetching: false, errorMes: '' });
    } catch (err) {
      setUI({ fetching: false, errorMes: String(err) });
    }
  };

  useEffect(() => {
    // will getData when screen is focused
    const unsubscribe = navigation.addListener('focus', () => {
      getData(1);
    });
    return unsubscribe;
  }, []);

  const renderMyStores = () => {
    return (
      <>
        <Grid marginHorizontal={-20} xs='100%' md='40%' mb2>
          <Col round1 bgWhite p2>
            <Text caption>Create a new connection</Text>
            <Col mv2 height={1} backgroundColor={COLOR.GREY_LIGHT} />
            <Row>
              <Col flex1 middle>
                <Image source={ASSETS.SHOPIFY} style={{ height: 113, aspectRatio: 1 }} resizeMode='contain' />
                <Text mt1 center>Shopify</Text>

                <Button
                  onPress={() => {
                    navigation.reset({
                      index: 0,
                      routes: [{ name: SCREEN.UpsertStore, params: { id: 'new', storetype: 'shopify' } }],
                    });
                  }}
                  backgroundColor={COLOR.GREY}
                  bgHovered={COLOR.MAIN}
                  text='Connect'
                  borderRadius={20}
                  iconLeft={(
                    <SVG.Connect />
                  )}
                  mt2
                />
              </Col>
              <Col flex1 middle>
                <Image source={ASSETS.WOOCOMMERCE} style={{ height: 113, aspectRatio: 1 }} resizeMode='contain' />
                <Text mt1 center>WooCommerce</Text>
                <Button
                  onPress={() => 
                    showPopupMessage({
                      title: '',
                      content: 'Coming soon',
                      buttonOkText: 'OK',
                      // 
                      typeHighlight: 'warning',
                      contentHighlight: 'Notice'
                
                    })
                    // alert('Coming soon')
                  }
                  backgroundColor={COLOR.GREY}
                  bgHovered={COLOR.MAIN}
                  text='Connect'
                  borderRadius={20}
                  mt2
                  iconLeft={(
                    <SVG.Connect />
                  )}
                />
              </Col>
            </Row>
          </Col>
        </Grid>
        {shops.length > 0 && (
          <Col flex1 bgWhite round1 p2 marginHorizontal={-20}>
            <Row
              height={50} stretch
              borderBottomColor={COLOR.GREY_BG}
              borderBottomWidth={1}
            >
              <Col flex1 m1>
                <Text color={COLOR.GREY}>Store Name</Text>
              </Col>
              <Col flex1 m1>
                <Text color={COLOR.GREY}>URL</Text>
              </Col>
              <Col flex1 m1>
                <Text color={COLOR.GREY}>Type</Text>
              </Col>
              <Col flex1 m1>
                <Text color={COLOR.GREY}>Edit</Text>
              </Col>
            </Row>
            <Col>
              {shops.map((val, i) => {
                return (
                  <Row height={50} stretch key={val.id}>
                    <Col flex1 m1>
                      <Text>{val.name}</Text>
                    </Col>
                    <Col flex1 m1>
                      <Text >{val.url}</Text>
                    </Col>
                    <Col flex1 m1>
                      <Text>{val.type}</Text>
                    </Col>
                    <Col flex1 m1>
                      <TouchField
                        onPress={() => navigate(SCREEN.UpsertStore, { id: val.id })}
                        width={30} height={30} borderRadius={15} middle>
                        <Entypo name="edit" size={17} color="black" />
                      </TouchField>
                    </Col>
                  </Row>
                )
              })}
            </Col>
          </Col>
        )}
      </>
    );
  }

  return (
    <CMSLayout requireAuthen>
      <Row m2 marginBottom={0} justifyContent={'space-between'}>
        <Text h3 >My Stores</Text>
      </Row>
      <Col flex1 m2 marginTop={-5} p2>
        {errorMes ? (
          <Col flex1 middle>
            <Text color="red" subtitle1>{errorMes}</Text>
          </Col>
        ) : (
          fetching ? (
            <Row height={200} stretch marginHorizontal={-30}>
              <Col round1 bgWhite flex={1} m1 p1>
                <ShimmerLoading flex1 />
              </Col>
              <Col round1 bgWhite flex={1} m1 p1>
                <ShimmerLoading flex1 />
              </Col>
              <Col round1 bgWhite flex={1} m1 p1>
                <ShimmerLoading flex1 />
              </Col>
              <Col round1 bgWhite flex={1} m1 p1>
                <ShimmerLoading flex1 />
              </Col>
            </Row>
          ) : (
            renderMyStores()
          )
        )}

      </Col>
    </CMSLayout>
  );
};

ListStore.routeInfo = {
  title: 'My Stores - Bottled Goose',
  path: '/my-stores',
};

export default ListStore;