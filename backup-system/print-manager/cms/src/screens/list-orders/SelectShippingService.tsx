import { Radio, Space } from "antd";
import { Col, Text } from "components";
import React from "react";

const SelectShippingService = ({ onChange, value }) => {
  return (
    <Col mb2>
      <Text mb1>{value === "DPD" ? "Shipping service" : "Select shipping service"}</Text>
      <Radio.Group onChange={(e) => onChange(e.target.value)} value={value}>
        {value === "DPD" ?
          <Space direction="vertical">
            <Radio value={"DPD"}>DPD {`(£10.99)`}</Radio>
          </Space>
          :
          <Space direction="vertical">
            <Radio value={"RM24"}>Royal Mail 24 {`(£5.20)`}</Radio>
            <Radio value={"RM48"}>Royal Mail 48 {`(£4.18)`}</Radio>
          </Space>
        }
      </Radio.Group>
    </Col>
  )
}

export default SelectShippingService;
