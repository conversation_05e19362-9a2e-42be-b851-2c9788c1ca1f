import { Col, Row, Text, TouchField, Button, Grid } from 'components';
import React, { useState, useMemo } from 'react';
import Store from 'store';
import { useNavFunc } from 'navigation';
import { SCREEN, COLOR } from 'const';
import { Image } from 'react-native';
import moment from 'moment';
import { ASSETS } from 'assets';
import { Feather, EvilIcons, AntDesign } from '@expo/vector-icons';
import { useDynamicResponsiveValue } from 'quickly-react';
import { Tooltip } from 'react-tippy';
import { commonFuncs } from './ListOrders.funcs';
import RequestUtils from 'store/api-client/Request.utils';
import { Checkbox, ClientName, DateDispatched, DownloadArtwork, OrderLineItems, OrderNo, PreviewIcon, TableRow, TrackingID } from './ListOrders.Comps';

const OrderTable = ({ onRefresh, jobs, setJobs, status, selectedJobs, setSelectedJobs, isSearch = false }) => {
  const rV = useDynamicResponsiveValue(['xs', 'lg']);
  const device = rV({ xs: 'mobile', lg: 'desktop' });
  const { navigation } = useNavFunc();
  const UserStore = Store.useUserStore();
  const { user } = UserStore;

  const OrderStore = Store.useOrderStore();

  const isSelectedAll = Object.keys(selectedJobs).length === jobs.length;

  const atLeastOneSelected = useMemo(() => {
    return Object.keys(selectedJobs).filter(id => !!selectedJobs[id]).length > 0;
  }, [selectedJobs]);

  const listSelected = useMemo(() => {
    return Object.keys(selectedJobs).map(stringId => {
      if (!selectedJobs[stringId]) return undefined;
      const findJob = jobs.find(val => `${val.Id}_${val['Order ID']}` === stringId);
      return findJob;
    }).filter(Boolean);
  }, [selectedJobs, jobs]);

  const removeListSelected = () => {
    setJobs(l => l.filter(val => !selectedJobs[`${val.Id}_${val['Order ID']}`]));
    setSelectedJobs({});
  }

  const toggleSelectedAll = () => {
    if (isSelectedAll) {
      setSelectedJobs({});
    } else {
      const obj = {};
      jobs.forEach(v => {
        obj[`${v.Id}_${v['Order ID']}`] = true;
      });
      setSelectedJobs(obj);
    }
  };

  const deleteOrder = async (val) => {
    const success = await commonFuncs.onDelete([val]);
    if (!success) return;
    setJobs(l => l.filter(i => i.Id !== val.Id));
  }

  const Columns = useMemo(() => {
    return {
      Select: {
        Title: ({ toggleSelectedAll, isSelectedAll }) => (
          <Col width={30} m1>
            <Checkbox toggleSelect={toggleSelectedAll} isSelected={isSelectedAll} color={COLOR.GREY} />
          </Col>
        ),
        Content: ({ toggleSelect, isSelected }) => (
          <Col width={30} m1>
            <Checkbox toggleSelect={toggleSelect} isSelected={isSelected} />
          </Col>
        )
      },
      OrderNo: {
        Title: () => (
          <Col width={70} m1>
            <Text color={COLOR.GREY} numberOfLines={1}>Order No</Text>
          </Col>
        ),
        Content: ({ order }) => (
          <OrderNo order={order} canShowId={user?.role === 'admin'} />
        )
      },
      Client: {
        Title: () => (
          <Col flex1 m1>
            <Text color={COLOR.GREY}>Client</Text>
          </Col>
        ),
        Content: ({ order }) => (
          <ClientName order={order} canShowId={user?.role === 'admin'} />
        )
      },
      Product: {
        Title: () => (
          <Col flex={2} m1>
            <Text color={COLOR.GREY}>Product</Text>
          </Col>
        ),
        Content: ({ order }) => (
          <Col flex={2} m1>
            <OrderLineItems order={order['Raw Data']} supportedItemIDs={order['Item Supported']} />
          </Col>
        )
      },
      DateCreated: {
        Title: () => (
          <Col flex1 m1>
            <Text color={COLOR.GREY}>Date created</Text>
          </Col>
        ),
        Content: ({ order }) => (
          <Col flex1 m1>
            <Text>{moment(order.CreatedAt).format('DD/MM/YYYY')}</Text>
          </Col>
        )
      },
      DateDispatched: {
        Title: () => (
          <Col flex1 m1>
            <Text color={COLOR.GREY}>Date dispatched</Text>
          </Col>
        ),
        Content: ({ order }) => (
          <DateDispatched order={order} />
        )
      },
      Status: {
        Title: () => (
          <Col flex1 m1>
            <Text color={COLOR.GREY}>Status</Text>
          </Col>
        ),
        Content: ({ order }) => {
          const lastPipeline = order.Pipelines.length === 0 ? undefined : order.Pipelines[order.Pipelines.length - 1];
          const { reprint, isPaid, isAdminApproved, resellerReprint } = lastPipeline?.SharedData || {};

          const renderPendingStatus = () => {
            if (!isPaid) return 'Pending';
            if (user?.role !== 'admin') {
              return !isAdminApproved ? 'Paid - Pending review' : 'Pending'
            }
            return !isAdminApproved ? 'Paid - Pending review' : 'Pending';
          }

          const renderAcceptedStatus = () => {
            const inproduction = () => OrderStore.isInProduction(order) ? 'In production' : 'Accepted';
            return inproduction();
          }

          return (
            <Col flex1 m1>
              <Text>{
                order.Status === 'Accepted' ? renderAcceptedStatus() :
                  order.Status === 'Pending' ? renderPendingStatus() : order.Status
              }</Text>
              {!!reprint && (
                <Text caption mt0>FOC - reprint</Text>
              )}
              {!!resellerReprint && (
                <Text caption mt0>Reprint</Text>
              )}
            </Col>
          )
        },
      },
      Artwork: {
        Title: () => (
          <Col flex1 m1>
            <Text color={COLOR.GREY}>Artwork</Text>
          </Col>
        ),
        Content: ({ order, apiSlug }) => (
          <Col flex1 m1 alignItems="flex-start">
            <DownloadArtwork order={order} apiSlug={apiSlug} onRefresh={onRefresh} />
          </Col>
        ),
      },
      PackingSlips: {
        Title: () => (
          <Col flex1 m1>
            <Text color={COLOR.GREY}>Packing Slips</Text>
          </Col>
        ),
        Content: ({ order }) => (
          <Col flex1 m1>
            <a href={`${RequestUtils.host}/api/online-stores/order-packing-slips/${order['Order ID']}?clientId=${order['Client ID']}`} target="_blank" style={{ textDecoration: 'none' }}
            >
              <TouchField cirle middle>
                {/* PACKING SLIPS */}
                <Feather name="external-link" size={24} color="black" />
              </TouchField>
            </a>
          </Col>
        ),
      },
      TrackingID: {
        Title: () => (
          <Col flex1 m1>
            <Text color={COLOR.GREY}>Job Info</Text>
          </Col>
        ),
        Content: ({ order }) => (
          <TrackingID order={order} />
        ),
      },
      RePrint: {
        Title: () => user?.role !== 'admin' ? null : (
          <Col width={50} m1>
            <Text color={COLOR.GREY}>Reprint</Text>
          </Col>
        ),
        Content: ({ order }) => (
          <Col width={50} m1>
            <TouchField cirle middle onPress={async () => {
              if (user?.role !== 'admin') {
                commonFuncs.resellerReprintOrder([order], navigation);
                return;
              }
              await commonFuncs.reprint(order);
              navigation.setParams({
                stage: 'in-production',
              });
            }}>
              <AntDesign name="retweet" size={24} color="black" />
            </TouchField>
          </Col>
        ),
      }
    }
  }, [user]);

  const renderTableHeaders = () => {
    if (device === 'mobile') return null;
    return (
      <Grid md='100%' lg='1:any'>
        <Row
          height={50}
          borderBottomColor={COLOR.GREY_BG}
          borderBottomWidth={1}
        >
          <Columns.Select.Title toggleSelectedAll={toggleSelectedAll} isSelectedAll={isSelectedAll} />
          <Columns.OrderNo.Title />
          <Columns.Client.Title />
          <Columns.Product.Title />
          <Columns.DateCreated.Title />
          {(['accepted', 'fulfilled'].includes(status) || isSearch) && (
            <Columns.DateDispatched.Title />
          )}
          <Columns.Status.Title />
          {(status === 'accepted' || isSearch) && (
            <Columns.Artwork.Title />
          )}
          {(status === 'accepted' || isSearch) && (
            <Columns.PackingSlips.Title />
          )}
          {(status === 'fulfilled' || isSearch) && (
            <Columns.TrackingID.Title />
          )}
        </Row>
        {!isSearch ?
          <Col width={170} m1 alignItems={'flex-end'}>
            <Tooltip
              title={'If you delete a job, you will find it in here'}
              position="top"
            >
              <Button
                text='Recycling bin'
                height={30} borderRadius={15}
                onPress={() => navigation.navigate(SCREEN.DeletedOrders)}
              />
            </Tooltip>
            {atLeastOneSelected && status !== 'fulfilled' && (
              <TouchField ml1 cirle middle onPress={async () => {
                const success = await commonFuncs.onDelete(listSelected);
                if (success) removeListSelected();
              }}>
                <EvilIcons name="trash" size={24} color={COLOR.FONT} />
              </TouchField>
            )}
            {status === 'fulfilled' && (
              <Columns.RePrint.Title />
            )}
          </Col>
          :
          <Col width={170} m1 alignItems={'flex-end'}>
            <Columns.RePrint.Title />
          </Col>
        }
      </Grid>
    )
  }

  if (jobs.length === 0) {
    return (
      <Col flex1>
        <Text>No jobs found</Text>
      </Col>
    )
  }
  return (
    <Col>
      {renderTableHeaders()}
      <Col>
        {jobs.map((val, i) => {
          const orderStatus = String(val.Status).toLowerCase();
          const isSelected = selectedJobs[`${val.Id}_${val['Order ID']}`];
          const toggleSelect = () => {
            const obj = { ...selectedJobs };
            if (isSelected) {
              delete obj[`${val.Id}_${val['Order ID']}`];
            } else {
              obj[`${val.Id}_${val['Order ID']}`] = true;
            }
            setSelectedJobs(obj);
          }
          const isSampleRequest = val['Raw Data']?.is_sample_request
            || !!val.Pipelines?.[val.Pipelines?.length - 1]?.SharedData?.isSampleRequest;

          return (
            <TableRow index={i} isSelected={isSelected} key={`${val.Id}_${val['Order ID']}`}>
              <Grid md='100%' lg='1:any' key={val.id}>
                <Row
                  minHeight={50}
                  borderBottomColor={COLOR.GREY_BG}
                  borderBottomWidth={1}
                >
                  <Columns.Select.Content toggleSelect={toggleSelect} isSelected={isSelected} />
                  <Columns.OrderNo.Content order={val} />
                  <Columns.Client.Content order={val} />
                  <Columns.Product.Content order={val} />
                  <Columns.DateCreated.Content order={val} />
                  {(['accepted', 'fulfilled'].includes(status) || isSearch) && (
                    <Columns.DateDispatched.Content order={val} />
                  )}
                  <Columns.Status.Content order={val} />
                  {(status === 'accepted' || isSearch) && (
                    <Columns.Artwork.Content order={val} apiSlug={val.ApiSlug} />
                  )}
                  {(status === 'accepted' || isSearch) && (
                    <Columns.PackingSlips.Content order={val} />
                  )}
                  {(status === 'fulfilled' || isSearch) && (
                    <Columns.TrackingID.Content order={val} />
                  )}
                </Row>
                <Row width={170} m1 justifyContent={'flex-end'}>
                  {isSampleRequest &&
                    <Col round2 mr1 p0 backgroundColor={COLOR.MAIN}>
                      <Text colorWhite bold fontSize={10}>SAMPLE</Text>
                    </Col>
                  }
                  {orderStatus !== 'fulfilled' && (
                    <TouchField p0 borderWidth={0.5} borderThin
                      onPress={() => navigation.navigate(SCREEN.OrderDetail, { orderId: val['Order ID'] })}
                    >
                      <Text fontSize={8}>Job Info</Text>
                    </TouchField>
                  )}
                  <TouchField cirle middle onPress={() => commonFuncs.showCustomerInfo(val['Raw Data'])}>
                    <Image source={ASSETS.CUSTOMER_ADDRESS_ICON} style={{ width: 20, height: 40 }} resizeMode='contain' />
                  </TouchField>
                  <PreviewIcon order={val} />
                  {orderStatus !== 'fulfilled' && (
                    <TouchField cirle middle onPress={() => deleteOrder(val)}>
                      <EvilIcons name="trash" size={24} color={COLOR.FONT} />
                    </TouchField>
                  )}
                  {orderStatus === 'fulfilled' && (
                    <Columns.RePrint.Content order={val} />
                  )}
                </Row>
              </Grid>
            </TableRow>
          )
        })}
      </Col>
    </Col>
  )
};

export default OrderTable;
