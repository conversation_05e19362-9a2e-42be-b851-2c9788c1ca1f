import React, { useRef, useState } from 'react';
import { Col, ImageItem, Row, Text, TextAreaField, UploadFile } from 'components';
import { TProduct } from 'type';
import { Button as AntButton } from 'antd';
import { COLORS } from 'const';
import { LayoutChangeEvent } from 'react-native';
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { SortableContext, horizontalListSortingStrategy, useSortable, arrayMove } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

interface IProps {
  product?: TProduct;
  updateProduct?: any;
  updateProductData?: any;
}

const SortableImageItem = ({ id, url, onPressRemove, itemWidth }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    width: itemWidth,
    cursor: 'move',
    marginTop: 20,
    marginLeft: 20,
  };

  return (
    <div ref={setNodeRef} style={style} {...attributes} {...listeners}>
      <ImageItem
        uri={url}
        canRemove
        onPressRemove={() => onPressRemove(url)}
        style={{ width: itemWidth }}
      />
    </div>
  );
};

const UpsertProductEditorConfig = ({ product, updateProduct, updateProductData }: IProps) => {
  const uploadRef = useRef<{ showDialog: Function }>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [containerWidth, setContainerWidth] = useState(0);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5,
      },
    }),
    useSensor(KeyboardSensor)
  );

  const onImageUploaded = (urls) => {
    console.log('onImageUploaded', urls);
    if (urls.length === 0) return setIsLoading(false);
    updateProductData({
      exampleArtworks: [...(product?.data?.exampleArtworks || []), ...urls]
    })
    setIsLoading(false)
  };

  const onPressRemove = (url: string) => {
    updateProductData({
      exampleArtworks: product?.data?.exampleArtworks?.filter((val) => val !== url)
    })
  }

  const onLayout = (event: LayoutChangeEvent) => {
    setContainerWidth(event.nativeEvent.layout.width);
  }

  const column = Math.round((containerWidth - 40) / 200);
  const itemWidth = (containerWidth - 40) / column;

  const handleDragEnd = (event) => {
    const { active, over } = event;

    if (active.id !== over.id) {
      const oldIndex = product?.data?.exampleArtworks.indexOf(active.id);
      const newIndex = product?.data?.exampleArtworks.indexOf(over.id);

      updateProductData({
        exampleArtworks: arrayMove(product?.data?.exampleArtworks, oldIndex, newIndex)
      });
    }
  };

  return (
    <Col flex1 bgWhite onLayout={onLayout}>
      <TextAreaField
        title="Example artworks"
        icon="edit"
        value={product?.data?.exampleArtworks?.join('\n') || ''}
        onChangeText={(v) => updateProductData({ exampleArtworks: v.split('\n') })}
      />
      <AntButton
        size="large"
        type="primary"
        shape="round"
        loading={isLoading}
        onClick={() => {
          uploadRef.current?.showDialog();
          setIsLoading(true);
        }}
        style={{ width: 200, marginTop: 8 }}
      >
        <Text fontSize={16} fontWeight="600" colorWhite>
          Upload
        </Text>
      </AntButton>
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
      >
        <SortableContext
          items={product?.data?.exampleArtworks || []}
          strategy={horizontalListSortingStrategy}
        >
          <Row flexWrap="wrap" alignItems='flex-start' justifyContent="flex-start" marginLeft={-20}>
            {product?.data?.exampleArtworks?.map((url, idx) => (
              <SortableImageItem
                key={url + idx}
                id={url}
                url={url}
                onPressRemove={onPressRemove}
                itemWidth={itemWidth}
              />
            ))}
          </Row>
        </SortableContext>
      </DndContext>
      <UploadFile
        ref={uploadRef}
        onUploaded={onImageUploaded}
        isMulti
        accept="image/*"
        uploadFolder="bg-example-artworks"
      />
    </Col>
  )
};

export default UpsertProductEditorConfig;
