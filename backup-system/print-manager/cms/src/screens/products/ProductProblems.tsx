import React from 'react';
import { Col, Text, Row, TouchField, modal } from 'components';
import { FontAwesome5 } from '@expo/vector-icons';
import { Tooltip } from 'react-tippy';
import { useWindowDimensions } from 'react-native';
import { COLOR } from 'const';

const ProblemModal = ({ problems }) => {
  const { width } = useWindowDimensions();

  return (
    <Col p2 bgWhite shadow round1 width={Math.max(width * 0.5, 300)}>
      <Text mb2>The product with problems will not be visible to the resellers until the problems are fixed</Text>
      {problems.map((problem, index) => (
        <Col key={index} mb2>
          <Text mb0><Text bold>Problem: </Text> {problem.problem}</Text>
          <Text mb0><Text bold>How to fix: </Text> {problem.howToFix}</Text>
        </Col>
      ))}
    </Col>
  )
}

const ProductProblems = ({ problems }) => {

  const showProblems = () => {
    modal.show(<ProblemModal problems={problems} />)
  };

  return problems.length === 0 ? (
    <Text fontSize={10} textAlign={'right'} color='green' caption>Ready for{'\n'}resellers</Text>
  ) : (
    <Tooltip
      title={"This product has problems. Click to see details"}
      trigger="mouseenter"
      position="top"
    >
      <TouchField width={24} height={24} borderRadius={12} middle onPress={showProblems}>
        <FontAwesome5 name="info-circle" size={20} color="red" />
      </TouchField>
    </Tooltip>
  );
};

export default ProductProblems