import React, { useEffect, useState, useRef } from 'react';
import { IScreen } from 'type';
import { CMSLayout, Col, Row, Text, Button, ShimmerLoading, TouchField, Input02, Select01, RatioCol, RatioCol2, UploadFile, useUIState, useRefState, Grid, modal } from 'components';
import { useNavFunc } from 'navigation';
import { useDynamicResponsiveValue } from 'quickly-react';
import { COLOR, SCREEN } from 'const';
import Store from 'store';
import { Image } from 'react-native';
import { Entypo, AntDesign, EvilIcons, FontAwesome5, MaterialCommunityIcons, MaterialIcons } from '@expo/vector-icons';
import PrintAreas from './UpsertProduct.PrintAreas';
import { saveAs } from 'file-saver';
import { INCH_TO_MM } from 'const';
import ActivateModal from './UpsertProduct.Activate.Modal';
import { ValHelper } from 'helpers';

const TAB = {
  LISTING_INFO: 'LISTING_INFO',
  PRINT_INFO: 'PRINT_INFO',
  PRICING: 'PRICING',
  items: [
    { title: 'Listing Info', key: 'LISTING_INFO' },
    { title: 'Print Info', key: 'PRINT_INFO' },
    { title: 'Pricing', key: 'PRICING' },
  ],
}

type TSelectResellerOption = {
  label: string,
  value: string,
  data?: any,
}

const LeftColumn = Col;
const MiddleColumn = Col;
const RightColumn = Col;

type TVariantOption = {
  variant: string,
  prices: Array<{
    amount: any,
    price: any,
  }>
}

const UpsertProduct: IScreen = () => {

  const UserStore = Store.useUserStore();
  const ProductStore = Store.useProductStore();
  const { navigation, route } = useNavFunc();
  // @ts-ignore
  const { id } = route.params || {};
  const { product, setProduct, uiState, hasDoneSomeEditing } = ProductStore.useProduct(id);
  const productRef = useRef(product);
  useEffect(() => {
    productRef.current = product;
  }, [product])

  const physicalRatio = !product || !product.physicalHeight || !product.physicalWidth ? 1 : product.physicalWidth / product.physicalHeight;

  const canEdit = UserStore.user?.role === 'admin';

  const [curTab, setCurTab] = useState(TAB.LISTING_INFO);

  const [listResellers, setListResellers] = useState<Array<TSelectResellerOption>>([]);
  const [selectedResellers, setSelectedResellers] = useState<Array<TSelectResellerOption>>([]);

  const [unit, setUnit] = useState<'inch' | 'mm'>('mm');
  const [show360ProductEditor, setShow360ProductEditor] = useState<boolean>(false);

  const onEditorMessage = async (data: { event: string, payload: any } | undefined) => {
    console.log('onEditorMessage', data);
    if (!!data && data.event === 'PRODUCT_LIBRARY_SAVE') {
      if (!!data.payload && data.payload.productId === id) {
        const res = await Store.Api.Product.detail({ id });
        if (res.data.success) setProduct({
          ...productRef.current,
          data: {
            ...productRef.current.data,
            preview: res.data.data.data?.preview,
          },
        });
      }
    }
  }

  const variantOptions: Array<TVariantOption> = product?.variations && product?.variations.length > 0 ? product?.variations : [
    {
      variant: 'No Var + No Mirr',
      prices: [
        { amount: 1, price: '' },
        { amount: 10, price: '' },
        { amount: 25, price: '' },
        { amount: 50, price: '' },
        { amount: 100, price: '' },
        { amount: 250, price: '' },
        { amount: 350, price: '' },
      ]
    },
    {
      variant: 'Var + No Mirr',
      prices: [
        { amount: 1, price: '' },
        { amount: 10, price: '' },
        { amount: 25, price: '' },
        { amount: 50, price: '' },
        { amount: 100, price: '' },
        { amount: 250, price: '' },
        { amount: 350, price: '' },
      ]
    },
    {
      variant: 'Var + Mirr',
      prices: [
        { amount: 1, price: '' },
        { amount: 10, price: '' },
        { amount: 25, price: '' },
        { amount: 50, price: '' },
        { amount: 100, price: '' },
        { amount: 250, price: '' },
        { amount: 350, price: '' },
      ]
    },
  ];
  const setVariantOptions = (newOptions: Array<TVariantOption>) => {
    // setVariantOptions(product.variations);
    setProduct({
      ...product,
      variations: newOptions,
    })
  };

  // useEffect(() => {
  //   if (!product) return;
  //   if (product.variations && product.variations.length > 0) {
  //     setVariantOptions(product.variations);
  //   }
  // }, [product]);

  const [{ loading: imageLoading }, setImageUI] = useUIState();
  const [{ loading: submitting }, setSubmitUI] = useUIState();
  const [{ loading: removing }, setRemoveUI] = useUIState();

  const [imageId, getImageId, setImageId] = useRefState<'main' | 'galleries' | 'blueprint'>('main');

  const [focusedArea, setFocusedArea] = useState(-1);

  useEffect(() => {
    // setListResellers([
    //   { label: 'All resellers', value: 'all' },
    // ])
    (async () => {
      const { list } = await UserStore.getListResellers(1);
      if (list && list.length > 0) {
        setListResellers([
          { label: 'All resellers', value: 'all' },
          ...list.map(v => ({
            label: `${v.firstName} ${v.lastName}`,
            value: v.id,
          }))
        ]);
      }
    })();
  }, []);

  useEffect(() => {
    if (id !== 'new' && !product) return;
    if (
      !product?.wholeSale &&
      !product?.printOnDemand &&
      !product?.customProduct
    ) {
      console.log('here showActivateModal');
      setTimeout(() => {
        showActivateModal(product);
      }, 500);
    }
  }, [product, id]);

  const showActivateModal = (p) => {

    modal.show(
      <ActivateModal
        productType={{
          wholeSale: Boolean(product?.wholeSale),
          printOnDemand: Boolean(product?.printOnDemand),
          customProduct: Boolean(product?.customProduct),
        }}
        onUpdateProductType={async newType => {
          setProduct({
            ...p,
            ...newType,
          });
          if (id !== 'new') {
            await Store.Api.Product.upsert({
              id: product.id,
              ...newType,
            });
          }
        }}
      />
    )
  }

  const rV = useDynamicResponsiveValue(['xs', 'md']);
  const breakpoint = rV({ xs: 'xs', md: 'md' });

  const uploadRef = useRef<{ showDialog: Function }>(null);

  useEffect(() => {
    // if (product?.availableForResellerIds && !!product?.availableForResellerIds['all']) {
    //   setSelectedResellers([
    //     { label: 'All resellers', value: 'all' },
    //   ]);
    // }
    if (product?.availableForResellerIds) {
      const resellerIds = Object.keys(product?.availableForResellerIds).filter(id => !!product?.availableForResellerIds[id]);
      const selected = [];
      resellerIds.forEach(id => {
        const findReseller = listResellers.find(v => v.value === id);
        if (findReseller) selected.push(findReseller);
      });
      setSelectedResellers(selected);
    }
  }, [product?.availableForResellerIds, listResellers]);

  const onImageUploaded = urls => {
    console.log(getImageId());
    console.log('onImageUploaded urls', urls)
    if (urls.length === 0) return setImageUI({ loading: false });
    let newProduct = { ...product };
    switch (getImageId()) {
      case 'main':
        newProduct.image = urls[0];
        break;
      case 'galleries':
        newProduct.previewData = [
          ...(newProduct.previewData || []),
          ...urls.map(v => ({
            image: v,
            title: '',
            data: {},
          })),
        ];
        break;
      case 'blueprint':
        newProduct.bluePrintImage = urls[0];
        break;
    }
    setProduct(newProduct);
    setImageUI({ loading: false });
  }

  const validateNumberInput = (label) => () => {
    if (!product) return;
    const value = (() => {
      if (!label.includes('.')) return product[label];
    })();
    if (!value) return;
    if (isNaN(+value)) {
      alert('Please input number value');
      setProduct({ ...product, [label]: 0 })
    } else {
      setProduct({ ...product, [label]: +value })
    }
  }
  const onChangePrintArea = (label, index) => (newValue) => {
    const areas = (product?.printAreas || []).slice();
    areas[index][label] = newValue;
    setProduct({
      ...product,
      printAreas: areas,
    });
  }
  const onBlurPrintArea = (label, index) => () => {
    setFocusedArea(-1)
    const areas = (product?.printAreas || []).slice();
    const value = areas[index][label];
    if (isNaN(+value)) {
      alert('Please input number value');
      areas[index][label] = 0;
      console.log('areas', areas);
      setProduct({ ...product, printAreas: areas })
    } else {
      areas[index][label] = +value;
      setProduct({ ...product, printAreas: areas })
    }
  }
  console.log('product', product);
  const onChangePDFSKU = (label, index) => (newValue) => {
    console.log('newValue', newValue);
    const skuPDFs = (product?.data?.skuPDFs || []).slice();
    skuPDFs[index][label] = newValue;
    setProduct({
      ...product,
      data: {
        ...product?.data,
        skuPDFs,
      }
    });
  }

  const renderTab = () => {
    return (
      <Row mb0 height={40} marginLeft={-5}>
        {TAB.items.map((val, i) => (
          <Button
            key={val.key}
            text={val.title}
            outline={val.key !== curTab}
            height={30}
            borderRadius={15}
            width={100}
            m0
            onPress={() => val.key !== curTab && setCurTab(val.key)}
            bgHovered={val.key !== curTab ? COLOR.GREY_BG : undefined}
          />
        ))}
      </Row>
    );
  };

  const renderReady = () => {
    if (breakpoint === 'xs') return (
      <Col flex1 middle>
        <Text>Please use bigger screen to see this page.</Text>
      </Col>
    )

    return (
      <Row flex1 stretch>
        {curTab === TAB.LISTING_INFO && renderTabListing()}
        {curTab === TAB.PRINT_INFO && renderTabPrint()}
        {curTab === TAB.PRICING && renderTabPricing()}
      </Row>
    );
  };

  const swapGalaryImageWithMainImage = (galleryIndex) => {
    const shouldContinue = confirm('Are you sure you want to swap this image with the main image?');
    if (!shouldContinue) return;
    setProduct({
      ...product,
      image: product?.galleries[galleryIndex],
      galleries: product?.galleries.map((v, i) => i === galleryIndex ? product?.image : v)
    })
  }

  const renderCommonListingInfo = () => {
    return (
      <>
        <MiddleColumn flex={2} backgroundColor={'#E6E6E6'}>
          {show360ProductEditor ? (
            <Col flex1>
              <iframe
                src={window.location.href.includes('iframe_dev=1') ? `http://localhost:3000/product/${id}` : `${ValHelper.editorHost}/product/${id}`}
                style={{
                  width: '100%',
                  height: '100%',
                  border: 'none',
                  backgroundColor: COLOR.MAIN,
                }}
                onLoad={Store.Client.editor.initOnLoadAndOpenCommunication(onEditorMessage)}
                allowFullScreen
              />
            </Col>
          ) : (
            <Col flex1 middle>
              <RatioCol
                width='80%'
                ratio={1}
                borderColor={COLOR.GREY}
                borderStyle='dashed'
                borderWidth={!product?.image ? 1 : 0}
              >
                <Image source={{ uri: product?.image }} style={{ width: '100%', height: '100%' }} resizeMode='contain' />
              </RatioCol>

              <Row
                mt2 minWidth='90%' minHeight={70}
                borderColor={COLOR.GREY} borderStyle='dashed'
                borderWidth={!product?.galleries?.length ? 1 : 0}
                flexWrap='wrap' middle
              >
                {product?.galleries && product?.galleries.map((val, i) => (
                  <Col width={60} height={60} m0 key={'gallery-' + i}>
                    <Image
                      source={{ uri: val }}
                      style={{ width: '100%', height: '100%' }}
                      resizeMode='contain'
                    />
                    <Col
                      absolute
                      top={-5}
                      right={-5}
                      p0
                      onPress={() => {
                        setProduct({
                          ...product,
                          // @ts-ignore
                          previewData: product?.previewData.filter((item, itemIndex) => itemIndex !== i),
                        })
                      }}
                    >
                      <AntDesign name="closecircle" size={12} color="black" />
                    </Col>
                    <Col absolute bottom={-20} left={0} right={0}
                      onPress={() => swapGalaryImageWithMainImage(i)}
                    >
                      <Row middle>
                        <MaterialIcons name="published-with-changes" size={16} color="rgba(0,0,0,0.5)" />
                        <Text ml0 caption color='rgba(0,0,0,0.5)'>Swap</Text>
                      </Row>
                    </Col>
                    
                  </Col>
                ))}
              </Row>
            </Col>
          )}
          {canEdit && (
            <Row absolute top={10} left={10} right={10}>
              {product?.galleries && product?.galleries.length > 0 && product?.printAreas && product?.printAreas.length > 0 && (
                <Button
                  outline
                  bgHovered={COLOR.GREY_BG}
                  text='Toggle Preview'
                  height={30}
                  borderRadius={15}
                  width={150}
                  iconLeft={
                    <FontAwesome5 name={show360ProductEditor ? "toggle-on" : "toggle-off"} size={20} color="black" />
                  }
                  mh0
                  onPress={() => {
                    setShow360ProductEditor(!show360ProductEditor);
                  }}
                />
              )}
              <Button
                isLoading={imageLoading && imageId === 'main'}
                outline
                bgHovered={COLOR.GREY_BG}
                text='Main Image'
                height={30}
                borderRadius={15}
                width={120}
                iconLeft={
                  <Entypo name="upload" size={20} color="black" />
                }
                mh0
                onPress={() => {
                  setImageId('main');
                  setImageUI({ loading: true });
                  uploadRef.current.showDialog();
                }}
              />
              <Button
                mh0
                isLoading={imageLoading && imageId === 'galleries'}
                outline
                bgHovered={COLOR.GREY_BG}
                text='Galleries'
                height={30}
                borderRadius={15}
                width={110}
                iconLeft={
                  <Entypo name="upload" size={20} color="black" />
                }
                onPress={() => {
                  setImageId('galleries');
                  setImageUI({ loading: true });
                  uploadRef.current.showDialog();
                }}
              />
            </Row>
          )}

        </MiddleColumn>

        <UploadFile
          ref={uploadRef}
          onUploaded={onImageUploaded}
        />
      </>
    )
  }

  const renderTabListing = () => {
    const tagOptions = (product?.tags || '').trim().split(',').filter(val => !!val).map(val => ({ label: val, value: val }));
    // console.log('tagOptions', tagOptions);
    return (
      <>
        <LeftColumn flex={2} p2 overflow={'scroll'}>
          {renderTab()}
          <Text caption mb1>* Listing Info is the information of the product that will be displayed in public {`(eg: Reseller website, Shopify website)`}</Text>

          <Text subtitle1 mb1>Product Name</Text>
          <Input02
            height={35}
            value={product?.name || ''}
            onChange={(v) => setProduct({ ...product, name: v })}
            mb1
            inputProps={{ editable: canEdit }}
          />

          {/* <Text subtitle1 mb1>Tags</Text> */}
          {/* <Input02
            height={35}
            value={product?.tags || ''}
            onChange={(v) => setProduct({ ...product, tags: v })}
            mb1
          /> */}
          {/* <Select01
            placeholder='Input tags here'
            value={tagOptions}
            isMulti
            onChange={(newValues) => {
              setProduct({
                ...product,
                tags: newValues.map(val => val.value).join(', '),
              });
            }}
            loadOptions={(inputValue, callback) => {
              callback([
                {
                  label: 'Input new tags',
                  options: [
                    { label: inputValue, value: inputValue }
                  ]
                }
              ])
            }}
            mb1
          /> */}

          {UserStore.user?.role === 'admin' && (
            <>
              <Text subtitle1 mb1>Brands:</Text>
              <Select01
                value={selectedResellers}
                isMulti
                onChange={(newVal) => {
                  setSelectedResellers(newVal);
                  // triggle save
                  setProduct({
                    ...product,
                  });
                }}
                options={listResellers}
                mb1
              />
            </>
          )}

          <Text subtitle1 mb1>Description</Text>
          <Input02
            value={product?.description || ''}
            onChange={(v) => setProduct({ ...product, description: v })}
            mb1
            inputProps={{ multiline: true, editable: canEdit }}
            height={150}
          />
        </LeftColumn>
        {renderCommonListingInfo()}
      </>
    )
  }

  const renderUnitSelection = () => {
    return null;
    // const colors = {
    //   mm: {
    //     bg: unit === 'mm' ? COLOR.MAIN : COLOR.GREY_LIGHT,
    //     text: unit === 'mm' ? 'white' : undefined,
    //   },
    //   inch: {
    //     bg: unit === 'inch' ? COLOR.MAIN : COLOR.GREY_LIGHT,
    //     text: unit === 'inch' ? 'white' : undefined,
    //   }
    // }
    // return (
    //   <Row ml1>
    //     <Col round1 p0 backgroundColor={colors.mm.bg} onPress={() => setUnit('mm')}>
    //       <Text caption color={colors.mm.text}>mm</Text>
    //     </Col>
    //     <Col round1 p0 ml1 backgroundColor={colors.inch.bg} onPress={() => setUnit('inch')}>
    //       <Text caption color={colors.inch.text}>inch</Text>
    //     </Col>
    //   </Row>
    // );
  }

  const renderTabPrint = () => {
    return (
      <>
        <LeftColumn flex={2} p2 overflow={'scroll'}>
          {renderTab()}
          <Text caption mb1>* Print Info is the technical information for designing and printing.</Text>

          <Row mb1>
            <Text subtitle1 >Print Size</Text>
            {renderUnitSelection()}
          </Row>

          <Text caption mb1>* Print width & height will be the size of the output PDF document.</Text>
          <Row>
            <Col flex1 mr1>
              <Text subtitle1 mb1>Width {`(${unit})`}</Text>
              <Input02
                height={35}
                value={product?.physicalWidth || ''}
                onChange={(v) => setProduct({ ...product, physicalWidth: v })}
                mb1
                onBlur={validateNumberInput('physicalWidthIn')}
                inputProps={{ editable: canEdit }}
              />
            </Col>
            <Col flex1>
              <Text subtitle1 mb1>Height {`(${unit})`}</Text>
              <Input02
                height={35}
                value={product?.physicalHeight || ''}
                onChange={(v) => setProduct({ ...product, physicalHeight: v })}
                mb1
                onBlur={validateNumberInput('physicalHeightIn')}
                inputProps={{ editable: canEdit }}
              />
            </Col>
          </Row>

          {
            product?.physicalHeight && product?.physicalWidth && (
              <>
                {canEdit ? (
                  <Button
                    outline text="Add print area"
                    mb1
                    height={30}
                    borderRadius={4}
                    borderWidth={1}
                    bgHovered={COLOR.GREY_LIGHT}
                    onPress={() => {
                      const areas = (product?.printAreas || []).slice();
                      const height = Number((product?.physicalHeight / 3).toFixed(2));
                      areas.push({
                        width: product?.physicalWidth || 0,
                        height,
                        top: product?.physicalHeight - height,
                        left: 0,
                      });
                      setProduct({
                        ...product,
                        printAreas: areas,
                      })
                    }}
                  />
                ) : (
                  <Text subtitle1 mb1>Print Areas</Text>
                )}
              </>

            )
          }


          {(product?.printAreas || []).map((val, i) => {
            return (
              <Col key={'print-area-' + i}>
                <Row alignItems={'flex-end'}>
                  <Col flex1 margin={2.5}>
                    <Text caption mb0 numberOfLines={1}>Width ({unit})</Text>
                    <Input02
                      value={val.width}
                      onChange={onChangePrintArea('width', i)}
                      onBlur={onBlurPrintArea('width', i)}
                      onFocus={() => setFocusedArea(i)}
                      inputProps={{ editable: canEdit }}
                    />
                  </Col>
                  <Col flex1 margin={2.5}>
                    <Text caption mb0 numberOfLines={1}>Height ({unit})</Text>
                    <Input02
                      value={val.height}
                      onChange={onChangePrintArea('height', i)}
                      onBlur={onBlurPrintArea('height', i)}
                      onFocus={() => setFocusedArea(i)}
                      inputProps={{ editable: canEdit }}
                    />
                  </Col>
                  <Col flex1 margin={2.5}>
                    <Text caption mb0 numberOfLines={1}>Top ({unit})</Text>
                    <Input02
                      value={val.top}
                      onChange={onChangePrintArea('top', i)}
                      onBlur={onBlurPrintArea('top', i)}
                      onFocus={() => setFocusedArea(i)}
                      inputProps={{ editable: canEdit }}
                    />
                  </Col>
                  <Col flex1 margin={2.5}>
                    <Text caption mb0 numberOfLines={1}>Left ({unit})</Text>
                    <Input02
                      value={val.left}
                      onChange={onChangePrintArea('left', i)}
                      onBlur={onBlurPrintArea('left', i)}
                      onFocus={() => setFocusedArea(i)}
                      inputProps={{ editable: canEdit }}
                    />
                  </Col>
                  <Col width={40}>
                    <TouchField cirle middle onPress={() => {
                      const areas = (product?.printAreas || []).slice();
                      setProduct({
                        ...product,
                        printAreas: [
                          ...areas.slice(0, i),
                          ...areas.slice(i + 1, areas.length),
                        ],
                      });
                    }}>
                      <EvilIcons name="trash" size={24} color={COLOR.FONT} />
                    </TouchField>
                  </Col>
                </Row>
              </Col>
            );
          })}

          {canEdit ? (
            <Button
              outline text="Add SKU PDF"
              mb1
              mt2
              height={30}
              borderRadius={4}
              borderWidth={1}
              bgHovered={COLOR.GREY_LIGHT}
              onPress={() => {
                const skuPDFs = (product?.data?.skuPDFs || []).slice();
                skuPDFs.push({
                  sku: '',
                  url: '',
                });
                setProduct({
                  ...product,
                  data: {
                    ...product?.data,
                    skuPDFs
                  },
                })
              }}
            />
          ) : (
            <Text subtitle1 mb1>SKU PDF</Text>
          )}
          <Text caption mb1>* Some products do not need personalisation. Define SKU PDF here will help the system to skip PDF making process and use the PDF link instead</Text>

          {(product?.data?.skuPDFs || []).map((val, i) => {
            return (
              <Col key={'sku-pdf-' + i}>
                <Row alignItems={'flex-end'}>
                  <Col flex1 margin={2.5}>
                    <Text caption mb0 numberOfLines={1}>SKU</Text>
                    <Input02
                      value={val.sku}
                      onChange={onChangePDFSKU('sku', i)}
                      inputProps={{ editable: canEdit }}
                    />
                  </Col>
                  <Col flex1 margin={2.5}>
                    <Text caption mb0 numberOfLines={1}>PDF</Text>
                    <Input02
                      value={val.url}
                      onChange={onChangePDFSKU('url', i)}
                      inputProps={{ editable: canEdit }}
                    />
                  </Col>
                  <Col width={40}>
                    <TouchField cirle middle onPress={() => {
                      const skuPDFs = (product?.data?.skuPDFs || []).slice();
                      setProduct({
                        ...product,
                        data: {
                          ...product?.data,
                          skuPDFs: [
                            ...skuPDFs.slice(0, i),
                            ...skuPDFs.slice(i + 1, skuPDFs.length),
                          ]
                        },
                      });
                    }}>
                      <EvilIcons name="trash" size={24} color={COLOR.FONT} />
                    </TouchField>
                  </Col>
                </Row>
              </Col>
            );
          })}

        </LeftColumn>
        <PrintAreas
          product={product}
          physicalRatio={physicalRatio}
          focusedArea={focusedArea}
        />
        {/* <RightColumn flex={1} middle>
          {canEdit && (
            <>
              <Button
                isLoading={imageLoading && imageId === 'blueprint'}
                outline
                bgHovered={COLOR.GREY_BG}
                text='Blueprint Image'
                height={30}
                borderRadius={15}
                width={150}
                iconLeft={
                  <Entypo name="upload" size={20} color="black" />
                }
                mb2
                mh0
                onPress={() => {
                  setImageId('blueprint');
                  setImageUI({ loading: true });
                  uploadRef.current.showDialog();
                }}
              />
              <Text caption ph2>* Blueprint Image should be a blueprint, or an flat-front image of bottle, without any extra space. Using other type of image will result in miscalculation</Text>
            </>
          )}

          
        </RightColumn> */}
        <UploadFile
          ref={uploadRef}
          onUploaded={onImageUploaded}
        />
      </>
    )
  }

  const renderTabPricing = () => {
    return (
      <>
        <LeftColumn flex={2} p2 overflow={'scroll'}>

          {renderTab()}

          <Text caption mb1>* Pricing options that will be displayed and applied for resellers (each download).</Text>

          <Row mb4>
            <Text subtitle1 >Pricing</Text>
          </Row>
          <Grid xs='100%' md='33%' alignItems={'flex-start'}>
            {variantOptions.map((vO, i) => {
              return (
                <Col key={'variant-' + i} alignItems='flex-start'
                  borderLeftColor={COLOR.GREY_LIGHT}
                  borderLeftWidth={i === 0 ? 0 : 1}
                  pl1
                >
                  <Col flex1 ml0>
                    <Text bold mb2 ml1>{vO.variant}</Text>
                    {vO.prices.map((vP, vPI) => {
                      return (
                        <Row p1 key={'variant-item-' + i + '_' + vPI} width100p>
                          <Col flex1>
                            <Text caption>Amount</Text>
                            <Input02
                              inputProps={{ editable: canEdit }}
                              value={vP.amount}
                              onChange={(value) => {
                                const newVariants = variantOptions.slice();
                                newVariants[i].prices[vPI].amount = value;
                                setVariantOptions(newVariants);
                              }}
                            />
                          </Col>
                          <Col flex1 ml0>
                            <Text caption>Price</Text>
                            <Input02
                              inputProps={{ editable: canEdit }}
                              value={vP.price}
                              onChange={(value) => {
                                const newVariants = variantOptions.slice();
                                newVariants[i].prices[vPI].price = value;
                                setVariantOptions(newVariants);
                              }}
                            />
                          </Col>
                          <Col mt1 ml0>
                            <TouchField cirle middle onPress={() => {
                              const newVariants = variantOptions.slice();
                              newVariants[i].prices = newVariants[i].prices.filter((item, itemIndex) => itemIndex !== vPI);
                              setVariantOptions(newVariants);
                            }}>
                              <EvilIcons name="trash" size={24} color={COLOR.GREY} />
                            </TouchField>
                          </Col>
                        </Row>
                      );
                    })}
                    <Row justifyContent={'space-between'} pr2>
                      <TouchField cirle middle
                        onPress={() => {
                          const newVariants = variantOptions.slice();
                          newVariants[i].prices = newVariants[i].prices.sort((a, b) => a.amount > b.amount ? 1 : -1);
                          setVariantOptions(newVariants);
                        }}
                      >
                        <MaterialCommunityIcons name="sort-numeric-ascending" size={24} color={COLOR.GREY} />
                      </TouchField>
                      <TouchField p0 onPress={() => {
                        const newVariants = variantOptions.slice();
                        newVariants[i].prices.push({
                          amount: '',
                          price: '',
                        });
                        setVariantOptions(newVariants);
                      }}>
                        <Text>+ Add variation</Text>
                      </TouchField>
                    </Row>
                  </Col>
                </Col>
              );
            })}
          </Grid>
        </LeftColumn>
        {/* {renderCommonListingInfo()} */}
      </>
    )
  }

  const activateText = (() => {
    if (!product?.customProduct && !product?.wholeSale && !product?.printOnDemand) return 'Activate this product';
    const arr = [];
    if (product?.wholeSale) arr.push('Whole Sale');
    if (product?.printOnDemand) arr.push('Print on Demand');
    if (product?.customProduct) arr.push('Custom product');
    return arr.join(', ');
  })();

  return (
    <CMSLayout requireAuthen>
      <Row m2 marginBottom={0} justifyContent={'space-between'}>
        <Text h3>{id === 'new' ? 'Create new product' : product?.name}</Text>
        {canEdit && (
          <Row>
            <Button
              outline
              text={activateText}
              height={40}
              borderRadius={20}
              width={200}
              onPress={() => {
                showActivateModal(product);
              }}
              mr1
            />
            {id !== 'new' && (
              <Button
                mr1
                isLoading={removing}
                text='Delete'
                height={40}
                borderRadius={20}
                width={100}
                {...Button.colors.redSolid}
                onPress={async () => {
                  const shouldRemove = confirm('Are you sure you want to delete this product?');
                  if (!shouldRemove) return;
                  setRemoveUI({ loading: true });
                  try {
                    const res = await Store.Api.Product.remove({ id });
                    if (res.data.error) {
                      alert(res.data.error);
                    } else {
                      navigation.navigate(SCREEN.ListProducts);
                    }
                  } catch (err) {
                    alert(String(err));
                  }
                  setRemoveUI({ loading: false });
                }}
              />
            )}
            <Button
              isLoading={submitting}
              text={id === 'new' ? "Create" : "Save"}
              width={100} height={40} borderRadius={20}
              backgroundColor={!hasDoneSomeEditing ? 'rgba(0,0,0,0.3)' : COLOR.MAIN}
              onPress={!hasDoneSomeEditing ? undefined : async () => {
                if (!product) return;
                setSubmitUI({ loading: true });
                const availableForResellerIds = {};
                selectedResellers.forEach(({ value }) => {
                  availableForResellerIds[value] = true;
                })
                console.log('product.data', product.data);
                const res = await Store.Api.Product.upsert({
                  id: product.id,
                  name: product.name,
                  description: product.description,
                  image: product.image,
                  galleries: product.galleries,
                  physicalWidth: product.physicalWidth,
                  physicalHeight: product.physicalHeight,
                  printAreas: product.printAreas,
                  availableForResellerIds,
                  tags: product.tags,
                  data: {
                    ...product.data,
                  },
                  previewData: product.previewData,
                  variations: variantOptions.map(v => {
                    v.prices = v.prices.map(vP => {
                      vP.amount = Number(vP.amount);
                      vP.price = Number(vP.price);
                      return vP;
                    });
                    return v;
                  }),
                  wholeSale: product.wholeSale,
                  printOnDemand: product.printOnDemand,
                  customProduct: product.customProduct,
                });
                if (res.data.error) {
                  alert(res.data.error)
                } else if (res.data.data.id) {
                  navigation.navigate(SCREEN.UpsertProduct, { id: res.data.data.id });
                  if (product.id) {
                    alert('Save product successfully');
                  }
                }
                setSubmitUI({ loading: false });
              }}
            />
          </Row>
        )}
      </Row>
      <Col flex1 m2 mv1 round1 bgWhite borderWidth={1} borderColor={'white'}>
        {uiState.errorMes ? (
          <Col flex1 middle>
            <Text color="red" subtitle1>{uiState.errorMes}</Text>
          </Col>
        ) : (
          uiState.fetching ? (
            <Row height={50} stretch>
              <ShimmerLoading round1 flex={1} m1 />
              <ShimmerLoading round1 flex={1} m1 />
              <ShimmerLoading round1 flex={1} m1 />
              <ShimmerLoading round1 flex={1} m1 />
            </Row>
          ) : (
            renderReady()
          )
        )}
      </Col>
    </CMSLayout>
  );
};

UpsertProduct.routeInfo = {
  title: 'Product - Bottled Goose',
  path: '/product/:id',
};

export default UpsertProduct;
