import React, { useState } from 'react';
import { <PERSON>ton, Col, Row, Text, TouchField, modal } from 'components';
import { FontAwesome5, Ionicons } from '@expo/vector-icons';
import { COLOR } from 'const';

const Checkbox = ({ isSelected, toggleSelect, translateY = 0 }) => {
  // return (
  //   <TouchField cirle middle onPress={toggleSelect} transform={[{ translateY: translateY }]}>
  //     {isSelected ? (
  //       <Ionicons name="checkmark-circle" size={18} color={COLOR.MAIN} />
  //     ) : (
  //       <FontAwesome5 name="circle" size={18} color="black" />
  //     )}
  //   </TouchField>
  // );
  return (
    <Col height={25} middle>
      {isSelected ? (
        <Ionicons name="checkmark-circle" size={20} color={COLOR.MAIN} />
      ) : (
        <FontAwesome5 name="circle" size={18} color="black" />
      )}
    </Col>
  )
}

const ActivateModal = ({ productType, onUpdateProductType }) => {
  const [{ wholeSale, printOnDemand, customProduct }, setType] = useState(productType);
  const [errorMes, setErrorMes] = useState('');
  const toggleSelect = (key) => {
    setType(v => ({ ...v, [key]: !v[key] }))
  };
  console.log('ActivateModal');
  return (
    <Col round0 p2 activeOpacity={1} bgWhite width={320}>
      <Text mb2 subtitle1>Activate this product</Text>
      <Col mb0 onPress={() => toggleSelect('wholeSale')}>
        <Row >
          <Checkbox isSelected={wholeSale} toggleSelect={() => toggleSelect('wholeSale')} />
          <Text ml1>Wholesale</Text>
        </Row>
      </Col>
      <Col mb0 onPress={() => toggleSelect('printOnDemand')}>
        <Row >
          <Checkbox isSelected={printOnDemand} toggleSelect={() => toggleSelect('printOnDemand')} />
          <Text ml1>Print On Demand</Text>
        </Row>
      </Col>
      <Col mb0 onPress={() => toggleSelect('customProduct')}>
        <Row >
          <Checkbox isSelected={customProduct} toggleSelect={() => toggleSelect('customProduct')} />
          <Text ml1>Custom Product</Text>
        </Row>
      </Col>
      {errorMes && (
        <Col mt1 alignItems={'flex-end'}>
          <Text subtitle1 color='red'>{errorMes}</Text>
        </Col>
      )}
      <Row mt1 justifyContent={'flex-end'}>
        <Button
          solid={false}
          outline
          text="Cancel" onPress={() => modal.hide()}
          mr1
          height={30}
          width={100}
          borderRadius={15}
        />
        <Button
          solid
          outline={false}
          text="Activate" onPress={() => {
            if (!wholeSale && !printOnDemand && !customProduct) {
              return setErrorMes('Please select at least one!');
            }
            onUpdateProductType({ wholeSale, printOnDemand, customProduct });
            modal.hide();
          }}
          height={30}
          width={100}
          borderRadius={15}
        />
      </Row>
    </Col>
  )
}

export default ActivateModal;