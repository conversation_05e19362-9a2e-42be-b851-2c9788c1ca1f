import React, { useEffect, useState, useRef, useMemo } from 'react';
import { IScreen, TDesign, TOnlineStore } from 'type';
import { CMSLayout, Col, Row, Text, Button, ShimmerLoading, TouchField, Input02, Select01, RatioCol, RatioCol2, UploadFile, useUIState, useRefState, Grid, modal, showPopupMessage } from 'components';
import { useNavFunc } from 'navigation';
import { useDynamicResponsiveValue } from 'quickly-react';
import { COLOR, SCREEN } from 'const';
import Store from 'store';
import { Image } from 'react-native';
import { Image as ImageAnt } from 'antd';
import { Entypo, AntDesign } from '@expo/vector-icons';
import Variants from './ResellerCreateMyOwnProduct.Variants';
import PublishProductModal from './PublishProductModal';
import EditDesignModal from './EditDesignModal';
import { notification } from 'antd';
import { modalConfirm, <PERSON>Hel<PERSON> } from 'helpers';
import { useNavigationMiddleWare } from 'navigation/NavigationContext';
import { Tooltip } from 'react-tippy';
import OrderSampleModal from './OrderSampleModal';

const TAB = {
  LISTING_INFO: 'LISTING_INFO',
  PRINT_INFO: 'PRINT_INFO',
  PRICING: 'PRICING',
  items: [
    { title: 'Listing Info', key: 'LISTING_INFO' },
    { title: 'Print Info', key: 'PRINT_INFO' },
  ],
}

const LeftColumn = Col;
const MiddleColumn = Col;
const RightColumn = Col;

type TVariantOption = {
  variant: string,
  prices: Array<{
    amount: any,
    price: any,
  }>
}

const RequireSaved = ({ children, design }) => {
  const ButtonWrapper = design?.id ? Col : Tooltip;
  return (
    <ButtonWrapper
      title="You need to save product before publishing to store"
      trigger="mouseenter"
      position="top"
    >
      {children}
    </ButtonWrapper>
  )
}

const screenName = "ResellerCreateMyOwnProduct";

const ResellerCreateMyOwnProduct: IScreen = () => {

  const UserStore = Store.useUserStore();
  const { user } = UserStore;
  const ProductStore = Store.useProductStore();
  const ShopStore = Store.useShopStore();
  const DesignStore = Store.useDesignStore();
  const { navigation, route } = useNavFunc();
  const [show360ProductEditor, setShow360ProductEditor] = useState<boolean>(false);
  // @ts-ignore
  const { productId, designId } = route.params || {};
  const { product, setProduct, uiState, hasDoneSomeEditing } = ProductStore.useProduct(productId);
  const [sampleDesignData, setSampleDesignData] = useState({
    designId: '',
    image: '',
    galleries: [],
    style: '',
  });

  const productRef = useRef(product);
  useEffect(() => {
    productRef.current = product;
  }, [product])


  const sampleModelRef = useRef<any>(null);

  const [curTab, setCurTab] = useState(TAB.LISTING_INFO);
  const [listBrandOption, setListBrandOption] = useState<Array<{ label: string, value: string, data: TOnlineStore }>>([]);
  const { design, setDesign, uiState: uiStateDesign } = DesignStore.useDesign(designId, false, false);
  const [loadingTemplate, setLoadingTemplate] = useState(false);
  const { updateEditting } = useNavigationMiddleWare();
  const [justPublished, setJustPublished] = useState(false);
  const [{ loading: saving }, setSavingUI] = useUIState();

  const updateDesign = (obj) => {
    setDesign((d: any) => {
      return {
        ...d,
        ...obj,
      }
    });
    updateEditting(screenName, true);
    setJustPublished(false);
  }

  const setDesignData = (obj) => {
    setDesign((d: any) => {
      return {
        ...d,
        data: {
          ...d?.data,
          ...obj,
        }
      }
    });
    updateEditting(screenName, true);
    setJustPublished(false);
  }

  const shouldUpdateShopify = () => new Promise<boolean>((resolve) => {
    modalConfirm({
      title: 'Update',
      content: 'Do you want to update the product and variants on Shopify store?',
      onOk: () => resolve(true),
      onCancel: () => resolve(false),
      cancelText: "No",
    })
  })

  const getData = async () => {
    try {
      const u = await UserStore.onReady();
      const resellerId = UserStore.getResellerId(u);
      const list = await ShopStore.getAll(resellerId);
      setListBrandOption(list.map((x) => ({ label: x.name, value: x.id, data: x })));
    } catch (err) { }
  };

  useEffect(() => {
    if (product && designId === "new") {
      const d = {
        name: product.name,
        isCustomizable: true,
        productId: product.id,
        width: product.physicalWidth,
        height: product.physicalHeight,
        printAreas: product.printAreas,
        customProduct: !!product.customProduct,
        printOnDemand: !!product.printOnDemand,
        wholeSale: !!product.wholeSale,
        image: product.image,
        resalePrice: 0,
        description: product.description,
      }
      setDesign(d);
      setTimeout(() => {
        onSubmit(d as TDesign);
      }, 500);
    }
  }, [product]);

  useEffect(() => {
    getData();
  }, []);

  const rV = useDynamicResponsiveValue(['xs', 'md']);
  const breakpoint = rV({ xs: 'xs', md: 'md' });

  const scentVariantOpts = useMemo(() => {
    return product?.data?.scentVariants?.split(/,|\n/).filter(i => i.trim()).map(i => ({
      label: i,
      value: i,
    }))
  }, [product]);
  const isCancdleSceneInfoAdded = useRef(false);
  const didAutoFillUserInfoToCandle = useRef(false);

  const isCandle = useMemo(() => {
    return typeof product?.tags?.includes === 'function' && product?.tags.includes('candle');
  }, [product]);

  useEffect(() => {
    if (!design) return;
    if (!isCandle) return;

    isCancdleSceneInfoAdded.current = 
      !!(design?.data?.candle?.defaultScentName || design?.data?.candle?.scentName)
      && !!(design?.data?.candle?.companyName)
      && !!(design?.data?.candle?.address);
    // console.log('isCancdleSceneInfoAdded.current', isCancdleSceneInfoAdded.current);
    // console.log('design', design);

    if (didAutoFillUserInfoToCandle.current) return;
    const newDesign = { ...design };
    if (!!user && !design.data?.candle?.companyName) {
      console.log('user?.accountName', user?.accountName);
      console.log('user', user);
      newDesign.data = {
        ...design?.data,
        candle: {
          ...design?.data?.candle,
          companyName: user?.accountName,
        }
      }
    }
    if (!!user && !design.data?.candle?.address) {
      newDesign.data = {
        ...design.data,
        candle: {
          ...newDesign.data?.candle,
          address: [user?.addressLine1, user?.addressLine2, user?.postCode].filter(Boolean).join('\n'),
        }
      }
    }
    setDesign(newDesign);
    didAutoFillUserInfoToCandle.current = true;
  }, [design, isCandle, user]);

  const previewCandleTemplate = async () => {
    if (loadingTemplate) return;
    setLoadingTemplate(true);

    const resellerId = UserStore.user?.role === 'reseller' ? UserStore?.user?.id : UserStore?.user?.resellerId;
    const res = await Store.Api.PackingSlip.getPackingSlip({ resellerId });
    const { scentName, defaultScentName, companyName, address } = design?.data?.candle || {};
    try {
      const res2 = await Store.Api.Pdf.generateCandleTemplate({
        address,
        scentName: scentName || defaultScentName || scentVariantOpts?.[0]?.value,
        templateUrl: product?.data?.candleTemplate,
        companyName,
        companyLogo: res?.data?.data?.companyLogo,
      });
      if (res2.data.data) {
        window.open(res2.data.data, "_blank");
      }
    } catch (error) {
      notification.error({ message: error.message })
    } finally {
      setLoadingTemplate(false);
    }
  }

  const onSubmit = async (design : TDesign | undefined) => {
    let hasError = false;
    // validate variants, reject if any variant has no style or price
    if ((design?.variants || []).some(v => !v.style || !v.price)) {
      showPopupMessage({
        title: '',
        content: 'Please fill in all variant style and price.',
        buttonOkText: 'OK',
       
        typeHighlight: 'danger',
        contentHighlight: 'Error'
  
      });
      // alert('Please fill in all variant style and price');
      hasError = true;
      return hasError;
    }
    setSavingUI({ loading: true });
    try {
      let updateShopify = false;
      if (design?.products?.length) {
        updateShopify = await shouldUpdateShopify();
      }
      let res;
      if (!designId || designId === "new") {
        res = await Store.Api.Design.upsert({
          name: design.name,
          isCustomizable: true,
          productId: product.id,
          width: product.physicalWidth,
          height: product.physicalHeight,
          printAreas: product.printAreas,
          customProduct: !!product.customProduct,
          printOnDemand: !!product.printOnDemand,
          wholeSale: !!product.wholeSale,
          image: product.image,
          description: design.description,
          resalePrice: +design.resalePrice,
          data: design.data || {},
          brands: design.brands,
          updateShopify,
        });
      } else {
        res = await Store.Api.Design.upsert({
          id: designId,
          name: design.name,
          resalePrice: +design.resalePrice,
          data: design.data,
          brands: design.brands,
          description: design.description,
          updateShopify,
          variants: design.variants.map(v => ({
            ...v,
            price: +v.price,
          }))
        });
      }

      updateEditting(screenName, false);
      if (res.data.success) {
        if (designId === "new") {
          navigation.reset({
            index: 0,
            routes: [{ name: SCREEN.ResellerCreateMyOwnProduct, params: { designId: res.data.data.id, productId: product.id } }],
          });
        }
      }
    } catch (err) {
      showPopupMessage({
        title: '',
        content: String(err),
        buttonOkText: 'OK',
   
        typeHighlight: 'danger',
        contentHighlight: 'Error'
  
      });
      // alert(err);
      hasError = true;
    }
    setSavingUI({ loading: false });
    return hasError;
  }

  const showPublishProductModal = async () => {
    if (isCandle && !isCancdleSceneInfoAdded.current) {
      modalConfirm({
        title: 'Warning',
        content: 'Please update Candle label information',
        onOk: () => { },
        onCancel: () => { },
      });
      return;
    }
    if (!design.resalePrice || design.resalePrice == 0) {
      modalConfirm({
        title: 'Warning',
        content: 'Please fill in Resale price',
        onOk: () => { },
        onCancel: () => { },
      });
      return;
    }
    const hasError = await onSubmit(design);
    if (hasError) return;
    modal.show(
      <PublishProductModal
        design={design}
        setDesign={setDesign}
        cost={product?.price}
        beforePublish={async () => {
          await Store.Api.Design.upsert({
            id: designId,
            name: design.name,
            resalePrice: +design.resalePrice,
            data: design.data,
            brands: design.brands,
            description: design.description,
          });
          setJustPublished(true);
        }}
        navigation={navigation}
      />
    );
  }

  const showEditDesignModal = async () => {
    const hasError = await onSubmit(design);
    if (hasError) return;
    navigation.navigate(SCREEN.ResellerEditDesign, {
      productId: design.productId,
      designId: design.id,
    });
    // modal.show(
    //   <EditDesignModal design={design} navigation={navigation} setDesign={updateDesign} />
    // );
  }

  const renderReady = () => {
    if (breakpoint === 'xs') return (
      <Col flex1 middle>
        <Text>Please use bigger screen to see this page.</Text>
      </Col>
    )

    return (
      <Row flex1 stretch>
        {curTab === TAB.LISTING_INFO && renderTabListing()}
      </Row>
    );
  };

  const preview3dAvailable = (product?.previewData || []).filter(v => v.previewType === '3d-model').length > 0;

  const renderCommonListingInfo = () => {
    const find3dGroup = (product?.previewData || []).find(v => v.previewType === '3d-model');
    const data = (find3dGroup?.previewItems || [])[0]?.data;
    const demoImage = 'https://print-manager-media.s3.eu-west-1.amazonaws.com/personify-full-transparent.png';
    const shouldShowVariants = (design?.variants || []).length > 0;
    return (
      <>
        <MiddleColumn flex={2} backgroundColor={'#E6E6E6'}>
          {shouldShowVariants ? (
            <Variants
              design={design}
              setDesign={setDesign}
              onRequestSampleOrder={variant => {
                if (isCandle && !isCancdleSceneInfoAdded.current) {
                  modalConfirm({
                    title: 'Warning',
                    content: 'Please update Candle label information',
                    onOk: () => { },
                    onCancel: () => { },
                  });
                  return;
                }
                setSampleDesignData({
                  designId: variant.variantDesignId,
                  image: variant.image,
                  galleries: variant.galleries,
                  style: variant.style,
                });
                sampleModelRef.current.show();
              }}
              product={product}
            />
          ) : show360ProductEditor ? (
            <Col flex1>
              <iframe
                src={window.location.href.includes('iframe_dev=1') ? `http://localhost:3009${data}&image=${demoImage}` : `${ValHelper.editorHost}.app${data}&image=${demoImage}`}
                style={{
                  width: '100%',
                  height: '100%',
                  border: 'none',
                  backgroundColor: COLOR.MAIN,
                }}
                allowFullScreen
              />
            </Col>
          ) : (
            <Col flex1 alignItems={'center'}>
              <Row mt2 middle flexWrap={'wrap'} paddingHorizontal={10}>
                <RequireSaved design={design}>
                  <Button
                    height={30}
                    borderRadius={15}
                    width={180}
                    text='Edit design'
                    onPress={showEditDesignModal}
                    iconLeft={(
                      <AntDesign name="edit" size={16} color="white" />
                    )}
                    disabled={!design?.id}
                    opacity={design?.id ? 1 : 0.5}
                    m0
                  />
                </RequireSaved>
                {(design?.variants || []).length === 0 && (design?.galleries || []).length > 0 && (
                  <>
                    <Button
                      m0
                      outline
                      bgHovered={COLOR.GREY_BG}
                      text='Add variant'
                      height={30}
                      borderRadius={15}
                      width={180}
                      iconLeft={(
                        <AntDesign name="pluscircle" size={16} color={COLOR.MAIN} />
                      )}
                      onPress={() => {
                        setDesign({
                          ...design,
                          variants: [
                            {
                              style: "Default",
                              image: design.image,
                              galleries: design.galleries,
                              variantDesignId: design.id,
                              price: design.resalePrice || 0,
                            }
                          ]
                        })
                      }}
                    />
                    <Button
                      m0
                      outline
                      bgHovered={COLOR.GREY_BG}
                      text='Order a sample'
                      height={30}
                      borderRadius={15}
                      width={180}
                      iconLeft={
                        <Entypo name="shopping-cart" size={18} color={COLOR.MAIN} />
                      }
                      onPress={() => {
                        if (isCandle && !isCancdleSceneInfoAdded.current) {
                          modalConfirm({
                            title: 'Warning',
                            content: 'Please update Candle label information',
                            onOk: () => { },
                            onCancel: () => { },
                          });
                          return;
                        }
                        setSampleDesignData({
                          designId: design.id,
                          image: design.image,
                          galleries: design.galleries,
                          style: 'Default Style',
                        });
                        sampleModelRef.current.show();
                      }}
                    />
                  </>

                )}
                <Button
                  m0
                  outline
                  bgHovered={COLOR.GREY_BG}
                  text='Download artboard'
                  height={30}
                  borderRadius={15}
                  width={180}
                  iconLeft={
                    <Entypo name="download" size={18} color={COLOR.MAIN} />
                  }
                  onPress={() => {
                    window.open(product?.artboardUrl || "https://www.bottledgoose.co.uk/pages/artboards", "_blank");
                  }}
                />
                <Button
                  m0
                  outline
                  bgHovered={COLOR.GREY_BG}
                  text='Artwork guidelines'
                  height={30}
                  borderRadius={15}
                  width={180}
                  iconLeft={
                    <Entypo name="download" size={18} color={COLOR.MAIN} />
                  }
                  onPress={() => {
                    window.open("https://print-manager-media.s3.eu-west-1.amazonaws.com/bg/Artwork-Guide-Customiser.pdf", "_blank");
                  }}
                />
              </Row>
              <Row
                mt2 minWidth='90%' minHeight={70}
                borderColor={COLOR.GREY} borderStyle='dashed'
                borderWidth={!design?.galleries?.length ? 1 : 0}
                flexWrap='wrap' middle
              >
                <ImageAnt.PreviewGroup>
                  {design?.galleries && design?.galleries.map((val, i) => (
                    <Col width={80} height={80} m0 key={'gallery-' + i}>
                      <ImageAnt
                        src={val}
                        style={{ width: '100%', height: '100%' }}
                      />
                    </Col>
                  ))}
                </ImageAnt.PreviewGroup>
              </Row>
              <RatioCol
                width='80%'
                ratio={1}
                mt2
                borderColor={COLOR.GREY}
                borderStyle='dashed'
                borderWidth={!product?.image ? 1 : 0}
              >
                <Image source={{ uri: design?.image || product?.image }} style={{ width: '100%', height: '100%' }} resizeMode='contain' />
              </RatioCol>

              

            </Col>
          )}
        </MiddleColumn>
      </>
    )
  }

  const renderTabListing = () => {
    const shouldIncludeVAT = UserStore.user?.country === "United Kingdom";
    return (
      <>
        <LeftColumn flex={2} p2 overflow={'scroll'}>
          <Text subtitle1 mb1>Product name</Text>
          <Input02
            height={35}
            value={design?.name}
            onChange={(v) => {
              updateDesign({ name: v })
            }}
            mb1
          />

          <Text subtitle1 mb1>Description</Text>
          <Input02
            value={design?.description || ''}
            onChange={(v) => updateDesign({ description: v })}
            multiline
            mb1
            inputProps={{ multiline: true }}
            height={150}
          />

          <Text subtitle1 mb1>Packaging</Text>
          <Text mb2>{product?.packagingDescription}</Text>

          {/* <Text subtitle1 mb1>Our price: <Text fontSize={24}>£{shouldIncludeVAT ? (Number(product?.price) * 1.2).toFixed(2) : product?.price}</Text> <Text caption ml1>{shouldIncludeVAT ? "VAT included" : ""}</Text></Text> */}
          <Text subtitle1 mb1>Our price: <Text fontSize={24}>£{product?.price}</Text> <Text caption ml1>exclude VAT</Text></Text>
          <Text mb2>This is how much you will be charged each time you sell this product*</Text>

          <Text subtitle1 mb1>Stores</Text>
          <Col>
            <Select01
              value={listBrandOption.filter(v => !!(design?.brands || []).find(p => p.storeId === v.value))}
              options={listBrandOption}
              onChange={newValues => {
                updateDesign({
                  brands: newValues.map(v => ({ storeId: v.value, name: v.label }))
                })
              }}
              placeholder=''
              isMulti
              mb2
            />
          </Col>


          <Text subtitle1 mb1>Resale price</Text>
          <Input02
            height={35}
            value={`£ ${design?.resalePrice}`}
            onChange={(v) => updateDesign({ resalePrice: v.replace("£", "").replace(/ /g, "").trim() })}
            mb1
          />
          {!!design?.resalePrice && !isNaN(design?.resalePrice) && !!product?.price && (
            <Text mb1>Profit: £{design?.resalePrice} (sell price) - £{product?.price} = £{(+design?.resalePrice - product?.price).toFixed(2)}</Text>
          )}
          {isCandle && (
            <>
              <Col mt1>
                <Text h4 mb1>Candle label information</Text>
                <Col mb2 round0 borderThin borderColor='rgba(0,0,0,0.5)' pv1 ph2 backgroundColor={'rgb(255,205,42)'}>
                  <Text>This is legally required sticker on the bottom of the candle. We allow for customization of this label!</Text>
                </Col>
                <Text subtitle1 mb1>Scent name</Text>
                <Text caption mb0>Our recommend names for this scent - Let us name it for you!</Text>
                <Select01
                  height={35}
                  value={design?.data?.candle?.defaultScentName ? {
                    value: design?.data?.candle?.defaultScentName,
                    label: design?.data?.candle?.defaultScentName,
                  } : undefined}
                  options={scentVariantOpts}
                  onChange={newValue => {
                    setDesignData({
                      candle: {
                        ...design?.data?.candle,
                        defaultScentName: newValue?.value,
                      }
                    })
                  }}
                  placeholder={"Select a name"}
                  mb1
                />
                <Text caption mb0>Or</Text>
                <Input02
                  height={35}
                  placeholder='Type your bespoke candle name here'
                  value={design?.data?.candle?.scentName}
                  onChange={(v) => {
                    setDesignData({
                      candle: {
                        ...design?.data?.candle,
                        scentName: v,
                      }
                    })
                  }}
                  mb1
                />

                <Text subtitle1 mb1>Brand/Company name</Text>
                <Input02
                  height={35}
                  value={design?.data?.candle?.companyName}
                  onChange={(v) => {
                    setDesignData({
                      candle: {
                        ...design?.data?.candle,
                        companyName: v,
                      }
                    })
                  }}
                  mb1
                />

                <Text subtitle1 mb1>Business address</Text>
                <Input02
                  height={80}
                  multiline
                  value={design?.data?.candle?.address}
                  inputProps={{ multiline: true }}
                  onChange={(v) => {
                    console.log(v);
                    setDesignData({
                      candle: {
                        ...design?.data?.candle,
                        address: v,
                      }
                    })
                  }}
                  mb1
                />
                {isCandle && !!product?.data?.candleTemplate &&
                  <Button
                    outline
                    isLoading={loadingTemplate}
                    bgHovered={COLOR.GREY_BG}
                    text='Candle Label preview'
                    height={30}
                    borderRadius={15}
                    width={180}
                    iconLeft={
                      <Entypo name="download" size={18} color={COLOR.MAIN} />
                    }
                    onPress={previewCandleTemplate}
                  />
                }
              </Col>
            </>
          )}
        </LeftColumn>
        {renderCommonListingInfo()}
      </>
    )
  }

  return (
    <CMSLayout requireAuthen
      breadcrumbs={[
        { title: `My Products`, screen: SCREEN.ListDessignsReseller },
        { title: design?.name || `Product details` },
      ]}
    >
      <Row m2 mb1 justifyContent={'space-between'}>
        {/* <Text h3>{design?.name}</Text> */}
        <Row />
        <Row>
          <RequireSaved design={design}>
            <Button
              outline
              mr1
              height={30}
              borderRadius={15}
              text={design?.products?.length ? 'Store products' : 'Publish'}
              onPress={showPublishProductModal}
              disabled={!design?.id}
              opacity={design?.id ? 1 : 0.5}
            />
          </RequireSaved>
          <Button
            isLoading={saving}
            text='Save'
            height={30}
            borderRadius={15}
            onPress={() => {
              onSubmit(design);
            }}
          />
        </Row>
      </Row>
      <Col flex1 m2 mv1 round1 bgWhite borderWidth={1} borderColor={'white'}>
        {uiState.errorMes ? (
          <Col flex1 middle>
            <Text color="red" subtitle1>{uiState.errorMes}</Text>
          </Col>
        ) : (
          uiState.fetching ? (
            <Row height={50} stretch>
              <ShimmerLoading round1 flex={1} m1 />
              <ShimmerLoading round1 flex={1} m1 />
              <ShimmerLoading round1 flex={1} m1 />
              <ShimmerLoading round1 flex={1} m1 />
            </Row>
          ) : (
            renderReady()
          )
        )}
        <OrderSampleModal
          ref={sampleModelRef}
          productId={design?.productId}
          productName={product?.name}
          sampleDesignData={sampleDesignData}
        />
      </Col>
    </CMSLayout>
  );
};

ResellerCreateMyOwnProduct.routeInfo = {
  title: 'Create my own - Bottled Goose',
  path: '/product-library/:productId/create-my-own/:designId',
};

export default ResellerCreateMyOwnProduct;
