import React, { useEffect, useMemo, useState } from 'react';
import { IScreen, ManualCreateInvoiceLineItemType, ManualCreateInvoiceType } from 'type';
import { Button, CMSLayout, Col, Grid, Input, Row, Select, Text, showPopupMessage, useUIState } from 'components';
import { useNavFunc } from 'navigation';
import _ from 'lodash';
import { COLOR } from 'const';
import DatePicker from 'react-datepicker';
import axios from 'axios';
import { Modal, Radio, Result } from 'antd';
import { OrderSearchSelect } from './components/OrderSearchSelect';
import apiClient from 'store/api-client';
const moment = require('moment');
var { Liquid } = require('liquidjs');
var engine = new Liquid();

const LIQUID_TEMPLATE = `
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <style type="text/css">
    * {
      margin: 0;
      padding: 0;
      text-indent: 0;
    }
    body {
      padding: 12px;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      width: {{ viewWidth }};
      height: {{ viewHeight }};
    }
    .logo {
      margin-left: 8pt;
    }
    .header-container {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }
    .normal-text {
      text-indent: 0pt;
      line-height: 11pt;
      text-align: left;
    }
    .row {
      display: flex;
      flex-direction: row;
    }
    .s1 {
      color: black;
      font-family: Calibri, sans-serif;
      font-style: normal;
      font-weight: normal;
      text-decoration: none;
      font-size: 24pt;
    }
    .main-content {
      display: flex;
      flex: 1;
      flex-direction: column;
      justify-content: center;
    }
    .total-container {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
    }
    .devider {
      height: 1px;
      background-color: black;
      width: 250pt;
      margin-top: 8pt;
      margin-bottom: 8pt;
    }
    .p,
    p {
      color: black;
      font-family: Calibri, sans-serif;
      font-style: normal;
      font-weight: normal;
      text-decoration: none;
      font-size: 9pt;
      margin: 0pt;
    }
    h2 {
      color: black;
      font-family: Calibri, sans-serif;
      font-style: normal;
      font-weight: bold;
      text-decoration: none;
      font-size: 9pt;
    }
    .s2 {
      color: black;
      font-family: Calibri, sans-serif;
      font-style: normal;
      font-weight: bold;
      text-decoration: none;
      font-size: 9pt;
    }
    .s3 {
      color: black;
      font-family: Calibri, sans-serif;
      font-style: normal;
      font-weight: normal;
      text-decoration: none;
      font-size: 9pt;
    }
    .s4 {
      color: black;
      font-family: "Times New Roman", serif;
      font-style: normal;
      font-weight: normal;
      text-decoration: none;
      font-size: 9pt;
    }
    h1 {
      color: black;
      font-family: Calibri, sans-serif;
      font-style: normal;
      font-weight: bold;
      text-decoration: none;
      font-size: 10.5pt;
    }
    .s5 {
      color: #333;
      font-family: Calibri, sans-serif;
      font-style: normal;
      font-weight: normal;
      text-decoration: none;
      font-size: 8pt;
    }
    table,
    tbody {
      vertical-align: top;
      overflow: visible;
    }
    body {
      box-sizing: border-box;
    }
    @media screen {
      body {
        padding: 40px;
      }
    }
  </style>
</head>
<body>
  <div class="row">
    {% if logo1 != blank %}
      <img class="logo" width="auto" height="50" src="{{ logo1 }}" />
    {% endif %}
    {% if logo2 != blank %}
      <img class="logo" width="auto" height="50" src="{{ logo2 }}" />
    {% endif %}
    {% if logo3 != blank %}
      <img class="logo" width="auto" height="50" src="{{ logo3 }}" />
    {% endif %}
  </div>
  <p><br /></p>
  <div class="header-container">
    <div>
      <img src="https://print-manager-media.s3.eu-west-1.amazonaws.com/bg/invoice_logo_cropped.png" style="height: 120px; width: auto; " />
      <p class="s1" style="padding-top: 1pt;padding-left: 5pt;text-indent: 0pt;text-align: left;">TAX INVOICE</p>
      <p style="padding-top: 4pt;padding-left: 33pt;text-indent: 0pt;text-align: left;">{{ customerName }}</p>
      <p style="padding-top: 2pt;padding-left: 33pt;text-indent: 0pt;line-height: 118%;text-align: left;">{{ billingAddress }}</p>
    </div>
    <div class="row">
      <div style="margin-right: 12px; min-width: 150px">
        <h2 style="padding-top: 3pt;" class="normal-text">Invoice Date</h2>
        <p class="normal-text">{{ invoiceDate }}</p>
        <h2 style="padding-top: 6pt;" class="normal-text">Invoice Number</h2>
        <p class="normal-text">{{ invoiceNumber }}</p>
        <h2 style="padding-top: 6pt;" class="normal-text">Reference
        </h2>
        <p class="normal-text">Order {{ orderNumber }}</p>
        <h2 style="padding-top: 6pt;" class="normal-text">VAT Number
        </h2>
        <p class="normal-text">{{ vatNumber }}</p>
      </div>
      <div style="margin-right: 12px; min-width: 150px">
        <p style="padding-top: 4pt;padding-left: 5pt;text-indent: 0pt;line-height: 118%;text-align: left;">{{ bgAddress }}</p>
      </div>
    </div>
  </div>

  <p><br /></p>
  <div class="main-content">
    <table style="border-collapse:collapse" cellspacing="0">
      <tr style="height:16pt">
        <td style="border-bottom-style:solid;border-bottom-width:1pt">
          <p class="s2" style="padding-left: 2pt;text-indent: 0pt;line-height: 9pt;text-align: left;">Description</p>
        </td>
        <td style="width:152pt;border-bottom-style:solid;border-bottom-width:1pt">
          <p class="s2" style="padding-right: 22pt;text-indent: 0pt;line-height: 9pt;text-align: right;">Quantity</p>
        </td>
        <td style="width:58pt;border-bottom-style:solid;border-bottom-width:1pt">
          <p class="s2" style="padding-right: 16pt;text-indent: 0pt;line-height: 9pt;text-align: right;">VAT</p>
        </td>
        <td style="width:68pt;border-bottom-style:solid;border-bottom-width:1pt">
          <p class="s2" style="padding-right: 2pt;text-indent: 0pt;line-height: 9pt;text-align: right;">Amount GBP</p>
        </td>
      </tr>
      {% for lineItem in lineItems %}
        <tr style="height:33pt">
          <td
            style="border-top-style:solid;border-top-width:1pt;border-bottom-style:solid;border-bottom-width:1pt;border-bottom-color:#CCCCCC">
            <p class="s3" style="padding-top: 4pt;padding-left: 2pt;padding-right: 38pt;text-indent: 0pt;text-align: left;">
              {{ lineItem.description }}
            </p>
          </td>
          <td
            style="width:152pt;border-top-style:solid;border-top-width:1pt;border-bottom-style:solid;border-bottom-width:1pt;border-bottom-color:#CCCCCC">
            <p class="s3" style="padding-top: 4pt;padding-right: 22pt;text-indent: 0pt;text-align: right;">
            {{ lineItem.quantity }}
            </p>
          </td>
          <td
            style="width:58pt;border-top-style:solid;border-top-width:1pt;border-bottom-style:solid;border-bottom-width:1pt;border-bottom-color:#CCCCCC">
            <p class="s3" style="padding-top: 4pt;padding-right: 13pt;text-indent: 0pt;text-align: right;">
            {{ lineItem.vat }}
            </p>
          </td>
          <td
            style="width:68pt;border-top-style:solid;border-top-width:1pt;border-bottom-style:solid;border-bottom-width:1pt;border-bottom-color:#CCCCCC">
            <p class="s3" style="padding-top: 4pt;padding-right: 2pt;text-indent: 0pt;text-align: right;">
            {{ lineItem.amount }}
            </p>
          </td>
        </tr>
      {% endfor %}
      <tr style="height:16pt">
        <td style="border-top-style:solid;border-top-width:1pt;border-top-color:#CCCCCC">
          <p style="text-indent: 0pt;text-align: left;"><br /></p>
        </td>
        <td style="width:152pt;border-top-style:solid;border-top-width:1pt;border-top-color:#CCCCCC">
          <p style="text-indent: 0pt;text-align: left;"><br /></p>
        </td>
      </tr>
    </table>
    <p style="text-indent: 0pt;text-align: left;"><br /></p>

    <div class="total-container" style="padding-right: 2pt;">
      <div class="row">
        <p class="normal-text">TOTAL VAT 20%</p>
        <p style="width: 80pt; text-align: right">{{ vatTotal }}</p>
      </div>
      <div class="devider"></div>
      <div class="row">
        <p class="normal-text" style="font-weight: bold;">TOTAL GBP</p>
        <p style="width: 80pt; text-align: right; font-weight: bold;">{{ total }}</p>
      </div>
    </div>

    <h1 style="padding-top: 33pt;padding-left: 5pt;text-indent: 0pt;text-align: left;">Due Date: {{ dueDate }}</h1>
    <p style="padding-left: 5pt;text-indent: 0pt;text-align: left;">
      Invoices can be paid to Proper Goose Ltd via BACS. Account number- ******** Sort code- 30-65-41
    </p>
  </div>
  <img width="100%" height="13"
    src="data:image/png;base64,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" />
  <div class="header-container" style="margin-top: 5pt;">
    <div>
      <p class="s1" style="padding-left: 8pt;text-indent: 0pt;text-align: left;">PAYMENT ADVICE</p>
      <p style="padding-top: 24pt;padding-left: 53pt;text-indent: -17pt;line-height: 118%;text-align: left;">To: {{ billingAddress }}</p>
      <p style="text-indent: 0pt;text-align: left;"><br /></p>
    </div>
    <div>
      <div class="row">
        <p class="normal-text" style="font-weight: bold; width: 90pt">Customer</p>
        <p>{{ customerName }}</p>
      </div>
      <div class="row">
        <p class="normal-text" style="font-weight: bold; width: 90pt">Invoice Number</p>
        <p>{{ invoiceNumber }}</p>
      </div>
      <div class="devider" style="background-color: gray;"></div>
      <div class="row">
        <p class="normal-text" style="font-weight: bold; width: 90pt">Amount Due</p>
        <p style="font-weight: bold;">{{ amountDue }}</p>
      </div>
      <div class="row">
        <p class="normal-text" style="font-weight: bold; width: 90pt">Due Date </p>
        <p>{{ dueDate }}</p>
      </div>
      <div class="devider" style="background-color: gray;"></div>
      <h2 class="normal-text" style="font-weight: bold;">Amount Enclosed</h2>
      <div class="devider"></div>
      <p class="s5" style="padding-top: 3pt;padding-left: 104pt;text-indent: 0pt;text-align: left;">Enter the amount you are paying above</p>
    </div>
  </div>
  <p style="text-indent: 0pt;text-align: left; padding-top: 10pt; font-size: 7pt;">Company Registration No: 09119257. Registered Office: Attention: Nicola Parr, 85 Island Farm Road, West Molesey, Surrey, KT8 2LN, United Kingdom</p>
</body>
</html>
`

const renderAddress = (data: string[]) => {
  if (!data) return '';
  return data.filter(Boolean).join('<br />');
}

const handleLineItems = (lineItems: ManualCreateInvoiceLineItemType[]) => {
  const arr = [];
  for (let i = 0; i < lineItems.length; i++) {
    const line = lineItems[i];
    arr.push({
      description: line.description,
      amount: Number(line.amount),
      quantity: Number(line.quantity),
      vat: line.vat === "20%" ? "20%" : "",
    });
  }
  return arr;
};

const ManualCreateInvoice: IScreen = () => {
  const { route } = useNavFunc();
  // @ts-ignore
  const { id } = route.params || {};

  const [{ loading: submitting }, setSubmitUI] = useUIState();
  const [dataForm, setDataForm] = useState<ManualCreateInvoiceType>({});
  const [lineItems, setLineItems] = useState<ManualCreateInvoiceLineItemType[]>([{}]);
  const [isPreview, setIsPreview] = useState(false);
  const [dataPreview, setDataPreview] = useState('');
  const [invoicePDFUrl, setInvoicePDFUrl] = useState(null);
  const [isFetchingInvoice, setIsFetchingInvoice] = useState(false);
  const [isResetSearch, setIsResetSearch] = useState(false);

  const onChangeField = (label) => (newValue) => {
    setDataForm((prev) => ({ ...prev, [label]: newValue }));
  };

  const onChangeLineItem = (index) => (label) => (newValue) => {
    setLineItems((prev) => {
      const newLineItems = [...prev];
      newLineItems[index] = { ...newLineItems[index], [label]: newValue };
      return newLineItems;
    });
    onChangeField("lineItems")(lineItems);
  };

  const onCreateInvoice = async () => {
    setSubmitUI({ loading: true });
    if (!lineItems || lineItems.length === 0) {
      showPopupMessage({
        title: "",
        content: "Please add line items first",
        buttonOkText: "OK",
        // 
        typeHighlight: "danger",
        contentHighlight: "Error",
      });
      setSubmitUI({ loading: false });
      return;
    } else if (lineItems.some(v => !v.description || !v.amount || !v.quantity)) {
      showPopupMessage({
        title: "",
        content: "Please fill in all required fields in line items",
        buttonOkText: "OK",
        // 
        typeHighlight: "danger",
        contentHighlight: "Error",
      });
      setSubmitUI({ loading: false });
      return;
    }
    if (!dataForm?.orderNumber) {
      showPopupMessage({
        title: "",
        content: "Please fill in all required fields",
        buttonOkText: "OK",
        // 
        typeHighlight: "danger",
        contentHighlight: "Error",
      });
      setSubmitUI({ loading: false });
      return;
    }
    const html = await engine.parseAndRender(LIQUID_TEMPLATE, handleDisplayData());

    try {
      const res = await axios.request({
        url: "https://puppeteer.personify.tech/api/html2pdf",
        headers: {'Content-Type': 'application/json'},
        method: "POST",
        data: {
          html: html,
          viewport: {width: 595, height: 842},
          returnUrl: true,
        }
      });
      if (res.data) {
        setInvoicePDFUrl(res.data.data);
      }
      
    } catch (err) {
      showPopupMessage({
        title: "",
        content: String(err),
        buttonOkText: "OK",
        // 
        typeHighlight: "danger",
        contentHighlight: "Error",
      });
    }
    setSubmitUI({ loading: false });
  };

  const handleDisplayData = () => {
    const displayData = {
      viewWidth: "100%",
      viewHeight: "100%",
      customerName: [dataForm?.first_name, dataForm?.last_name].filter(Boolean).join(" "),
      invoiceNumber: dataForm?.invoiceNumber,
      orderNumber: dataForm?.orderNumber,
      vatNumber: "*********",
      bgAddress: renderAddress([
        dataForm?.companyName,
        dataForm?.bgAddress1,
        dataForm?.bgAddress2,
        dataForm?.bgZipCode,
        dataForm?.bgPhoneNumber,
      ]),
      billingAddress: renderAddress([
        dataForm?.billingAddress1,
        dataForm?.billingAddress2,
        dataForm?.billingAddressCity,
        dataForm?.billingAddressCountry,
      ]),
      amountDue: dataForm?.status === "paid" ? "PAID" : dataForm.total,
      total: dataForm.total,
      vatTotal: Number(dataForm?.vatTotal),
      subTotal: "",
      invoiceDate: dataForm.invoiceDate ? moment(dataForm.invoiceDate).format("DD-MM-YYYY HH:mm") : "",
      dueDate: dataForm.dueDate ? moment(dataForm.dueDate).format("DD-MM-YYYY HH:mm") : "",
      lineItems: handleLineItems(lineItems),
    }
    return displayData;
  }

  const onPreview = async () => {
    if (!lineItems || lineItems.length === 0) {
      showPopupMessage({
        title: "",
        content: "Please add line items first",
        buttonOkText: "OK",
        // 
        typeHighlight: "danger",
        contentHighlight: "Error",
      });
      return;
    } else if (lineItems.some(v => !v.description || !v.amount || !v.quantity)) {
      showPopupMessage({
        title: "",
        content: "Please fill in all required fields in line items",
        buttonOkText: "OK",
        // 
        typeHighlight: "danger",
        contentHighlight: "Error",
      });
      return;
    }
    if (!dataForm?.orderNumber) {
      showPopupMessage({
        title: "",
        content: "Please fill in all required fields",
        buttonOkText: "OK",
        // 
        typeHighlight: "danger",
        contentHighlight: "Error",
      });
      return;
    }
    
    setIsPreview(!isPreview);
    if (isPreview) return;
  
    const html = await engine.parseAndRender(LIQUID_TEMPLATE, handleDisplayData());

    setIsPreview(true);
    setDataPreview(html);
  };

  const createMarkup = () => {
    return {__html: dataPreview};
  };

  const handleSelectOrder = async (order) => {

    const orderId = order?.['Order ID'];
    if (isPreview) setIsPreview(false);
    if (!orderId) return;
    setIsFetchingInvoice(true);
    let invoice = await apiClient.Api.Payment.getOrderInvoice({ orderId });
    if (invoice.data.success === false || !invoice.data?.data?.stripeInvoice) {
      const lines = (() => {
        const list = order['Raw Data'].line_items;
        const arr = [];
        for (let i = 0; i < list.length; i++) {
          const line = list[i];
          arr.push({
            description: line.title,
            amount: (Number(line.price) * line.quantity).toString(),
            quantity: line.quantity,
            vat: '20%',
          });
        }
        const shippingFee = order['Raw Data'].shipping_lines[0];
        if (shippingFee) {
          arr.push({
            description: "Shipping fee",
            amount: shippingFee.price,
            quantity: 1,
            vat: '20%',
          });
        }
        return arr;
      });

      const lastPipeline = order.Pipelines.length === 0 ? undefined : order.Pipelines[order.Pipelines.length - 1];
      const { isSampleRequest } = lastPipeline?.SharedData || {};
      const isSample = order['Raw Data']?.is_sample_request || isSampleRequest || order.OrderType === "Sample";

      onChangeField("first_name")(isSample ? order['Raw Data'].shipping_address.first_name : order['Raw Data'].customer.first_name);
      onChangeField("last_name")(isSample ? order['Raw Data'].shipping_address.last_name : order['Raw Data'].customer.last_name);
      onChangeField("orderNumber")(order['Order Number']);
      onChangeField("billingAddress1")(order['Raw Data'].shipping_address.address1);
      onChangeField("billingAddress2")(order['Raw Data'].shipping_address.address2);
      onChangeField("billingAddressCity")(order['Raw Data'].shipping_address.city);
      onChangeField("billingAddressCountry")(order['Raw Data'].shipping_address.country);
      onChangeField("status")(order["Raw Data"].financial_status === "paid" ? "paid" : "unpaid");
      onChangeField("invoiceDate")(new Date());
      onChangeField("dueDate")(new Date());
      setLineItems(lines);
      onChangeField("lineItems")(lines);
    } else {

      const invoiceData = invoice.data.data;
      const stripeInvoice = invoiceData.stripeInvoice;
      
      const lines = (() => {
        const list = stripeInvoice.lines.data;
  
        const arr = [];
        let lastIndexWithVAT = -1;
        let lastVATOrderNumber;
        // console.log('original list: ' + list.length, list);
        for (let i = 0; i < list.length; i++) {
          const line = list[i];
          const orderNumber = line.description.startsWith('#') ? line.description.split(' ')[0].replace('#', '') : undefined;
          if (line.description?.includes('VAT')) {
            // console.log('VAT line lastIndexWithVAT', lastIndexWithVAT);
            // detect order number in the description, format: #1234 VAT (20%)
            lastVATOrderNumber = orderNumber;
            lastIndexWithVAT = i;
            continue;
          }
          arr.push({
            description: line.description,
            amount: line.amount / 100,
            quantity: line.quantity,
            vat: (() => {
              if (lastIndexWithVAT !== -1 && lastIndexWithVAT === i - 1) return '20%';
              if (orderNumber === lastVATOrderNumber) return '20%';
              const previousItem = arr[arr.length -  1];
              if (!previousItem) return '';
              // console.log('previousItem', previousItem);
              // @ts-ignore
              if (previousItem.description.includes('Shipping fee') && previousItem.vat === '20%') return '20%';
              return '';
            })(),
          });
        }
        return arr.reverse();
      })();
      
      onChangeField("first_name")(invoiceData.customerInfo?.first_name);
      onChangeField("last_name")(invoiceData.customerInfo?.last_name);
      onChangeField("invoiceNumber")(invoiceData.id);
      onChangeField("orderNumber")(invoiceData.orderNumber);
      onChangeField("billingAddress1")(invoiceData.shippingAddress?.address1);
      onChangeField("billingAddress2")(invoiceData.shippingAddress?.address2);
      onChangeField("billingAddressCity")(invoiceData.shippingAddress?.city);
      onChangeField("billingAddressCountry")(invoiceData.shippingAddress?.country);
      onChangeField("status")(stripeInvoice?.status === "paid" ? "paid" : "unpaid");
      onChangeField("invoiceDate")(new Date(invoiceData?.createdAt));
      onChangeField("dueDate")(new Date(invoiceData?.paidAt));
      setLineItems(lines);
      onChangeField("lineItems")(lines);
  
    }
    setIsFetchingInvoice(false);
  };

  useEffect(() => {
    const totalVAT = (lineItems.filter(v => v.vat === "20%").reduce((acc, v) => acc + Number(v.amount), 0)) * 0.2;
    const total = lineItems.reduce((acc, v) => {
      const amountWithVAT = v.vat === "20%" ? Number(v.amount) * 1.2 : Number(v.amount);
      return acc + Number(amountWithVAT);
    }, 0);
    onChangeField("vatTotal")(totalVAT.toString() ? totalVAT.toFixed(2) : "");
    onChangeField("total")(total.toString() ? total.toFixed(2) : "");
  }, [dataForm.lineItems])

  useEffect(() => {
    setDataForm({
      companyName: "Proper Goose Ltd",
      bgAddress1: "85 Island Farm Road",
      bgAddress2: "West Molesey",
      bgZipCode: "KT8 2LN",
      bgPhoneNumber: "020 8941 3481",
      vatNumber: "*********",
      dueDate: new Date(),
      invoiceDate: new Date(),
    });
  }, []);

  return (
    <CMSLayout requireAuthen>
      <OrderSearchSelect placeholder="Order Id or Order Number" style={{ width: "400px", marginTop: 20, marginLeft: 20 }} onSelectOrder={handleSelectOrder} isReset={isResetSearch} />
      {
        invoicePDFUrl ? (
          <Result
            status="success"
            title="Successfully created invoice"
            subTitle="You can download the PDF file below"
            extra={// @ts-ignore
            <Row flex1 alignContent={"center"} justifyContent={"center"} style={{ gap: "10px" }}>
              <Button text='Download PDF' onPress={() => window.open(invoicePDFUrl, '_blank')}/>
              <Button text="Create new Invoice" outline 
              onPress={() => {
                setInvoicePDFUrl(null);
                setDataForm({
                  companyName: "Proper Goose Ltd",
                  bgAddress1: "85 Island Farm Road",
                  bgAddress2: "West Molesey",
                  bgZipCode: "KT8 2LN",
                  bgPhoneNumber: "020 8941 3481",
                  vatNumber: "*********",
                  dueDate: new Date(),
                  invoiceDate: new Date(),
                });
                setLineItems([{}]);
                setDataPreview("");
                setIsPreview(false);
                setIsResetSearch(true);
                }}></Button>
            </Row>
          }
          />
        ) : (
          <>
            <Row m2 marginBottom={0} justifyContent={"space-between"}>
              <Row>
                <Text h3>
                  {id === "new"
                    ? "Create invoice"
                    : 'Edit invoice'}
                </Text>
                {/* <UpsertResellerTabs
                  activeTab={tab}
                  ml1
                  onChangeTab={(newKey) => {
                    navigation.navigate(SCREEN.UpsertReseller, { id, tab: newKey });
                  }}
                /> */}
              </Row>
              <Row>
                
                <Button
                  // isLoading={submitting}
                  text={isPreview ? "Edit" : "Preview"}
                  mr1
                  width={100}
                  height={40}
                  borderRadius={20}
                  onPress={onPreview}
                />
                <Button
                  isLoading={submitting}
                  text={id === "new" ? "Create" : "Save"}
                  width={100}
                  height={40}
                  borderRadius={20}
                  onPress={onCreateInvoice}
                />
              </Row>
            </Row>
            
            {
              isPreview ? (
                <Col flex1 m2 mv1 p1 round1 bgWhite overflow={"scroll"}>
                  <div dangerouslySetInnerHTML={createMarkup()} />
                </Col>
              ) : (
                <Col flex1 m2 mv1 p1 round1 bgWhite overflow={"scroll"}>
                  <Grid xs="100%" md="50%" alignItems={"flex-start"} mb2>
                    <Col m1>
                      <Text subtitle1 mb2>
                        Invoice Information
                      </Text>
                      <Text mb0 caption>
                        First Name
                      </Text>
                      <Input
                        placeholder="First Name"
                        inputProps={{
                          placeholderTextColor: COLOR.GREY,
                        }}
                        value={dataForm.first_name}
                        mb1
                        onChange={onChangeField("first_name")}
                      />
                      <Text mb0 caption>
                        Last Name
                      </Text>
                      <Input
                        placeholder="Last Name"
                        value={dataForm.last_name || ""}
                        inputProps={{
                          placeholderTextColor: COLOR.GREY,
                        }}
                        mb1
                        onChange={onChangeField("last_name")}
                      />
                      <Text mb0 caption>
                        Invoice Number
                      </Text>
                      <Input
                        placeholder="Invoice Number"
                        value={dataForm.invoiceNumber || ""}
                        inputProps={{
                          placeholderTextColor: COLOR.GREY,
                        }}
                        mb1
                        onChange={onChangeField("invoiceNumber")}
                      />
                      <Text mb0 caption>
                        Order Number <span style={{color: 'red'}}>*</span>
                      </Text>
                      <Input
                        placeholder="Order Number"
                        value={dataForm.orderNumber || ""}
                        inputProps={{
                          placeholderTextColor: COLOR.GREY,
                        }}
                        mb1
                        onChange={onChangeField("orderNumber")}
                      />
                      <Text mb0 caption>
                        VAT Number
                      </Text>
                      <Input
                        placeholder="VAT Number"
                        value={dataForm.vatNumber || ""}
                        inputProps={{
                          placeholderTextColor: COLOR.GREY,
                          defaultValue: "*********"
                        }}
                        mb1
                        onChange={onChangeField("vatNumber")}
                      />
                      <Text mb0 caption>
                        Company Name
                      </Text>
                      <Input
                        placeholder="Proper Goose Ltd"
                        inputProps={{
                          placeholderTextColor: COLOR.GREY,
                          defaultValue: "Proper Goose Ltd"
                        }}
                        value={dataForm.companyName || ""}
                        mb1
                        onChange={onChangeField("companyName")}
                      />
                      <Text mb0 caption>
                        BG Address 1
                      </Text>
                      <Input
                        placeholder="BG Address 1"
                        inputProps={{
                          placeholderTextColor: COLOR.GREY,
                          defaultValue: "85 Island Farm Road"
                        }}
                        value={dataForm.bgAddress1 || ""}
                        mb1
                        onChange={onChangeField("bgAddress1")}
                      />
                      <Text mb0 caption>
                        BG Address 2
                      </Text>
                      <Input
                        placeholder="BG Address 2"
                        inputProps={{
                          placeholderTextColor: COLOR.GREY,
                          defaultValue: "West Molesey"
                        }}
                        value={dataForm.bgAddress2 || ""}
                        mb1
                        onChange={onChangeField("bgAddress2")}
                      />
                      <Text mb0 caption>
                        BG ZIP Code
                      </Text>
                      <Input
                        placeholder="BG ZIP Code"
                        inputProps={{
                          placeholderTextColor: COLOR.GREY,
                          defaultValue: "KT8 2LN"
                        }}
                        value={dataForm.bgZipCode || ""}
                        mb1
                        onChange={onChangeField("bgZipCode")}
                      />
                      <Text mb0 caption>
                        BG Phone Number
                      </Text>
                      <Input
                        placeholder="BG Phone Number"
                        inputProps={{
                          placeholderTextColor: COLOR.GREY,
                          defaultValue: "020 8941 3481"
                        }}
                        value={dataForm.bgPhoneNumber || ""}
                        mb1
                        onChange={onChangeField("bgPhoneNumber")}
                      />
                      <Text mb0 caption>
                        Billing Address 1
                      </Text>
                      <Input
                        placeholder="Billing Address 1"
                        inputProps={{
                          placeholderTextColor: COLOR.GREY,
                        }}
                        value={dataForm.billingAddress1 || ""}
                        mb1
                        onChange={onChangeField("billingAddress1")}
                      />
                      <Text mb0 caption>
                        Billing Address 2
                      </Text>
                      <Input
                        placeholder="Billing Address 2"
                        inputProps={{
                          placeholderTextColor: COLOR.GREY,
                        }}
                        value={dataForm.billingAddress2 || ""}
                        mb1
                        onChange={onChangeField("billingAddress2")}
                      />
                      <Text mb0 caption>
                        Billing Address City
                      </Text>
                      <Input
                        placeholder="Billing Address City"
                        inputProps={{
                          placeholderTextColor: COLOR.GREY,
                        }}
                        value={dataForm.billingAddressCity || ""}
                        mb1
                        onChange={onChangeField("billingAddressCity")}
                      />
                      <Text mb0 caption>
                        Billing Address Country
                      </Text>
                      <Input
                        placeholder="Billing Address Country"
                        inputProps={{
                          placeholderTextColor: COLOR.GREY,
                        }}
                        value={dataForm.billingAddressCountry || ""}
                        mb1
                        onChange={onChangeField("billingAddressCountry")}
                      />
                      <Text mb0 caption>
                        Invoice Status
                      </Text>
                      <Radio.Group onChange={(e) => {
                        onChangeField("status")(e.target.value);
                      }} value={dataForm.status} defaultValue={"unpaid"} style={{marginTop: 5, marginBottom: 10}}>
                        <Radio value={"unpaid"}>Unpaid</Radio>
                        <Radio value={"paid"}>Paid</Radio>
                      </Radio.Group>
                      <Text mb0 caption>
                        Total
                      </Text>
                      <Input
                        placeholder="Total"
                        inputProps={{
                          placeholderTextColor: COLOR.GREY,
                        }}
                        value={dataForm.total || ""}
                        mb1
                        onChange={onChangeField("total")}
                      />
                      <Text mb0 caption>
                        Total VAT
                      </Text>
                      <Input
                        placeholder="Total VAT"
                        inputProps={{
                          placeholderTextColor: COLOR.GREY,
                        }}
                        value={dataForm.vatTotal || ""}
                        mb1
                        onChange={onChangeField("vatTotal")}
                      />
                      <Text mb0 caption>
                        Invoice Date
                      </Text>
                      <DatePicker
                        selected={dataForm?.invoiceDate}
                        onChange={onChangeField("invoiceDate")}
                        showTimeSelect
                        customInput={<Input mb1/>}
                        dateFormat="dd/MM/yyyy HH:mm aa"
                      />
                      <Text mb0 caption>
                        Due Date
                      </Text>
                      <DatePicker
                        selected={dataForm?.dueDate}
                        mb1
                        customInput={<Input mb1/>}
                        onChange={onChangeField("dueDate")}
                        showTimeSelect
                        dateFormat="dd/MM/yyyy HH:mm aa"
                      />
                    </Col>
                    <Col m1>
                      <Text subtitle1 mb2>
                        Items
                      </Text>
                      {
                        lineItems.map((lineItem, index) => {
                          return (
                            <React.Fragment key={index}>
                              <Row flex1 justifyContent={"space-between"} borderTopColor={COLOR.GREY_LIGHT} pt1 borderTopWidth={1} >
                                <Text mb1 pt1 fontSize={18} color={COLOR.BLUE} >
                                  Item {index + 1}
                                </Text>
                                <Button
                                  text={"Delete"}
                                  outline
                                  borderColor={COLOR.RED}
                                  width={70}
                                  height={30}
                                  borderRadius={20}
                                  onPress={() => {
                                    setLineItems(lineItems.filter((_, i) => i !== index));
                                  }}
                                />
                              </Row>
                              <Text mb0 caption>
                                Description <span style={{color: 'red'}}>*</span>
                              </Text>
                              <Input
                                placeholder="Description"
                                inputProps={{
                                  placeholderTextColor: COLOR.GREY,
                                }}
                                value={lineItem.description || ""}
                                mb1
                                onChange={onChangeLineItem(index)("description")}
                              />
                              <Grid md="100%" xl="30%" alignItems={"flex-start"} justifyContent={"space-between"} gap={10} mb2>
                                <Col>
                                  <Text mb0 caption>
                                    Quantity <span style={{color: 'red'}}>*</span>
                                  </Text>
                                  <Input
                                    placeholder="Quantity"
                                    inputProps={{
                                      placeholderTextColor: COLOR.GREY,
                                    }}
                                    value={lineItem.quantity || ""}
                                    mb1
                                    onChange={onChangeLineItem(index)("quantity")}
                                  />
                                </Col>
                                <Col>
                                  <Text mb0 caption>
                                    Amount <span style={{color: 'red'}}>*</span>
                                  </Text>
                                  <Input
                                    placeholder="Amount"
                                    inputProps={{
                                      placeholderTextColor: COLOR.GREY,
                                    }}
                                    value={lineItem.amount || ""}
                                    mb1
                                    onChange={onChangeLineItem(index)("amount")}
                                  />
                                </Col>
                                <Col>
                                  <Text mb0 caption>
                                    VAT
                                  </Text>
                                  <Col>
                                    <Radio.Group onChange={(e) => {
                                      onChangeLineItem(index)("vat")(e.target.value);
                                    }} value={lineItem.vat} defaultValue={"disable"} style={{marginTop: 11}}>
                                      <Radio value={"20%"}>20%</Radio>
                                      <Radio value={"disable"}>Disable</Radio>
                                    </Radio.Group>
                                  </Col>
                                </Col>
                              </Grid>
                              
                            </React.Fragment>
                          )
                        })
                      }
                      <Button
                        text={"Add Line Item"}
                        minWidth={100}
                        height={40}
                        borderRadius={20}
                        paddingLeft={10}
                        paddingRight={10}
                        onPress={() => {
                          setLineItems([...lineItems, {}]);
                        }}
                      />
                    </Col>
                  </Grid>
                </Col>
              )
            }
          </>
        )
      }
      <Modal open={isFetchingInvoice} footer={null} closable={false}>
        <div style={{textAlign: 'center'}}>
          Fill invoice data...
        </div>
      </Modal>
    </CMSLayout>
  );
};

ManualCreateInvoice.routeInfo = {
  title: 'Manual Create Invoice',
  path: '/manual-create-invoice',
};

export default ManualCreateInvoice;
