import Request from '../Request.utils'
export interface ICheckDownloadBillingRequestParams {
  productId?: string;
  quantity?: number;
  variantName?: string;
}
export interface IStripeCreatePaymentRequestBody {
  amount?: number;
  paymentMethodId?: string;
  productId?: string;
  variationAmount?: number;
  variationName?: string;
  variationPrice?: number;
}
export interface IMarkOrderCompleteRequestBody {
  amount?: number;
  fulfillAmount?: number;
  price?: number;
  printJobId?: string;
}
export interface IRejectOrderRequestBody {
  deleteShopify?: boolean;
  orders?: any[];
  reason?: string;
}
export interface ICreateInvoiceRequestBody {
  order?: any;
}
export interface IOrderInvoicesRequestQuery {
  month?: string;
  resellerId?: string;
}
export interface IUpdateInvoiceRequestBody {
  fulfilledAt?: string;
  orderId: string;
  paidAt?: string;
}
export interface IGenOrderInvoiceRequestParams {
  orderId: string;
}
export interface ICancelOrderRequestBody {
  deleteShopify?: boolean;
  orders?: any[];
}
export interface IUpdateOrderInvoiceManuallyRequestBody {
  id: string;
  url?: string;
}
export interface IManuallyOverrideInvoiceRequestBody {
  invoiceId?: string;
  manualInvoicePdf?: string;
  postage?: number;
  productPrice?: number;
  total?: number;
  vat?: number;
}
export interface ICreateDraftInvoiceRequestBody {
  customAddress?: {
    address1?: string;
    address2?: string;
    country?: string;
    county?: string;
    email?: string;
    first_name?: string;
    last_name?: string;
    phone?: string;
    town?: string;
    zip?: string;
  };
  designId: string;
  productId: string;
  quantity: number;
  style?: string;
  type: string;
}
export interface IUpdateCustomerAddressRequestBody {
  address: object;
  orderId: string;
}
export interface IReCalculateStripeInvoiceRequestBody {
  forceUpdate?: boolean;
  orderId: string;
}
export interface ICheckEtsyOrdersRequestBody {
  orders: any[];
}
export interface IGetEtsyReceiptsRequestQuery {
  storeId: string;
}
export interface IReGenerateStripeInvoiceRequestBody {
  orderId: string;
}
export interface IUpdateShippingStripeInvoiceRequestBody {
  orderId: string;
  shippingService?: string;
}


class OrderAPI {
  checkDownloadBilling = async (params: ICheckDownloadBillingRequestParams) => {
    const res = await Request.call('/api/orders/check-download-billings/:productId', 'POST', params, undefined, undefined, );
    return res;
  }
  stripeCreatePayment = async (body: IStripeCreatePaymentRequestBody) => {
    const res = await Request.call('/api/orders/create-payment', 'POST', undefined, undefined, body, );
    return res;
  }
  markOrderComplete = async (body: IMarkOrderCompleteRequestBody) => {
    const res = await Request.call('/api/orders/mark-order-complete', 'POST', undefined, undefined, body, );
    return res;
  }
  rejectOrder = async (body: IRejectOrderRequestBody) => {
    const res = await Request.call('/api/orders/reject', 'POST', undefined, undefined, body, );
    return res;
  }
  createInvoice = async (body: ICreateInvoiceRequestBody) => {
    const res = await Request.call('/api/order/invoice', 'POST', undefined, undefined, body, );
    return res;
  }
  orderInvoices = async (query: IOrderInvoicesRequestQuery) => {
    const res = await Request.call('/api/order/invoices', 'GET', undefined, query, undefined, );
    return res;
  }
  updateInvoice = async (body: IUpdateInvoiceRequestBody) => {
    const res = await Request.call('/api/order/update-invoice', 'POST', undefined, undefined, body, );
    return res;
  }
  genOrderInvoice = async (params: IGenOrderInvoiceRequestParams) => {
    const res = await Request.call('/api/order/gen-invoice/:orderId', 'GET', params, undefined, undefined, );
    return res;
  }
  cancelOrder = async (body: ICancelOrderRequestBody) => {
    const res = await Request.call('/api/orders/cancel', 'POST', undefined, undefined, body, );
    return res;
  }
  updateOrderInvoiceManually = async (body: IUpdateOrderInvoiceManuallyRequestBody) => {
    const res = await Request.call('/api/orders/update-invoice-manually', 'POST', undefined, undefined, body, );
    return res;
  }
  manuallyOverrideInvoice = async (body: IManuallyOverrideInvoiceRequestBody) => {
    const res = await Request.call('/api/orders/manually-override-invoice', 'POST', undefined, undefined, body, );
    return res;
  }
  createDraftInvoice = async (body: ICreateDraftInvoiceRequestBody) => {
    const res = await Request.call('/api/order/draft-invoice', 'POST', undefined, undefined, body, );
    return res;
  }
  updateCustomerAddress = async (body: IUpdateCustomerAddressRequestBody) => {
    const res = await Request.call('/api/order/update-customer-address', 'POST', undefined, undefined, body, );
    return res;
  }
  reCalculateStripeInvoice = async (body: IReCalculateStripeInvoiceRequestBody) => {
    const res = await Request.call('/api/order/recalculate-stripe-invoice', 'POST', undefined, undefined, body, );
    return res;
  }
  checkEtsyOrders = async (body: ICheckEtsyOrdersRequestBody) => {
    const res = await Request.call('/api/order/check-etsy-orders', 'POST', undefined, undefined, body, );
    return res;
  }
  getEtsyReceipts = async (query: IGetEtsyReceiptsRequestQuery) => {
    const res = await Request.call('/api/order/get-etsy-receipts', 'GET', undefined, query, undefined, );
    return res;
  }
  reGenerateStripeInvoice = async (body: IReGenerateStripeInvoiceRequestBody) => {
    const res = await Request.call('/api/order/recreate-stripe-invoice', 'POST', undefined, undefined, body, );
    return res;
  }
  updateShippingStripeInvoice = async (body: IUpdateShippingStripeInvoiceRequestBody) => {
    const res = await Request.call('/api/order/update-shipping-service', 'POST', undefined, undefined, body, );
    return res;
  }
}
export default new OrderAPI()