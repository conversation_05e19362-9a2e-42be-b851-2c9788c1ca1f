import Request from '../Request.utils'
export interface IDetailRequestParams {
  id: string;
}
export interface IListBrandRequestQuery {
  page?: number;
}
export interface IUpsertBrandRequestBody {
  id?: string;
  resellerId?: string;
  storeName?: string;
  type?: string;
  url?: string;
}


class BrandAPI {
  detail = async (params: IDetailRequestParams) => {
    const res = await Request.call('/api/band/:id', 'GET', params, undefined, undefined, );
    return res;
  }
  listBrand = async (query: IListBrandRequestQuery) => {
    const res = await Request.call('/api/brand', 'GET', undefined, query, undefined, );
    return res;
  }
  listAllBrand = async () => {
    const res = await Request.call('/api/brand-all', 'GET', undefined, undefined, undefined, );
    return res;
  }
  upsertBrand = async (body: IUpsertBrandRequestBody) => {
    const res = await Request.call('/api/brand', 'POST', undefined, undefined, body, );
    return res;
  }
}
export default new BrandAPI()