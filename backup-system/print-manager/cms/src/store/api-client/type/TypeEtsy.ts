export interface TEtsyListing {
  listing_id?: number,
  user_id?: number,
  shop_id?: number,
  title?: string,
  description?: string,
  state?: string,
  creation_timestamp?: number,
  created_timestamp?: number,
  ending_timestamp?: number,
  original_creation_timestamp?: number,
  last_modified_timestamp?: number,
  updated_timestamp?: number,
  state_timestamp?: number,
  quantity?: number,
  shop_section_id?: any,
  featured_rank?: number,
  url?: string,
  num_favorers?: number,
  non_taxable?: boolean,
  is_taxable?: boolean,
  is_customizable?: boolean,
  is_personalizable?: boolean,
  personalization_is_required?: boolean,
  personalization_char_count_max?: any,
  personalization_instructions?: any,
  listing_type?: string,
  tags?: string[],
  materials?: any,
  shipping_profile_id?: number,
  return_policy_id?: any,
  processing_min?: number,
  processing_max?: number,
  who_made?: string,
  when_made?: string,
  is_supply?: boolean,
  item_weight?: any,
  item_weight_unit?: any,
  item_length?: any,
  item_width?: any,
  item_height?: any,
  item_dimensions_unit?: any,
  is_private?: boolean,
  style?: any,
  file_data?: string,
  has_variations?: boolean,
  should_auto_renew?: boolean,
  language?: string,
  price?: {
    amount?: number,
    divisor?: number,
    currency_code?: string,
  },
  taxonomy_id?: number,
  production_partners?: any,
  skus?: any,
  views?: number,
  shipping_profile?: any,
  shop?: any,
  images?: any,
  videos?: any,
  user?: any,
  translations?: any,
  inventory?: any,

  transaction_id?: number, // internal use - added by cron job
}

export interface TEtsyTransaction {
  transaction_id?: number,
  title?: string,
  description?: string,
  seller_user_id?: number,
  buyer_user_id?: number,
  create_timestamp?: number,
  created_timestamp?: number,
  paid_timestamp?: number,
  shipped_timestamp?: any,
  quantity?: number,
  listing_image_id?: number,
  receipt_id?: number,
  is_digital?: boolean,
  file_data?: string,
  listing_id?: number,
  sku?: string,
  product_id?: number,
  transaction_type?: string,
  price?: {
    amount?: number,
    divisor?: number,
    currency_code?: string,
  },
  shipping_cost?: {
    amount?: number,
    divisor?: number,
    currency_code?: string,
  },
  variations?: {
    property_id?: number,
    value_id?: any,
    formatted_name?: string,
    formatted_value?: string,
  }[],
  product_data?: any,
  shipping_profile_id?: number,
  min_processing_days?: number,
  max_processing_days?: number,
  shipping_method?: any,
  shipping_upgrade?: any,
  expected_ship_date?: number,
  buyer_coupon?: number,
  shop_coupon?: number,
}

export interface TEtsyReceipt {
  receipt_id?: number,
  receipt_type?: number,
  seller_user_id?: number,
  seller_email?: string,
  buyer_user_id?: number,
  buyer_email?: any,
  name?: string,
  first_line?: string,
  second_line?: string,
  city?: string,
  state?: string,
  zip?: string,
  status?: string,
  formatted_address?: string,
  country_iso?: string,
  payment_method?: string,
  payment_email?: any,
  message_from_payment?: any,
  message_from_seller?: any,
  message_from_buyer?: any,
  is_shipped?: boolean,
  is_paid?: boolean,
  create_timestamp?: number,
  created_timestamp?: number,
  update_timestamp?: number,
  updated_timestamp?: number,
  is_gift?: boolean,
  gift_message?: string,
  gift_sender?: string,
  grandtotal?: {
    amount?: number,
    divisor?: number,
    currency_code?: string,
  },
  subtotal?: {
    amount?: number,
    divisor?: number,
    currency_code?: string,
  },
  total_price?: {
    amount?: number,
    divisor?: number,
    currency_code?: string,
  },
  total_shipping_cost?: {
    amount?: number,
    divisor?: number,
    currency_code?: string,
  },
  total_tax_cost?: {
    amount?: number,
    divisor?: number,
    currency_code?: string,
  },
  total_vat_cost?: {
    amount?: number,
    divisor?: number,
    currency_code?: string,
  },
  discount_amt?: {
    amount?: number,
    divisor?: number,
    currency_code?: string,
  },
  gift_wrap_price?: {
    amount?: number,
    divisor?: number,
    currency_code?: string,
  },
  shipments?: any,
  transactions?: TEtsyTransaction[],
  refunds?: any[],
  supportedListings?: TEtsyListing[], // internal use
}
