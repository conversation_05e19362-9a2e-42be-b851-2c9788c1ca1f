type TTodoUpdateLater = any;

export type TSingleLineItem = {
  id: number,
  name: string,
  title: string,
  price: string,
  product_id: string,
  properties: Array<{ name: string, value: string }>,
  quantity: number,
  sku: string,
  [other: string]: TTodoUpdateLater,
}

export type TSingleLineWooItem = {
  id: number,
  name: string,
  product_id: number,
  variation_id: number,
  quantity: number,
  total: string,
  subtotal: string,
  subtotal_tax: string,
  total_tax: string,
  tax_class: string,
  sku: string,
  price: number,
  [other: string]: TTodoUpdateLater,
}

export type TCustomer = {
  id: number,
  email: string,
  accepts_marketing: boolean,
  first_name: string,
  last_name: string,
  state: string,
  note?: string,
  verified_email: boolean,
  multipass_identifier?: string,
  tax_exempt: boolean,
  tags: string,
  currency: string,
  phone?: string,
  admin_graphql_api_id: string,
  default_address?: any,
}

export type TShopifyShippingAddress = {
  first_name?: string,
  last_name?: string,
  address1?: string,
  address2?: string,
  phone?: string,
  city?: string,
  zip?: string,
  province?: string,
  country?: string,
  company?: string,
  name?: string,
  country_code?: string,
  province_code?: string,
  country_name?:  string,
}

export type TShopifyOrder = {
  id: number,
  created_at: string,
  currency: string,
  contact_email: string,
  customer: TCustomer,
  billing_address: TTodoUpdateLater,
  shipping_address: TShopifyShippingAddress,
  line_items: Array<TSingleLineItem>,
  name: string,
  order_number: number,
  total_price?: string,
  [other: string]: TTodoUpdateLater,
}

export type TWooOrder = {
  id: number,
  date_created: string,
  date_modified: string,
  date_paid: string,
  status: string,
  total: string,
  total_tax: string,
  customer_id: number,
  order_key: string,
  billing: {
    first_name: string,
    last_name: string,
    address_1: string,
    address_2: string,
    city: string,
    postcode: string,
    country: string,
    email: string,
    phone: string,
  },
  shipping: {
    first_name: string,
    last_name: string,
    address_1: string,
    address_2: string,
    city: string,
    postcode: string,
    country: string,
    email: string,
    phone: string,
  },
  line_items: Array<TSingleLineWooItem>,
  [other: string]: TTodoUpdateLater,
}

export type TShopifyListOrder = {
  limit: number;
  offset: number;
  filter: string;
  startDate: string;
  endDate: string;
  status: string;
  dateType: 'CreatedAt' | 'UpdatedAt';
  supported?: boolean;
  clientId?: string | number;
  includeInactive?: boolean;
  inactive?: boolean;
  env?: string;
  stage?: string;
  stageStatus?: string;
  orderType?: string;
  sort?: string;
  orderId?: string;
  orderNumber?: string | number;
};

export type TCMSOrder = {
  'Order ID': string | number,
  'Order Source ID': string | number,
  'Order Name': string,
  'Order Number': number,
  'Customer Email': string,
  'Customer Name': string,
  'Item Processed': string,
  'Item Supported': string,
  'Raw Data': TShopifyOrder,
  'All Item IDs': string,
  'All Product Names': string,
  'Status': string,
  'Other Data': any,
  'Client ID'?: string, // BG only
  'Client Name'?: string, // BG only
  'Store ID'?: string, // BG only
  [other: string]: TTodoUpdateLater,
  // CMS autogenerated
  Id: number,
  CreatedAt: string,
  UpdatedAt: string,
}

export type TBGCMSOrder = TCMSOrder & {
  StageStatus?: "Awaiting Payment" | "Queued For Production" | "Held" | "Held By Admin" | "On Time" | "Delayed" | string,
  Stage?: "Pre Production" | "In Production" | "Fulfillment" | "Cancelled" | string,
}