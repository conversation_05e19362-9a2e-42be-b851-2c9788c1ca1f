
export type TUserType = 'guess' | 'admin' | 'user' | 'reseller';

export type TUser = {
  id: string;
  clientId?: string;
  role: TUserType;
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  photoUrl?: string;
  otherData?: any;

  addressLine1?: string;
  addressLine2?: string;
  town?: string;
  country?: string;
  postCode?: string;

  resellerStripeId?: string;
  resellerId?: string;
  onlineStoreId?: string;
  onlineStore?: any;
  // db fields
  createdAt?: string;
  updatedAt?: string;
}