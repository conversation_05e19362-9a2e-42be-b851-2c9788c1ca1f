import J<PERSON>Zip from 'jszip';
import { saveAs } from 'file-saver';
import axios from 'axios';
import Store from 'store';
import { showPopupMessage } from 'components';

// Add type declaration for deviceMemory
declare global {
  interface Navigator {
    deviceMemory?: number;
  }
}

const moment = require('moment')

// Configure axios with timeout and retry logic
const createAxiosInstance = () => {
  return axios.create({
    timeout: 0, // no timeout
    maxContentLength: Infinity,
    maxBodyLength: Infinity,
  });
};

export const downloadPDFsAsZip = async (urls, updateProgress) => {
  if (!urls || !urls.length) return;
  const totalProgress = urls.length * 100;
  let progressByIdx = {};

  const intervalUpdateProgress = setInterval(() => {
    const totalCurrentProgress = Object.keys(progressByIdx).reduce((total, idx) => {
      return total + Number(progressByIdx[idx] || 0);
    }, 0);
    updateProgress(totalCurrentProgress / totalProgress);
  }, 60);

  const zip = new JSZip();

  await Promise.all(
    urls.map(async (url, idx) => {
      try {
        const response = await createAxiosInstance().get(url, {
          responseType: 'blob',
          onDownloadProgress: (progressEvent) => {
            const progress = Math.round((progressEvent.loaded / progressEvent.total) * 100);
            progressByIdx[idx] = progress;
          },
        });
        const urlParts = url.split('/');
        const fileName = urlParts[urlParts.length - 1];
        zip.file(fileName, response.data);
      } catch (error) {
        console.error(`Error downloading from ${url}: ${error}`);
      }
    })
  );

  zip.generateAsync({ type: 'blob' }).then((content) => {
    updateProgress(1);
    clearInterval(intervalUpdateProgress);
    saveAs(content, `${new Date().getTime()}.zip`);
  });
}

const putLineIdToFileName = (params: {
  name: string, orderId: string, lineId: string | number
}) => {
  const { name, orderId, lineId } = params;
  const orderIdIdx = name.indexOf(orderId);
  if (orderIdIdx === -1) return name;
  const last3LineIdChars = String(lineId).slice(-3);
  return name.replace(orderId, `${orderId}[${last3LineIdChars}]`);
}

const getBrowserMemory = () => {
  // navigator.deviceMemory returns memory in GB
  let memoryGB = navigator.deviceMemory || 1; // fallback to 1GB if not available
  if (memoryGB > 1) {
    memoryGB = 1;
  }
  // Convert to bytes and use 80% of available memory as max file size
  return Math.floor(memoryGB * 1024 * 1024 * 1024 * 0.5);
};

const MAX_FILE_SIZE = getBrowserMemory(); // Use browser's available memory

// Helper function to download with retry logic
const downloadWithRetry = async (url: string, options: any, maxRetries = 2) => {
  let lastError;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      const axiosInstance = createAxiosInstance();
      return await axiosInstance.get(url, options);
    } catch (error) {
      lastError = error;
      console.warn(`Download attempt ${attempt + 1} failed for ${url}:`, error.message);
      
      // If it's the last attempt, don't wait
      if (attempt < maxRetries) {
        // Wait before retrying (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }
  }
  
  throw lastError;
};

export const downloadArtworkAndPackingSlip = async ({ artworks = [], slips = [], candles = [] }, updateProgress, noticeMultipleZip) => {
  let currentZipSize = 0;
  let zipIndex = 1;

  let zip = new JSZip();
  let folderArtwork = zip.folder("artworks");
  let folderCandle = zip.folder("candles"); 
  let folderSlip = zip.folder("packing_slips");

  console.log('artworks', artworks);
  updateProgress(0.1);

  // artworks is array of { url, name, quantity }
  // if name is the same, check the url if same then combine the quantity, else change name
  const mergeArtworks = (() => {
    const map = {};
    artworks.forEach(({ name, url, quantity, lineId, ...rest }) => {
      if (map[name]) {
        if (map[name].url === url) {
          const oldNameMatch = `-${map[url].quantity}.pdf`;
          map[url].quantity += quantity;
          map[url].name = map[url].name.replace(oldNameMatch, `-${map[url].quantity}.pdf`);
        } else {
          // edit the existing
          const newName = putLineIdToFileName({
            name,
            lineId: map[name].lineId,
            orderId: map[name].orderId,
          })
          map[newName] = {
            ...map[name],
            name: newName,
          };
          delete map[name];

          // add new
          const newLineName = putLineIdToFileName({
            name,
            lineId,
            orderId: rest.orderId,
          })
          map[newLineName] = { name: newLineName, url, quantity, lineId, ...rest };
        }
      } else {
        map[name] = { name, url, quantity, lineId, ...rest };
      }
    });
    return Object.values(map);
  })();

  const totalProgress = (mergeArtworks.length + slips.length + candles.length) * 100;
  let progressByIdx = {};
  const failedJobs = [];

  const intervalUpdateProgress = setInterval(() => {
    const totalCurrentProgress = Object.keys(progressByIdx).reduce((total, idx) => {
      return total + Number(progressByIdx[idx] || 0);
    }, 0);
    updateProgress(totalCurrentProgress / totalProgress);
  }, 60);

  const createNewZip = () => {
    // Create completely new JSZip instance to avoid carrying over previous content
    zip = new JSZip();
    folderArtwork = zip.folder("artworks");
    folderCandle = zip.folder("candles");
    folderSlip = zip.folder("packing_slips");
    currentZipSize = 0;
  };

  const saveCurrentZip = async () => {
    try {
      const content = await zip.generateAsync({ type: 'blob' });
      saveAs(content, `${moment().format('DD.MM.YYYY')}_Orders_${zipIndex}.zip`);
      if (zipIndex === 1) {
        noticeMultipleZip?.();
      }
      zipIndex++;
      // Create new zip after saving current one
      createNewZip();
    } catch (error) {
      console.error('Error saving zip file:', error);
      failedJobs.push({ 
        type: 'zip_save', 
        error: error.message 
      });
    }
  };

  // Process artworks in parallel batches of 5
  for (let i = 0; i < mergeArtworks.length; i += 5) {
    const batch = mergeArtworks.slice(i, i + 5);
    await Promise.allSettled(batch.map(async (artwork: any) => {
      try {
        if (!artwork) return;
        
        const response = await downloadWithRetry(artwork.url, {
          responseType: 'blob',
          onDownloadProgress: (progressEvent) => {
            const progress = Math.round((progressEvent.loaded / progressEvent.total) * 100);
            progressByIdx[mergeArtworks.indexOf(artwork)] = progress;
          },
        });

        currentZipSize += response.data.size;
        folderArtwork.file(artwork.name, response.data);
      } catch (error) {
        console.error(`Error downloading artwork from ${artwork.url}: ${error}`);
        failedJobs.push({ 
          type: 'artwork', 
          url: artwork.url, 
          name: artwork.name, 
          orderId: artwork.orderId, 
          lineId: artwork.lineId, 
          error: error.message 
        });
        // Mark as completed to avoid blocking progress
        progressByIdx[mergeArtworks.indexOf(artwork)] = 100;
      }
    }));
    
    if (currentZipSize >= MAX_FILE_SIZE) {
      await saveCurrentZip();
    }
  }

  // Process slips one by one
  for (let i = 0; i < slips.length; i++) {
    const slip = slips[i];
    try {
      let response;
      if (slip.pdfUrl) {
        response = await downloadWithRetry(slip.pdfUrl, {
          responseType: 'blob',
          onDownloadProgress: (progressEvent) => {
            const progress = Math.round((progressEvent.loaded / progressEvent.total) * 100);
            progressByIdx[mergeArtworks.length + i] = progress;
          },
        });
      } else {
        const res = await axios.get(slip.url);
        response = await axios.request({
          url: 'https://puppeteer.personify.tech/api/html2pdf',
          headers: { 'Content-Type': 'application/json' },
          method: 'post',
          data: JSON.stringify({
            html: res.data,
            viewport: { width: 595, height: 842 },
          }),
          responseType: 'blob',
          onDownloadProgress: (progressEvent) => {
            const progress = Math.round((progressEvent.loaded / progressEvent.total) * 100);
            progressByIdx[mergeArtworks.length + slips.indexOf(slip)] = progress;
          },
        });
      }

      currentZipSize += response.data.size;
      folderSlip.file(slip.name, response.data);
    } catch (error) {
      console.error(`Error downloading slip from ${slip.url}: ${error}`);
      failedJobs.push({ 
        type: 'slip', 
        url: slip.url, 
        name: slip.name, 
        orderId: slip.orderId, 
        error: error.message 
      });
      // Mark as completed to avoid blocking progress
      progressByIdx[mergeArtworks.length + i] = 100;
    }
    
    if (currentZipSize >= MAX_FILE_SIZE) {
      await saveCurrentZip();
    }
  }

  // Process candles in parallel batches of 4
  for (let i = 0; i < candles.length; i += 4) {
    const batch = candles.slice(i, i + 4);
    await Promise.allSettled(batch.map(async (candle) => {
      try {
        const response = await downloadWithRetry(candle.url, {
          responseType: 'blob',
          onDownloadProgress: (progressEvent) => {
            const progress = Math.round((progressEvent.loaded / progressEvent.total) * 100);
            progressByIdx[mergeArtworks.length + slips.length + candles.indexOf(candle)] = progress;
          },
        });

        currentZipSize += response.data.size;
        folderCandle.file(candle.name, response.data);
      } catch (error) {
        console.error(`Error downloading candle from ${candle.url}: ${error}`);
        failedJobs.push({ 
          type: 'candle', 
          url: candle.url, 
          name: candle.name, 
          orderId: candle.orderId, 
          lineId: candle.lineId, 
          error: error.message 
        });
        // Mark as completed to avoid blocking progress
        progressByIdx[mergeArtworks.length + slips.length + candles.indexOf(candle)] = 100;
      }
    }));
    
    if (currentZipSize >= MAX_FILE_SIZE) {
      await saveCurrentZip();
    }
  }

  return new Promise((resolve, reject) => {
    // Only generate final zip if there's content to save
    if (currentZipSize > 0) {
      zip.generateAsync({ type: 'blob' }).then((content) => {
        updateProgress(1);
        clearInterval(intervalUpdateProgress);
        if (content.size > 0) {
          if (zipIndex === 1) {
            saveAs(content, `${moment().format('DD.MM.YYYY')}_Orders.zip`);
          } else {
            saveAs(content, `${moment().format('DD.MM.YYYY')}_Orders_${zipIndex}.zip`);
          }
        }
        resolve({ failedJobs, zipIndex });
      }).catch(e => {
        console.log("download err", e);
        showPopupMessage({
          title: '',
          content: String(e?.message || JSON.stringify(e)),
          buttonOkText: 'OK',
          typeHighlight: 'danger',
          contentHighlight: 'Error'
        });
        reject(e);
        clearInterval(intervalUpdateProgress);
      });
    } else {
      clearInterval(intervalUpdateProgress);
      resolve({ failedJobs, zipIndex });
    }
  });
}
