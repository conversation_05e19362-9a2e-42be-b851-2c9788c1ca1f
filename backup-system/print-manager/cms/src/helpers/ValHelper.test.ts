/**
 * @jest-environment jsdom
 */

import <PERSON><PERSON><PERSON><PERSON> from "./<PERSON><PERSON><PERSON><PERSON>";

// test calculateDPI
test('calculateDPI', () => {
  const testSet = [
    {
      input: [210, 297],
      output: 355,
    },
    {
      input: [148, 210],
      output: 381,
    },
    {
      input: [231.5, 60],
      output: 359,
    },
    {
      input: [248, 135],
      output: 330,
    },
    {
      input: [280, 160],
      output: 341,
    },
    {
      input: [196.33, 110],
      output: 366,
    },
    {
      input: [233.8, 101],
      output: 337,
    },
    {
      input: [244.92, 83],
      output: 411,
    },
    {
      input: [218.2, 130],
      output: 364,
    },
    {
      input: [218.2, 137],
      output: 315,
    },
  ]
  testSet.forEach((test) => {
    expect(ValHelper.calculateDPI(test.input[0], test.input[1])).toBe(test.output);
  });
});