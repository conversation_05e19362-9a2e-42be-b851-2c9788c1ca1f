import { useEffect } from 'react';
import { useNavFunc } from 'navigation';

export const useFocusEffect = (callback: () => void | (() => void), deps: any[] = []) => {
  const { navigation } = useNavFunc();

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      callback();
    });

    // Run on initial mount
    callback();

    // Cleanup subscription on unmount
    return () => {
      unsubscribe();
    };
  }, deps);
};

