import React from "react";
import { Entypo, Ionicons } from "@expo/vector-icons";
import { Col, Row, Text } from "components/base";
import { COLOR } from "const";
import { ValHelper } from "helpers";
import { usePaymentStore } from "store/Payment.Store";

type TRenderingProps = ({ balance, loading } : { balance : any, loading : boolean }) => any

const WalletBalanceRenderer = ({ children }: { children: TRenderingProps }) => {
  const { balance, loading } = usePaymentStore();
  return children({ balance, loading })
}

export default React.memo(WalletBalanceRenderer);
