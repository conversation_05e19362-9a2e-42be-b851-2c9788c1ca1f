import React, {useState} from "react";
import {View, Text, TouchableOpacity, StyleSheet, TextInput} from 'react-native';
import {BgIcon} from 'components';

const inputPassComponents = () => {
  return (
    <View >
      <TextInput 
        placeholder={'Enter your password'}
        placeholderTextColor={'#8F95B2'}
        onChangeText={(text) => setPassword(text)}
        secureTextEntry={shouldOpenEyePassword}
        value={password}
      ></TextInput>
      <TouchableOpacity style={[styles.styleEye, password 
        && styles.styleEyePasswordInputed, {outline: 'none',}]} onPress={openEyePassword}
      >
        <BgIcon name={shouldOpenEyePassword ? "eye-open" : "eye-off"} size={sizeText16} color="black" />
      </TouchableOpacity>             
    </View>
  );
}

export default inputPassComponents;


