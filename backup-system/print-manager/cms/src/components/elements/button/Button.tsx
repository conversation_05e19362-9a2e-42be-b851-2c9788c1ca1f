import React, { useMemo } from 'react';
import { Col, IColProps } from 'components';
import { Text, Row } from 'components';
import { COLOR } from 'const';
import {
  ActivityIndicator,
} from 'react-native';
import { LightenDarkenColor } from 'lighten-darken-color';
import <PERSON>Field from './TouchField';

interface IButtonProps extends IColProps {
  mainColor?: string;
  outline?: boolean;
  text: string;
  iconLeft?: any,
  iconRight?: any,
  isLoading?: boolean,
  bgHovered?: string,
  touchWidth?: number,
  fitContent?: boolean,
  numberOfLineTitle?: number,
  textStyle?: any,
}

const Button = ({ text, mainColor, outline, iconLeft, iconRight, isLoading, bgHovered, fitContent, textStyle, ...props }: IButtonProps) => {
  const btnColor = mainColor || COLOR.MAIN;
  const backgroundColor = outline ? 'transparent' : btnColor;
  const textColor = !outline ? 'white' : btnColor;
  const borderColor = !outline ? 'transparent' : btnColor;

  const width = useMemo(() => {
    if (fitContent) return undefined;
    return props.touchWidth ? props.touchWidth : 150;
  }, [props.touchWidth, fitContent]);

  return (
    <TouchField
      height={40}
      width={width}
      borderColor={borderColor}
      borderWidth={2}
      borderRadius={4}
      backgroundColor={backgroundColor}
      bgHovered={bgHovered || LightenDarkenColor(backgroundColor, 20)}
      {...props}
    >
      <Row flex1 middle={!fitContent}>
        {isLoading ? (
          <ActivityIndicator
            size={"small"}
            color={outline ? COLOR.MAIN : 'white'}
            style={{ marginRight: 5 }}
          />
        ) : iconLeft}
        <Text color={textColor} center numberOfLines={props.numberOfLineTitle ? props.numberOfLineTitle : 2} marginLeft={!!iconLeft ? 5 : undefined} style={textStyle}>
          {text}
        </Text>
        {iconRight}
      </Row>
    </TouchField>
  );
};

Button.colors = {
  redSolid: {
    backgroundColor: 'red',
    bgHovered: 'rgba(255,255,255,0.2)',
  },
}

export default Button;
