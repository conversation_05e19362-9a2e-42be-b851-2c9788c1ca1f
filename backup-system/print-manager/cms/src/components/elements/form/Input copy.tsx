import React from 'react';
import { Col, IColProps } from 'components';
import { TextInput, TextInputProps } from 'react-native';
import { FONT } from 'const';

interface IProps extends IColProps {
  value?: any;
  onChange?(newValue: any): void;
  inputProps?: TextInputProps;
  placeholder?: string;
  password?: boolean;
}

const Input = ({ value, onChange, inputProps, placeholder, password, ...props }: IProps) => {
  return (
    <Col round0 borderThin height={45} overflow='hidden' {...props}>
      <TextInput
        placeholder={placeholder}
        secureTextEntry={password}
        {...inputProps}
        style={[
          {
            width: '100%',
            padding: 10,
            fontFamily: FONT.defaultFont,
            color: '#383838',
            overflow: 'hidden',
            height: '100%',
            // @ts-ignore
            outline: 'none',
          },
          inputProps?.style,
        ]}
        onChangeText={onChange}
      />
    </Col>
  );
};

export default Input;
