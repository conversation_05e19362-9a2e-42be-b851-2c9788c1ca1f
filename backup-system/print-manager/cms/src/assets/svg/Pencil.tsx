import * as React from "react"
import { SVGProps } from "react"

const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={35}
    height={35}
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    viewBox="0 0 35 35"
    {...props}
  >
    <path fill="url(#a)" d="M0 0h35v35H0z" />
    <defs>
      <pattern
        id="a"
        patternContentUnits="objectBoundingBox"
        width={1}
        height={1}
      >
        <use xlinkHref="#b" transform="scale(.00195)" />
      </pattern>
      <image
        id="b"
        width={512}
        height={512}
        xlinkHref="data:image/png;base64,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"
      />
    </defs>
  </svg>
)

export default SvgComponent
