import qs = require('qs');
import { DB } from './src/db';
import { AWSHelper } from 'helpers';
const fastify = require("fastify");
const axios = require('axios');

const path = require('path')
class Backend {
  constructor(port: number) {
    this.instance = fastify({
      logger: true,
      disableRequestLogging: true,
      // bodyLimit: 31457280, // ~ 30 mb
      bodyLimit: 124857600
    });
    this.setupBasicMiddlewares();
    this.instance
      .listen(port, "::")
      .then(() => {
        console.log("🚀 THE BACKEND SERVER IS RUNNING AT PORT " + port);
      })
      .catch((err) => {
        console.log(err);
      });

  }

  instance;
  db;

  _countFeedApiCall = 0;

  // the middlewares that almost every express projects need
  setupBasicMiddlewares() {
    if (!this.instance) return;
    // this.instance.register(require('@fastify/formbody'))
    // this.instance.register(require('fastify-multipart'));
    // this.instance.addContentTypeParser('application/x-www-form-urlencoded', async function(request, payload, done) {
    //   console.log('PAYLOAD', payload);
    //   const parsed = qs.parse(payload);
    //   try {
    //     return parsed
    //   } catch(err) {
    //     return {};
    //   }
    // })
    this.instance.register(require('@fastify/static'), {
      root: path.join(__dirname, './src/uploads'),
      prefix: '/b/public/',
    });
    this.instance.addContentTypeParser(
      "application/x-www-form-urlencoded",
      { parseAs: "string" },
      function (req, body, done) {
        try {
          // console.log('body', body);
          done(null, qs.parse(body));
        } catch (error) {
          error.statusCode = 400;
          done(error, undefined);
        }
      }
    );
    if (process.env.LOG_API_PATH) {
      this.instance.addHook('onRequest', (request, reply, done) => {
        console.log('API: ' + request.method, request.url);
        done();
      });
    }
    this.instance.register(require('@fastify/multipart'), { attachFieldsToBody: true });
    this.instance.register(require("fastify-cors"), {
      // put your options here
      methods: "GET,PUT,POST, OPTIONS, DELETE",
      optionsSuccessStatus: 200,
    });

    this.instance.setErrorHandler(function (error, request, reply) {
      // Send error response
      reply.status(200).send({
        success: false,
        error: String(error),
        errorDetails: error,
      });
    });
    this.instance.get('/api/image/*', async function (request, reply) {
      const key = request.params['*'];
      try {
        const signedUrl = AWSHelper.getSignedUrl(key);
        const response = await axios.get(signedUrl, { responseType: 'arraybuffer' });
        reply
          .header('Content-Type', response.headers['content-type'])
          .send(response.data);
      } catch (error) {
        reply.code(404).send({ error: 'Image not found' });
      }
    });
  }

  // define apis
  async initAPIs(Apis: Array<any>) {
    if (!this.instance) return;
    if (!Array.isArray(Apis)) return;

    for (let i = 0; i < Apis.length; i++) {
      const ApiObject = Apis[i].apis;
      for (let api in ApiObject) {
        // console.log('api', api)
        const apiInstance = ApiObject[api];
        console.log('API: ' + apiInstance.method, apiInstance.url);
        // if (process.env.LOG_API_PATH) {
        //   console.log('setup log api')
        //   const originalHandler = apiInstance.handler;
        //   apiInstance.handler = async (request, reply) => {
        //     console.log('API CALL ', apiInstance.url);
        //     return originalHandler(request, reply);
        //   }
        // }
        this.instance.route(apiInstance);
      }
    }
  }

  setupDatabase() {
    this.db = DB.init();
  }
  onDBReady = DB.onReady
}

export default Backend;
