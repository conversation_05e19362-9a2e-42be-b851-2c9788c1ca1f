const sharp = require("sharp")
const fs = require('fs');
const { convertFile } = require('convert-svg-to-png');

// sharp('tempSVGFile-1665557034514.svg').png().toFile('output.png');

// const png = convert(`
// <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2763.078258841925 1193.630898815374" width="2763.078258841925" height="1193.630898815374">
//   <image
//     x="0"
//     y="0"
//     width="2763.078258841925"
//     height="1193.630898815374"
//     preserveAspectRatio="xMidYMid slice"
//     href="https://print-manager-media.s3.eu-west-1.amazonaws.com/cl8swrsc30000356kn6x24ur8-0.png">
//   </image>
// </svg>
// `, {

// }).then(res => {
//   console.log('res', res);
// })

(async() => {
  const inputFilePath = 'tempSVGFile-1665557034514.svg';
  const outputFilePath = await convertFile(inputFilePath);

  console.log(outputFilePath);
  //=> "/path/to/my-image.png"
})();