
const all = {
  c: true, r: true, u: true, d: true, o: true,
};

const readOnly = {
  c: false, r: true, u: false, d: false, o: true,
};

const none = {
  c: false, r: false, u: false, d: false, o: false,
};

const p = (obj: any) => Object.assign({}, obj);

export const PERMISSIONS = {
  user: {
    admin: p(all),
    reseller: p(none),
  },
  product: {
    admin: p(all),
    reseller: p(readOnly),
  },
  design: {
    admin: p(all),
    reseller: p(readOnly),
  },
};