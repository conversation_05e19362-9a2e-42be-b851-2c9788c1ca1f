import Joi from "joi";
import moment from "moment";
import Bg from "./@utils/Bg";
import { nextjs2api } from 'api/api-middlewares';

const orderReportMonthApi = async (req, res) => {
  await Bg.initDB();
  const { month = moment().format("MM-YYYY"), clientId, env } = req.query;

  const fromDate = moment(month, "MM-YYYY").startOf("month").toISOString();
  const toDate = moment(month, "MM-YYYY").endOf("month").toISOString();

  const ordersInMonth = await Bg.Order.getAllList({
    fromDate,
    toDate,
    clientId,
    env
  });

  let customers = {};
  let productsByEmail = {};
  let products = {};
  let emailsByProduct = {};
  let totalProduct = 0;
  const orders: any = [];

  ordersInMonth.forEach(order => {
    if (order["Other Data"]?.isTestOrder) return;
    if (!order["Item Supported"]?.trim()) return;
    if (order.inactive) return;

    if (order["Item Processed"]) {
      orders.push(order);
    }

    const customerEmail = order["Customer Email"];
    if (!customers[customerEmail] && order["Item Processed"]) {
      customers[customerEmail] = {
        ...order["Raw Data"].customer,
        clientId: order["Client ID"],
        clientName: order["Client Name"],
        createdAt: order.created_at,
      };
    }

    order["Raw Data"]?.line_items?.forEach((lineItem) => {
      if (order["Item Processed"]?.includes(String(lineItem.id))) {
        totalProduct += 1;

        const arr = productsByEmail[customerEmail] || [];
        arr.push({
          ...lineItem,
          createdAt: order.created_at,
        });
        productsByEmail[customerEmail] = arr;

        const product = products[lineItem.id] || {};

        products[lineItem.id] = {
          ...product,
          ...lineItem,
          clientId: order["Client ID"],
          clientName: order["Client Name"], 
          totalQuantity: (product?.quantity || 0) + 1,
          createdAt: order.created_at,
        }

        const emails = emailsByProduct[lineItem.id] || [];
        if (!emails.includes(customerEmail)) emails.push(customerEmail);
        emailsByProduct[lineItem.id] = emails;
      }
    })
  })

  res.json({
    success: true,
    data: {
      customers: Object.keys(customers).map(key => ({
        ...customers[key],
        products: productsByEmail[key] || [],
      })),
      products: Object.keys(products).map(key => ({
        ...products[key],
        customers: emailsByProduct[key] || [],
      })),
      orders,
    },
  });
};

export default nextjs2api(orderReportMonthApi, {
  url: '/api/bg/order-report-month',
  method: 'GET',
  checkAuthen: true,
  apiSchema: {
    query: Joi.object({
      month: Joi.string().optional(),
      clientId: Joi.string().optional(),
      env: Joi.string().optional()
    })
  }
});
