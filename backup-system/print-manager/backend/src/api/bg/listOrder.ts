import { TShopifyListOrder } from "type/TShopifyOrder";
import Bg from "./@utils/Bg";
import { Op } from "sequelize";
import { nextjs2api } from 'api/api-middlewares'
import Joi from "joi";

const moment = require('moment');
const UPDATED_AT_COLUMN = 'updated_at';
const CREATED_AT_COLUMN = 'created_at';

export const listOrder = async ({
  limit,
  offset,
  sort,
  filter,
  status,
  stageStatus,
  stage,
  orderType,
  supported,
  clientId,
  startDate,
  endDate,
  orderId,
  orderNumber,
  env,
  inactive,
  includeInactive,
  dateType,
}: TShopifyListOrder) => {
  const _sort = (() => {
    if (!sort) return [
      [UPDATED_AT_COLUMN, 'DESC']
    ];
    if (sort.startsWith('-') || sort.startsWith('+')) {
      const order = sort.startsWith('-') ? 'DESC' : 'ASC';
      const field = sort.slice(1);
      return [
        [field.toUpperCase() === 'UPDATEDAT' ? UPDATED_AT_COLUMN : CREATED_AT_COLUMN, order]
      ];
    }
    return [
      [UPDATED_AT_COLUMN, 'DESC']
    ];
  })();

  const query: any = {
    limit,
    offset,
    order: _sort,
    where: {}
  };
  if (startDate && endDate) {
    const _dateType = dateType === 'CreatedAt' ? CREATED_AT_COLUMN : UPDATED_AT_COLUMN;
    const reformatStartDate = startDate.includes('/') ? moment(startDate, 'DD/MM/YYYY').format('YYYY-MM-DD')
      : startDate.includes('-') ? startDate : '';
    const reformatEndDate = endDate.includes('/') ? moment(endDate, 'DD/MM/YYYY').add(1, 'days').format('YYYY-MM-DD')
      : endDate.includes('-') ? moment(endDate, 'YYYY-MM-DD').add(1, 'days').format('YYYY-MM-DD') : '';
    query.where[_dateType] = {
      [Op.gte]: moment(reformatStartDate, 'YYYY-MM-DD').format(),
      [Op.lte]: moment(reformatEndDate, 'YYYY-MM-DD').format(),
    }
  }
  if (status) query.where.Status = status;
  if (stageStatus) {
    query.where.StageStatus = {
      [Op.in]: stageStatus.split(',').map(v => v.trim()),
    }
  }
  if (stage && stage !== "All") {
    query.where.Stage = {
      [Op.in]: stage.split(',').map(v => v.trim()),
    }
  }
  if (orderType) {
    query.where.OrderType = {
      [Op.in]: orderType.split(',').map(v => v.trim()),
    }
  }
  if (supported) query.where['Item Supported'] = { [Op.not]: null };
  if (clientId) query.where['Client ID'] = clientId;
  if (!includeInactive && !inactive && !filter) query.where.inactive = false;
  if (inactive) query.where.inactive = true;
  if (env) query.where.env = env;
  if (orderId) query.where['Order ID'] = orderId;
  if (orderNumber) query.where['Order Number'] = orderNumber;
  if (filter) {
    const filterWithoutHash = filter.replace('#', '');
    const isFirstFourLetterNumber = !isNaN(+filterWithoutHash.slice(0, 4));
    const isNumber = !isNaN(+filterWithoutHash);
    query.where[Op.or] = [
      isFirstFourLetterNumber ? { 'Order ID': filterWithoutHash } : null,
      isNumber ? { 'Order Number': filterWithoutHash } : null,
      { 'Order Name': { [Op.like]: `%${filterWithoutHash}%` } },
      { 'Customer Name': { [Op.like]: `%${filterWithoutHash}%` } },
      { 'Customer Email': { [Op.like]: `%${filterWithoutHash}%` } },
      { 'All Product Names': { [Op.like]: `%${filterWithoutHash}%` } },
    ].filter(Boolean);
  }

  const data = await Bg.Order.findAndCountAll({
    ...query,
    raw: true,
    logging: console.log,
  });
  data.rows = Bg.mapNocoId(data.rows);
  const orderIds = (data.rows || [])?.map(i => i["Order ID"]);
  // console.log('orderIds', orderIds);
  const pipelines = await Bg.Pipeline.getByOrderIds(orderIds);
  const withPipelines = (data.rows || []).map((item) => {
    const filterPipelines = pipelines?.map(pData => {
      return pData.OrderId == item["Order ID"] ? pData : null;
    }).filter(Boolean);
    return {
      ...item,
      Pipelines: filterPipelines,
    }
  })
  return {
    rows: data.rows,
    withPipelines,
    count: data.count,
  }
}

const listOrderApi = async (req, res) => {
  await Bg.initDB();
  const body: TShopifyListOrder = req.query;

  if (!body.limit || (!body.offset && String(body.offset) !== '0')) {
    res.json({ success: false });
    return;
  }

  const data = await listOrder(body);

  res.json({
    success: true,
    data: {
      list: data.withPipelines,
      pageInfo: {
        totalRows: data.count,
        page: +body.offset / +body.limit + 1,
        pageSize: +body.limit,
        isFirstPage: +body.offset === 0,
        isLastPage: +body.offset + +body.limit >= data.count,
      }
    },
  });
};

export default nextjs2api(listOrderApi, {
  url: '/api/bg/listOrder',
  method: 'GET',
  checkAuthen: true,
  apiSchema: {
    query: Joi.object({
      limit: Joi.number().required(),
      offset: Joi.number().required(),
      sort: Joi.string().allow(''),
      filter: Joi.string().allow(''),
      status: Joi.string().allow(''),
      stageStatus: Joi.string().allow(''),
      stage: Joi.string().allow(''),
      orderType: Joi.string().allow(''),
      supported: Joi.boolean().allow(''),
      clientId: Joi.string().allow(''),
      startDate: Joi.string().allow(''),
      endDate: Joi.string().allow(''),
      orderId: Joi.string().allow(''),
      orderNumber: Joi.string().allow(''),
      env: Joi.string().allow(''),
      inactive: Joi.boolean().allow(''),
      includeInactive: Joi.boolean().allow(''),
      dateType: Joi.string().allow(''),
    }),
  }
});
