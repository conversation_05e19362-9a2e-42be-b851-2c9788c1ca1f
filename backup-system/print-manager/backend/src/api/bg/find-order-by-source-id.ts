import Joi from "joi";
import Bg from "./@utils/Bg";
import { nextjs2api } from 'api/api-middlewares';

const findOrderBySourceIdApi = async (req, res) => {
  await Bg.initDB();
  const { id } = req.query;
  const order = await Bg.Order.findByOrderSourceID(id)
  res.json({
    success: true,
    data: order,
  });
};

export default nextjs2api(findOrderBySourceIdApi, {
  url: '/api/bg/find-order-by-source-id',
  method: 'GET',
  checkAuthen: true,
  apiSchema: {
    query: Joi.object({
      id: Joi.string().required(),
    }),
  }
});
