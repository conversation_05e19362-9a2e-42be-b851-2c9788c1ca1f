import Joi from "joi";
import Bg from "./@utils/Bg";
import { nextjs2api } from 'api/api-middlewares'
import { TEtsyReceipt } from "type";
import { runPipeline } from "./services";
import { etsyToShopifyFormat } from "./@utils/PipelineHelper";

let lastRuns = {};
const THROTTLE_TIME = 2000;
const etsyWebhookApi = async (req, res) => {
  const body: TEtsyReceipt = req.body;
  const { clientId, env, clientName, storeId, resubmit } = req.query || {};

  const now = new Date().getTime();
  const orderSourceId = String(body.receipt_id);
  const lastRun = lastRuns[orderSourceId];
  if (lastRun) {
    if (now - lastRun < THROTTLE_TIME) {
      res.json({
        success: true,
        message: "Too many request with the same order",
      });
      return;
    }
  }
  lastRuns[orderSourceId] = now;

  await Bg.initDB();

  const existed = await Bg.Order.findByOrderSourceID(String(body.receipt_id));
  if (existed?.id && !resubmit) {
    return res.json({
      success: true,
      message: "Order ID existed",
      data: existed,
    });
  }
  const { orderNumber, orderId } = await (async () => {
    try {
      let countStoreOrder = await Bg.Order.count(storeId ? {
        'Store ID': storeId,
      } : {
        'Client ID': clientId,
      });
      const orderNumber = countStoreOrder + 1000;
      let countSameOrderNo = await Bg.Order.count({
        'Order Number': orderNumber,
      });
      const numberToBase26 = (val, tail = '') => {
        if (val <= 26) {
          return `${String.fromCharCode(val + 64)}${tail}`;
        }
        const remainder = val % 26 || 26;
        const division = Math.trunc(val / 26) - (remainder === 26 ? 1 : 0);
        return numberToBase26(division, `${String.fromCharCode(remainder + 64)}${tail}`);
      };
      let orderNo = countSameOrderNo + 1;
      let orderId = `${orderNumber}${numberToBase26(orderNo)}`;
      let isUnique = false;
      while (!isUnique) {
        let countSameOrderId = await Bg.Order.count({
          'Order ID': orderId,
        });
        if (countSameOrderId) {
          orderNo += 1;
          orderId = `${orderNumber}${numberToBase26(orderNo)}`;
        } else {
          isUnique = true;
        }
      }
      return {
        orderNumber,
        orderId,
      };
    } catch (err) {
      console.log('err orderUniqueId', err);
      return {
        orderNumber: `9${String(Math.floor(Math.random() * 999)).padStart(3, '0')}`,
        orderId: 'ES_' + Math.random().toString(36).substring(7),
      };
    }
  })();

  const order = await Bg.Order.create({
    'Order ID': orderId,
    'Order Source ID': String(body.receipt_id),
    'Order Name': `#${orderNumber}`,
    'Order Number': orderNumber,
    'Customer Email': body.buyer_email || body.seller_email || "",
    'Customer Name': body.name || "",
    'Raw Data': etsyToShopifyFormat(body),
    'All Item IDs': body.supportedListings?.map(i => i.listing_id)?.join(', '),
    'All Product Names': body.supportedListings?.map(i => i.title)?.join(', '),
    'Status': 'Pending',
    'Client ID': clientId,
    'Client Name': clientName,
    'Store ID': storeId,
    'env': env,
    'Stage': 'Pre Production',
    'StageStatus': "Awaiting Payment",
    'OrderType': 'Etsy',
  });

  res.json({
    success: true,
    message: "Done",
    data: order,
  });

  if (order?.Id) {
    runPipeline({ order });
  }
};

export default nextjs2api(etsyWebhookApi, {
  url: '/api/bg/etsy-webhook',
  method: 'POST',
  checkAuthen: true,
  apiSchema: {
    body: Joi.object().optional(),
    query: Joi.object({
      clientId: Joi.string().optional(),
      env: Joi.string().optional(),
      clientName: Joi.string().optional(),
      storeId: Joi.string().optional(),
    }),
  }
});
