import Bg from "./@utils/Bg";
import { nextjs2api } from 'api/api-middlewares'
const TEST_ORDER = require('./__tests__/order.json');

const cleanTestOrderApi = async (req, res) => {
  await Bg.initDB();
  const order = await Bg.Order.findByOrderSourceID(TEST_ORDER.id)
  if (order?.Id) {
    await Bg.Order.deleteOrder(order.Id)
    await Bg.Pipeline.deleteByOrderId(order["Order ID"])
  }
  const order2 = await Bg.Order.findByOrderSourceID(TEST_ORDER.id)
  if (order2?.Id) {
    return res.json({
      success: false,
      message: "Order not deleted",
    });
  }
  res.json({
    success: true,
  });
};

export default nextjs2api(cleanTestOrderApi, {
  url: '/api/bg/test/clean-test-order',
  method: 'POST',
  checkAuthen: true,
});
