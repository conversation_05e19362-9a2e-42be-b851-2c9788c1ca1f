import Joi from "joi";
import Bg from "./@utils/Bg";
import { nextjs2api } from 'api/api-middlewares'

const updateOrderDataApi = async (req, res) => {
  await Bg.initDB();
  const { id, orderId } = req.query;
  const { data } = req.body;

  let newData;
  const params = {
    "Raw Data": data,
  };

  if (orderId) {
    newData = await Bg.Order.updateByOrderId(orderId, params);
  } else {
    newData = await Bg.Order.updateById(id, params);
  }

  res.json({
    success: true,
    data: newData,
  });
};

export default nextjs2api(updateOrderDataApi, {
  url: '/api/bg/updateOrderData',
  method: 'POST', 
  checkAuthen: true,
  apiSchema: {
    body: Joi.object({
      data: Joi.object().required(),
    }),
    query: Joi.object({
      id: Joi.number().optional(),
      orderId: Joi.string().optional(), 
    }),
  }
});
