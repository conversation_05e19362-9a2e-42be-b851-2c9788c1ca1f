import Joi from "joi";
import Bg from "../@utils/Bg";
import { nextjs2api } from 'api/api-middlewares'
import { TCMSStage, TJobStatus } from "type/TCMSPipeline";
import JobStore from "../@utils/JobStore";
import { handleOrder } from "./HandleOrder";
import { TCMSOrder } from "type";
import StageStore from "../@utils/StageStore";

export const runPipeline = async ({ order }: {
  order: TCMSOrder;
}) => {
  const runningPipeline = await Bg.Pipeline.findUndoneByOrderId(String(order["Order ID"]));
  if (runningPipeline?.Id) {
    throw "A pipeline with the same Order ID is already running";
  }

  let stages: { [key: string]: TCMSStage } = {};

  handleOrder.forEach((i, idx) => {
    stages[`s${idx}`] = {
      Title: i.title,
      Jobs: i.jobs?.map(i => i.title),
    };
  })

  const pipeline = await Bg.Pipeline.create({
    OrderId: String(order["Order ID"]),
    "Order Source ID": String(order["Order Source ID"]),
    Stages: stages,
    Jobs: {},
    SharedData: {},
    Status: 'Running',
    StartTime: new Date().getTime(),
    DeploymentAddress: process.env.BACKEND_CMS_URL,
  });
  if (!pipeline?.Id) return console.log("Create/Find Pipeline Failed");

  const preStageStatus: TJobStatus[] = [];
  for (let i = 0; i < handleOrder.length; i++) {
    const stageId = `s${i}`;
    const stageStore = new StageStore({
      data: { ...stages[stageId] },
      pipelineId: Number(pipeline.Id),
      stageId,
    });
    if (preStageStatus.includes("Failed")) {
      await stageStore.update({
        Status: 'Cancelled',
      });
      continue;
      // failed fast: skip stage if has failed
    }
    if (preStageStatus.includes("Pending")) {
      await stageStore.update({
        Status: 'Pending',
      });
      continue;
    }
    await stageStore.update({
      StartTime: new Date().getTime(),
      Status: 'Running',
    });
    try {
      const ts = new Date().getTime();
      const results = await Promise.all(
        handleOrder[i].jobs.map((job, idx) => {
          const jobStore = new JobStore({
            data: {
              StageId: `s${i}`,
              Idx: idx,
              Title: job.title,
            },
            projectSlug: 'bg',
            pipelineId: Number(pipeline.Id),
            jobId: `s${i}_${ts}_${idx}`,
          });

          return new Promise(async (resolve) => {
            try {
              let shouldSkipCallback = true;
              if (job.callbackUrl) {
                shouldSkipCallback = false;
                if (job.handlerBeforeCallback) {
                  await jobStore.log(`[__handlerBeforeCallback__]: ${new Date().toISOString()}`)
                  const beforeCallbackResult = await job.handlerBeforeCallback({ order }, jobStore);
                  if (beforeCallbackResult?.shouldSkipCallback) {
                    shouldSkipCallback = true;
                  }
                }
              }
              if (!shouldSkipCallback) {
                await jobStore.sendLog();
                await jobStore.update({
                  Status: "Pending",
                })
                resolve("Pending");
                return;
              }
              await jobStore.update({
                Status: "Running",
                StartTime: new Date().getTime(),
              })
              await jobStore.log(`[__handler__]: ${new Date().toISOString()}`)
              await job.handler({ order }, jobStore);
              await jobStore.sendLog();
              await jobStore.update({
                Status: "Success",
                EndTime: new Date().getTime(),
              })
              resolve("Success");
            } catch (error) {
              await jobStore.log(`[__ERROR__] ${error?.message || JSON.stringify(error)}`);
              await jobStore.sendLog();
              await jobStore.update({
                Status: "Failed",
                EndTime: new Date().getTime(),
              })
              resolve("Failed");
            }
          });
        })
      )

      let stageStatus = "Success";
      if (results.includes("Failed")) stageStatus = "Failed";
      if (results.includes("Pending")) stageStatus = "Pending";

      await stageStore.update({
        EndTime: new Date().getTime(),
        Status: stageStatus,
      });
      preStageStatus.push(stageStatus);
    } catch (error) {
      await stageStore.update({
        Status: "Failed",
        EndTime: new Date().getTime(),
      })
      preStageStatus.push("Failed");
    }
  }

  if (preStageStatus.includes("Failed")) {
    await Bg.Pipeline.updateById(pipeline?.Id, {
      Status: "Failed",
      EndTime: new Date().getTime(),
    });
  } else if (preStageStatus.includes("Pending")) {
    await Bg.Pipeline.updateById(pipeline?.Id, {
      Status: "Pending",
    });
  } else {
    await Bg.Pipeline.updateById(pipeline?.Id, {
      Status: "Success",
      EndTime: new Date().getTime(),
    });
  }
};

const runPipelineApi = async (req, res) => {
  await Bg.initDB();
  const { data } = req.body || {};
  const order: TCMSOrder = data?.rows?.[0];
  if (!order) {
    res.json({ success: false });
    return;
  }

  try {
    await runPipeline({ order });
  } catch (error) {
    res.json({
      success: false,
      message: error?.message || (typeof error === 'string' ? error : JSON.stringify(error)),
    });
    return;
  }

  res.json({
    success: true,
  });
};

export default nextjs2api(runPipelineApi, {
  url: '/api/bg/pipeline/run',
  method: 'POST',
  checkAuthen: true,
  apiSchema: {
    body: Joi.object({
      type: Joi.string().required(),
      data: Joi.any().required(),
    }),
  }
});
