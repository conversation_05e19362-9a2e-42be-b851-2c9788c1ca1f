import Joi from "joi";
import Bg from "../@utils/Bg";
import { nextjs2api } from 'api/api-middlewares'

export const updatePipelineShareData = async ({ data, pipelineId }) => {
  const result = await Bg.Pipeline.updateSharedData({
    data,
    pipelineId
  });
  return result
};

const updatePipelineShareDataApi = async (req, res) => {
  await Bg.initDB();
  const { data, pipelineId } = req.body;
  const result = await updatePipelineShareData({ data, pipelineId });
  res.json({
    success: true,
    message: result,
  });
};

export default nextjs2api(updatePipelineShareDataApi, {
  url: '/api/bg/pipeline/update-pipeline-share-data',
  method: 'POST',
  checkAuthen: true,
  apiSchema: {
    body: Joi.object({
      data: Joi.any(),
      pipelineId: Joi.string().required(),
    }),
  }
});
