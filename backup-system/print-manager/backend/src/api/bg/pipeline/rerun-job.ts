import Joi from "joi";
import Bg from "../@utils/Bg";
import { nextjs2api } from 'api/api-middlewares'
import { TCMSJob } from "type/TCMSPipeline";
import JobStore from "../@utils/JobStore";
import { handleOrder } from "./HandleOrder";
import { continuePipeline } from "./continue-pipeline";

export const rerunJob = async ({ jobId, pipelineId, thisJobOnly, forceSkipCallback }: {
  jobId: string;
  pipelineId: number;
  thisJobOnly?: boolean;
  forceSkipCallback?: boolean;
}) => {
  if (!jobId || !pipelineId) {
    throw "Invalid request";
  }
  const pipeline = await Bg.Pipeline.findById(pipelineId);
  if (!pipeline?.Id) {
    throw "Pipeline not found";
  }
  const job = pipeline.Jobs?.[jobId] as TCMSJob;
  if (!job) {
    throw "Job not found";
  }
  if (!pipeline.OrderId) {
    throw "OrderId not found";
  }
  const order = await Bg.Order.findByOrderID(pipeline.OrderId);
  if (!order?.Id) {
    throw "Order not found";
  }
  if (job.Status === "Running") {
    throw "This job is already running";
  }

  const stageIdx = Number(job.StageId?.replace("s", ""));
  const jobIdx = Number(job.Idx);
  if (isNaN(stageIdx) || isNaN(jobIdx)) {
    throw "Invalid Pipeline";
  }

  const jobStore = new JobStore({
    data: {
      ...job,
    },
    projectSlug: 'bg',
    pipelineId: Number(pipeline.Id),
    jobId,
  });

  const jobCode = handleOrder[stageIdx]?.jobs?.[jobIdx];

  await jobStore.getInitialLog();
  await jobStore.log(`[__RERUN__]: ${new Date().toISOString()}\n\n`)

  const rerunRes = await new Promise(async (resolve) => {
    try {
      let shouldSkipCallback = true;
      if (jobCode.callbackUrl) {
        shouldSkipCallback = false;
        if (jobCode.handlerBeforeCallback) {
          await jobStore.log(`[__handlerBeforeCallback__]: ${new Date().toISOString()}`)
          const beforeCallbackResult = await jobCode.handlerBeforeCallback({ order }, jobStore);
          if (beforeCallbackResult?.shouldSkipCallback) {
            shouldSkipCallback = true;
          }
        }
      }
      if (!shouldSkipCallback && !forceSkipCallback) {
        await jobStore.sendLog();
        await jobStore.update({
          Status: "Pending",
        })
        resolve("Pending");
        return;
      }
      await jobStore.update({
        Status: "Running",
        StartTime: new Date().getTime(),
      })
      await jobStore.log(`[__handler__]: ${new Date().toISOString()}`)
      await jobCode.handler({ order }, jobStore);
      await jobStore.sendLog();
      await jobStore.update({
        Status: "Success",
        EndTime: new Date().getTime(),
      })
      resolve("Success");
    } catch (error) {
      await jobStore.log(`[__ERROR__] ${error?.message || JSON.stringify(error)}`);
      await jobStore.sendLog();
      await jobStore.update({
        Status: "Failed",
        EndTime: new Date().getTime(),
      })
      resolve("Failed");
    }
  })
  if (!thisJobOnly && rerunRes !== 'Pending') {
    try {
      await continuePipeline({
        pipelineId,
      })
    } catch (error) { }
  }
};

const rerunJobApi = async (req, res) => {
  await Bg.initDB();
  const { jobId, pipelineId, thisJobOnly, forceSkipCallback } = req.body || {};

  try {
    await rerunJob({ jobId, pipelineId, thisJobOnly, forceSkipCallback });
  } catch (error) {
    res.json({
      success: false,
      message: error?.message || (typeof error === 'string' ? error : JSON.stringify(error)),
    });
    return;
  }

  res.json({
    success: true,
  });
};

export default nextjs2api(rerunJobApi, {
  url: '/api/bg/pipeline/rerun-job',
  method: 'POST',
  checkAuthen: true,
  apiSchema: {
    body: Joi.object({
      jobId: Joi.string().required(),
      pipelineId: Joi.number().required(),
      thisJobOnly: Joi.boolean().optional(),
      forceSkipCallback: Joi.boolean().optional(),
    }),
  }
});
