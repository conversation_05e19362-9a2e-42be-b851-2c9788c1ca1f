import { fetchRoyalMailOrders } from 'api/online-stores/getRoyalMailOrders';
import axios from 'axios';
import { DB } from 'db';
import { Op } from 'sequelize';
import { TJobPayload, TJobTemplate } from 'type/TPipelineTemplate';
import { TCMSOrder } from 'type/TShopifyOrder';

export const royalMailCreateOrder = async (
  payload: TJobPayload,
  canBeProcessedItems: TCMSOrder['Raw Data']['line_items'],
  log = console.log,
) => {
  const fullOrder = payload.order?.['Raw Data'];
  if (!fullOrder) throw new Error('Order data undefined');
  const billing_address = fullOrder?.billing_address;
  const shipping_address = fullOrder?.shipping_address;
  const customer = fullOrder?.customer;

  const subtotal = +fullOrder?.current_subtotal_price;
  const shippingCostCharged = (() => {
    const shipping_lines = fullOrder?.shipping_lines || [];
    return shipping_lines.length === 0 ? 0 : shipping_lines.length === 1 ? +shipping_lines[0].price :
      shipping_lines.reduce((a, b) => ({ price: +a.price + +b.price })).price;
  })();
  const total = +(fullOrder?.current_total_price || fullOrder?.total_price);
  const getAddress = (obj) => {
    const address = {
      fullName: `${obj.first_name} ${obj.last_name || ''}`,
      companyName: obj.company || '',
      addressLine1: obj.address1 || `${obj.city || obj.town}`,
      addressLine2: obj.address2 || undefined,
      addressLine3: obj.province || undefined,
      city: obj.city || obj.town,
      county: obj.country,
      postcode: obj.zip,
      countryCode: obj.country_code || 'GB',
    };
    for (let key in address) {
      if (!address[key]) {
        delete address[key];
      }
    }
    return address;
  }

  const printJobIds = canBeProcessedItems?.map(v => v.printJobId).filter(Boolean);
  const products = await (async () => {
    if (printJobIds?.length) {
      try {
        const products = {};
        await Promise.all(printJobIds.map(async id => {
          const printJob = await DB.PrintJob.findByPk(String(id));
          if (!printJob) return;
          const { productId } = printJob;
          const product = await DB.Product.findByPk(String(productId));
          if (!product) return;
          products[id] = product;
        }))
        return products;
      } catch (error) {
        await log(`[Get Products Info Error]: ${error?.message || JSON.stringify(error)}`);
      }
    }
    return {};
  })();
  await log(`[Get Products Info]: ${JSON.stringify(products)}`);

  const item = {
    // orderReference: '#' + String(fullOrder.order_number),
    orderReference: payload.order['Order ID'],
    recipient: {
      address: getAddress(shipping_address || billing_address),
      phoneNumber: shipping_address?.phone || '',
      emailAddress: customer.email,
    },
    billing: {
      address: getAddress(billing_address),
      phoneNumber: billing_address.phone || '',
      emailAddress: customer.email,
    },
    packages: fullOrder.line_items.map((item, idx) => {
      const findPrintJobId = item.properties?.find(val => val.name === 'Print Job')?.value;
      let weight = findPrintJobId ? products?.[findPrintJobId]?.data?.weight : 1;
      if (!weight) weight = 1;

      let lineItemSku = item.sku;
      if (lineItemSku) {
        const existedSku = fullOrder.line_items.filter(v => v.sku === lineItemSku);
        if (existedSku.length > 1) {
          lineItemSku = `${lineItemSku}-${idx}`;
        }
      }
      return {
        packageFormatIdentifier: `parcel`,
        contents: [
          {
            name: item.name,
            SKU: lineItemSku || ('EMPTY-SKU-' + idx),
            quantity: item.quantity,
            unitWeightInGrams: weight,
            unitValue: +item.price,
          }
        ],
        weightInGrams: weight * item.quantity,
      }
    }),
    specialInstructions: '',
    orderDate: fullOrder?.created_at,
    subtotal,
    shippingCostCharged,
    total,
  }
  const serviceCode = payload?.additionData?.serviceCode || payload.order?.['Other Data']?.shippingService;
  if (serviceCode) {
    // @ts-ignore // current API doesn't support serviceCode
    // item.postageDetails = { serviceCode };
    // @ts-ignore
    item.tags = [{
      key: "shipping-service",
      value: serviceCode,
    }]
    item.specialInstructions = `Shipping Service: ${serviceCode}`;
  }

  await log('royal mail api payload', {
    items: [item],
  });
  const apiCallRes: any = await axios.request({
    url: 'https://api.parcel.royalmail.com/api/v1/orders',
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer b1eed28a-9beb-4e93-b8c9-5a0083c43bfd',
    },
    data: JSON.stringify({
      items: [item],
    }),
  });
  await log('royal mail api response', apiCallRes.data);
  if (apiCallRes.data?.successCount > 0) {
    const orderIdentifier = apiCallRes.data?.createdOrders[0].orderIdentifier;
    await log(`Successfully created order. Order Identifier: ${orderIdentifier}`);
    return orderIdentifier;
  }
  return null;
}

const createRMOrderWithRetry = async (payload, store, sharedData, tryCount = 1) => {
  let orderIdentifier;
  try {
    orderIdentifier = await royalMailCreateOrder(
      payload,
      sharedData.canBeProcessedItems,
      console.log
    );
  } catch (error) {
    if (tryCount > 3) return;
    let message = error?.message || JSON.stringify(error);
    console.log(`[RoyalMailCreateOrder retry Error]: ${message}`);
    if (message.includes('status code 500')) {
      setTimeout(() => {
        createRMOrderWithRetry(payload, store, sharedData, tryCount + 1);
      }, 1000 * 60)
    }
  }
  if (orderIdentifier) {
    store.shareData({
      royalMailOrderIdentifier: orderIdentifier,
    });
  }
}

export const dispatchToRoyalMail: TJobTemplate = {
  title: 'Create Order in RoyalMail',
  callbackUrl: '/api/bg/trigger-dispatch-royalmail',
  handler: async (payload, store) => {
    const sharedData = await store.getSharedData();
    let orderRM;
    try {
      await store.log(`[GetRoyalMailOrder in DB request]: ${payload?.order?.["Order ID"]}`);
      const orderId = payload?.order?.["Order ID"];
      if (orderId) {
        const order = await DB.GeneralData.findOne({
          where: {
            type: 'royal-mail-order',
            [Op.or]: [{
              field1: orderId,
            }, {
              field2: orderId,
            }]
          }
        });
        if (order?.id) {
          orderRM = order;
        } else {
          const orders = await fetchRoyalMailOrders();
          orderRM = orders?.find(i => i.orderIdentifier === orderId || i.orderReference === orderId);
        }
      }
    } catch (error) {
      await store.log(`[GetRoyalMailOrder Error]: ${error?.message || JSON.stringify(error)}`);
    }
    let orderIdentifier;
    let errorMessage = '';
    if (orderRM?.field1 || orderRM?.orderIdentifier) {
      await store.log(`[GetRoyalMailOrder in DB]: ${JSON.stringify(orderRM)}`);
      orderIdentifier = orderRM?.field1 || orderRM?.orderIdentifier;
    } else {
      try {
        orderIdentifier = await royalMailCreateOrder(
          payload,
          sharedData.canBeProcessedItems,
          store.log
        );
        await store.log(`[GetRoyalMailOrder]: ${orderIdentifier}`);
      } catch (error) {
        errorMessage = error?.message || JSON.stringify(error);
        if (String(errorMessage)?.includes('status code 500')) {
          createRMOrderWithRetry(payload, store, sharedData, 1);
        }
      }
    }
    if (errorMessage) {
      await store.log(`[RoyalMailCreateOrder Error]: ${errorMessage}`);
    }
    if (orderIdentifier) {
      await store.shareData({
        royalMailOrderIdentifier: orderIdentifier,
      });
    }
  }
};
