import axios from 'axios';
import { DB } from 'db';
import { TJobTemplate } from 'type/TPipelineTemplate';
import moment from 'moment';
import Etsy from 'helpers/EtsyHelper';
import { LIQUID_TEMPLATE, LIQUID_TEMPLATE_ETSY } from 'api/online-stores/orderPackingSlips';
import { LIQUID_TEMPLATE_ETSY_GIFT } from 'api/online-stores/orderPackingSlips';
const { Liquid } = require('liquidjs');
const engine = new Liquid();

export const generatePackingSlip: TJobTemplate = {
  title: "Generate Packing Slip",
  handler: async (payload, store) => {
    const { order } = payload;
    if (!order['Store ID'] && !order['Client ID']) {
      await store.log('[gen-packing-slip] No store ID or client ID');
      return;
    }
    const packingSlipData = await DB.PackingSlip.findOne({
      where: order['Store ID'] ? {
        storeId: order['Store ID'],
      } : {
        resellerId: order['Client ID'],
      }
    });
    const sharedData = await store.getSharedData();

    const filterSupported = (items) => {
      // if (slug === 'piw') return items.map(v => withOnlyPersonaliseTextProperty(v)).filter(v => v.properties.length > 0);
      // if (slug ==='piw') return supported.map(v => withOnlyPersonaliseTextProperty(v));
      const supported = items.filter(v => order['Item Supported']?.includes(String(v.id)));
      const addImagePreview = supported.map(v => {
        const findCanBeProcessedItem = (sharedData?.canBeProcessedItems || []).find(val => val.id === v.id);
        if (findCanBeProcessedItem) {
          return {
            ...v,
            image: findCanBeProcessedItem.previewUrl,
          }
        }
        return v;
      })
      return addImagePreview;
    }

    const orderRawData = order['Raw Data'];

    let line_items = filterSupported(orderRawData.line_items)

    for (let i = 0; i < line_items.length; i++) {
      const variant = line_items[i];
      const findPrintJob = variant.properties.find(val => val.name === 'Print Job');
      if (findPrintJob) {
        const printJobId = findPrintJob.value;
        const printJob = await DB.PrintJob.findByPk(printJobId);
        if (printJob) {
          if (printJob?.data?.previewImages) {
            line_items[i].previewImages = printJob.data?.previewImages || [];
          }
          const design = await DB.Design.findByPk(printJob.designId);
          if (design) {
            line_items[i].designName = design.name;
            if (design.galleries && !line_items[i].previewImages?.length) {
              line_items[i].previewImages = design.galleries;
            }
          }
        }
      }
      const designId = variant.properties.find(val =>
        val.name === 'BG Product Variant Number' ||
        val.name === 'BG Product Number'
      );
      if (designId) {
        const design = await DB.Design.findByPk(designId.value);
        if (design?.galleries && !line_items[i].previewImages?.length) {
          line_items[i].previewImages = design.galleries;
        }
      }
    }

    const displayData = {
      shop: {
        name: packingSlipData?.companyName || 'Bottled Goose',
        email: packingSlipData?.email,
        address: packingSlipData?.address,
        phone: packingSlipData?.phone,
        logo: packingSlipData?.companyLogo,
      },
      orderId: order['Order ID'],
      order: orderRawData,
      shipping_address: orderRawData.shipping_address,
      billing_address: orderRawData.billing_address,
      line_items_in_shipment: line_items,
      format_date: moment(orderRawData.created_at).format('MMMM DD, YYYY'),
      show_properties: false,
      currrency: orderRawData.currrency,
      subtotal_price: orderRawData.subtotal_price,
      total_discounts: orderRawData.total_discounts,
      total_tax: orderRawData.total_tax,
      total_price: orderRawData.total_price,
      total_shipping_price: orderRawData.total_shipping_price_set?.shop_money?.amount || 0,
      grand_total: "",
    }
    await store.log(`[gen-packing-slip displayData]`, displayData);

    let html = ""
    if (order.OrderType === "Etsy") {
      const store = await DB.OnlineStore.findByPk(order['Store ID']);
      const etsy = new Etsy(store.data?.etsyAccessToken, store.data?.etsyRefreshToken);
      const shopInfo = await etsy.getShop()

      if (shopInfo) {
        displayData.shop = {
          ...displayData.shop,
          ...shopInfo,
        }
      }
      if (shopInfo.shop_name) {
        displayData.shop.name = shopInfo.shop_name
      }
      if (shopInfo.icon_url_fullxfull) {
        displayData.shop.logo = shopInfo.icon_url_fullxfull
      }
      if (shopInfo.url) {
        // @ts-ignore
        displayData.shop.website = shopInfo.url
      }
      const receiptData = await etsy.getReceiptById({ id: orderRawData.id });
      if (receiptData?.total_price?.amount) {
        displayData.total_price = (Number(receiptData?.total_price?.amount) / Number(receiptData?.total_price?.divisor)).toFixed(2);
      }
      if (receiptData?.total_shipping_cost?.amount) {
        displayData.total_shipping_price = (Number(receiptData?.total_shipping_cost?.amount) / Number(receiptData?.total_shipping_cost?.divisor)).toFixed(2);
      }
      if (receiptData?.grandtotal?.amount) {
        displayData.grand_total = (Number(receiptData?.grandtotal?.amount) / Number(receiptData?.grandtotal?.divisor)).toFixed(2);
      }
      if (receiptData?.payment_method) {
        // 'cc' (credit card), 'paypal', 'check', 'mo' (money order), 'bt' (bank transfer), 'other', 'ideal', 'sofort', 'apple_pay', 'google', 'android_pay', 'google_pay', 'klarna', 'k_pay_in_4' (klarna), 'k_pay_in_3' (klarna), or 'k_financing' (klarna).
      }
      if (receiptData?.is_gift) {
        html = await engine.parseAndRender(LIQUID_TEMPLATE_ETSY_GIFT, displayData);
      } else {
        html = await engine.parseAndRender(LIQUID_TEMPLATE_ETSY, displayData);
      }
    } else {
      html = await engine.parseAndRender(LIQUID_TEMPLATE, displayData);
    }

    const res2 = await axios.request({
      url: 'https://puppeteer.personify.tech/api/html2pdf',
      headers: { 'Content-Type': 'application/json' },
      method: 'post',
      data: JSON.stringify({
        html,
        viewport: { width: 595, height: 842 },
        returnUrl: true,
      }),
    });
    await store.log(`[gen-packing-slip res.data]`, res2.data);
    await store.shareData({
      // @ts-ignore
      packingSlipUrl: res2.data?.data,
    });
  }
};
