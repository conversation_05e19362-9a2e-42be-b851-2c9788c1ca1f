import { requestGenerateCandleSticker } from 'api/pdf/sendRequestGenerateCandleSticker';
import { TJobTemplate } from 'type/TPipelineTemplate';

let lastRuns = {};
const THROTTLE_TIME = 60000;
export const generateCandleSticker: TJobTemplate = {
  title: "Generate Candle Sticker",
  callbackUrl: '/api/bg/request-generate-candle-sticker-callback',
  handlerBeforeCallback: async (payload, store) => {
    const sharedData = await store.getSharedData();
    const requestParams = {
      items: sharedData?.canBeProcessedItems,
      orderNumber: payload.order?.['Order Number'],
      storeId: payload.order?.['Store ID'],
      callbackUrl: `${process.env.BACKEND_CMS_URL}/api/bg/request-generate-candle-sticker-callback?orderId=${payload.order?.["Order ID"]}`,
    };

    if (sharedData?.isITLuggage || sharedData?.isBlankPdf) {
      return { shouldSkipCallback: true }
    }

    await store.log(`[Request Candle Sticker]: ${JSON.stringify(requestParams)}`);
    const lintItemIds = requestParams.items.map(v => v.id).join('-');
    const now = new Date().getTime();
    const lastRun = lastRuns[lintItemIds];
    if (lastRun) {
      if (now - lastRun < THROTTLE_TIME) {
        console.log(`[Generate Candle Sticker]: throttled ${lintItemIds}`);
        return;
      }
    }
    lastRuns[lintItemIds] = now;

    if (process.env.SKIP_PDF_GENERATION) {
      return;
    }
    await requestGenerateCandleSticker(requestParams)

    await store.log(`[Request Candle Sticker Res]: Request received`);
  },
  handler: async (payload, store) => {
    const pdfs: Array<{ lineId: number, pdfUrl: string }> = payload?.additionData?.pdfs || [];
    if (pdfs?.length) {
      await store.log(`Generated Stickers: ${JSON.stringify(pdfs)}`);
      await store.saveFiles(pdfs.map(v => ({
        name: `Candle Sticker for line: ${v.lineId}`,
        url: v.pdfUrl,
      })));
      const shareData = await store.getSharedData();
      const canBeProcessedItems = shareData?.canBeProcessedItems?.map(v => {
        const findPdf = pdfs.find(p => p.lineId === v.id);
        return findPdf ? {
          ...v,
          candleSticker: findPdf.pdfUrl,
        } : v;
      }) || [];
      await store.shareData({
        canBeProcessedItems,
      });
    }
  },
};
