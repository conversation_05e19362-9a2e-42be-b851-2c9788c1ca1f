import axios from 'axios';
import { TJobTemplate } from 'type/TPipelineTemplate';

export const awaitAcceptance: TJobTemplate = {
  title: 'Await acceptance',
  callbackUrl: '/api/bg/updateOrderStatus',
  handlerBeforeCallback: async (payload, store) => {
    // DISABLE FOR NOW

    // const resellerId = payload.order?.["Client ID"] || 440478626329;
    // const autoAcceptRes = await axios.request({
    //   url: `https://dev.bg-production.personify.tech/api/reseller/${resellerId}/check-auto-accept`,
    //   headers: { 'Content-Type': 'application/json' },
    //   method: 'get',
    // });
    // const autoAccept = autoAcceptRes.data?.data?.isAutoAccept;
    // if (autoAccept && payload.order?.Id) {
    //   await BgCMS.Order.updateById(payload.order?.Id, {
    //     Status: 'Accepted',
    //   });
    //   return { shouldSkipCallback: true };
    // }
  },
  handler: async (payload, store) => {
    await store.log(`Order has been accepted. Proceeding next stage`);
  }
};