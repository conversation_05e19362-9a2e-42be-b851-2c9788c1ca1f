import Bg from 'api/bg/@utils/Bg';
import axios from 'axios';
import { TJobTemplate } from 'type/TPipelineTemplate';
import { DB } from 'db';
export const generatePDF: TJobTemplate = {
  title: 'Generate PDF',
  callbackUrl: '/api/bg/request-generate-pdf-callback',
  handlerBeforeCallback: async (payload, store) => {
    const sharedData = await store.getSharedData();
    if (payload.order?.["Raw Data"]?.is_sample_request && sharedData.canBeProcessedItems.length === 0) {
      return { shouldSkipCallback: true }
    }
    if (!sharedData?.canBeProcessedItems?.length) {
      await store.log(`[request generate pdf]: canBeProcessedItems is empty`);
      return { shouldSkipCallback: true }
    }

    const regularItems = [];
    const engravedLogoItems = {};
    const isITLuggage = sharedData?.isITLuggage;
    const isBlankPdf = sharedData?.isBlankPdf;

    for (const item of sharedData.canBeProcessedItems) {
      const lineItem = payload.order?.["Raw Data"]?.line_items?.find(li => li.id === item.id);

      const properties = lineItem?.properties || [];
      const orderTypeProperty = properties.find(p => p.name === 'Order Type');
      const orderType = orderTypeProperty?.value;
      if (orderType === 'engraved-logo') {

        const logoTypeProperty = properties.find(p => p.name === 'Logo Type');
        const logoType = logoTypeProperty?.value || 'unknown';

        if (!engravedLogoItems[logoType]) {
          engravedLogoItems[logoType] = [];
        }
        engravedLogoItems[logoType].push({
          ...item,
          logoType,
          quantity: lineItem.quantity || 1
        });
      } else {
        regularItems.push(item);
      }
    }

    await store.shareData({
      regularItems,
      engravedLogoItems,
      pdfs: []
    });

    if (regularItems.length > 0) {
      const requestData = {
        items: regularItems.map(v => ({
          id: v.id,
          printJobId: v.printJobId,
          customArtworkData: v.customArtworkData,
          designRenderId: v.designRenderId,
          isITLuggage,
          isBlankPdf,
        })),
        callbackUrl: `${process.env.BACKEND_CMS_URL}/api/bg/request-generate-pdf-callback?orderId=${payload.order?.["Order ID"]}`,
      };
      await store.log(`[requestData for regular items]: ${JSON.stringify(requestData)}`);
      
      if (!process.env.SKIP_PDF_GENERATION) {
        const res = await axios.request({
          url: `http://localhost:3000/api/pdf/send-request-generate-pdf`,
          headers: { 'Content-Type': 'application/json' },
          method: 'post',
          data: JSON.stringify(requestData),
        });
        await store.log(`[requestData res.data for regular items]`, res.data);
        // @ts-ignore
        if (!res.data?.success) throw new Error(res.data?.error);
      }
    }

    for (const logoType in engravedLogoItems) {
      if (engravedLogoItems[logoType].length > 0) {
        console.log(`engraved-logo items for type ${logoType}:`, engravedLogoItems[logoType]);
        
        const items = [];
        for (const item of engravedLogoItems[logoType]) {
          console.log(`artwork for item ${item.id}, printJobId: ${item.printJobId}`);
          
          let artworkUrl = null;
          if (item.printJobId) {
            const printJob = await DB.PrintJob.findByPk(item.printJobId);
            console.log(`print job:`, {
              id: printJob?.id,
              artworkUrls: printJob?.artworkUrls
            });
            
            if (printJob && printJob.artworkUrls && printJob.artworkUrls.length > 0) {
              artworkUrl = printJob.artworkUrls[0];
            }
          }
          
          if (artworkUrl) {
            items.push({
              imageUrl: artworkUrl,
              quantity: item.quantity
            });
          } else {
            await store.log(`No artwork URL found for engraved-logo item ${item.id}`);
          }
        }

        if (items.length > 0) {
          const requestData = {
            items,
            logoType,
            returnUrl: true
          };

          await store.log(`[requestData for engraved-logo items (${logoType})]: ${JSON.stringify(requestData)}`);
          
          if (!process.env.SKIP_PDF_GENERATION) {
            try {
              const res: any = await axios.request({
                url: `${process.env.ENGRAVED_LOGO_PDF_API_URL || 'https://puppeteer.personify.tech/api/engraving-logo2pdf'}`,
                headers: { 'Content-Type': 'application/json' },
                method: 'post',
                data: JSON.stringify(requestData),
              });

              await store.log(`[engraved-logo PDF response for ${logoType}]`, res.data);
              
              if (res.data?.success && res.data?.data) {
                console.log(`Successfully generated PDF for ${logoType}:`, res.data.data);
                // Store the PDF URL with associated item IDs
                const pdfs = (await store.getSharedData()).pdfs || [];
                pdfs.push({
                  url: res.data.data,
                  itemIds: engravedLogoItems[logoType].map(item => item.id),
                  logoType,
                  type: 'engraved-logo'
                });
                await store.shareData({ pdfs });
              } else {
                throw new Error(res.data?.error || 'Failed to generate PDF for engraved-logo items');
              }
            } catch (error) {
              await store.log(`[error generating engraved-logo PDF for ${logoType}]: ${error.message || JSON.stringify(error)}`);
              throw error;
            }
          }
        }
      }
    }

    if (Object.keys(engravedLogoItems).length > 0 && regularItems.length === 0) {
      return { shouldSkipCallback: true };
    }
  },
  handler: async (payload, store) => {
    const sharedData = await store.getSharedData();
    const pdfs: Array<{ lineId: number, pdfUrl: string, printJobId: string, imageQuality?: any }> = payload?.additionData?.pdfs || [];
    await store.log(`Generated regular PDFs: ${JSON.stringify(pdfs)}`);

    if (pdfs.length > 0) {
      await store.saveFiles(pdfs.map(v => ({
        name: `PDF for line: ${v.lineId}`,
        url: v.pdfUrl,
      })));

      const regularItems = sharedData.regularItems || [];
      const updatedRegularItems = regularItems.map(v => {
        const findPdf = pdfs.find(p => p.lineId === v.id);
        const newItem = findPdf ? {
          ...v,
          pdf: findPdf.pdfUrl,
          printJobId: findPdf.printJobId,
        } : v;
        if (findPdf?.imageQuality) newItem.imageQuality = findPdf.imageQuality;
        return newItem;
      }) || [];
        
      const allPdfs = sharedData.pdfs || [];
      pdfs.forEach(pdf => {
        allPdfs.push({
          url: pdf.pdfUrl,
          itemIds: [pdf.lineId],
          type: 'regular'
        });
      });
      
      await store.shareData({
        regularItems: updatedRegularItems,
        pdfs: allPdfs
      });
    }

    const canBeProcessedItems = [
      ...(sharedData.regularItems || []).map(item => {
        const findPdf = pdfs.find(p => p.lineId === item.id);
        if (findPdf) {
          return {
            ...item,
            pdf: findPdf.pdfUrl,
            printJobId: findPdf.printJobId,
            imageQuality: findPdf?.imageQuality,
            pdfType: 'regular'
          };
        }
        return item;
      }),
      ...Object.values(sharedData.engravedLogoItems || {}).flat().map((item: any) => {
        const pdfInfo = (sharedData.pdfs || []).find(p => 
          p.type === 'engraved-logo' && 
          p.itemIds.includes(item.id)
        );
        if (pdfInfo) {
          return {
            ...item,
            pdf: pdfInfo.url,
            pdfType: 'engraved-logo'
          };
        }
        return item;
      })
    ];
    
    await store.log(`[canBeProcessedItems]`, canBeProcessedItems);

    await store.shareData({
      canBeProcessedItems
    });
    
    if (payload.order?.["Order ID"]) {
      await Bg.Order.updateByOrderId(payload.order?.["Order ID"], {
        "Item Processed": canBeProcessedItems.filter(i => i.pdf).map(i => i.id).join(','),
      });
    }
  }
};
