import Bg from 'api/bg/@utils/Bg';
import axios from 'axios';
import { DB } from 'db';
import { ITLProductShopifyToBG } from 'helpers/VarHelper';
import { Op } from 'sequelize';
import { TSingleLineItem } from 'type';
import { TJobTemplate } from 'type/TPipelineTemplate';

export const checkTestOrderAndCanBeProcessed: TJobTemplate = {
  title: 'Check test order & check can be processed line items',
  handler: async (payload, store) => {
    const shopifyOrder = payload.order?.["Raw Data"];
    await store.log('shopifyOrder', shopifyOrder);
    const isTestOrder = (() => {
      const email = payload.order?.["Customer Email"];
      const name = payload.order?.["Customer Name"];
      if (email === '<EMAIL>') return true;
      if (email === '<EMAIL>') return true;
      if (email === '<EMAIL>') return true;
      if (email?.includes('minhminhvn0210')) return true;
      if (email?.includes('@devteam.london')) return true;
      if (email?.includes('@personify.tech')) return true;
      if (name?.includes('Dev') || name?.includes('Test')) return true;
      return false;
    })();
    let isSampleRequest = shopifyOrder?.is_sample_request;
    const { line_items } = shopifyOrder || { line_items: [] };
    const canBeProcessedItems: Array<TSingleLineItem> = [];

    let shouldUpdateRawData = false;
    let isITLuggage = payload.order?.["Store ID"] === '123415927657';
    let isBlankPdf = payload.order?.["Store ID"] === '123781293930';

    for (let i = 0; i < line_items.length; i++) {
      const variant = line_items[i];
      let findPrintJob = variant.properties.find(val => val.name === 'Print Job');
      const findCustomArtwork = variant.properties.find(val => val.name === 'Custom Artwork');
      const findDesignID = variant.properties.find(val => val.name === 'Design ID');

      // no customization, using design to render PDF
      const findBGProductNumber = variant.properties.find(val => val.name === 'BG Product Number');
      // sample request
      let findBGProductLibrary = variant.properties.find(val => val.name === 'BG Product Library');
      if (!isSampleRequest) isSampleRequest = variant.properties.some(val => val.name === "Order A Sample");

      let findDesignIdInBGDB;
      let designRender = null;
      let variantDesignId;

      if (isITLuggage) {
        findPrintJob = variant.properties.find(val => val.name === 'Print ID')
        const bgProductData = ITLProductShopifyToBG[variant.product_id];
        if (bgProductData) {
          findDesignIdInBGDB = bgProductData.designId;
          findBGProductLibrary = bgProductData.productId;
          designRender = await DB.Design.findByPk(bgProductData.designId);
        }
        shouldUpdateRawData = true;
        line_items[i].properties.push({
          name: 'BG Product Number',
          value: String(findDesignIdInBGDB),
        });
        line_items[i].properties.push({
          name: 'Print Job',
          value: findPrintJob,
        });
        line_items[i].properties.push({
          name: 'BG Product Library',
          value: findBGProductLibrary,
        });
      } else {

      if (!findPrintJob && !findCustomArtwork && !findBGProductNumber && !isSampleRequest) {
        console.log('Searching from bgdb', variant.product_id, variant.id);
        designRender = await DB.Design.findOne({
          where: {
            products: {
              [Op.like]: `%:${variant.product_id},%`,
            }
          }
        });
        if (designRender?.id) findDesignIdInBGDB = designRender.id;
        
        if (!findDesignIdInBGDB && findDesignID?.value) {
          findDesignIdInBGDB = findDesignID?.value;
        }
        if (!findDesignIdInBGDB) continue;
        shouldUpdateRawData = true;
        line_items[i].properties.push({
          name: 'BG Product Number',
          value: String(findDesignIdInBGDB),
        });
        console.log(`UPDATE VARIANT ${i}: ADD BG PRODUCT NUMBER`, line_items[i].properties);
        // check designRender variants
        const findProduct = designRender?.products.find(p => p.productId == variant.product_id);
        if (findProduct && findProduct.matchDesignId) {
          const matchDesignId = findProduct.matchDesignId;
          variantDesignId = matchDesignId[String(variant.variant_id)] || matchDesignId[variant.variant_id];
          if (variantDesignId) {
            line_items[i].properties.push({
              name: 'BG Product Variant Number',
              value: variantDesignId,
            });
            designRender = await DB.Design.findByPk(variantDesignId);
          }
        }
      }

      if (!designRender && !!findBGProductNumber?.value) {
        designRender = await DB.Design.findByPk(findBGProductNumber?.value);
      }
      }

      const previewUrl = await (async () => {
        if (isITLuggage) {
          const findPreviewProperty = variant.properties.find(val => val.name === 'Preview');
          if (findPreviewProperty?.value) return findPreviewProperty.value;
        }
        if (findCustomArtwork?.value) return `https://print-manager-media.s3.eu-west-1.amazonaws.com/bg-custom-artwork/${findCustomArtwork?.value}`;
        if (findPrintJob?.value) {
          const printJob = await DB.PrintJob.findByPk(findPrintJob?.value);
          return printJob?.previewUrl;
        }
        if (designRender) {
          return designRender.image;
        }
        if (findBGProductLibrary) {
          const bgProductId = findBGProductLibrary.value;

          const product = await DB.Product.findByPk(bgProductId);
          if (product?.image) return product.image;
        }
      })();

      canBeProcessedItems.push({
        ...variant,
        printJobId: findPrintJob?.value,
        previewUrl,
        customArtworkData: {
          customArtwork: findCustomArtwork?.value,
          designId: findDesignID?.value,
        },
        designRenderId: designRender?.id,
        productRenderId: findBGProductLibrary?.value,
        isSampleRequest,
      });
    }
    await store.shareData({
      isTestOrder,
      canPrint: canBeProcessedItems.length > 0,
      skipped: canBeProcessedItems.length === 0 && !isSampleRequest,
      canBeProcessedItems,
      isSampleRequest,
      isITLuggage,
      isBlankPdf,
    });
    if (canBeProcessedItems.length === 0 && !isSampleRequest) {
      throw new Error('[SKIPPED] No supported product in this order');
    }
    axios.request({
      url: `http://localhost:3000/api/order/invoice`,
      method: 'post',
      headers: { 'Content-Type': 'application/json' },
      data: JSON.stringify({
        order: {
          ...payload.order,
          "Item Supported": canBeProcessedItems.map(i => i.id).join(','),
          "Other Data": { isTestOrder },
        }
      })
    })
    await store.log(`[upsert invoice]`, JSON.stringify({
      order: {
        ...payload.order,
        "Item Supported": canBeProcessedItems.map(i => i.id).join(','),
        "Other Data": { isTestOrder },
      }
    }));
    if (payload.order?.["Order ID"]) {
      await Bg.Order.updateByOrderId(payload.order?.["Order ID"], {
        "Item Supported": canBeProcessedItems.map(i => i.id).join(','),
        "Other Data": { isTestOrder },
        ...(shouldUpdateRawData ? {
          "Raw Data": {
            ...shopifyOrder,
            line_items,
          }
        } : {}),
      })
    }
  }
}
