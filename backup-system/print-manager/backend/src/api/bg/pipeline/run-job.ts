import Joi from "joi";
import Bg from "../@utils/Bg";
import { nextjs2api } from 'api/api-middlewares'
import { TCMSJob } from "type/TCMSPipeline";
import JobStore from "../@utils/JobStore";
import { handleOrder } from "./HandleOrder";
import { continuePipeline } from "./continue-pipeline";

export const runJob = async ({ jobId, pipelineId, additionData, thisJobOnly, error }: {
  jobId: string;
  pipelineId: number;
  additionData?: any;
  thisJobOnly?: boolean;
  error?: string;
}) => {
  if (!jobId || !pipelineId) {
    throw "Invalid request";
  }
  const pipeline = await Bg.Pipeline.findById(pipelineId);
  if (!pipeline?.Id) {
    throw "Pipeline not found";
  }
  const job = pipeline.Jobs?.[jobId] as TCMSJob;
  if (!job) {
    throw "Job not found";
  }
  if (!pipeline.OrderId) {
    throw "OrderId not found";
  }
  const order = await Bg.Order.findByOrderID(pipeline.OrderId);
  if (!order?.Id) {
    throw "Order not found";
  }

  if (job.Status === "Running") {
    throw "This job is already running";
  }

  let stageIdx = Number(job.StageId?.replace("s", ""));
  let jobIdx = Number(job.Idx);
  if (isNaN(stageIdx) || isNaN(jobIdx)) {
    throw "Invalid Pipeline";
  }

  // re check by comparing the job title
  if (handleOrder[stageIdx]?.jobs?.[jobIdx]?.title !== job.Title) {
    console.log('job index seems wrong, recheck by comparing the job title...');
    // loop through all jobs to find the correct job
    let found = false;
    for (let i = 0; i < handleOrder.length; i++) {
      if (found) break;
      const stage = handleOrder[i];
      if (!stage) continue;
      for (let j = 0; j < stage.jobs.length; j++) {
        if (stage.jobs[j].title === job.Title) {
          found = true;
          stageIdx = i;
          jobIdx = j;
          break;
        }
      }
    }
  }

  if (job.Status === "Pending" || job.Status === "Success") {
    const jobStore = new JobStore({
      data: {
        ...job,
      },
      projectSlug: 'bg',
      pipelineId: Number(pipeline.Id),
      jobId,
    });
    try {
      await jobStore.getInitialLog();
      if (!error) {
        await jobStore.update({
          Status: "Running",
          StartTime: new Date().getTime(),
        })
        await handleOrder[stageIdx]?.jobs?.[jobIdx]?.handler({ order, additionData }, jobStore);
        await jobStore.sendLog();
        await jobStore.update({
          Status: "Success",
          EndTime: new Date().getTime(),
        })
      } else {
        jobStore.log(`[__ERROR_CALLBACK__]`, error);
        await jobStore.sendLog();
        await jobStore.update({
          Status: "Failed",
          EndTime: new Date().getTime(),
        })
      }
      // continue the pipelines if pipeline didn't succeed
      if (!thisJobOnly && pipeline.Status !== 'Success' && !error) {
        try {
          await continuePipeline({
            pipelineId,
          })
        } catch (error) { }
      }
    } catch (error) {
      await jobStore.log(`[__ERROR__] ${error?.message || JSON.stringify(error)}`);
      await jobStore.sendLog();
      await jobStore.update({
        Status: "Failed",
        EndTime: new Date().getTime(),
      })
    }
    return;
  }

  // retry job
  const jobStore = new JobStore({
    data: {
      ...job,
    },
    projectSlug: 'bg',
    pipelineId: Number(pipeline.Id),
    jobId,
  });
  try {
    await jobStore.getInitialLog();
    await jobStore.update({
      Status: "Running",
      StartTime: new Date().getTime(),
    })
    await handleOrder[stageIdx]?.jobs?.[jobIdx]?.handler({ order, additionData }, jobStore);
    await jobStore.sendLog();
    await jobStore.update({
      Status: "Success",
      EndTime: new Date().getTime(),
    })
  } catch (error) {
    await jobStore.log(`[__ERROR__] ${error?.message || JSON.stringify(error)}`);
    await jobStore.sendLog();
    await jobStore.update({
      Status: "Failed",
      EndTime: new Date().getTime(),
    })
  }
};

const runJobApi = async (req, res) => {
  await Bg.initDB();
  const { jobId, pipelineId, additionData, thisJobOnly, error } = req.body || {};

  try {
    await runJob({ jobId, pipelineId, additionData, thisJobOnly, error });
  } catch (error) {
    res.json({
      success: false,
      message: error?.message || (typeof error === 'string' ? error : JSON.stringify(error)),
    });
    return;
  }

  res.json({
    success: true,
  });
};

export default nextjs2api(runJobApi, {
  url: '/api/bg/pipeline/run-job',
  method: 'POST',
  checkAuthen: true,
  apiSchema: {
    body: Joi.object({
      jobId: Joi.string().required(),
      pipelineId: Joi.number().required(),
      additionData: Joi.any().optional(),
      thisJobOnly: Joi.boolean().optional(),
      error: Joi.string().optional(),
    }),
  }
});
