import Joi from "joi";
import Bg from "../@utils/Bg";
import { nextjs2api } from 'api/api-middlewares'
import moment from "moment";
import { Op } from "sequelize";

export const listPipeline = async ({ limit, offset, sort, startDate, endDate, orderId, searchByOrderId }: {
  limit: number;
  offset: number;
  sort?: string;
  startDate?: string;
  endDate?: string;
  orderId?: string;
  searchByOrderId?: string;
}) => {
  const query: any = {
    limit,
    offset,
    order: [
      [sort || 'StartTime', 'DESC'],
    ],
    where: {},
    logging: console.log,
  };
  if (startDate && endDate) {
    const reformatStartDate = moment(startDate, startDate.includes('/') ? 'DD/MM/YYYY' : 'YYYY-MM-DD').toDate().getTime();
    const reformatEndDate = moment(endDate, endDate.includes('/') ? 'DD/MM/YYYY' : 'YYYY-MM-DD').add(1, 'days').toDate().getTime();
    query.where.StartTime = {
      [Op.gte]: reformatStartDate,
    }
    query.where.EndTime = {
      [Op.gte]: reformatEndDate,
    }
  }
  if (orderId) query.where.OrderId = orderId;
  if (searchByOrderId) query.where.OrderId = { [Op.like]: `%${searchByOrderId}%` };

  const data = await Bg.Pipeline.findAndCountAll(query);
  data.rows = Bg.mapNocoId(data.rows);
  return data;
};

const listPipelineApi = async (req, res) => {
  await Bg.initDB();
  const { limit, offset } = req.query;
  if (!limit || (!offset && String(offset) !== '0')) {
    res.json({ success: false });
    return;
  }
  const data = await listPipeline(req.query);

  res.json({
    success: true,
    data: {
      list: data.rows,
      pageInfo: {
        totalRows: data.count,
        page: Number(offset) / Number(limit) + 1,
        pageSize: Number(limit),
        isFirstPage: Number(offset) === 0,
        isLastPage: Number(offset) + Number(limit) >= data.count,
      }
    },
  });
};

export default nextjs2api(listPipelineApi, {
  url: '/api/bg/pipeline/list',
  method: 'GET',
  checkAuthen: true,
  apiSchema: {
    query: Joi.object({
      limit: Joi.number().required(),
      offset: Joi.number().required(),
      sort: Joi.string().optional(),
      startDate: Joi.string().optional(),
      endDate: Joi.string().optional(),
      orderId: Joi.string().optional(),
      searchByOrderId: Joi.string().optional(),
    }),
  }
});
