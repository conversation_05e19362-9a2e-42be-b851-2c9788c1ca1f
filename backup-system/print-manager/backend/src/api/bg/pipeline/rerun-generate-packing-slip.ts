import Joi from "joi";
import Bg from "../@utils/Bg";
import { nextjs2api } from 'api/api-middlewares'
import JobStore from "../@utils/JobStore";
import { generatePackingSlip } from "./handleOrder/s3.generatePackingSlip";

export const rerunGeneratePackingSlip = async ({ pipelineId }: {
  pipelineId: number;
}) => {
  if (!pipelineId) {
    throw "Invalid request";
  }
  const pipeline = await Bg.Pipeline.findById(pipelineId);
  if (!pipeline?.Id) {
    throw "Pipeline not found";
  }
  if (!pipeline?.OrderId) {
    throw "OrderId not found"; 
  }
  const order = await Bg.Order.findByOrderID(pipeline.OrderId);
  if (!order?.Id) {
    throw "Order not found";
  }

  const existedJob = Object.keys(pipeline?.Jobs || {}).find(v => {
    return pipeline?.Jobs?.[v]?.Title === generatePackingSlip.title;
  });

  const jobStore = new JobStore({
    data: existedJob ? pipeline?.Jobs?.[existedJob] : {
      StageId: `s3`,
      Idx: 0,
      Title: generatePackingSlip.title,
    },
    projectSlug: 'bg',
    pipelineId: Number(pipeline.Id),
    jobId: existedJob || `s3_${Date.now()}_0`,
  });

  await jobStore.getInitialLog();
  try {
    await jobStore.update({
      Status: "Running", 
      StartTime: new Date().getTime(),
    })
    await generatePackingSlip.handler({ order }, jobStore);
    await jobStore.sendLog();
    await jobStore.update({
      Status: "Success",
      EndTime: new Date().getTime(),
    })
  } catch (error) {
    await jobStore.log(`[__ERROR__] ${error?.message || JSON.stringify(error)}`);
    await jobStore.sendLog();
    await jobStore.update({
      Status: "Error",
      EndTime: new Date().getTime(),
    })
  }
};

const rerunGeneratePackingSlipApi = async (req, res) => {
  await Bg.initDB();
  const { pipelineId } = req.body || {};

  try {
    await rerunGeneratePackingSlip({ pipelineId });
  } catch (error) {
    res.json({
      success: false,
      message: error?.message || (typeof error === 'string' ? error : JSON.stringify(error)),
    });
    return;
  }

  res.json({
    success: true,
  });
};

export default nextjs2api(rerunGeneratePackingSlipApi, {
  url: '/api/bg/pipeline/rerun-generate-packing-slip',
  method: 'POST',
  checkAuthen: true,
  apiSchema: {
    body: Joi.object({
      pipelineId: Joi.number().required(),
    }),
  }
});
