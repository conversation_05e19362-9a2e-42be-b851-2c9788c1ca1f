import { TStageTemplate } from 'type/TPipelineTemplate';
import { checkTestOrderAndCanBeProcessed } from './handleOrder/s0.checkTestOrderAndCanBeProcessed';
import { generatePDF } from './handleOrder/s1.generatePDF';
import { generateCandleSticker } from './handleOrder/s2.generateCandleSticker';
import { generatePackingSlip } from './handleOrder/s3.generatePackingSlip';
import { awaitAcceptance } from './handleOrder/s4.awaitAcceptance';
import { dispatchToRoyalMail } from './handleOrder/s5.dispatchToRoyalMail';
import { awaitFulfillment } from './handleOrder/s6.awaitFulfillment';

const stage0: TStageTemplate = {
  title: "Validation",
  jobs: [
    checkTestOrderAndCanBeProcessed,
  ]
}

const stage1: TStageTemplate = {
  title: "Pre-processed",
  jobs: [
    generatePDF,
  ],
}

const stage2: TStageTemplate = {
  title: "Candle Sticker",
  jobs: [
    generateCandleSticker,
  ],
}

const stage3: TStageTemplate = {
  title: "Packing slip",
  jobs: [
    generatePackingSlip
  ],
}

const stage4: TStageTemplate = {
  title: "Acceptance",
  jobs: [
    awaitAcceptance
  ]
}

const stage5: TStageTemplate = {
  title: "Dispatch to RoyalMail",
  jobs: [
    dispatchToRoyalMail,
  ]
}

const stage6: TStageTemplate = {
  title: "Fulfilled",
  jobs: [
    awaitFulfillment,
  ]
}

export const handleOrder = [
  stage0,
  stage1,
  stage2,
  stage3,
  stage4,
  stage5,
  stage6,
];
