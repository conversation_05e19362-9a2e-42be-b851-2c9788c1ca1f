import Joi from "joi";
import Bg from "../@utils/Bg";
import { nextjs2api } from 'api/api-middlewares'
import { TCMSPipeline, TCMSStage, TJobStatus } from "type/TCMSPipeline";
import { handleOrder } from "./HandleOrder";
import { getLastestJobs, getUnDoneJob } from "../@utils/PipelineHelper";
import StageStore from "../@utils/StageStore";
import JobStore from "../@utils/JobStore";

export const continuePipeline = async ({ pipelineId, pipeline: pipelineData }: {
  pipelineId?: number;
  pipeline?: TCMSPipeline;
}) => {
  let pipeline = pipelineData;
  if (!pipeline && pipelineId) {
    pipeline = await Bg.Pipeline.findById(pipelineId);
  }
  if (!pipeline?.OrderId || !pipeline?.Id) {
    throw "Pipeline not found";
  }
  const order = await Bg.Order.findByOrderID(pipeline?.OrderId);
  if (!order?.Id) {
    throw "Order not found";
  }

  let stages: { [key: string]: TCMSStage } = {};
  handleOrder.forEach((i, idx) => {
    stages[`s${idx}`] = {
      Title: i.title,
      Jobs: i.jobs?.map(i => i.title),
      ...pipeline.Stages?.[`s${idx}`],
    };
  })

  const lastestJobs = getLastestJobs(pipeline);
  const preStageStatus: TJobStatus[] = [];

  for (let i = 0; i < handleOrder.length; i++) {
    const stageId = `s${i}`;
    // skip the stage if succeeded
    if (pipeline.Stages?.[stageId]?.Status === "Success") continue;

    const stageStore = new StageStore({
      data: { ...stages[stageId] },
      pipelineId: Number(pipeline.Id),
      stageId,
    });

    if (preStageStatus.includes("Failed")) {
      await stageStore.update({
        Status: 'Cancelled',
      });
      continue;
      // failed fast: skip stage if has failed
    }
    if (preStageStatus.includes("Pending")) {
      await stageStore.update({
        Status: 'Pending',
      });
      continue;
    }

    await stageStore.update({
      StartTime: new Date().getTime(),
      Status: 'Running',
    });

    // run each job inside stage
    try {
      const ts = new Date().getTime();
      const results = await Promise.all(
        handleOrder[i].jobs.map((job, idx) => {
          // skip the job if succeeded
          if (lastestJobs[`s${i}_${idx}`]?.Status === "Success"
            || lastestJobs[`s${i}_${idx}`]?.allStatus?.includes("Success")
          ) {
            return new Promise(resolve => resolve("Success"));
          }
          const existedJob = getUnDoneJob(pipeline, `s${i}_${idx}`)
          const jobStore = new JobStore({
            data: existedJob ? existedJob : {
              StageId: `s${i}`,
              Idx: idx,
              Title: job.title,
            },
            projectSlug: 'bg',
            pipelineId: Number(pipeline.Id),
            jobId: existedJob ? existedJob.jobId : `s${i}_${ts}_${idx}`,
          });

          return new Promise(async (resolve) => {
            // initialize job as Pending status, to avoid the case
            // when after await job.handlerBeforeCallback it run both api continue and add job data
            // api continue run but job data not yet been saved to the pipeline
            await jobStore.update({
              Status: "Pending",
              StartTime: new Date().getTime(),
            })
            try {
              await jobStore.getInitialLog();
              let shouldSkipCallback = true;
              if (job.callbackUrl) {
                shouldSkipCallback = false;
                if (job.handlerBeforeCallback) {
                  await jobStore.log(`[__handlerBeforeCallback__]: ${new Date().toISOString()}`)
                  const beforeCallbackResult = await job.handlerBeforeCallback({ order }, jobStore);
                  if (beforeCallbackResult?.shouldSkipCallback) {
                    shouldSkipCallback = true;
                  }
                }
              }
              if (!shouldSkipCallback) {
                await jobStore.sendLog();
                await jobStore.update({
                  Status: "Pending",
                })
                resolve("Pending");
                return;
              }
              await jobStore.update({
                Status: "Running",
                StartTime: new Date().getTime(),
              })
              await jobStore.log(`[__handler__]: ${new Date().toISOString()}`)
              await job.handler({ order }, jobStore);
              await jobStore.sendLog();
              await jobStore.update({
                Status: "Success",
                EndTime: new Date().getTime(),
              })
              resolve("Success");
            } catch (error) {
              await jobStore.log(`[__ERROR__] ${error?.message || JSON.stringify(error)}`);
              await jobStore.sendLog();
              await jobStore.update({
                Status: "Failed",
                EndTime: new Date().getTime(),
              })
              resolve("Failed");
            }
          });
        })
      )

      let stageStatus = "Success";
      if (results.includes("Failed")) stageStatus = "Failed";
      if (results.includes("Pending")) stageStatus = "Pending";

      await stageStore.update({
        EndTime: new Date().getTime(),
        Status: stageStatus,
      });
      preStageStatus.push(stageStatus);
    } catch (error) {
      await stageStore.update({
        Status: "Failed",
        EndTime: new Date().getTime(),
      })
      preStageStatus.push("Failed");
    }
  }

  // calculate pipeline status
  if (preStageStatus.includes("Failed")) {
    await Bg.Pipeline.updateById(pipeline?.Id, {
      Status: "Failed",
      EndTime: new Date().getTime(),
    });
  } else if (preStageStatus.includes("Pending")) {
    await Bg.Pipeline.updateById(pipeline?.Id, {
      Status: "Pending",
    });
  } else {
    await Bg.Pipeline.updateById(pipeline?.Id, {
      Status: "Success",
      EndTime: new Date().getTime(),
    });
  }
};

const continuePipelineApi = async (req, res) => {
  await Bg.initDB();
  const { pipelineId } = req.body || {};

  try {
    await continuePipeline({ pipelineId });
  } catch (error) {
    res.json({
      success: false,
      message: error?.message || (typeof error === 'string' ? error : JSON.stringify(error)),
    });
    return;
  }

  res.json({
    success: true,
  });
};

export default nextjs2api(continuePipelineApi, {
  url: '/api/bg/pipeline/continue',
  method: 'POST',
  checkAuthen: true,
  apiSchema: {
    body: Joi.object({
      pipelineId: Joi.number().required(),
    }),
  }
});
