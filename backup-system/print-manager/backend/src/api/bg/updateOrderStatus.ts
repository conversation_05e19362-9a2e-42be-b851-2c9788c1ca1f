import Joi from "joi";
import Bg from "./@utils/Bg";
import { nextjs2api } from 'api/api-middlewares'
import { isUndefined, omitBy } from "lodash";
import { runJob } from "./pipeline/run-job";

export const updateOrderStatus = async ({ id, orderId, Status, Stage, StageStatus }: {
  id?: number;
  orderId?: string;
  Status?: string;
  Stage?: string;
  StageStatus?: string;
}) => {
  let data;
  const params = omitBy({
    Status,
    Stage,
    StageStatus,
  }, isUndefined);

  if (orderId) {
    data = await Bg.Order.updateByOrderId(orderId, params);
  } else {
    data = await Bg.Order.updateById(id, params);
  }

  if (Status === 'Accepted' || Status === 'Fulfilled') {
    const order = !!orderId ? await Bg.Order.findByOrderID(orderId) : await Bg.Order.findByID(id);
    if (order?.Id) {
      const pipeline = await Bg.Pipeline.findUndoneByOrderId(String(order["Order ID"]));
      if (!!pipeline && !!pipeline.Jobs) {
        let jobId;
        // some special condition to get the job
        Object.keys(pipeline.Jobs).forEach(i => {
          if (jobId) return;
          const jobData = pipeline.Jobs?.[i];
          if (!jobData) return;

          if (
            (Status === 'Accepted' && jobData.Status === "Pending" && jobData.Title === "Await acceptance") ||
            (Status === 'Fulfilled' && jobData.Status === "Pending" && jobData.Title === "Await fulfillment")
          ) {
            jobId = i;
          }
        });
        if (jobId) {
          await runJob({
            jobId,
            pipelineId: pipeline.Id,
          })
        }
      }
    }
  }
  return data
};

const updateOrderStatusApi = async (req, res) => {
  await Bg.initDB();
  const { Status, Stage, StageStatus } = req.body;
  const id = req.body.id || req.query.id;
  const orderId = req.body.orderId || req.query.orderId;
  const result = await updateOrderStatus({ id, orderId, Status, Stage, StageStatus });
  res.json({
    success: true,
    data: result,
  });
};

export default nextjs2api(updateOrderStatusApi, {
  url: '/api/bg/updateOrderStatus',
  method: 'POST',
  checkAuthen: true,
  apiSchema: {
    body: Joi.object({
      Stage: Joi.string().allow(''),
      StageStatus: Joi.string().allow(''),
      Status: Joi.string().allow(''),
      id: Joi.string().allow(''),
      orderId: Joi.string().allow(''),
    }),
    query: Joi.object({
      id: Joi.string().optional(),
      orderId: Joi.string().optional(),
    }),
  }
});
