import Joi from "joi";
import moment from "moment";
import Bg from "./@utils/Bg";
import { nextjs2api } from 'api/api-middlewares';

const orderReportApi = async (req, res) => {
  await Bg.initDB();
  const clientId = req.query?.clientId;
  const env = req.query?.env;

  let pendingOrder = 0;
  let unpaidOrder = 0;
  let unpaidOrderThisYear = 0;
  let acceptedOrder = 0;
  let pendingOrderToday = 0;
  let acceptedOrderToday = 0;
  let fulfilledOrder = 0;
  let fulfilledOrderWithIn28d = 0;
  let totalOrder = 0;
  let totalOrderValueMonthly = 0;
  let totalProcessedValueMonthly = 0;
  let countByClient = {};

  const allOrders = await Bg.Order.getAllList({ clientId, env });

  const totalProcessedItemsValue = allOrders.reduce((total, order) => {
    const isThisMonth = moment(order.CreatedAt || order.created_at).isSame(moment(), "month");

    const totalItemsValue = order["Raw Data"]?.line_items?.reduce((t, lineItem) => {
      if (!order["Other Data"]?.isTestOrder && order["Item Processed"]?.includes(String(lineItem.id))) {
        if (isThisMonth) {
          totalProcessedValueMonthly += Number(lineItem.price) * Number(lineItem.quantity);
        }
        return t + Number(lineItem.price) * Number(lineItem.quantity);
      }
      return t;
    }, 0);
    return total + totalItemsValue;
  }, 0)

  const totalOrderPrice = allOrders.reduce((total, order) => {
    if (order["Other Data"]?.isTestOrder) return total;
    const isThisMonth = moment(order.CreatedAt || order.created_at).isSame(moment(), "month");
    if (isThisMonth) {
      totalOrderValueMonthly += Number(order["Raw Data"]?.total_price);
    }
    return total + Number(order["Raw Data"]?.total_price);
  }, 0);

  const orderByMonth = {};
  const fulfilledByMonth = {};
  const awaitingPaymentOrders = allOrders.filter(order => order.StageStatus === "Awaiting Payment" || order.Stage === "Pre Production");
  const awaitingPaymentPipelines = await Bg.Pipeline.getByOrderIds(awaitingPaymentOrders.map(i => String(i["Order ID"])));

  allOrders.forEach(order => {
    if (order["Other Data"]?.isTestOrder) return;
    if (!order["Item Supported"]?.trim()) return;
    if (order.inactive) return;

    totalOrder += 1;

    const clientId = order["Client ID"] || "unknown";
    const clientName = order["Client Name"] || "Unknown";
    countByClient[clientId] = {
      ...countByClient[clientId],
      clientId,
      clientName,
      total: (countByClient[clientId]?.total || 0) + 1,
    }

    const month = moment(order.CreatedAt || order.created_at).format("YYYYMM");
    orderByMonth[month] = (orderByMonth[month] || 0) + 1;
    if (order.Stage === "Fulfillment") {
      fulfilledOrder += 1;
      fulfilledByMonth[month] = (fulfilledByMonth[month] || 0) + 1;
      countByClient[clientId] = {
        ...countByClient[clientId],
        fulfilled: (countByClient[clientId]?.fulfilled || 0) + 1,
      }
      if (moment(order.CreatedAt || order.created_at).isAfter(moment().subtract(28, "d").startOf("d"))) {
        fulfilledOrderWithIn28d += 1;
      }
    }
    if (order.Stage === "Pre Production") {
      const _pipeline = awaitingPaymentPipelines.find(i => i.OrderId === order["Order ID"]);
      if (!!_pipeline) {
        if (!_pipeline?.SharedData?.skipped) {
          if (!_pipeline?.SharedData?.isSampleRequest || order.StageStatus !== "Awaiting Payment") {
            pendingOrder += 1;
          }
        }
      }
    }
    if (order.StageStatus === "Awaiting Payment") {
      unpaidOrder += 1;
      const isThisYear = moment(order.CreatedAt || order.created_at).isSame(moment(), "year");

      const _pipeline = awaitingPaymentPipelines.find(i => i.OrderId === order["Order ID"]);
      if (!!_pipeline) {
        if (!_pipeline?.SharedData?.skipped) {
          if (_pipeline?.SharedData?.isSampleRequest) {
            // sample and unpaid order
          } else {
            if (isThisYear) {
              unpaidOrderThisYear += 1;
            }
          }
        }
      }
    }
    if (order.Stage === "In Production") {
      acceptedOrder += 1;
    }
    if (moment(order.CreatedAt || order.created_at).isSame(moment(), "d")) {
      if (order.Stage === "Pre Production") {
        pendingOrderToday += 1;
      }
      if (order.Stage === "In Production") {
        acceptedOrderToday += 1;
      }
    }
  })

  res.json({
    success: true,
    data: {
      pendingOrder,
      unpaidOrder,
      unpaidOrderThisYear,
      acceptedOrder,
      pendingOrderToday,
      acceptedOrderToday,
      fulfilledOrder,
      fulfilledOrderWithIn28d,
      totalOrder,
      totalOrderPrice,
      totalProcessedItemsValue,
      orderByMonth,
      fulfilledByMonth,
      totalOrderValueMonthly,
      totalProcessedValueMonthly,
      countByClient,
    },
  });
};

export default nextjs2api(orderReportApi, {
  url: '/api/bg/order-report',
  method: 'GET',
  checkAuthen: true,
  apiSchema: {
    query: Joi.object({
      clientId: Joi.string().optional(),
      env: Joi.string().optional()
    })
  }
});
