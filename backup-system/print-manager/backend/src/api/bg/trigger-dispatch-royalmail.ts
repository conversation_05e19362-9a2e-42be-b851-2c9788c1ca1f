import Joi from "joi";
import Bg from "./@utils/Bg";
import { nextjs2api } from 'api/api-middlewares'
import JobStore from "./@utils/JobStore";
import { dispatchToRoyalMail } from "./pipeline/handleOrder/s5.dispatchToRoyalMail";

export const triggerDispatchRoyalMail = async ({ id, orderId, serviceCode, force }: {
  id?: number;
  orderId?: string;
  serviceCode?: string;
  force?: boolean;
}) => {
  let data;

  const order = !!orderId ? await Bg.Order.findByOrderID(orderId) : await Bg.Order.findByID(id);
  if (!order?.Id) {
    throw new Error("Order not found");
  }
  console.log('triggerDispatchRoyalMail order', order);
  let pipeline;
  if (force) {
    pipeline = await Bg.Pipeline.findByOrderId(String(order["Order ID"]));
  } else {
    pipeline = await Bg.Pipeline.findUndoneByOrderId(String(order["Order ID"]));
  }
  if (!pipeline?.Id) {
    throw new Error("Pipeline not found");
  }
  if (!pipeline?.OrderId) {
    throw new Error("OrderId not found");
  }
  const existedJob = Object.keys(pipeline?.Jobs || {}).find(v => {
    return pipeline?.Jobs?.[v]?.Title === "Dispatch to RoyalMail";
  });
  const jobStore = new JobStore({
    data: existedJob ? pipeline?.Jobs?.[existedJob] : {
      StageId: `s5`,
      Idx: 0,
      Title: "Dispatch to RoyalMail",
    },
    projectSlug: 'bg',
    pipelineId: Number(pipeline.Id),
    jobId: existedJob || `s5_${Date.now()}_0`,
  });
  await jobStore.getInitialLog();
  try {
    await jobStore.update({
      Status: "Running",
      StartTime: new Date().getTime(),
    })
    await dispatchToRoyalMail.handler({ order }, jobStore);
    await jobStore.sendLog();
    await jobStore.update({
      Status: "Success",
      EndTime: new Date().getTime(),
    })
  } catch (error) {
    await jobStore.log(`[__ERROR__] ${error?.message || JSON.stringify(error)}`);
    await jobStore.sendLog();
    await jobStore.update({
      Status: "Error",
      EndTime: new Date().getTime(),
    })
  }
  return data
};

const triggerDispatchRoyalMailApi = async (req, res) => {
  await Bg.initDB();
  const { id, orderId, serviceCode, force } = req.query;

  try {
    const result = await triggerDispatchRoyalMail({ id, orderId, serviceCode, force });
    res.json({
      success: true,
      data: result,
    });
  } catch (error) {
    res.json({
      success: false,
      message: error?.message || JSON.stringify(error),
    });
  }
};

export default nextjs2api(triggerDispatchRoyalMailApi, {
  url: '/api/bg/trigger-dispatch-royalmail',
  method: 'POST',
  checkAuthen: true,
  apiSchema: {
    query: Joi.object({
      id: Joi.string().optional(),
      orderId: Joi.string().optional(),
      serviceCode: Joi.string().optional(),
      force: Joi.boolean().optional(),
    }),
  }
});
