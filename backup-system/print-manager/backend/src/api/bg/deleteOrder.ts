import Joi from "joi";
import Bg from "./@utils/Bg";
import { nextjs2api } from 'api/api-middlewares'

const deleteOrderApi = async (req, res) => {
  await Bg.initDB();
  const { ids, deletedBy } = req.body;

  for (let i = 0; i < ids.length; i++) {
    await Bg.Order.updateById(ids[i], { inactive: true, deletedBy });
  }

  res.json({
    success: true,
  });
};

export default nextjs2api(deleteOrderApi, {
  url: '/api/bg/deleteOrder',
  method: 'POST', 
  checkAuthen: true,
  apiSchema: {
    body: Joi.object({
      ids: Joi.array(),
      deletedBy: Joi.string(),
    }),
  }
});
