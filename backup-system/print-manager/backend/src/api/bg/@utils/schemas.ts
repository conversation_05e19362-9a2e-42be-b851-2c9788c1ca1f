import { BuildOptions, Model, DataTypes, Sequelize } from "sequelize";

const tableOrderSchema = {
  table_name: `Orders`,
  columns: [
    {
      nocodb: { column_name: 'id', uidt: 'ID', title: 'Id', ai: true, pk: true, },
      db: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true, }
    },
    {
      nocodb: {
        column_name: 'created_at', uidt: 'DateTime', title: 'CreatedAt', dt: 'timestamp with time zone', cdf: 'now()', dtx: 'timestamp without time zone', dtxp: '6'
      },
      db: { timestamps: true, createdAt: "created_at", }
    },
    {
      nocodb: { column_name: 'updated_at', uidt: 'DateTime', title: 'UpdatedAt', dt: 'timestamp with time zone', cdf: 'now()', dtx: 'timestamp without time zone', dtxp: '6', au: true },
      db: { timestamps: true, updatedAt: "updated_at", }
    },
    {
      nocodb: { column_name: 'Order ID', uidt: 'SingleLineText' },
      db: { type: DataTypes.TEXT, }
    },
    {
      nocodb: { column_name: 'Order Source ID', uidt: 'SingleLineText' },
      db: { type: DataTypes.TEXT, }
    },
    {
      nocodb: { column_name: 'Order Name' },
      db: { type: DataTypes.TEXT, }
    },
    {
      nocodb: { column_name: 'Order Number', uidt: 'Number' },
      db: { type: DataTypes.FLOAT, }
    },
    {
      nocodb: { column_name: 'Customer Email' },
      db: { type: DataTypes.TEXT, }
    },
    {
      nocodb: { column_name: 'All Item IDs', uidt: 'LongText' },
      db: { type: DataTypes.TEXT, }
    },
    {
      nocodb: {
        column_name: 'All Product Names', uidt: 'LongText',
      },
      db: { type: DataTypes.TEXT, }
    },
    {
      nocodb: { column_name: 'All Product Names', uidt: 'LongText' },
      db: { type: DataTypes.TEXT, }
    },
    {
      nocodb: { column_name: 'Customer Name', uidt: 'SingleLineText' },
      db: { type: DataTypes.TEXT, }
    },
    {
      nocodb: { column_name: 'Item Processed', uidt: 'LongText' },
      db: { type: DataTypes.TEXT, }
    },
    {
      nocodb: { column_name: 'Item Supported', uidt: 'LongText' },
      db: { type: DataTypes.TEXT, }
    },
    {
      nocodb: { column_name: 'Raw Data', uidt: 'JSON' },
      db: { type: DataTypes.JSON, }
    },
    {
      nocodb: { column_name: 'Other Data', uidt: 'JSON' },
      db: { type: DataTypes.JSON, }
    },
    {
      nocodb: {
        column_name: 'Status', uidt: 'SingleSelect',
        options: [

        ]
      },
      db: {
        type: DataTypes.TEXT,
      }
    },
    {
      nocodb: { column_name: 'Stage', uidt: 'SingleSelect' },
      db: {
        type: DataTypes.TEXT,
      }
    },
    {
      nocodb: { column_name: 'env', uidt: 'SingleLineText' },
      db: {
        type: DataTypes.TEXT,
      }
    },
    {
      nocodb: { column_name: 'Store ID', uidt: 'SingleLineText' },
      db: {
        type: DataTypes.TEXT,
      }
    },
    {
      nocodb: { column_name: 'Client ID', uidt: 'SingleLineText' },
      db: {
        type: DataTypes.TEXT,
      }
    },
    {
      nocodb: { column_name: 'Client Name', uidt: 'SingleLineText' },
      db: {
        type: DataTypes.TEXT,
      }
    },
    {
      nocodb: {
        column_name: 'OrderType', uidt: 'SingleSelect',
        options: [
          "Sample",
          "Shopify",
        ]
      },
      db: {
        type: DataTypes.TEXT,
      }
    },
    {
      nocodb: {
        column_name: 'StageStatus', uidt: 'SingleSelect',
        options: [
          "Awaiting Payment",
          "Queued For Production",
          "Held",
          "Held By Admin",
          "On Time",
          "Delayed",
        ]
      },
      db: {
        type: DataTypes.TEXT,
      }
    },
    {
      nocodb: { column_name: 'inactive', uidt: 'Checkbox' },
      db: {
        type: DataTypes.BOOLEAN,
      }
    },
    {
      nocodb: { column_name: 'Is Supported', uidt: 'Checkbox' },
      db: {
        type: DataTypes.BOOLEAN,
      }
    },
  ]
}

const tablePipelineSchema = {
  table_name: `Pipelines`,
  columns: [
    {
      nocodb: { column_name: 'id', uidt: 'ID', title: 'Id', ai: true, pk: true },
      db: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
    },
    {
      nocodb: { column_name: 'OrderId', uidt: 'SingleLineText' },
      db: { type: DataTypes.TEXT },
    },
    {
      nocodb: { column_name: 'Stages', uidt: 'SpecificDBType', dt: 'jsonb' },
      db: { type: DataTypes.JSONB },
    },
    {
      nocodb: { column_name: 'Jobs', uidt: 'SpecificDBType', dt: 'jsonb' },
      db: { type: DataTypes.JSONB },
    },
    {
      nocodb: { column_name: 'SharedData', uidt: 'SpecificDBType', dt: 'jsonb' },
      db: { type: DataTypes.JSONB },
    },
    {
      nocodb: { column_name: 'DeploymentAddress' },
      db: { type: DataTypes.TEXT },
    },
    {
      nocodb: { column_name: 'StartTime', uidt: 'Number' },
      db: { type: DataTypes.FLOAT },
    },
    {
      nocodb: { column_name: 'EndTime', uidt: 'Number' },
      db: { type: DataTypes.FLOAT },
    },
    {
      nocodb: { column_name: 'Status' },
      db: { type: DataTypes.TEXT },
    },
    // {
    //   db: { timestamps: true, createdAt: "created_at", }
    // },
    // {
    //   db: { timestamps: true, updatedAt: "updated_at", }
    // },
  ]
}

export const schemas = {
  Order: tableOrderSchema,
  Pipeline: tablePipelineSchema,
};
