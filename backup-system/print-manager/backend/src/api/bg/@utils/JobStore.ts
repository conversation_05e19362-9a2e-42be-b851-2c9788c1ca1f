import axios from 'axios';
import { TCM<PERSON>Job } from "type/TCMSPipeline";
import Bg from "./Bg";
import { AWSHelper } from 'helpers';

interface JobStoreProps {
  data: TCMSJob;
  projectSlug: string;
  pipelineId: number;
  jobId: string;
}

class JobStore {
  data: TCMSJob;
  logTxt: string = "";
  projectSlug: string;
  pipelineId: number;
  jobId: string;

  constructor(props: JobStoreProps) {
    this.data = { ...props.data };
    this.projectSlug = props.projectSlug;
    this.pipelineId = props.pipelineId;
    this.jobId = props.jobId;
  }

  getInitialLog = async () => {
    if (!this.data.Log) return;
    const res = await axios.get(this.data.Log);
    if (typeof res.data === 'string') {
      this.logTxt += res.data + '\n';
    }
  }

  log = async (...args: any[]) => {
    if (!args) return;
    console.log(...args);
    this.logTxt += `\n\n`;
    args.forEach(arg => {
      this.logTxt += `${typeof arg === "object" ? JSON.stringify(arg) : String(arg)}\n`;
    })
  }

  sendLog = async () => {
    const fileLogURL = await AWSHelper.uploadRaw({
      data: this.logTxt,
      key: `${this.projectSlug}/${this.jobId}_log.txt`,
      contentType: "text/plain",
    })
    this.data.Log = fileLogURL;
  }

  update = async (data: TCMSJob) => {
    this.data = Object.assign(this.data, data);
    await Bg.Pipeline.updateJob({
      data: { [this.jobId]: this.data },
      pipelineId: this.pipelineId,
    })
  }

  saveFiles = async (files: { name: string, url: string, data?: any }[]) => {
    const allFiles = this.data.Files || [];
    for (let i = 0; i < files.length; i++) {
      if (!files[i].url) continue;
      const isLocal = !files[i].url.includes('http');
      if (isLocal) {
        const uploadedUrl = await AWSHelper.upload({
          filePath: files[i].url,
          key: `${this.projectSlug}/pipeline-files/` + files[i].name,
        });
        allFiles.push({
          url: uploadedUrl,
          name: files[i].name,
          data: files[i].data,
        })
      } else {
        allFiles.push({
          url: files[i].url,
          name: files[i].name,
          data: files[i].data,
        })
      }
    };
  }

  getPipelineData = async () => {
    const res = await Bg.Pipeline.findById(this.pipelineId);
    return res;
  }

  getSharedData = async () => {
    const data = await this.getPipelineData();
    return data?.SharedData;
  }

  shareData = async (data: { [key: string]: any }) => {
    this.log(`[__Data_Shared__] ${JSON.stringify(data)}`)
    await Bg.Pipeline.updateSharedData({
      data,
      pipelineId: this.pipelineId,
    })
  }
}

export default JobStore;
