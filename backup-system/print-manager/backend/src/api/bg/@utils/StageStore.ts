import { TCMSStage } from "type/TCMSPipeline";
import Bg from "./Bg";

interface StageStoreProps {
  data: TCMSStage;
  pipelineId: number;
  stageId: string;
}

class StageStore {
  data: TCMSStage;
  stageId: string;
  pipelineId: number;

  constructor(props: StageStoreProps) {
    this.data = { ...props.data };
    this.stageId = props.stageId;
    this.pipelineId = props.pipelineId;
  }

  update = async (data: TCMSStage) => {
    this.data = Object.assign(this.data, data);
    await Bg.Pipeline.updateStage({
      data: { [this.stageId]: this.data },
      pipelineId: this.pipelineId,
    })
  }
}

export default StageStore;
