import { Model, Op } from "sequelize";
import { TBGCMSOrder } from "type/TShopifyOrder";
import { schemas } from "./schemas";
import { TCMSJob, TCMSPipeline, TCMSStage } from "type/TCMSPipeline";
import { isUndefined, omitBy } from "lodash";
import { DB as MainDB } from 'db';
let DB;

const cleanString = (inputString) => {
  const specialCharsRegex = /[‘’']/g;
  const cleanedString = inputString.replace(specialCharsRegex, '');
  return cleanedString;
}

const parseObjectNoco = (v) => {
  if (!v) return v;
  const data = v.get ? v.get({ plain: true }) : v.toJSON ? v.toJSON() : v;
  return {
    Id: data["id"],
    ...data,
    UpdatedAt: data["updated_at"],
    CreatedAt: data["created_at"],
  };
};
const mapObjectsNoco = (arr) => arr.map(parseObjectNoco);

class BG {

  nocoProject = 'p_7yqohwae0b0i1h';
  nocoDBPrefix = 'nc_euxw__';
  ready = false;

  async initDB() {
    if (this.ready || !!DB) return;
    await MainDB.microApisDescribeTableFromSchema('BG', this.nocoDBPrefix, schemas);
    // @ts-ignore
    // const { Order, Pipleine } = MainDB.BG;
    // DB = {
    //   BG: {
    //     Order,
    //     Pipleine,
    //   }
    // }
    DB = MainDB;
    this.ready = true;
  }

  mapNocoId = mapObjectsNoco;

  Order = {
    async create(payload: Partial<TBGCMSOrder>): Promise<TBGCMSOrder> {
      const newOrder = await DB.BG.Order.create(payload);
      return parseObjectNoco(newOrder);
    },
    async findAndCountAll(query: any) {
      return parseObjectNoco(await DB.BG.Order.findAndCountAll(query));
    },
    async getList(limit: number, offset: number): Promise<Array<TBGCMSOrder>> {
      return mapObjectsNoco(await DB.BG.Order.findAll({
        limit,
        offset,
      }));
    },
    async findByOrderIds(ids: string[]): Promise<Array<TBGCMSOrder>> {
      return mapObjectsNoco(await DB.BG.Order.findAll({
        where: {
          'Order ID': {
            [Op.in]: ids,
          }
        }
      }));
    },
    async getAllList(params?: { clientId?: string, fromDate?: string, toDate?: string, env?: string, }): Promise<Array<TBGCMSOrder>> {
      return mapObjectsNoco(await DB.BG.Order.findAll({
        where: omitBy({
          ["Client ID"]: params?.clientId || undefined,
          env: params?.env || undefined,
          created_at: (() => {
            if (!params?.fromDate || !params?.toDate) return undefined;
            return {
              [Op.gt]: params.fromDate,
              [Op.lt]: params.toDate,
            }
          })(),
          inactive: {
            [Op.not]: true,
          },
        }, isUndefined)
      }));
    },
    async getListByCondition(limit: number, offset: number, where) {
      return mapObjectsNoco(await DB.BG.Order.findAll({
        limit,
        offset,
        where,
      }));
    },
    async findByCustomerEmail(email): Promise<TBGCMSOrder | null> {
      return parseObjectNoco(await DB.BG.Order.findOne({
        where: {
          'Customer Email': email,
        }
      }))
    },
    findByID: async (id: number): Promise<TBGCMSOrder | null> => {
      const res = await DB.BG.Order.findByPk(id);
      if (!res) throw new Error("CMS call failed");
      return parseObjectNoco(res) as TBGCMSOrder;
    },
    findByOrderID: async (orderId: any): Promise<TBGCMSOrder | null> => {
      const res = await DB.BG.Order.findOne({
        where: {
          'Order ID': orderId,
        }
      })
      if (!res) throw new Error("CMS call failed");
      return parseObjectNoco(res) as TBGCMSOrder;
    },
    findByOrderNumber: async (orderNumber: number): Promise<TBGCMSOrder | null> => {
      const res = parseObjectNoco(await DB.BG.Order.findOne({
        where: {
          'Order Number': orderNumber,
        }
      }))
      if (!res) throw new Error("CMS call failed");
      return parseObjectNoco(res) as TBGCMSOrder;
    },
    findByOrderSourceID: async (orderSourceID: string): Promise<TBGCMSOrder | null> => {
      const res = parseObjectNoco(await DB.BG.Order.findOne({
        where: {
          'Order Source ID': String(orderSourceID),
        }
      }))
      return parseObjectNoco(res) as TBGCMSOrder | null;
    },
    updateByOrderId: async (
      orderId: any,
      data: Partial<TBGCMSOrder>
    ): Promise<TBGCMSOrder | null> => {
      const order = await DB.BG.Order.findOne({
        where: {
          'Order ID': orderId,
        },
      })
      if (!order?.id) throw new Error("Order not found");
      Object.keys(data).forEach(key => {
        if (data[key] !== undefined) order[key] = data[key];
      });
      await order.save();
      return parseObjectNoco(order);
    },
    updateById: async (
      id: number,
      data: Partial<TBGCMSOrder>
    ): Promise<TBGCMSOrder | null> => {
      const order = await DB.BG.Order.findByPk(id);
      if (!order?.id) throw new Error("Order not found");
      Object.keys(data).forEach(key => {
        if (data[key] !== undefined) order[key] = data[key];
      });
      await order.save();
      return parseObjectNoco(order);
    },
    count: async (where: any) => {
      return await DB.BG.Order.count({
        where,
      });
    },
    deleteOrder: async (id: number) => {
      const order = await DB.BG.Order.findByPk(id);
      if (!order) throw new Error("Order not found");
      await order.destroy();
    }
  }

  Pipeline = {
    updateSharedData: async (params: { data: { [key: string]: any }, pipelineId: number }) => {
      const pipeline = await DB.BG.Pipeline.findByPk(params.pipelineId);
      if (!pipeline) throw new Error("Pipeline not found");
      const newCanBeProcessedItems = params.data.canBeProcessedItems;
      let canBeProcessedItems = pipeline.SharedData?.canBeProcessedItems;
      if (canBeProcessedItems?.length) {
        if (newCanBeProcessedItems?.length) {
          const isSameItems = canBeProcessedItems.length === newCanBeProcessedItems.length
            && canBeProcessedItems.every(item => newCanBeProcessedItems.some(i => i.id === item.id));
          if (!isSameItems) {
            canBeProcessedItems = newCanBeProcessedItems;
          } else {
            canBeProcessedItems = canBeProcessedItems.map(item => ({
              ...item,
              ...(newCanBeProcessedItems.find(i => i.id === item.id) || {})
            }))
          }
        }
      } else {
        canBeProcessedItems = newCanBeProcessedItems;
      }
      const newSharedData = {
        ...pipeline.SharedData,
        ...params.data,
        canBeProcessedItems,
      }
      pipeline.SharedData = newSharedData;
      return await pipeline.save();
    },
    updateJob: async (params: { data: { [key: string]: TCMSJob }, pipelineId: number }) => {
      const command = `UPDATE "${this.nocoDBPrefix}_Pipelines" SET "Jobs" = "Jobs" || '${cleanString(JSON.stringify(params.data))}' WHERE id = ${params.pipelineId}`;
      return await DB.BG.Pipeline.sequelize.query(command);
    },
    updateStage: async (params: { data: { [key: string]: TCMSStage }, pipelineId: number }) => {
      const command = `UPDATE "${this.nocoDBPrefix}_Pipelines" SET "Stages" = "Stages" || '${JSON.stringify(params.data)}' WHERE id = ${params.pipelineId}`;
      return await DB.BG.Pipeline.sequelize.query(command);
    },
    async getByOrderIds(ids: string[]): Promise<Array<TCMSPipeline>> {
      return mapObjectsNoco(await DB.BG.Pipeline.findAll({
        where: {
          OrderId: {
            [Op.in]: ids
          },
        },
        raw: true,
        // logging: console.log,
      }));
    },
    async getList(limit: number, offset: number, where = {}, sort = undefined): Promise<Array<TCMSPipeline>> {
      return mapObjectsNoco(await DB.BG.Pipeline.findAll({
        limit,
        offset,
        where,
        order: sort,
        raw: true,
      }));
    },
    async findAndCountAll(query: any) {
      return await DB.BG.Pipeline.findAndCountAll(query);
    },
    async create(data: TCMSPipeline) {
      const pipeline = await DB.BG.Pipeline.create({
        ...data,
      });
      return parseObjectNoco(pipeline);
    },
    async bulkCreate(data: TCMSPipeline[]) {
      return await DB.BG.Pipeline.bulkCreate(data);
    },
    async findUndoneByOrderId(orderId: any): Promise<TCMSPipeline | null> {
      const pipeline = await DB.BG.Pipeline.findOne({
        where: {
          OrderId: orderId,
          Status: {
            [Op.not]: 'Success'
          }
        }
      });
      return parseObjectNoco(pipeline);
    },
    findByOrderId: async (orderId: string): Promise<TCMSPipeline | null> => {
      const pipeline = await DB.BG.Pipeline.findOne({
        where: {
          OrderId: orderId,
        },
      })
      if (!pipeline?.id) return null;
      return parseObjectNoco(pipeline);
    },
    findById: async (pipelineId: number): Promise<TCMSPipeline | null> => {
      const pipeline = await DB.BG.Pipeline.findByPk(pipelineId);
      if (!pipeline?.id) return null;
      return parseObjectNoco(pipeline);
    },
    updateById: async (
      pipelineId: number,
      data: Partial<TCMSPipeline>
    ): Promise<TCMSPipeline | null> => {
      const pipeline = await DB.BG.Pipeline.findByPk(pipelineId);
      if (!pipeline?.id) throw new Error("Order not found");
      Object.keys(data).forEach(key => {
        if (data[key] !== undefined) pipeline[key] = data[key];
      });
      await pipeline.save();
      return parseObjectNoco(pipeline);
    },
    deleteByOrderId: async (orderId: any) => {
      const pipelines = await DB.BG.Pipeline.findAll({
        where: {
          OrderId: orderId,
        }
      });
      if (pipelines?.length) {
        await Promise.all(pipelines.map(pipeline => pipeline.destroy()));
      }
    }
  }
}

export default new BG();
