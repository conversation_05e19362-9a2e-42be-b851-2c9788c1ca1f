import { TShopifyOrder } from "type";
import { TEtsyReceipt } from "type";
import { TCMSJob } from "type/TCMSPipeline";
import { TCMSPipeline } from "type/TCMSPipeline";
import moment from "moment";

export type TLastestJob = TCMSJob & {
  allStatus?: string[];
}

export const getLastestJobs = (pipeline: TCMSPipeline) => {
  const jobsWithTs = {};
  const jobStatus = {};
  const lastestJobs: { [key: string]: TLastestJob } = {};
  Object.keys(pipeline?.Jobs || {}).forEach(key => {
    if (!pipeline.Jobs?.[key]) return;
    const [stageId, ts, idx] = key.split("_");
    const jobIdx = `${stageId}_${idx}`;
    const lastTs = Number(jobsWithTs[jobIdx] || 0);
    if (lastTs < Number(ts)) {
      lastestJobs[jobIdx] = pipeline.Jobs?.[key];
      jobsWithTs[jobIdx] = Number(ts);
    }
    const arr = jobStatus[jobIdx] || [];
    if (!arr.includes(pipeline.Jobs?.[key]?.Status)) {
      arr.push(pipeline.Jobs?.[key]?.Status);
      jobStatus[jobIdx] = arr;
    }
  })
  Object.keys(lastestJobs).forEach(key => {
    lastestJobs[key].allStatus = jobStatus[key];
  })
  return lastestJobs;
}

export const getUnDoneJob = (pipeline: TCMSPipeline, jobIdx: string) => {
  if (!jobIdx) return;
  let job;
  let jobId;
  Object.keys(pipeline?.Jobs || {}).forEach(key => {
    if (!pipeline.Jobs?.[key]) return;
    const [stageId, ts, idx] = key.split("_");
    if (jobIdx === `${stageId}_${idx}`) {
      job = pipeline?.Jobs?.[key]
      jobId = key
    }
  })
  if (!job || !jobId) return;
  return {
    ...job,
    jobId,
  };
}

export const etsyToShopifyFormat = (receipt: TEtsyReceipt): TShopifyOrder => {
  const [firstName, lastName] = receipt?.name?.split(' ') || [];
  const defaultAddress = {
    id: receipt.receipt_id,
    customer_id: receipt.buyer_user_id,
    first_name: firstName || "",
    last_name: lastName || "",
    company: null,
    address1: receipt.first_line,
    address2: receipt.second_line,
    city: receipt.city,
    province: receipt.state,
    country: receipt.country_iso === 'GB' ? 'United Kingdom' : receipt.country_iso,
    zip: receipt.zip,
    phone: null,
    name: receipt.name,
    province_code: receipt.state,
    country_code: receipt.country_iso,
    country_name: receipt.country_iso,
    default: true,
  };
  return {
    id: receipt.receipt_id,
    created_at: moment.unix(receipt.create_timestamp).format('YYYY-MM-DDTHH:mm:ssZ'),
    currency: receipt?.grandtotal?.currency_code || "GBP",
    contact_email: receipt?.seller_email,
    // @ts-ignore
    customer: {
      email: receipt?.buyer_email,
      first_name: firstName || "",
      last_name: lastName || "",
      // @ts-ignore
      default_address: defaultAddress
    },
    billing_address: defaultAddress,
    shipping_address: defaultAddress,
    // @ts-ignore
    line_items: receipt?.supportedListings.map(listing => {
      const designId = listing.tags?.find(i => String(i).startsWith("d-"))?.split('-')?.[1] || "";
      return {
        id: listing.transaction_id || listing.listing_id,
        grams: `${listing.item_weight} ${listing.item_weight_unit}`,
        name: listing.title,
        price: (Number(listing.price.amount) / Number(listing.price.divisor)).toFixed(2),
        price_set: {
          shop_money: {
            amount: (Number(listing.price.amount) / Number(listing.price.divisor)).toFixed(2),
            currency_code: listing.price.currency_code
          },
          presentment_money: {
            amount: (Number(listing.price.amount) / Number(listing.price.divisor)).toFixed(2),
            currency_code: listing.price.currency_code
          },
        },
        product_exists: true,
        product_id: listing.listing_id,
        properties: designId ? [
          {
            name: "Design ID",
            value: designId
          }
        ] : [],
        quantity: receipt.transactions?.find(t => t.listing_id === listing.listing_id)?.quantity || 1,
        requires_shipping: true,
        sku: "",
        taxable: true,
        title: listing.title,
        total_discount: "0.00",
        tax_lines: [],
        duties: [],
        discount_allocations: []
      }
    }),
    name: `etsy-${receipt.receipt_id}`,
    order_number: receipt.receipt_id,
    current_subtotal_price: Number((receipt?.subtotal?.amount || 0) / 100).toFixed(2),
    current_total_price: Number((receipt?.grandtotal?.amount || 0) / 100).toFixed(2),
    total_price: Number((receipt?.grandtotal?.amount || 0) / 100).toFixed(2),
  }
}
