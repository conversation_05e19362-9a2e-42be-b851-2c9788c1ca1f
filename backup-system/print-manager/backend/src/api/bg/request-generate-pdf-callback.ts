import Joi from "joi";
import Bg from "./@utils/Bg";
import { nextjs2api } from 'api/api-middlewares'
import { runJob } from "./pipeline/run-job";

const requestGeneratePDFCallbackApi = async (req, res) => {
  await Bg.initDB();
  const { orderId } = req.query;
  const { pdfs, error } = req.body;
  const order = await Bg.Order.findByOrderID(orderId);
  if (!order?.Id) {
    console.log("Order not found")
    res.json({
      success: false,
      message: "Order not found",
    });
    return;
  }
  const pipeline = await Bg.Pipeline.findUndoneByOrderId(orderId);
  if (!pipeline || !pipeline.Jobs) {
    console.log("Pipeline not found")
    res.json({
      success: false,
      message: "Pipeline not found",
    });
    return;
  }

  let jobId;
  // some special condition to get the job
  Object.keys(pipeline.Jobs).forEach(i => {
    if (jobId) return;
    const jobData = pipeline.Jobs?.[i];
    if (!jobData) return;
    if (
      jobData.Status === "Pending" &&
      jobData.Title === "Generate PDF"
    ) {
      jobId = i;
    }
  });

  if (!jobId) {
    res.json({
      success: false,
      message: "Job not found",
    });
    return;
  }

  await runJob({
    jobId,
    pipelineId: pipeline.Id,
    additionData: { pdfs },
    error,
  })
  res.json({ success: true });
};

export default nextjs2api(requestGeneratePDFCallbackApi, {
  url: '/api/bg/request-generate-pdf-callback',
  method: 'POST',
  apiSchema: {
    body: Joi.object({
      pdfs: Joi.any(),
      error: Joi.string().optional(),
    }),
    query: Joi.object({
      orderId: Joi.string().required(),
    }),
  }
});
