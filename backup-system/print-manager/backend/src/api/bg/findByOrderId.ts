import Joi from "joi";
import Bg from "./@utils/Bg";
import { nextjs2api } from 'api/api-middlewares';

const findByOrderId = async (req, res) => {
  await Bg.initDB();
  const { orderId, orderNo } = req.query;
  const order = !!orderId ? await Bg.Order.findByOrderID(orderId) : (
    !!orderNo ? await Bg.Order.findByOrderNumber(+orderNo) : null
  );
  res.json({
    success: true,
    data: order,
  });
};

export default nextjs2api(findByOrderId, {
  url: '/api/bg/findByOrderId',
  method: 'GET',
  checkAuthen: true,
  apiSchema: {
    query: Joi.object({
      orderId: Joi.string(),
      orderNo: Joi.string(),
    }),
  }
});
