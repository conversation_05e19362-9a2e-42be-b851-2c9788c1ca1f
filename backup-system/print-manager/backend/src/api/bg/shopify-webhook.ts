import Joi from "joi";
import Bg from "./@utils/Bg";
import { nextjs2api } from 'api/api-middlewares'
import { TShopifyOrder } from "type";
import { runPipeline } from "./services";

let lastRuns = {};
const THROTTLE_TIME = 2000;
const shopifyWebhookApi = async (req, res) => {
  const body: TShopifyOrder = req.body;
  const { clientId, env, clientName, storeId } = req.query || {};

  const now = new Date().getTime();
  const orderSourceId = String(body.id);
  const lastRun = lastRuns[orderSourceId];
  if (lastRun) {
    if (now - lastRun < THROTTLE_TIME) {
      res.json({
        success: true,
        message: "Too many request with the same order",
      });
      return;
    }
  }
  lastRuns[orderSourceId] = now;

  await Bg.initDB();
  const isPOSOrder = body.client_details?.user_agent?.includes('Shopify POS');
  if (isPOSOrder) {
    return res.json({
      success: true,
      message: "POS Order not supported",
    });
  }
  const existed = await Bg.Order.findByOrderSourceID(String(body.id));
  if (existed?.id && !body.resubmit) {
    return res.json({
      success: true,
      message: "Order ID existed",
      data: existed,
    });
  }
  const orderUniqueId = await (async () => {
    try {
      // get list of order with same order number
      const orderCount = await Bg.Order.count({
        'Order Number': body.order_number,
      });
      // convert ordderCOunt to alphabet, from A -> Z
      // if orderCount is more than Z, use AA -> ZZ, AAA -> ZZZ
      const numberToBase26 = (val, tail = '') => {
        if (val <= 26) {
          return `${String.fromCharCode(val + 64)}${tail}`;
        }
        const remainder = val % 26 || 26;
        const division = Math.trunc(val / 26) - (remainder === 26 ? 1 : 0);
        return numberToBase26(division, `${String.fromCharCode(remainder + 64)}${tail}`);
      };
      let orderNo = orderCount + 1;
      let orderId = `${body.order_number}${numberToBase26(orderNo)}`;
      let isUnique = false;
      while (!isUnique) {
        let countSameOrderId = await Bg.Order.count({
          'Order ID': orderId,
        });
        if (countSameOrderId) {
          orderNo += 1;
          orderId = `${body.order_number}${numberToBase26(orderNo)}`;
        } else {
          isUnique = true;
        }
      }
      return orderId;
    } catch (err) {
      // false safe ID
      console.log('err orderUniqueId', err);
      return 'FS_' + Math.random().toString(36).substring(7);
    }
  })();

  const order = await Bg.Order.create({
    'Order ID': orderUniqueId,
    'Order Source ID': String(body.id),
    'Order Name': body.name,
    'Order Number': body.order_number,
    'Customer Email': body.customer.email,
    'Customer Name': [body.customer.first_name, body.customer.last_name].filter(Boolean).join(' '),
    'Raw Data': body,
    'All Item IDs': body.line_items?.map(i => i.id)?.join(', '),
    'All Product Names': body.line_items?.map(i => i.name)?.join(', '),
    'Status': 'Pending',
    'Client ID': clientId,
    'Client Name': clientName,
    'Store ID': storeId,
    'env': env,
    'Stage': 'Pre Production',
    'StageStatus': "Awaiting Payment",
    'OrderType': body.is_sample_request ? 'Sample' : (body.orderType || 'Shopify'),
  });

  res.json({
    success: true,
    message: "Done",
    data: order,
  });

  if (order?.Id) {
    runPipeline({ order });
  }
};

export default nextjs2api(shopifyWebhookApi, {
  url: '/api/bg/shopify-webhook',
  method: 'POST',
  apiSchema: {
    body: Joi.object().optional(),
    query: Joi.object({
      clientId: Joi.string().optional(),
      env: Joi.string().optional(),
      clientName: Joi.string().optional(),
      storeId: Joi.string().optional(),
    }),
  }
});
