import Joi from "joi";
import Bg from "./@utils/Bg";
import { nextjs2api } from 'api/api-middlewares'

const restoreOrderApi = async (req, res) => {
  await Bg.initDB();
  const { ids } = req.body;

  for (let i = 0; i < ids.length; i++) {
    await Bg.Order.updateById(ids[i], { inactive: false });
  }

  res.json({
    success: true,
  });
};

export default nextjs2api(restoreOrderApi, {
  url: '/api/bg/restoreOrder',
  method: 'POST',
  checkAuthen: true,
  apiSchema: {
    body: Joi.object({
      ids: Joi.array(),
    }),
  }
});
