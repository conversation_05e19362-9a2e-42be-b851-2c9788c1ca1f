import axios from 'axios';

const API_BASE_URL = 'http://localhost:3000';
const OLD_BASE_URL = 'https://dev.services.personify.tech';
const objectToQueryString = (obj) => {
  return Object.keys(obj).map((key) => {
    return encodeURIComponent(key) + '=' + encodeURIComponent(obj[key]);
  }).join('&')
};
const requestApi = async (url: string, data?: { method?: any, body?: any, headers?: any, params?: any }) => {
  let completedUrl = url;
  if (!completedUrl.startsWith('http')) {
    completedUrl = `${API_BASE_URL}${url}`;
  }
  if (data?.params) {
    completedUrl += `?${objectToQueryString(data.params)}`;
  }
  const res = await axios.request({
    url: completedUrl,
    method: data?.method || "GET",
    headers: {
      ...(data?.headers || {}),
      "Content-Type": "application/json",
      "env": "dev",
      "X-Auth-Token": "P8UeTt92HS98",
    },
    data: data?.body,
  });
  return res.data
}

describe('Test all APIs', () => {
  describe('listOrder', () => {
    test('New API listOrder', async () => {
      const response: any = await requestApi('/api/bg/listOrder', {
        params: {
          offset: 0,
          limit: 10
        }
      })
      expect(response?.success).toBeTruthy();
      expect(response?.data?.list).toHaveLength(10);
      expect(response?.data?.pageInfo).toBeDefined();
    });

    test('Old API listOrder', async () => {
      const response: any = await requestApi(`${OLD_BASE_URL}/api/bg/listOrder`, {
        params: {
          offset: 0,
          limit: 10
        }
      })
      expect(response?.success).toBeTruthy();
      expect(response?.data?.list).toHaveLength(10);
      expect(response?.data?.pageInfo).toBeDefined();
    });
  });

  describe('Update Order Status API', () => {
    test('New API Update Order Status', async () => {
      const testOrderId = 67;
      // const orderId = 6647578165334;
      const response: any = await requestApi('/api/bg/updateOrderStatus', {
        method: 'POST',
        params: {
          id: testOrderId
        },
        body: {
          StageStatus: "Queued For Production"
        }
      })
      expect(response?.success).toBeTruthy();
      expect(response?.data?.StageStatus).toBe("Queued For Production");
      // Awaiting Payment
      const response2: any = await requestApi(`${OLD_BASE_URL}/api/bg/updateOrderStatus`, {
        method: 'POST',
        params: {
          id: testOrderId
        },
        body: {
          StageStatus: "Awaiting Payment"
        }
      })
      expect(response2?.success).toBeTruthy();
      expect(response2?.data?.StageStatus).toBe("Awaiting Payment");
      // check order
      const response3: any = await requestApi(`/api/bg/listOrder`, {
        params: {
          offset: 0,
          limit: 1,
          orderId: response?.data?.["Order ID"]
        }
      })
      expect(response3?.success).toBeTruthy();
      expect(response3?.data?.list[0]?.StageStatus).toBe("Awaiting Payment");
    });
  });

  describe('Update Pipeline Share Data API', () => {
    test('New API Update Pipeline Share Data', async () => {
      const testPipelineId = 61;
      const response: any = await requestApi('/api/bg/pipeline/update-pipeline-share-data', {
        method: 'POST',
        body: {
          data: {
            testField: true
          },
          pipelineId: testPipelineId
        }
      })
      expect(response?.success).toBeTruthy();
      expect(response?.message?.SharedData?.testField).toBe(true);

      const response2: any = await requestApi(`${OLD_BASE_URL}/api/bg/pipeline/update-pipeline-share-data`, {
        method: 'POST',
        body: {
          data: {
            testField: false
          },
          pipelineId: testPipelineId
        }
      })
      expect(response2?.success).toBeTruthy();
      expect(response2?.message?.SharedData?.testField).toBe(false);
      // check order
      const response3: any = await requestApi(`/api/bg/pipeline/list`, {
        params: {
          offset: 0,
          limit: 1,
          orderId: response?.message?.OrderId // 6647578165334
        }
      })

      expect(response3?.success).toBeTruthy();
      expect(response3?.data?.list?.[0]?.SharedData?.testField).toBe(false);
    });
  });
});
