import axios from 'axios';
const TEST_ORDER = require('./order.json');

const API_BASE_URL = 'http://localhost:3000';
const OLD_BASE_URL = 'https://dev.services.personify.tech';
const objectToQueryString = (obj) => {
  return Object.keys(obj).map((key) => {
    return encodeURIComponent(key) + '=' + encodeURIComponent(obj[key]);
  }).join('&')
};
const requestApi = async (url: string, data?: { method?: any, body?: any, headers?: any, params?: any }) => {
  let completedUrl = url;
  if (!completedUrl.startsWith('http')) {
    completedUrl = `${API_BASE_URL}${url}`;
  }
  if (data?.params) {
    completedUrl += `?${objectToQueryString(data.params)}`;
  }
  const res = await axios.request({
    url: completedUrl,
    method: data?.method || "GET",
    headers: {
      ...(data?.headers || {}),
      "Content-Type": "application/json",
      "env": "dev",
      "X-Auth-Token": "P8UeTt92HS98",
    },
    data: data?.body || {},
  });
  return res.data
}

describe('Test new order flow', () => {
  let orderId: string;

  test('Test new order flow', async () => {
    console.log("Starting test new order flow");
    console.log("Cleaning test order");
    const res: any = await requestApi('/api/bg/test/clean-test-order', {
      method: 'POST',
    })
    expect(res?.success).toBeTruthy();

    console.log("Creating new order");
    const res2: any = await requestApi('/api/bg/shopify-webhook', {
      method: 'POST',
      body: TEST_ORDER,
      params: {
        clientId: '717932601135',
        env: 'dev',
        clientName: 'Company test 1',
      }
    })
    expect(res2?.success).toBeTruthy();
    expect(res2?.data?.['Order ID']).toBeDefined();
    expect(res2?.data?.['Order Source ID']).toBe(String(TEST_ORDER.id));
    orderId = res2?.data?.['Order ID'];

    console.log("Waiting for pipeline to run");
    await new Promise(resolve => setTimeout(resolve, 10000));
    // check list pipeline
    const res3: any = await requestApi('/api/bg/pipeline/list', {
      method: 'GET',
      params: {
        limit: 10,
        offset: 0,
        orderId: orderId
      }
    });
    console.log("Checking list pipeline");
    expect(res3?.success).toBeTruthy();
    expect(res3?.data?.list?.length).toBe(1);
    expect(res3?.data?.list?.[0]?.SharedData).toBeDefined();
    expect(res3?.data?.list?.[0]?.SharedData?.canBeProcessedItems?.length).toBe(1);
    expect(Object.keys(res3?.data?.list?.[0]?.Stages).length).toBe(7);
    expect(Object.keys(res3?.data?.list?.[0]?.Jobs).length).toBeGreaterThan(1);
  });

  test('Test continue order flow', async () => {
    // check list pipeline
    console.log("Checking list pipeline");
    const res2: any = await requestApi('/api/bg/pipeline/list', {
      method: 'GET',
      params: {
        limit: 10,
        offset: 0,
        orderId: orderId
      }
    });
    expect(res2?.success).toBeTruthy();
    expect(res2?.data?.list?.length).toBe(1);
    expect(Object.keys(res2?.data?.list?.[0]?.Stages).length).toBe(7);
    const jobsObj = res2?.data?.list?.[0]?.Jobs;
    expect(Object.keys(jobsObj).length).toBeGreaterThan(1);
    const jobsArr = Object.keys(jobsObj).map(job => jobsObj[job]);

    const pdfJob = jobsArr.filter(job => job.Title === "Generate PDF");
    const candleStickerJob = jobsArr.filter(job => job.Title === "Generate Candle Sticker");
    const lineId = res2?.data?.list?.[0]?.SharedData?.canBeProcessedItems?.[0]?.id;
    expect(pdfJob?.length).toBe(1);
    expect(candleStickerJob?.length).toBe(0);

    requestApi('/api/bg/request-generate-pdf-callback', {
      method: 'POST',
      body: {
        "pdfs": [
          {
            "pdfUrl": "https://print-manager-media.s3.eu-west-1.amazonaws.com/puppeteer/1735986889969.pdf",
            "lineId": lineId,
            "printJobId": "825659688245",
            "imageQuality": { "labels": [], "sharpness": 2.64529636036959 }
          }
        ]
      },
      params: {
        orderId: orderId,
      }
    })
    await new Promise(resolve => setTimeout(resolve, 5000));

    requestApi('/api/bg/request-generate-candle-sticker-callback', {
      method: 'POST',
      body: {
        "pdfs": [
          {
            "lineId": lineId,
            "pdfUrl": "https://print-manager-media.s3.eu-west-1.amazonaws.com/puppeteer/1735986889969.pdf"
          }
        ]
      },
      params: {
        orderId: orderId,
      }
    })
    await requestApi('/api/bg/request-generate-candle-sticker-callback', {
      method: 'POST',
      body: {
        "pdfs": [
          {
            "lineId": lineId,
            "pdfUrl": "https://print-manager-media.s3.eu-west-1.amazonaws.com/puppeteer/1735986889970.pdf"
          }
        ]
      },
      params: {
        orderId: orderId,
      }
    })
    console.log("Waiting for pipeline to run");
    await new Promise(resolve => setTimeout(resolve, 15000));
    console.log("Checking list pipeline");
    const res4: any = await requestApi('/api/bg/pipeline/list', {
      method: 'GET',
      params: {
        limit: 10,
        offset: 0,
        orderId: orderId
      }
    });
    expect(res4?.success).toBeTruthy();
    expect(res4?.data?.list?.length).toBe(1);
    expect(Object.keys(res4?.data?.list?.[0]?.Stages).length).toBe(7);
    const jobsObj2 = res4?.data?.list?.[0]?.Jobs;
    expect(Object.keys(jobsObj2).length).toBeGreaterThan(1);
    const jobsArr2 = Object.keys(jobsObj2).map(job => jobsObj2[job]);
    const pdfJob2 = jobsArr2.filter(job => job.Title === "Generate PDF");
    const candleStickerJob2 = jobsArr2.filter(job => job.Title === "Generate Candle Sticker");

    expect(pdfJob2?.length).toBe(1);
    expect(candleStickerJob2?.length).toBe(1);

    console.log("Cleaning test order");
    const res5: any = await requestApi('/api/bg/test/clean-test-order', {
      method: 'POST',
    })
    expect(res5?.success).toBeTruthy();
  });
});
