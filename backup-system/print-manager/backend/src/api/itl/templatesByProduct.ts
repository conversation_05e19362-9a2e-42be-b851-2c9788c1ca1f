import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { ERROR } from 'const';
import Joi = require("joi");
import { Op } from "sequelize";

class TemplatesByProduct implements TypeAPIHandler {

  url = "/api/itl/templates-by-product";
  method = "GET";
  apiSchema = {
    query: Joi.object({
      productId: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { productId } = request.query;

    const find = await DB.GeneralData.findAll({
      where: {
        type: 'itl-template',
        [Op.or]: [
          { field1: productId },
          { field2: productId }
        ],
      },
    });

    if (!find) {
      throw new Error(ERROR.NOT_EXISTED);
    }

    return {
      success: true,
      data: find.map(i => {
        return {
          ...i.data,
          id: i.id,
        }
      }),
    }
  };
}

export default new TemplatesByProduct();