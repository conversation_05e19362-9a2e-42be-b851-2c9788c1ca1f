import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import Joi = require("joi");
import { <PERSON>ar<PERSON><PERSON><PERSON> } from "helpers";

class SaveTemplate implements TypeAPIHandler {

  url = "/api/itl/save-template";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      id: Joi.string().optional(),
      thumbnail: Joi.string().optional(),
      productId: Joi.string().required(),
      data: Joi.object().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { id, productId, data } = request.body;

    let existed = await DB.GeneralData.findByPk(id);
    if (existed) {
      existed.data = data;
      await existed.save();
    } else {
      existed = await DB.GeneralData.create({
        id: VarHelper.genId(),
        type: 'itl-template',
        field1: productId,
        field2: '',
        name: 'itl-template',
        data,
        userId: '1',
        publicPermission: {
          c: false, r: false, u: false, d: false,
        }
      });
    }

    return {
      success: true,
      data: existed,
    }
  };
}

export default new SaveTemplate();