import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON>en, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import Joi = require("joi");

class GeneralDataList implements TypeAPIHandler {

  url = "/api/itl/template/:id";
  method = "DELETE";
  apiSchema = {
    params: Joi.object({
      id: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
  ]);

  handler = async (request: TRequestUser, reply) => {
    const token = request.headers['Authentication'] || request.headers['authentication'];
    if (token !== 'Kalvin123') {
      return {
        success: false,
        error: 'Unauthorized',
      }
    }
    const find = await DB.GeneralData.findOne({
      where: {
        id: request.params.id,
        type: 'itl-template',
      },
    });
    await find.destroy();
    return {
      success: true,
    }
  };
}

export default new GeneralDataList();