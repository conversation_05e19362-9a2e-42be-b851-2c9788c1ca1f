import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, checkAuthen, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON><PERSON> } from "helpers";
import Joi = require("joi");
import { ERROR } from "const";

class DeleteProductInstace implements TypeAPIHandler {

  url = "/api/product-instances/:id";
  method = "DELETE";
  apiSchema = {
    params: Joi.object({
      id: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { id } = request.params;
    const user = request.user;
    if (user.role !== 'admin' && user.role !== 'reseller' && user.role !== 'user') throw new Error(ERROR.PERMISSION_DENIED);

    const instace = await DB.ProductInstance.findByPk(id);
    if (!instace) {
      return {
        success: true,
      }
    }

    if (instace.createdByUserId !== user.id && user.role !== 'admin') {
      throw new Error(ERROR.PERMISSION_DENIED);
    }
    
    await instace.destroy();

    return {
      success: true,
    }
  };
}

export default new DeleteProductInstace();