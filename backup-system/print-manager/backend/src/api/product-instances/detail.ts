import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, checkA<PERSON>en, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { <PERSON>ar<PERSON><PERSON><PERSON> } from "helpers";
import Joi = require("joi");
import { ERROR } from "const";
import { checkReseller } from "api/api-middlewares/authen";

class DetailProductInstace implements TypeAPIHandler {

  url = "/api/product-instances/:id";
  method = "GET";
  apiSchema = {
    params: Joi.object({
      id: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
    checkReseller
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { id } = request.params;
    const user = request.user;
    // if (user.role === 'user' || user.role === 'guess') throw new Error(ERROR.PERMISSION_DENIED);
    
    const instance = await DB.ProductInstance.findByPk(id);
    if (!instance) throw new Error(ERROR.NOT_EXISTED);
    // if (user.role === 'reseller' && instance.createdByUserId !== user.id) {
    //   throw new Error(ERROR.PERMISSION_DENIED);
    // }

    return {
      success: true,
      data: instance,
    }
  };
}

export default new DetailProductInstace();