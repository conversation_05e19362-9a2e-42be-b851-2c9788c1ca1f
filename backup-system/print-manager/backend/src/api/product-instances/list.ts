import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON><PERSON> } from "helpers";
import Joi = require("joi");
import { ERROR } from "const";
import { Op } from "sequelize";
import { checkReseller } from "api/api-middlewares/authen";

class ListProducts implements TypeAPIHandler {

  url = "/api/product-instances";
  method = "GET";
  apiSchema = {
    query: Joi.object({
      page: Joi.number(),
      resellerId: Joi.string().allow(''),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkReseller,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const user = request.user;
    // if (user.role === 'user' || user.role === 'guess') throw new Error(ERROR.PERMISSION_DENIED);
    let { page, resellerId } = request.query;
    // console.log('resellerId', resellerId);
    // if (user.role === 'reseller' && !resellerId) throw new Error(ERROR.PERMISSION_DENIED);
    // if (user.role === 'reseller' && resellerId !== user.id) throw new Error(ERROR.PERMISSION_DENIED);

    if (!page) page = 1;

    const PAGE_SIZE = 10;
    const result = await DB.ProductInstance.findAndCountAll({
      where: !!user.id ? {
        createdByUserId: user.id,
      } : {},
      order: [
        ['updatedAt', 'DESC'],
      ],
      offset: (page - 1) * PAGE_SIZE,
      limit: PAGE_SIZE,
    });

    return {
      success: true,
      data: {
        list: result.rows,
        total: result.count,
        hasNext: page * PAGE_SIZE < result.count,
      },
    }
  };
}

export default new ListProducts();