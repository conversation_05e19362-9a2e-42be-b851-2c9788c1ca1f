import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON><PERSON>, checkAuthenOptional, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON>per } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");

class UpsertPrintJob implements TypeAPIHandler {

  url = "/api/product-instances";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      id: Joi.string(),
      name: Joi.string(),
      designId: Joi.string(),
      productId: Joi.string(),
      image: Joi.string(),
      isCustomizable: Joi.boolean(),
      price: Joi.string(),
      skuNumber: Joi.string(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthenOptional,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { body, user } = request;
    if (!body.id && (!body.productId || !body.designId)) {
      throw new Error('Need to specify both product and design');
    }
    if (user.role == 'guess') throw new Error(ERROR.PERMISSION_DENIED);
    // UPDATE EXISTING
    if (body.id) {
      const find = await DB.ProductInstance.findByPk(request.body.id);
      if (!!find) {
        if (find.createdByUserId !== user.id) {
          throw new Error(ERROR.PERMISSION_DENIED);
        }
        for (let key in body) {
          find[key] = body[key];
        }
        // force isCustomizable
        const design = await DB.Design.findByPk(body.designId || find.designId);
        if (!design) throw new Error(ERROR.NOT_EXISTED);
        if (!design.isCustomizable) {
          find.isCustomizable = false;
        }
        await find.save();
        return { success: true, data: find };
      }
    }
    // CREATE NEW

    // force isCustomizable
    const design = await DB.Design.findByPk(body.designId);
    if (!design) throw new Error(ERROR.NOT_EXISTED);
    if (!design.isCustomizable) {
      body.isCustomizable = false;
    }

    const data = await DB.ProductInstance.create({
      id: VarHelper.genId(),
      ...body,
      createdByUserId: user.id,
      createdByUserType: user.role,
    });

    return {
      success: true,
      data,
    }
  };
}

export default new UpsertPrintJob();