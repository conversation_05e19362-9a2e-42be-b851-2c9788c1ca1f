import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON>en, checkAuthenOptional, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON>elper } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");
import MailHelper from "helpers/MailHelper";
import { Op } from "sequelize";
import { genEmailHtml } from "helpers/email-templates";

class UpsertPrintJob implements TypeAPIHandler {

  url = "/api/products";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      id: Joi.string(),
      name: Joi.string(),
      bluePrintImage: Joi.string(),
      description: Joi.string().allow(''),
      image: Joi.string(),
      banner: Joi.string().allow(''),
      label: Joi.string().allow(''),
      secondLabel: Joi.string().allow('').allow(null),
      estimatedLeadTime: Joi.string().allow(''),
      volume: Joi.string().allow(''),
      material: Joi.string().allow(''),
      galleries: Joi.array().items(Joi.string()),
      previewData: Joi.any(),
      category: Joi.string(),
      tags: Joi.string().allow('').allow(null),

      physicalWidth: Joi.number(),
      physicalHeight: Joi.number(),
      editorWidth: Joi.number(),
      editorHeight: Joi.number(),
      productWidth: Joi.number(),
      productHeight: Joi.number(),

      printAreas: Joi.array().items(Joi.object({
        width: Joi.number(),
        height: Joi.number(),
        top: Joi.number(),
        left: Joi.number(),
      })),

      availableForResellerIds: Joi.object(),
      data: Joi.any(),

      variations: Joi.array().items(Joi.object({
        variant: Joi.string(),
        prices: Joi.array().items(Joi.object({
          amount: Joi.number(),
          price: Joi.number(),
        })),
      })),

      wholeSale: Joi.boolean(),
      printOnDemand: Joi.boolean(),
      customProduct: Joi.boolean(),
      price: Joi.number(),
      originalPrice: Joi.number(),
      packagingDescription: Joi.string().allow(''),
      packagingImage: Joi.string().allow(''),
      artboardUrl: Joi.string().allow(''),
      dropletUrl: Joi.string().allow(''),
      printerIdentificatorCode: Joi.string().allow(''),
      dpi: Joi.number(),
      packPrices: Joi.array().items(Joi.object({
        size: Joi.number(),
        price: Joi.number(),
        discount: Joi.number(),
      })),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthenOptional,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { body, user } = request;
    if (user.role !== 'admin') throw new Error(ERROR.PERMISSION_DENIED);
    if (body.originalPrice && body.originalPrice < body.price) {
      throw 'Original Price must be greater than Discounted Price';
    }
    // UPDATE EXISTING
    if (body.id) {
      const find = await DB.Product.findByPk(request.body.id);
      if (!!find) {
        let shouldUpdateSize = false;
        if (body.data?.isOutStock !== find.data?.isOutStock) {
          // send email to all resellers have the product
          const resellers = {};
          const matchDesigns = await DB.Design.findAll({
            where: {
              productId: find.id,
            }
          });
          for (let i = 0; i < matchDesigns.length; i++) {
            const design = matchDesigns[i];
            if (design.createdByUserId && design.createdByUserType === 'reseller') {
              if (!resellers[design.createdByUserId]) {
                const result = await DB.User.findAll({
                  where: {
                    [Op.or]: [{
                      resellerId: design.createdByUserId,
                    }, {
                      id: design.createdByUserId,
                    }]
                  }
                });
                result.forEach(reseller => {
                  if (reseller.email && !resellers[reseller.email]) {
                    resellers[reseller.email] = {
                      productId: design.productId,
                      name: design.name,
                    };
                  }
                })
              }
            }
          }

          await Promise.all(Object.keys(resellers).map(async email => {
            await MailHelper.sendSMTPEmail({
              to: email,
              subject: 'Bottle Goose - Inventory Announcement',
              html: genEmailHtml({
                title: "Inventory Announcement",
                message: `We would like to inform you that the product ${resellers[email].name} is <strong>${find.data.isOutStock ? "out of stock" : "in stock"}</strong>.`,
                link: `https://${process.env.DEV ? 'bg-dev.' : 'bg-production.'}bottledgoose.co.uk/product/${resellers[email].productId}`,
              }),
            })
          }))
          // END - send email to all resellers have the product
        }
        for (let key in body) {
          if (
            ['editorWidth', 'editorHeight', 'physicalWidth', 'physicalHeight'].includes(key)
            && body[key] !== find[key]
          ) {
            shouldUpdateSize = true;
          }
          find[key] = body[key];
        }
        await find.save();
        if (shouldUpdateSize) {
          const designs = await DB.Design.findAll({
            where: {
              productId: find.id,
            }
          });
          for (let i=0; i<designs.length; i++) {
            const design = designs[i];
            design.width = find.physicalWidth;
            design.height = find.physicalHeight;
            design.editorHeight = find.editorHeight;
            design.editorWidth = find.editorWidth;
            await design.save();
          }
        }
        return { success: true, data: find };
      }
    }
    // CREATE NEW
    const data = await DB.Product.create({
      id: VarHelper.genId(),
      ...body,
      createdByUserId: user.id,
      createdByUserType: user.role,
    });

    return {
      success: true,
      data,
    }
  };
}

export default new UpsertPrintJob();
