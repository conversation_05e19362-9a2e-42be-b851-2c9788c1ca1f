import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, checkAuthen, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON><PERSON> } from "helpers";
import Joi = require("joi");
import { ERROR } from "const";
import { Op } from "sequelize";
import { checkReseller } from "api/api-middlewares/authen";

class ListProducts implements TypeAPIHandler {

  url = "/api/products/demo";
  method = "GET";
  apiSchema = {
    
  }

  preHandler = combineMiddlewares([
    // validateRequest(this.apiSchema),
    // checkAuthen,
    // checkReseller
  ]);

  handler = async (request: TRequestUser, reply) => {

    const result = await DB.Product.findAndCountAll({
      where: {
        availableForResellerIds: {
          all: true,
        },
        physicalWidth: {
          [Op.ne]: 0,
        },
        physicalHeight: {
          [Op.ne]: 0,
        },
        dropletUrl: {
          [Op.ne]: ''
        },
        printerIdentificatorCode: {
          [Op.ne]: ''
        },
        previewData: {
          [Op.like]: `%blender-file%`
        }
      },
      order: [
        ['updatedAt', 'DESC'],
      ],
    });

    return {
      success: true,
      data: {
        list: result.rows,
        total: result.count,
      },
    }
  };
}

export default new ListProducts();
