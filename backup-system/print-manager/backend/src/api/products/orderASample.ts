import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkA<PERSON>en, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { ERROR } from 'const';
import Joi = require("joi");
import axios from 'axios';
import { COUNTRIES } from "helpers";
const moment = require('moment');

class OrderASample implements TypeAPIHandler {

  url = "/api/product-library/order-a-sample";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      productId: Joi.string().required(),
      designId: Joi.string().required(),
      style: Joi.string().allow(''),
      productName: Joi.string().required(),
      customAddress: Joi.object().keys({
        email: Joi.string().allow(''),
        first_name: Joi.string().allow(''),
        address1: Joi.string().allow(''),
        country: Joi.string().allow(''),
        town: Joi.string().allow(''),
        zip: Joi.string().allow(''),
        last_name: Joi.string().allow(''),
        address2: Joi.string().allow(''),
        phone: Joi.string().allow(''),
        county: Joi.string().allow(''),
      }),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { body, user } = request;
    const { productId, productName, customAddress, designId, style } = body;
    if (user.role === 'admin') throw new Error(ERROR.PERMISSION_DENIED);
    const resellerId = user.resellerId || user.id;
    const slug = resellerId === '************' ? 'ghg' : resellerId === '************' ? 'piw' : 'bg';
    const client = await DB.User.findByPk(resellerId);
    const clientName = client.accountName || [client.firstName, client.lastName].filter(Boolean).join(' ');

    const product = await DB.Product.findByPk(productId);

    const orderNumber = 1000 + Math.floor(Math.random() * 1000)

    const countryCode = COUNTRIES.find(c => c.name === customAddress?.country)?.let2 || 'GB';

    const apiCall: any = await axios.request({
      url: `http://localhost:3000/api/bg/shopify-webhook?clientId=${resellerId}&clientName=${clientName}&env=${process.env.DEV ? 'dev' : 'prod'}`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
        'X-Auth-Token': process.env.MICRO_API_TOKEN,
      },
      data: JSON.stringify({
        id: new Date().getTime(),
        email: user.email,
        name: `sample-${orderNumber}`,
        order_number: orderNumber,
        is_sample_request: true,
        customer: {
          email: user.email,
          first_name: customAddress?.firstName,
          last_name: customAddress?.lastName,
          default_address: {
            first_name: customAddress?.first_name || user.firstName,
            address1: customAddress?.address1 || user.addressLine1,
            country: customAddress?.country || user.country,
            town: customAddress?.town || user.town,
            city: customAddress?.town || user.town,
            zip: customAddress?.zip || user.postCode,
            last_name: customAddress?.last_name || user.lastName,
            address2: customAddress?.address2 || user.addressLine2,
            phone: customAddress?.phone || null,
          }
        },
        current_total_price: "0",
        current_subtotal_price: "0",
        total_price: "0",
        shipping_lines: [],
        line_items: [{
          id: product.id,
          name: product.name + ' ' + style,
          price: "0",
          product_id: product.id,
          quantity: 1,
          title: product.name + ' ' + style,
          total_discount: "0.00",
          properties: [
            { name: 'Order A Sample', value: 'YES' },
            { name: 'BG Product Number', value: designId },
          ],
          grams: product?.data?.weight || 0,
        }],
        shipping_address: {
          first_name: customAddress?.first_name || user.firstName,
          address1: customAddress?.address1 || user.addressLine1,
          country: customAddress?.country || user.country,
          town: customAddress?.town || user.town,
          city: customAddress?.town || user.town,
          zip: customAddress?.zip || user.postCode,
          last_name: customAddress?.last_name || user.lastName,
          address2: customAddress?.address2 || user.addressLine2,
          phone: customAddress?.phone || null,
          name: customAddress?.first_name ? [customAddress?.first_name, customAddress?.last_name].filter(Boolean).join(' ') : `${user.firstName} ${user.lastName}`,
          countryCode,
          country_code: countryCode,
        },
        billing_address: {
          first_name: user.firstName,
          address1: user.addressLine1,
          country: user.country,
          zip: user.postCode,
          last_name: user.lastName,
          address2: user.addressLine2,
          town: customAddress?.town || user.town,
          city: customAddress?.town || user.town,
          countryCode: countryCode,
          country_code: countryCode,
        },
        created_at: moment().format('YYYY-MM-DD HH:mm:ssZ'),
        updated_at: moment().format('YYYY-MM-DD HH:mm:ssZ'),
      })
    });

    if (!apiCall.data?.data) {
      return {
        success: false,
        error: apiCall.data?.error,
      }
    }

    // CREATE NEW
    // const data = await DB.GeneralData.create({
    //   id: VarHelper.genId(),
    //   type: 'order-sample-request',
    //   userId: resellerId,
    //   field1: productId,
    //   field2: '',
    //   name: productName,
    //   publicPermission: { c: false, r: false, u: false, d: false },
    //   data: {}
    // });

    return {
      success: true,
      data: apiCall.data.data,
    }
  };
}

export default new OrderASample();
