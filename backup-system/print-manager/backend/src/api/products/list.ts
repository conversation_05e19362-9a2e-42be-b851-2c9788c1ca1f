import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, checkAuthen, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON><PERSON> } from "helpers";
import Joi = require("joi");
import { ERROR } from "const";
import { Op } from "sequelize";
import { checkReseller } from "api/api-middlewares/authen";

class ListProducts implements TypeAPIHandler {

  url = "/api/products";
  method = "GET";
  apiSchema = {
    query: Joi.object({
      page: Joi.number(),
      resellerId: Joi.string().allow(''),
      wholeSale: Joi.number(),
      printOnDemand: Joi.number(),
      customProduct: Joi.number(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
    // checkReseller
  ]);

  handler = async (request: TRequestUser, reply) => {
    const user = request.user;
    // if (user.role === 'user' || user.role === 'guess') throw new Error(ERROR.PERMISSION_DENIED);
    let { page, resellerId, wholeSale, printOnDemand, customProduct } = request.query;
    console.log('resellerId', resellerId);
    // if (user.role === 'reseller' && !resellerId) throw new Error(ERROR.PERMISSION_DENIED);
    // if (user.role === 'reseller' && resellerId !== user.id) throw new Error(ERROR.PERMISSION_DENIED);

    if (!page) page = 1;

    const productNewTypeQuery : any = {};
    if (wholeSale === '0' || wholeSale === '1') productNewTypeQuery.wholeSale = Boolean(wholeSale);
    if (printOnDemand === '0' || printOnDemand === '1') productNewTypeQuery.printOnDemand = Boolean(printOnDemand);
    if (customProduct === '0' || customProduct === '1') productNewTypeQuery.customProduct = Boolean(customProduct);

    const PAGE_SIZE = 1000;
    const result = await DB.Product.findAndCountAll({
      where: !!resellerId ? {
        [Op.or]: [
          {
            availableForResellerIds: {
              [resellerId]: true,
            }
          },
          {
            availableForResellerIds: {
              all: true,
            },
          },
        ],
        ...productNewTypeQuery,
        physicalWidth: {
          [Op.ne]: 0,
        },
        physicalHeight: {
          [Op.ne]: 0,
        },
        // dropletUrl: {
        //   [Op.ne]: ''
        // },
        printerIdentificatorCode: {
          [Op.ne]: ''
        },
        // previewData: {
        //   [Op.like]: `%blender-file%`
        // }
      } : {
        ...productNewTypeQuery,
      },
      order: [
        ['updatedAt', 'DESC'],
      ],
      offset: (page - 1) * PAGE_SIZE,
      limit: PAGE_SIZE,
    });

    return {
      success: true,
      data: {
        list: result.rows,
        total: result.count,
        hasNext: page * PAGE_SIZE < result.count,
      },
    }
  };
}

export default new ListProducts();
