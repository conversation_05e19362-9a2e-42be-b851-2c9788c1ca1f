import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, checkAuthen, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { <PERSON>ar<PERSON><PERSON><PERSON> } from "helpers";
import Joi = require("joi");
import { ERROR } from "const";

class DetailProducts implements TypeAPIHandler {

  url = "/api/products/:id";
  method = "GET";
  apiSchema = {
    params: Joi.object({
      id: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    // checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { id } = request.params;
    // const user = request.user;
    // if (user.role === 'user' || user.role === 'guess') throw new Error(ERROR.PERMISSION_DENIED);
    
    const product = await DB.Product.findByPk(id);
    if (!product) throw new Error(ERROR.NOT_EXISTED);
    // if (user.role === 'reseller'
    //   && (
    //     product.availableForResellerIds?.[user.id] !== true
    //     && product.availableForResellerIds?.all !== true
    //   )
    // ) {
    //   throw new Error(ERROR.PERMISSION_DENIED);
    // }

    return {
      success: true,
      data: product,
    }
  };
}

export default new DetailProducts();