import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import Joi = require("joi");
import { Op } from "sequelize";

class ListBySKUs implements TypeAPIHandler {

  url = "/api/products/list-by-skus";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      skus: Joi.array().items(
        Joi.string().allow('')
      ).required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { skus } = request.body;

    if (!skus?.length) {
      return {
        success: true,
        data: [],
      };
    }

    const products = await DB.Product.findAll({
      where: {
        data: {
          [Op.or]: skus.filter(Boolean).map(sku => ({
            [Op.like]: `%${sku}%`
          }))
        }
      }
    });

    return {
      success: true,
      data: products,
    }
  };
}

export default new ListBySKUs();
