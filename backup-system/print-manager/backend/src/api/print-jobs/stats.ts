import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, checkAuthen, validateRequest, checkAuthenOptional, checkAdmin } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON><PERSON> } from "helpers";
import Joi = require("joi");
import { ERROR } from "const";
import { Op } from "sequelize";
import { MATCH_SHOPIFY_PRODUCTS } from '../online-stores/partner-in-wine/INFO';
var sequelize = require('sequelize');

class Detail implements TypeAPIHandler {

  url = "/api/print-jobs/stats";
  method = "GET";
  apiSchema = {
    
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
    checkAdmin,
  ]);

  handler = async (request: TRequestUser, reply) => {

    const [
      numberOfPrintJobs,
      numberOfPrintedJobs
    ] = await Promise.all([
      DB.PrintJob.count(),
      DB.PrintJob.count({
        where: {
          isPrinted: true,
        }
      })
    ]);

    const listCountClients = await DB.PrintJob.findAll({
      group: ['clientId'],
      attributes: ['clientId', [sequelize.fn('COUNT', 'clientId'), 'count']],
    });

    const listPrintedCountClients = await DB.PrintJob.findAll({
      group: ['clientId'],
      attributes: ['clientId', [sequelize.fn('COUNT', 'clientId'), 'count']],
      where: {
        isPrinted: true,
      }
    });

    const jobsOverMonth = await DB.PrintJob.findAll({
      attributes: [
        // [sequelize.fn("MONTH", sequelize.col("createdAt")), "month"],
        [sequelize.literal(`to_date("createdAt"::TEXT,'YYYY-MM')`), 'month'],
        [sequelize.literal(`COUNT(*)`), 'count']
      ],
      group: ['month'],
    });

    const printedJobsOverMonth = await DB.PrintJob.findAll({
      attributes: [
        // [sequelize.fn("MONTH", sequelize.col("createdAt")), "month"],
        [sequelize.literal(`to_date("createdAt"::TEXT,'YYYY-MM')`), 'month'],
        [sequelize.literal(`COUNT(*)`), 'count']
      ],
      group: ['month'],
      where: {
        isPrinted: true,
      }
    });

    let shopifyLogs = await DB.GeneralData.findAll({
      where: {
        name: {
          [Op.or]: [
            'shopify-webhook',
            'shopify-webhook-partner-in-wine',
            'shopify-webhook-great-harbour-gifts',
          ]
        },
        data: {
          [Op.like]: `%customer%`,
        }
      }
    })
    
    // filter test data
    shopifyLogs = shopifyLogs.filter((v : any) => {
      const customer = v.data.customer;
      if (!customer) return false;
      if (customer.first_name === 'Dev' || customer.last_name === 'Dev') return false;
      if (customer.first_name === 'Test' || customer.last_name === 'Test') return false;
      if (customer.email === '<EMAIL>') return false;
      return true;
    })

    const customers = [];
    const clients = {
      'shopify-webhook': 'Bottled Goose',
      'shopify-webhook-partner-in-wine': 'Partner In Wine',
      'shopify-webhook-great-harbour-gifts': 'Great Harbour Gifts',
    };

    const products = await DB.Product.findAll();
    const productSKUPDFs = {};
    products.forEach(p => {
      if (!p.data || !p.data.skuPDFs) return;
      p.data.skuPDFs.forEach(s => {
        productSKUPDFs[s.sku] = true;
      });
    })
    // console.log('productSKUPDFs', productSKUPDFs);

    shopifyLogs.forEach(l => {
      const client = clients[l.name];
      let items = l.data.line_items.slice();
      if (client === 'Partner In Wine') {
        items = items.filter(v => 
          !!MATCH_SHOPIFY_PRODUCTS()[v.product_id]
          && v.properties.length > 0
          && !!v.properties.find(p => p.name === 'Personalise Text')
        );
        if (items.length === 0) return;
      } else if (client === 'Great Harbour Gifts') {
        items = items.filter(v => 
          !!v.sku &&
          !!productSKUPDFs[v.sku]
        );
        if (items.length === 0) return;
      }
      const customer = l.data.customer;
      const order_number = l.data.order_number;
      
      const findIndex = customers.findIndex(c => c.email === customer.email && client === c.client);
      if (findIndex === -1) {
        customers.push({
          email: customer.email,
          first_name: customer.first_name,
          last_name: customer.last_name,
          client: clients[l.name],
          products: items.map(i => ({ name: i.name, product_id: i.product_id, variant_id: i.variant_id, quantity: i.quantity, timestamp: l.data.created_at, order_number })),
        });
      } else {
        customers[findIndex].products = [
          ...customers[findIndex].products,
          ...items.map(i => ({ name: i.name, product_id: i.product_id, variant_id: i.variant_id, quantity: i.quantity, timestamp: l.data.created_at, order_number })),
        ];
      }
    });

    return {
      success: true,
      data: {
        numberOfPrintJobs,
        numberOfPrintedJobs,
        listCountClients,
        listPrintedCountClients,
        jobsOverMonth,
        printedJobsOverMonth,
        customers,
      },
    };

  };
}

export default new Detail();