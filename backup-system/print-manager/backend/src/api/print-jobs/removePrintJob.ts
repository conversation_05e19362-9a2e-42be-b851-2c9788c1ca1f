import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, checkAuthen, validateR<PERSON>quest, checkAdmin } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON><PERSON> } from "helpers";
import Joi = require("joi");
import { ERROR } from "const";

class DeletePrintJob implements TypeAPIHandler {

  url = "/api/print-jobs/:id";
  method = "DELETE";
  apiSchema = {
    params: Joi.object({
      id: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
    checkAdmin,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { id } = request.params;
    const printJob = await DB.PrintJob.findByPk(id);
    if (!printJob) return { success: true };
    await printJob.destroy();

    return {
      success: true,
    }
  };
}

export default new DeletePrintJob();