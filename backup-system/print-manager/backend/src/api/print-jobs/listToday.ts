import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, checkAuthen, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON><PERSON> } from "helpers";
import Joi = require("joi");
import { ERROR } from "const";
import { Op } from "sequelize";
const sequelize = require("sequelize");
const moment = require("moment");

class ListPrintJobs implements TypeAPIHandler {

  url = "/api/print-jobs/today";
  method = "GET";
  apiSchema = {
    
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const user = request.user;
    if (user.role !== 'admin') {
      throw new Error(ERROR.PERMISSION_DENIED);
    }
    const today = moment().format('YYYY-MM-DD');
    console.log('today', today);
    const TODAY_START = new Date().setHours(0, 0, 0, 0);
    const NOW = new Date();
    const result = await DB.PrintJob.findAndCountAll({
      where: {
        createdAt: {
          // [Op.like]: `%${today}%`,
          [Op.gt]: TODAY_START,
          [Op.lt]: NOW
        }
      },
      order: [
        ['updatedAt', 'DESC'],
      ],
    });

    result.rows = result.rows.map(val => {
      // console.log('val', val);
      // delete val.data?.design?.canvas;
      const data = Object.assign({}, val.data);
      delete data?.design?.canvas;
      val.data = data;
      return val;
    })

    return {
      success: true,
      data: {
        list: result.rows,
        total: result.count,
      },
    }
  };
}

export default new ListPrintJobs();