import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON><PERSON>, checkAuthenOptional, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, VarHelper } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");

class UpsertPrintJob implements TypeAPIHandler {

  url = "/api/print-jobs";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      id: Joi.string(),
      clientId: Joi.string(),
      designId: Joi.string(),

      productId: Joi.string(),
      productName: Joi.string(),

      previewUrl: Joi.string(),
      artworkUrls: Joi.array().items(Joi.string()),
      quantity: Joi.number(),

      isPrinted: Joi.boolean(),
      isPDFDownloaded: Joi.boolean(),
      isRePrinted: Joi.boolean(),

      data: Joi.object({
        product: Joi.object({
          editorWidth: Joi.number(),
          editorHeight: Joi.number(),
          physicalWidth: Joi.number(),
          physicalHeight: Joi.number(),
          printAreas: Joi.array().items(Joi.object({
            width: Joi.number(),
            height: Joi.number(),
            top: Joi.number(),
            left: Joi.number(),
          })),
        }),
        previewImages: Joi.array().items(Joi.string()),
        storeUrl: Joi.string(),
        storeProductId: Joi.string(),
        design: Joi.any(),
        candle: Joi.any(),
        other: Joi.any(),
      }),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthenOptional,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { body, user } = request;

    const productVariantionName = (() => {
      const { data } = body;
      if (!data) return '';
      if (!data.design) return '';
      const { settings } = data.design;
      if (!settings) return '';
      if (settings.mirrored && settings.varnish) return 'Var + Mirr';
      if (!settings.mirrored && settings.varnish) return 'Var + No Mirr';
      if (!settings.mirrored && !settings.varnish) return 'No Var + No Mirr';
    })();

    // UPDATE EXISTING
    if (request.body.id) {
      const find = await DB.PrintJob.findByPk(request.body.id);
      if (!!find) {
        // if (!user?.role) {
        //   throw new Error(ERROR.PERMISSION_DENIED);
        // }
        const otherData = { ...find.data, ...body.data };
        if (body.previewUrl && find.previewUrl !== body.previewUrl) {
          const { labels, sharpness }: any = await AWSHelper.detectModerationLabel(body.previewUrl);
          otherData.previewSharpness = sharpness;
          otherData.previewLabels = labels;
        }
        for (let key in body) {
          find[key] = body[key];
        }
        find.data = otherData;

        if (!!productVariantionName) find.productVariantionName = productVariantionName;
        await find.save();
        return { success: true, data: find };
      }
    }
    if (!body.designId) throw new Error('Missing designId field');
    // CREATE NEW
    if (body.designId && !body.productId) {
      const design = await DB.Design.findOne({
        where: { id: body.designId },
        attributes: ['productId'],
      });
      if (design) body.productId = design.productId;
    }
    if (!body.clientId && !!body.data?.storeUrl) {
      const storeUrl = body.data.storeUrl;
      const store = await DB.OnlineStore.findOne({
        where: {
          url: storeUrl,
        }
      });
      if (!!store) {
        body.clientId = store.resellerId;
      }
    }

    const otherData = { ...body.data };
    if (body.previewUrl) {
      const { labels, sharpness }: any = await AWSHelper.detectModerationLabel(body.previewUrl);
      otherData.previewSharpness = sharpness;
      otherData.previewLabels = labels;
    }
    const data = await DB.PrintJob.create({
      id: VarHelper.genId(),
      ...body,
      data: otherData,
      readyForPrint: !!user ? true : false,
      productVariantionName,
    });

    return {
      success: true,
      data,
    }
  };
}

export default new UpsertPrintJob();
