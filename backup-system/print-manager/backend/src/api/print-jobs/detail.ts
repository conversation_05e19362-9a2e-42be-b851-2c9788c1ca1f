import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, checkAuthen, validateRequest, checkAuthenOptional } from "api/api-middlewares";
import { DB } from "db";
import { <PERSON>ar<PERSON><PERSON><PERSON> } from "helpers";
import Joi = require("joi");
import { ERROR } from "const";

class Detail implements TypeAPIHandler {

  url = "/api/print-jobs/:id";
  method = "GET";
  apiSchema = {
    params: Joi.object({
      id: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthenOptional,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { id } = request.params;
    
    const printJob = await DB.PrintJob.findByPk(id);
    if (!printJob) throw new Error(ERROR.NOT_EXISTED);

    return {
      success: true,
      data: printJob,
    };

  };
}

export default new Detail();