import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import Joi = require("joi");
import { ERROR } from "const";

class GetProducts implements TypeAPIHandler {

  url = "/api/print-jobs/products";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      ids: Joi.array().items(Joi.any()),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { ids } = request.body;

    const products = {};
    await Promise.all(ids.map(async id => {
      const printJob = await DB.PrintJob.findByPk(String(id));
      if (!printJob) return;
      const { productId } = printJob;
      const product = await DB.Product.findByPk(String(productId));
      if (!product) return;
      products[id] = product;
    }))

    return {
      success: true,
      data: products,
    };
  };
}

export default new GetProducts();
