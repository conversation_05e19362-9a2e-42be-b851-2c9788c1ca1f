import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, checkAuthen, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON>el<PERSON> } from "helpers";
import Joi = require("joi");
import { ERROR } from "const";

class ListPrintJobs implements TypeAPIHandler {

  url = "/api/print-jobs";
  method = "GET";
  apiSchema = {
    query: Joi.object({
      page: Joi.number(),
      isPrinted: Joi.number(),
      readyForPrint: Joi.number(),
      clientId: Joi.string().allow(''),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const user = request.user;
    if (user.role === 'user' || user.role === 'guess') throw new Error(ERROR.PERMISSION_DENIED);
    let { page, isPrinted, readyForPrint, clientId } = request.query;
    if (user.role === 'reseller' && !!clientId && clientId !== user.id) {
      throw new Error(ERROR.PERMISSION_DENIED);
    }
    if (!page) page = 1;
    if (isPrinted !== undefined) isPrinted = Boolean(+isPrinted);
    const whereObj = VarHelper.removeUndefinedField(user.role === 'admin' ? {
      isPrinted,
      readyForPrint: Boolean(readyForPrint),
      clientId: clientId || undefined,
    } : {
      clientId: user.id,
      readyForPrint: Boolean(readyForPrint),
    });
    console.log(whereObj);

    const PAGE_SIZE = 10;
    const result = await DB.PrintJob.findAndCountAll({
      where: whereObj,
      order: [
        ['updatedAt', 'DESC'],
      ],
      offset: (page - 1) * PAGE_SIZE,
      limit: PAGE_SIZE,
    });

    result.rows = result.rows.map(val => {
      // console.log('val', val);
      // delete val.data?.design?.canvas;
      const data = Object.assign({}, val.data);
      delete data?.design?.canvas;
      val.data = data;
      return val;
    })

    return {
      success: true,
      data: {
        list: result.rows,
        total: result.count,
        hasNext: page * PAGE_SIZE < result.count,
      },
    }
  };
}

export default new ListPrintJobs();
