import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON><PERSON>, checkAuthenOptional, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON>elper } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");
import { Op } from "sequelize";

class MarkPrintedMultiple implements TypeAPIHandler {

  url = "/api/print-jobs/mark-printed-multiple";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      ids: Joi.array().items(Joi.string()),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { body } = request;

    const { ids } = body;

    await DB.PrintJob.update({
      isPrinted: true
    }, {
      where: {
        id: {
          [Op.in]: ids,
        }
      }
    })

    return {
      success: true,
    }
  };
}

export default new MarkPrintedMultiple();