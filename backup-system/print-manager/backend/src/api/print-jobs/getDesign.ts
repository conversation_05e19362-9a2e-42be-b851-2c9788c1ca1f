import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, validateRequest, checkAuthenOptional } from "api/api-middlewares";
import { DB } from "db";
import Joi = require("joi");
import { ERROR } from "const";

class GetDesign implements TypeAPIHandler {

  url = "/api/print-jobs/design/:id";
  method = "GET";
  apiSchema = {
    params: Joi.object({
      id: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthenOptional,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { id } = request.params;
    
    const printJob = await DB.PrintJob.findByPk(id);
    if (!printJob) throw new Error(ERROR.NOT_EXISTED);

    const design = await DB.Design.findByPk(printJob.designId);
    if (!design) throw new Error(ERROR.NOT_EXISTED);

    return {
      success: true,
      data: design,
    };

  };
}

export default new GetDesign();
