import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TProduct } from "type";
import { check<PERSON><PERSON><PERSON>, checkAdmin, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON>el<PERSON> } from "helpers";
import { ERROR, INCH_TO_MM } from 'const';
import Joi = require("joi");

class AddNewRandomPrintJob implements TypeAPIHandler {

  url = "/api/print-jobs/add-random";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      number: Joi.number(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
    checkAdmin,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const number = request.body.number || 1;

    // const randomImages = await VarHelper.getRandomImages('summer', number);
    // console.log('randomImages', randomImages);
    // if (randomImages.length !== number) throw new Error('Unslash image error');
    const randomImages = [{
      id: 'manual',
      url: 'https://print-manager-media.s3.eu-west-1.amazonaws.com/tiger.png',
      height: 300 * 634 / 718,
      width: 300,
    }];

    const list : Array<TPrintJob> = randomImages.map((val, index) => {
      return {
        id: VarHelper.genId(),
        // previewUrl: `https://dummyimage.com/400x400/fefefe/00000b.jpg&text=preview-unavailable-for-dummy-job`,
        previewUrl: randomImages[index].url,
        artworkUrls: [
          randomImages[index].url,
        ],

        clientId: '2022',
        designId: '2022',
        quantity: 1,
        isPrinted: false,
        isPDFDownloaded: false,
        isRePrinted: false,
        data: {
          product: {
            physicalWidth: 6 * INCH_TO_MM,
            physicalHeight: 6 * INCH_TO_MM,
            printAreas: [
              {
                width: 6 * INCH_TO_MM,
                height: 8 * INCH_TO_MM,
                top: 0,
                left: 0,
              }
            ]
          }
        },
        productId: '2022',
        productName: VarHelper.randomName(),
      };
    });

    await DB.PrintJob.bulkCreate(list);

    return {
      success: true,
      data: list,
    }
  };
}

export default new AddNewRandomPrintJob();