import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON><PERSON>, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON><PERSON> } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");
import <PERSON><PERSON> from 'stripe';
import { Op } from "sequelize";
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, { apiVersion: null });

class GeneralDataList implements TypeAPIHandler {

  url = "/api/users/is-reseller-complete-setup";
  method = "GET";
  apiSchema = {
    
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    if (request.user.role === 'admin') return { sucess: false, data: false };
    const resellerId = request.user.role === 'reseller' ? request.user.id : request.user.resellerId;
    console.log('resellerId', resellerId)
    const u = await DB.User.findByPk(resellerId);
    if (!u || !u.resellerStripeId) return { success: true, data: false };
    console.log('u.resellerStripeId', u.resellerStripeId)
    const transactions = await stripe.customers.listBalanceTransactions(u.resellerStripeId, { limit: 6 });
    if (transactions.data.length === 0) return { success: true, data: false };
    console.log('transactions.data.length', transactions.data.length)
    const publishedProducts = await DB.Design.count({
      where: {
        createdByUserId: resellerId,
        products: {
          [Op.like]: `%storeId%`
        }
      }
    });
    console.log('publishedProducts', publishedProducts);
    if (publishedProducts === 0) return { success: true, data: false };
    return {
      success: true,
      data: true,
    }
  };
}

export default new GeneralDataList();