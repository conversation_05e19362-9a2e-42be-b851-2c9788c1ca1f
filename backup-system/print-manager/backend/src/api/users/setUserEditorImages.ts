import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON><PERSON>, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON><PERSON> } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");

class GeneralDataList implements TypeAPIHandler {

  url = "/api/users/:userId/editor-images";
  method = "POST";
  apiSchema = {
    params: Joi.object({
      userId: Joi.string().required(),
    }),
    body: Joi.object({
      images: Joi.array().items(Joi.string()).required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    // checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { userId } = request.params;
    const { images } = request.body;
    let u = await DB.UserEditorImage.findOne({
      where: {
        userId,
      }
    });
    if (!u) {
      u = await DB.UserEditorImage.create({
        id: VarHelper.genId(),
        userId,
        images,
      });
      return { success: true, data: images };
    }
    u.images = images;
    await u.save();
    return {
      success: true,
      data: u.images
    }
  };
}

export default new GeneralDataList();