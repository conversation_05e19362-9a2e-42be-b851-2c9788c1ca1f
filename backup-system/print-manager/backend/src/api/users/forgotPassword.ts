import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'type';
import { validateRequest, combineMiddlewares } from '../api-middlewares'
import { ERROR } from 'const';
import { DB } from 'db';
import { VarHelper } from 'helpers';
import Joi = require("joi");
import MailHelper from 'helpers/MailHelper';
import { genEmailHtml } from 'helpers/email-templates';

class ForgotPassword implements TypeAPIHandler {
  url = '/api/users/forgot-password';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      email: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
  ]);

  handler = async (request) => {
    const { email } = request.body;

    const user = await DB.User.findOne({
      where: {
        email,
      }
    });
    if (!user) throw new Error(ERROR.NOT_EXISTED);

    const resetCode = VarHelper.genShortId(10);
    user.otherData = {
      ...user.otherData,
      resetPasswordCode: resetCode,
    }

    await user.save();

    await MailHelper.sendSMTPEmail({
      to: user.email,
      subject: 'Bottle Goose Password Reset',
      html: genEmailHtml({
        title: "Password reset",
        message: "You have requested a password reset. Please click here to reset your password. If you did not request a password reset, please contact your Bottle Goose Administrator",
        link: `https://${process.env.DEV ? 'bg-dev.' : 'bg-production.'}bottledgoose.co.uk/reset-password?code=${resetCode}`,
      }),
    })

    return {
      success: true,
    }
  }
}

export default new ForgotPassword();
