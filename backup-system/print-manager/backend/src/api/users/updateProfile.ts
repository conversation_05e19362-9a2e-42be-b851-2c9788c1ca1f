import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON><PERSON>, checkAdmin, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB, InMemory as RedisCache } from "db";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");
import { reGenerateCandleStickers } from "api/packing-slips/upsert";

class UpdateProfile implements TypeAPIHandler {

  url = "/api/users/me/profile";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      firstName: Joi.string().allow(''),
      lastName: Joi.string().allow(''),
      accountName: Joi.string().allow(''),
      password: Joi.string().allow(''),
      isAutoAccept: Joi.boolean().allow(''),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkA<PERSON><PERSON>,
  ]);

  updateRedisCache = async (user) => {
    const listToken = await RedisCache.hgetAsync('JWTs', user.id);
    if (listToken) {
      const parsedList = JSON.parse(listToken);
      const userJson =  JSON.stringify(user);
      parsedList.forEach((token) => {
        RedisCache.hsetAsync('JWT', token, userJson);
      });
    }
  }

  handler = async (request: TRequestUser, reply) => {
    const { body } = request;
    if (request.user.role !== 'reseller' && body.password) throw new Error(ERROR.PERMISSION_DENIED);
    if (body.password) {
      body.password = await AuthenHelper.hashPassword(body.password);
    }

    const find = await DB.User.findByPk(request.user.id);
    if (!!find) {
      if (body.firstName) {
        find.firstName = body.firstName;
      }
      if (body.lastName) {
        find.lastName = body.lastName;
      }
      if (body.accountName) {
        find.accountName = body.accountName;
      }
      if (body.password) {
        find.password = body.password;
      }
      if (body.isAutoAccept === false) {
        find.isAutoAccept = false;
      }
      if (body.isAutoAccept === true) {
        find.isAutoAccept = true;
      }
      await find.save();
      const user = await DB.User.findByPk(request.user.id,{
        include: {model: DB.OnlineStore,as: 'onlineStore'},
      });
      await this.updateRedisCache(user);
      const updateCandleSticker = await reGenerateCandleStickers(find.id);
      const publicInfo = Object.assign(JSON.parse(JSON.stringify(user)), { password: undefined });
      return { success: true, data: publicInfo, updateCandleSticker };
    }

    return {
      success: false, error: ERROR.NOT_EXISTED
    }
  };
}

export default new UpdateProfile();
