import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'type';
import { validateRequest, combineMiddlewares, checkAuthen, checkAdmin } from '../api-middlewares'
import { ERROR } from 'const';
import { DB, InMemory as RedisCache } from 'db';
import { Authen<PERSON>elper } from 'helpers';
import Joi = require("joi");

class Login implements TypeAPIHandler {
  url = '/api/users/admin-login-as-reseller';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      email: Joi.string().required(),
      passwordHash: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
    checkAdmin,
  ]);

  handler = async (request, reply) => {
    const { email, passwordHash } = request.body;
    const user = await DB.User.findOne({
      where: { email: email.toLowerCase() },
    });
    if (!user) throw new Error(ERROR.NOT_EXISTED);

    const isPasswordCorrect = user.password === passwordHash;
    if (!isPasswordCorrect) throw new Error(ERROR.LOGIN_FAILED);

    const token = AuthenHelper.genJWTToken({ id: user.id });
    const publicInfo = Object.assign(JSON.parse(JSON.stringify(user)), { 
      password: undefined, 
      adminInspect: request.user?.adminInspect || request.headers['Authorization'] || request.headers['authorization'],
    });
    _afterLogin(publicInfo, token);

    return {
      success: true,
      data: {
        token,
        publicInfo,
      }
    }
  }
}

async function _afterLogin(user, token) {
  const listToken = await RedisCache.hgetAsync('JWTs', user.id);
  if (listToken) {
    const parsedList = JSON.parse(listToken);
    if (!parsedList.includes(token)) {
      parsedList.push(token);
      await RedisCache.hsetAsync('JWTs', user.id, JSON.stringify(parsedList));
    }
  } else {
    await RedisCache.hsetAsync('JWTs', user.id, JSON.stringify([token]));
  }
  await RedisCache.hsetAsync('JWT', token, JSON.stringify(user));
}

export default new Login();
