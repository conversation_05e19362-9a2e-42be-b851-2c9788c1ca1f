import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, checkAuthen, validateRequest, checkAdmin } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON>elper } from "helpers";
import Joi = require("joi");
import { ERROR } from "const";

class GetResellerSampleToken implements TypeAPIHandler {

  url = "/api/users/reseller/sample-token";
  method = "GET";
  apiSchema = {
    
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    if (request.user?.role === 'admin') throw new Error(ERROR.PERMISSION_DENIED);
    const resellerId = request.user?.resellerId || request.user?.id;
    const user = await DB.User.findByPk(resellerId);

    return {
      success: true,
      data: typeof user?.otherData?.sampleToken === 'number' ? user?.otherData?.sampleToken : 10,
    }
  };
}

export default new GetResellerSampleToken();