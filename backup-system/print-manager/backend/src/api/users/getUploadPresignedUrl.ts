import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "helpers";
import { TypeAP<PERSON>Handler } from "type";
import { combineMiddlewares, checkAuthen } from "../api-middlewares";
import Joi = require("joi");

class GetUploadPresignedUrl implements TypeAPIHandler {
  url = "/api/users/me/get-upload-url";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      serverSidePath: Joi.string().required(),
      contentType: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    // checkAuthen,
  ]);

  handler = async (request, reply) => {
    const { serverSidePath, contentType } = request.body;
    if (!serverSidePath || !contentType)
      return { success: false, error: "Wrong input" };
    const url = AWSHelper.getSignedUrlForUpload(serverSidePath, contentType);
    return { success: true, data: url };
  };
}

export default new GetUploadPresignedUrl();
