import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON>en, checkAdmin, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB, InMemory as RedisCache } from "db";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");
import { reGenerateCandleStickers } from "api/packing-slips/upsert";

class UpsertReseller implements TypeAPIHandler {

  url = "/api/users/resellers";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      id: Joi.string(),
      firstName: Joi.string(),
      lastName: Joi.string(),
      email: Joi.string(),
      password: Joi.string(),
      photoUrl: Joi.string(),
      accountName: Joi.string().allow(''),
      addressLine1: Joi.string().allow(''),
      addressLine2: Joi.string().allow(''),
      town: Joi.string().allow(''),
      country: Joi.string().allow(''),
      currency: Joi.string().allow(''),
      postCode: Joi.string().allow(''),
      isAutoAccept: Joi.boolean(),
      otherData: Joi.any(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
    // checkAdmin,
  ]);

  updateRedisCache = async (user) => {
    const listToken = await RedisCache.hgetAsync('JWTs', user.id);
    if (listToken) {
      const parsedList = JSON.parse(listToken);
      const userJson =  JSON.stringify(user);
      parsedList.forEach((token) => {
        RedisCache.hsetAsync('JWT', token, userJson);
      });
    }
  }

  handler = async (request: TRequestUser, reply) => {
    const { body, user } = request;
    console.log("🚀 ~ file: upsertReseller.ts:37 ~ UpsertReseller ~ handler= ~ body:", body)
    if (body.password) {
      if (request.user?.role !== 'admin' && request.user.id !== body.id) throw new Error(ERROR.PERMISSION_DENIED);
      body.password = await AuthenHelper.hashPassword(body.password);
    }
    if (!!body.id && body.id !== user.id) {
      if (!body.id && request.user.role === 'user') throw new Error(ERROR.PERMISSION_DENIED);
      if (!!body.id && request.user?.role !== 'admin' && (request.user.id !== body.id || request.user.resellerId !== body.id)) throw new Error(ERROR.PERMISSION_DENIED);
    }
    
    // UPDATE EXISTING
    if (body.id) {
      const find = await DB.User.findByPk(request.body.id);
      if (!!find) {
        for (let key in body) {
          find[key] = body[key];
        }
        await find.save();
        const publicInfo = Object.assign(JSON.parse(JSON.stringify(find)), { password: undefined });
        this.updateRedisCache(find);
        const updateCandleSticker = await reGenerateCandleStickers(find.id);
        return { success: true, data: publicInfo, updateCandleSticker };
      }
    }
    // CREATE NEW
    const existed = await DB.User.findOne({
      where: {
        email: body.email.toLowerCase(),
      }
    });
    if (existed) throw new Error(ERROR.EMAIL_ALREADY_EXISTED);
    const data = await DB.User.create({
      id: VarHelper.genId(),
      ...body,
      role: 'reseller',
    });

    const publicInfo = Object.assign(JSON.parse(JSON.stringify(data)), { password: undefined });

    return {
      success: true,
      data: publicInfo,
    }
  };
}

export default new UpsertReseller();
