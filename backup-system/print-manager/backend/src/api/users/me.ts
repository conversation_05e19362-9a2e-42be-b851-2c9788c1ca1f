import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON>en, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON>per } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");

class GeneralDataList implements TypeAPIHandler {

  url = "/api/users/me";
  method = "GET";
  apiSchema = {
    
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    return {
      success: true,
      data: request.user,
    }
  };
}

export default new GeneralDataList();