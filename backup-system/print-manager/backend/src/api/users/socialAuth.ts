import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'type';
import { validateRequest, combineMiddlewares } from '../api-middlewares'
import { ERROR } from 'const';
import { DB, InMemory as RedisCache } from 'db';
import { <PERSON>the<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>him<PERSON>, VarHelper } from 'helpers';
import admin from 'firebase-admin';
import Joi = require("joi");
import DeviceDetector = require("device-detector-js");
import MailHelper from 'helpers/MailHelper';
import { genEmailHtml } from 'helpers/email-templates';

class SocialAuth implements TypeAPIHandler {
  url = '/api/users/social-auth';
  method = 'POST';
  
  apiSchema = {
    body: Joi.object({
      idToken: Joi.string().required(),
      provider: Joi.string().valid('google', 'facebook').required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
  ]);

  handler = async (request, reply) => {
    const { idToken, provider } = request.body;
    
    try {
      const decodedToken = await admin.auth().verifyIdToken(idToken);
      const { email, name, picture, uid } = decodedToken;
      
      let user = await DB.User.findOne({
        where: {
          email: email.toLowerCase(),
        }
      });

      if (!user) {
        const names = name.split(' ');
        const deviceDetector = new DeviceDetector();
        const deviceInfo = deviceDetector.parse(request.headers['user-agent']);

        user = await DB.User.create({
          id: VarHelper.genId(),
          email: email.toLowerCase(),
          firstName: names[0],
          lastName: names.slice(1).join(' '),
          photoUrl: picture,
          role: 'reseller',
          password: '',
          otherData: {
            provider,
            firebaseUid: uid,
            authType: 'social',
            registeredDevice: deviceInfo,
            createdVia: 'social_auth'
          }
        });

        // Send welcome email
        await MailHelper.sendSMTPEmail({
          to: email,
          cc: [],
          subject: "Welcome to Bottled Goose",
          html: genEmailHtml({
            title: "Hello",
            message: `Welcome to Bottled Goose! You have successfully registered via ${provider}.`,
            link: `https://${process.env.DEV ? 'bg-dev.' : 'bg-production.'}bottledgoose.co.uk/`,
          }),
        });

        try {
          if (process.env.MAILCHIMP_API_KEY) {
            await MailChimp.batchSubscribe([user]);
          }
        } catch(err) {
          console.log('MailChimp error:', err);
        }
      }

      const token = AuthenHelper.genJWTToken({ id: user.id });
      const publicInfo = Object.assign(JSON.parse(JSON.stringify(user)), { 
        password: undefined 
      });
      
      await _afterRegister(publicInfo, token);

      return {
        success: true,
        data: {
          token,
          publicInfo,
        }
      }
    } catch (error) {
      console.error('Social auth error:', error);
      throw new Error('Something went wrong with social auth');
    }
  }
}

async function _afterRegister(user, token) {
  const listToken = await RedisCache.hgetAsync('JWTs', user.id);
  if (listToken) {
    const parsedList = JSON.parse(listToken);
    if (!parsedList.includes(token)) {
      parsedList.push(token);
      await RedisCache.hsetAsync('JWTs', user.id, JSON.stringify(parsedList));
    }
  } else {
    await RedisCache.hsetAsync('JWTs', user.id, JSON.stringify([token]));
  }
  await RedisCache.hsetAsync('JWT', token, JSON.stringify(user));
}

export default new SocialAuth();
