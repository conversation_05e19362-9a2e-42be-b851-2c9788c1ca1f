import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, checkAuthen, validateRequest, checkAdmin } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON>elper } from "helpers";
import Joi = require("joi");
import { ERROR } from "const";

class CheckAutoAcceptOrder implements TypeAPIHandler {

  url = "/api/reseller/:id/check-auto-accept";
  method = "GET";
  apiSchema = {
    params: Joi.object({
      id: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { id } = request.params;
    const reseller = await DB.User.findOne({
      where: {
        id,
        role: 'reseller',
      }
    })
    if (!reseller) throw new Error(ERROR.NOT_EXISTED);

    return {
      success: true,
      data: {
        name: reseller.firstName + ' ' + reseller.lastName,
        isAutoAccept: <PERSON><PERSON><PERSON>(reseller.isAutoAccept),
      },
    }
  };
}

export default new CheckAutoAcceptOrder();