import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, checkAuthen, validateRequest, checkAdmin } from "api/api-middlewares";
import { DB } from "db";
import { <PERSON>ar<PERSON><PERSON><PERSON> } from "helpers";
import Joi = require("joi");
import { ERROR } from "const";

class UserDetail implements TypeAPIHandler {

  url = "/api/users/:id";
  method = "GET";
  apiSchema = {
    params: Joi.object({
      id: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { id } = request.params;
    console.log('id', id);
    console.log('request.user', request.user);
    if (request.user?.role !== 'admin' && (request.user.id !== id && request.user.resellerId !== id)) throw new Error(ERROR.PERMISSION_DENIED);
    const user = await DB.User.findByPk(id);
    if (!user) throw new Error(ERROR.NOT_EXISTED);
    const publicInfo = Object.assign(JSON.parse(JSON.stringify(user)), { password: undefined });

    return {
      success: true,
      data: publicInfo,
    }
  };
}

export default new UserDetail();