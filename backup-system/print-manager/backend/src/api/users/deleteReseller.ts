import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, checkAuthen, validateRequest, checkAdmin } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON>per } from "helpers";
import Joi = require("joi");
import { ERROR } from "const";

class UserDetail implements TypeAPIHandler {

  url = "/api/users/:id";
  method = "DELETE";
  apiSchema = {
    params: Joi.object({
      id: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
    checkAdmin,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { id } = request.params;
    if (request.user?.role !== 'admin') throw new Error(ERROR.PERMISSION_DENIED);
    const user = await DB.User.findByPk(id);
    if (!!user) {
      await DB.SurveyAnwser.destroy({ where: { userId: id } });

      await user.destroy();
    }

    return {
      success: true,
    }
  };
}

export default new UserDetail();