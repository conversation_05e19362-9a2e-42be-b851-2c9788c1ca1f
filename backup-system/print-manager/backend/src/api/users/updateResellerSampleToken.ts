import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, checkAuthen, validateRequest, checkAdmin } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON>per } from "helpers";
import Joi = require("joi");
import { ERROR } from "const";

class GetResellerSampleToken implements TypeAPIHandler {

  url = "/api/users/reseller/update-sample-token";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      resellerId: Joi.string().required(),
      token: Joi.number().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    if (request.user?.role !== 'admin') throw new Error(ERROR.PERMISSION_DENIED);
    const { resellerId, token } = request.body;
    const user = await DB.User.findByPk(resellerId);
    if (!user) throw new Error(ERROR.NOT_EXISTED);
    const otherData = Object.assign(user.otherData || {}, { sampleToken: token });
    user.otherData = otherData;
    await user.save();
    return {
      success: true,
      data: typeof user?.otherData?.sampleToken === 'number' ? user?.otherData?.sampleToken : 10,
    }
  };
}

export default new GetResellerSampleToken();