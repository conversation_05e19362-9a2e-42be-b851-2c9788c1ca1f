import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "helpers";
import { <PERSON>AP<PERSON><PERSON><PERSON><PERSON> } from "type";
import { combineMiddlewares, checkAuthen } from "../api-middlewares";
import Joi = require("joi");
import axios from "axios";

class CheckImageQuality implements TypeAPIHandler {
  url = "/api/users/me/check-image-quality";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      urls: Joi.array().items(Joi.string()),
      callbackUrl: Joi.string().allow(''),
    }),
  }

  preHandler = combineMiddlewares([
    // checkAuthen,
  ]);

  handler = async (request, reply) => {
    const { urls, callbackUrl } = request.body;
    if (!urls?.length) throw new Error("Invalid input");

    const qualities = await Promise.all(
      urls.map(async url => {
        const { labels, sharpness }: any = await AWSHelper.detectModerationLabel(url);
        return { labels, sharpness, url };
      })
    )

    if (callbackUrl) {
      await  axios.request({
        url: callbackUrl,
        method: 'post',
        headers: { 'Content-Type': 'application/json' },
        data: JSON.stringify({ qualities }),
      })
    }
   
    return {
      success: true,
      data: qualities,
    };
  };
}

export default new CheckImageQuality();
