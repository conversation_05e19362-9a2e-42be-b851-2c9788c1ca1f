import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, checkAuthen, validateR<PERSON><PERSON>, checkAdmin } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON><PERSON> } from "helpers";
import Joi = require("joi");
import { ERROR } from "const";
import Strip<PERSON> from 'stripe';
import { Op, Sequelize } from "sequelize";
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, { apiVersion: null });

class ListReseller implements TypeAPIHandler {

  url = "/api/users/resellers";
  method = "GET";
  apiSchema = {
    query: Joi.object({
      page: Joi.number(),
      pageSize: Joi.number(),
      search: Joi.string().allow(''),
      countDesign: Joi.boolean(),
      countStore: Joi.boolean(),
      sortBy: Joi.string().allow(''),
      sortOrder: Joi.string().allow(''),
      hasStore: Joi.boolean(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
    checkAdmin,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const user = request.user;
    let { page, pageSize, search, countDesign, countStore, sortBy, sortOrder, hasStore } = request.query;

    if (!page) page = 1;

    const PAGE_SIZE = pageSize || 1000;
    console.log('search', search);
    const escapeSearch = `%${search}%`.replace('+', '\\+');
    console.log('escapeSearch', escapeSearch);
    const searchQuery = search ? {
      [Op.or]: [
        { email: { [Op.iLike]: escapeSearch.toLowerCase() } },
        { firstName: { [Op.iLike]: escapeSearch } },
        { lastName: { [Op.iLike]: escapeSearch } },
        { accountName: { [Op.iLike]: escapeSearch } },
        Sequelize.literal(`CONCAT("firstName", ' ', "lastName") ILIKE '%${escapeSearch}%'`)
      ]
    } : {};

    const includeAttrs = [];
    if (countDesign) {
      includeAttrs.push([
        Sequelize.literal(`(
          SELECT COUNT(*)
          FROM "designs"
          WHERE "designs"."createdByUserId" = "users"."id"
            AND "designs"."inactive" IS NOT TRUE
        )`), 'designCount'
      ]);
    }
    if (countStore) {
      includeAttrs.push([
        Sequelize.literal(`(
          CASE WHEN EXISTS (
            SELECT 1
            FROM "online_stores"
            WHERE "online_stores"."resellerId" = "users"."id"
            AND "online_stores"."inactive" IS NOT TRUE
            LIMIT 1
          ) THEN TRUE ELSE FALSE END
        )`),
        'hasStore'
      ]);
    }

    if (['true', 'false'].includes(String(hasStore))) {
      searchQuery[Op.and] = searchQuery[Op.and] || [];
      searchQuery[Op.and].push(
        Sequelize.literal(
          `EXISTS (SELECT 1 FROM "online_stores" WHERE "online_stores"."resellerId" = "users"."id") = ${hasStore}`
        )
      );
    }

    let sortByClause: any = 'updatedAt';
    if (sortBy) {
      if (['walletBalance', 'salesTotal', 'vatTotal', 'shippingTotal', 'sampleSalesTotal', 'wholesaleTotal'].includes(sortBy)) {
        sortByClause = Sequelize.literal(`COALESCE("${sortBy}", 0)`);
      } else if (sortBy === 'designCount') {
        sortByClause = Sequelize.literal(`(
          SELECT COUNT(*)
          FROM "designs" 
          WHERE "designs"."createdByUserId" = "users"."id"
        )`);
      } else {
        sortByClause = sortBy;
      }
    }

    const result = await DB.User.findAndCountAll({
      where: {
        role: 'reseller',
        ...searchQuery,
      },
      attributes: {
        include: includeAttrs,
      },
      order: [
        [
          sortByClause,
          sortOrder || 'DESC'
        ]
      ],
      offset: (page - 1) * PAGE_SIZE,
      limit: PAGE_SIZE,
      logging: console.log,
    });

    for (let i = 0; i < result.rows.length; i++) {
      const reseller = result.rows[i];
      // if (!reseller.resellerStripeId) {
      //   continue;
      // }
      // const balances = await stripe.customers.retrieve(reseller.resellerStripeId, {
      //   expand: ['cash_balance'],
      // });
      // const balanceText = (() => {
      //   // @ts-ignore
      //   const _balance = reseller.walletBalance || 0;
      //   return VarHelper.formatBalance((_balance) / 100);
      // })();
      // @ts-ignore
      reseller.setDataValue('balanceText', '£ ' + VarHelper.formatBalance(reseller.walletBalance || 0));

    }

    return {
      success: true,
      data: {
        list: result.rows,
        total: result.count,
        hasNext: page * PAGE_SIZE < result.count,
      },
    }
  };
}

export default new ListReseller();
