import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkA<PERSON>en, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON>elper } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");
import <PERSON><PERSON><PERSON><PERSON> from "helpers/MailHelper";

class GeneralDataList implements TypeAPIHandler {

  url = "/api/users/send-email-with-desktop-link";
  method = "POST";
  apiSchema = {
    
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const userName = [request.user.firstName, request.user.lastName].filter(Boolean).join(" ");
    MailHelper.sendSMTPEmail({
      to: request.user.email,
      subject: "hello from Bottled Goose",
      html: `
        <p>Hi ${userName},</p>
        <p>Many thanks for creating a Bottled Goose account. Our platform is best viewed on a laptop or desktop rather than a mobile device.</p>
        <p>Here is the link: <a href="https://bg-production.bottledgoose.co.uk">https://bg-production.bottledgoose.co.uk</a> to open it when you're on laptop/desktop</p>
        <p>Have a great day!</p>
        <p>Bottled Goose team.</p>
      `,
    })
    return {
      success: true,
      data: request.user,
    }
  };
}

export default new GeneralDataList();