import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'type';
import { validateRequest, combineMiddlewares } from '../api-middlewares'
import { ERROR } from 'const';
import { DB } from 'db';
import { Authen<PERSON><PERSON><PERSON> } from 'helpers';
import Joi = require("joi");
import { Op } from 'sequelize';

class ResetPassword implements TypeAPIHandler {
  url = '/api/users/reset-password';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      code: Joi.string().required(),
      newPassword: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
  ]);

  handler = async (request) => {
    const { code, newPassword } = request.body;

    const user = await DB.User.findOne({
      where: {
        otherData: {
          [Op.like]: `%${code}%`,
        },
      }
    });
    if (!user) throw new Error(ERROR.NOT_EXISTED);
    if (user.otherData?.resetPasswordCode !== code) throw new Error(ERROR.NOT_AUTHEN);

    user.password = await AuthenHelper.hashPassword(newPassword);
    user.otherData = {
      ...user.otherData,
      resetPasswordCode: undefined,
    }

    await user.save();

    return {
      success: true,
    }
  }
}

export default new ResetPassword();
