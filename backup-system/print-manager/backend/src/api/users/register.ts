import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'type';
import { validateRequest, combineMiddlewares } from '../api-middlewares'
import { ERROR } from 'const';
import { DB, InMemory as RedisCache } from 'db';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>him<PERSON>, VarHelper } from 'helpers';
import Joi = require("joi");
import MailHelper from 'helpers/MailHelper';
import { genEmailHtml } from 'helpers/email-templates';
import DeviceDetector = require("device-detector-js");

class Register implements TypeAPIHandler {
  url = '/api/users/register';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      email: Joi.string().required(),
      password: Joi.string().required(),
      firstName: Joi.string().required(),
      lastName: Joi.string().allow(''),
      role: Joi.string().required(),
      photoUrl: Joi.string().allow(''),
      addressLine1: Joi.string().allow(''),
      addressLine2: Joi.string().allow(''),
      town: Joi.string().allow(''),
      country: Joi.string().allow(''),
      postCode: Joi.string().allow(''),
      accountName: Joi.string().allow(''),
      otherData: Joi.any(),
      __key: Joi.string(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
  ]);

  handler = async (request, reply) => {
    const { role, __key, email, password, firstName, lastName, photoUrl, otherData, addressLine1, addressLine2, town, country, postCode, accountName } = request.body;
    if (role === 'admin' && __key !== '3njkdnf231&*(*adbf') {
      throw new Error(ERROR.PERMISSION_DENIED);
    }
    const hashedPassword = await AuthenHelper.hashPassword(password);

    const existed = await DB.User.findOne({
      where: {
        email: email.toLowerCase(),
      }
    });
    if (existed) throw new Error(ERROR.EMAIL_ALREADY_EXISTED);

    const otherDataComputed = Object.assign({}, otherData);
    if (otherDataComputed.registeredUserAgent) {
      const deviceDetector = new DeviceDetector();
      otherDataComputed.registedDevice = deviceDetector.parse(otherDataComputed.registeredUserAgent);
    }
  
    const user = await DB.User.create({
      id: VarHelper.genId(),
      email: email.toLowerCase(),
      password: hashedPassword as string,
      firstName, lastName, photoUrl, otherData: otherDataComputed,
      role,
      addressLine1, addressLine2, town, country, postCode,
      accountName
    });

    const token = AuthenHelper.genJWTToken({ id: user.id });
    const publicInfo = Object.assign(JSON.parse(JSON.stringify(user)), { password: undefined });
    _afterRegister(publicInfo, token);

    if (role === 'reseller') {
      await MailHelper.sendSMTPEmail({
        to: email,
        cc: [],
        subject: "Welcome to Bottled Goose",
        html: genEmailHtml({
          title: "Hello",
          message: `Welcome to Bottled Goose! You have successfully registered as a reseller. Please click the link below to publish products and view your orders.`,
          link: `https://${process.env.DEV ? 'bg-dev.' : 'bg-production.'}bottledgoose.co.uk/`,
        }),
      });
      try {
        if (process.env.MAILCHIMP_API_KEY) await MailChimp.batchSubscribe([user]);
      } catch(err) {
        console.log(err);
      }
    }
    return {
      success: true,
      data: {
        token,
        publicInfo,
      }
    }
  }
}

async function _afterRegister(user, token) {
  const listToken = await RedisCache.hgetAsync('JWTs', user.id);
  if (listToken) {
    const parsedList = JSON.parse(listToken);
    if (!parsedList.includes(token)) {
      parsedList.push(token);
      await RedisCache.hsetAsync('JWTs', user.id, JSON.stringify(parsedList));
    }
  } else {
    await RedisCache.hsetAsync('JWTs', user.id, JSON.stringify([token]));
  }
  await RedisCache.hsetAsync('JWT', token, JSON.stringify(user));
}

export default new Register();
