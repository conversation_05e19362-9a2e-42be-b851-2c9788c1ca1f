import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, checkAuthen, validateRequest, checkAdmin } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON><PERSON> } from "helpers";
import Joi = require("joi");
import { ERROR } from "const";
import { checkReseller } from "api/api-middlewares/authen";

class UserResellerUser implements TypeAPIHandler {

  url = "/api/users-reseller/:id";
  method = "GET";
  apiSchema = {
    params: Joi.object({
      id: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
    checkReseller,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { id } = request.params;
    const user = await DB.User.findByPk(id,{
      include: {model: DB.OnlineStore,as: 'onlineStore'},
    });
    if (!user) throw new Error(ERROR.NOT_EXISTED);
    const publicInfo = Object.assign(JSON.parse(JSON.stringify(user)), { password: undefined });

    return {
      success: true,
      data: publicInfo,
    }
  };
}

export default new UserResellerUser();