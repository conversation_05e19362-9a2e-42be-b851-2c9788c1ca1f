import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, checkAuthen, validateRequest, checkAdmin } from "api/api-middlewares";
import { DB } from "db";
import Joi = require("joi");
import { Op, Sequelize } from "sequelize";
import Stripe from 'stripe';
import moment from 'moment';
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, { apiVersion: null });

class ListReseller implements TypeAPIHandler {

  url = "/api/users/resellers-sales-report";
  method = "GET";
  apiSchema = {
    query: Joi.object({
      fromDate: Joi.string().allow(''),
      toDate: Joi.string().allow(''),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
    checkAdmin,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const user = request.user;
    let { fromDate, toDate } = request.query;

    let totals = {
      salesTotal: 0,
      shippingTotal: 0,
      vatTotal: 0,
      sampleSalesTotal: 0,
      wholesaleTotal: 0,
    };
    let resellers = [];
    if (!fromDate && !toDate) {
      const result = await DB.User.findAll({
        where: {
          role: 'reseller',
        },
        attributes: [
          [Sequelize.fn('SUM', Sequelize.col('salesTotal')), 'totalSales'],
          [Sequelize.fn('SUM', Sequelize.col('shippingTotal')), 'totalShipping'],
          [Sequelize.fn('SUM', Sequelize.col('vatTotal')), 'totalVat'],
          [Sequelize.fn('SUM', Sequelize.col('sampleSalesTotal')), 'totalSampleSales'],
          [Sequelize.fn('SUM', Sequelize.col('wholesaleTotal')), 'totalWholesale'],
        ],
        raw: true,
      });
      if (result[0]) {
        // @ts-ignore
        const { totalSales, totalShipping, totalVat, totalSampleSales, totalWholesale } = result[0];
        totals = {
          salesTotal: totalSales || 0,
          shippingTotal: totalShipping || 0,
          vatTotal: totalVat || 0,
          sampleSalesTotal: totalSampleSales || 0,
          wholesaleTotal: totalWholesale || 0,
        }
      }
    } else {
      // get invoices from date to date

      const list = await DB.Invoice.findAll({
        where: {
          paidAt: {
            [Op.between]: [
              moment(fromDate, 'DD-MM-YYYY').startOf('day').toISOString(),
              moment(toDate, 'DD-MM-YYYY').endOf('day').toISOString()
            ],
          }
        },
        raw: true,
      });
      // Group invoices by resellerId
      const invoicesByReseller = {};
      for (const invoice of list) {
        const resellerId = invoice.resellerId;
        if (!invoicesByReseller[resellerId]) {
          invoicesByReseller[resellerId] = [];
        }
        invoicesByReseller[resellerId].push(invoice);
      }
    
      // Calculate totals for each reseller
      for (const resellerId in invoicesByReseller) {
        let salesTotal = 0;
        let shippingTotal = 0;
        let vatTotal = 0;
        let sampleSalesTotal = 0;
        let wholesaleTotal = 0;

        for (const invoice of invoicesByReseller[resellerId]) {
          try {
            let invoiceData = invoice.data;
            invoiceData = typeof invoiceData === "string" ? JSON.parse(invoiceData) : invoiceData;
    
            if (invoiceData?.stripeInvoiceID) {
              const stripeInvoice = await stripe.invoices.retrieve(invoiceData.stripeInvoiceID);
    
              // Calculate totals from invoice line items
              for (const item of stripeInvoice.lines.data) {
                if (item.description?.includes("Shipping fee")) {
                  shippingTotal += item.amount;
                } else if (item.description?.includes("VAT")) {
                  vatTotal += item.amount;
                } else {
                  if (invoiceData?.isWholesale) {
                    wholesaleTotal += item.amount;
                  } else if (invoiceData?.isSampleRequest) {
                    sampleSalesTotal += item.amount;
                  } else {
                    salesTotal += item.amount;
                  }
                }
              }
            }
          } catch (error) {
    
          }
        }

        const reseller = await DB.User.findByPk(resellerId);
        resellers.push({
          id: resellerId,
          name: [reseller?.firstName, reseller?.lastName].join(' '),
          email: reseller?.email,
          accountName: reseller?.accountName,
          salesTotal,
          shippingTotal,
          vatTotal,
          sampleSalesTotal,
          wholesaleTotal,
        });
      }
    }

    return {
      success: true,
      data: {
        totals,
        resellers,
      },
    }
  };
}

export default new ListReseller();
