import { <PERSON>AP<PERSON><PERSON><PERSON><PERSON> } from 'type';
import { validateRequest, combineMiddlewares } from '../api-middlewares'
import { ERROR } from 'const';
import { DB, InMemory as RedisCache } from 'db';
import Joi = require("joi");
import { AuthenHelper } from 'helpers';

class CheckInviteCode implements TypeAPIHandler {
  url = '/api/users/check-invite-code';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      email: Joi.string().required(),
      code: Joi.string().required(),
      password: Joi.string().required(),
      firstName: Joi.string().allow(''),
      lastName: Joi.string().allow(''),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
  ]);

  handler = async (request, reply) => {
    const { email, code, password, firstName, lastName } = request.body;
    const user = await DB.User.findOne({
      where: { email: email.toLowerCase() },
    });
    if (!user) throw new Error(ERROR.NOT_EXISTED);
    if (user.otherData?.inviteToken !== code) throw new Error("Invalid token");

    const hashedPassword = await AuthenHelper.hashPassword(password);
    user.password = hashedPassword;
    user.otherData = {};
    if (firstName) user.firstName = firstName;
    if (lastName) user.lastName = lastName;
    await user.save();

    const token = AuthenHelper.genJWTToken({ id: user.id });
    const publicInfo = Object.assign(JSON.parse(JSON.stringify(user)), { password: undefined });
    _afterLogin(publicInfo, token);

    return {
      success: true,
      data: {
        token,
        publicInfo,
      }
    }
  }
}

async function _afterLogin(user, token) {
  const listToken = await RedisCache.hgetAsync('JWTs', user.id);
  if (listToken) {
    const parsedList = JSON.parse(listToken);
    if (!parsedList.includes(token)) {
      parsedList.push(token);
      await RedisCache.hsetAsync('JWTs', user.id, JSON.stringify(parsedList));
    }
  } else {
    await RedisCache.hsetAsync('JWTs', user.id, JSON.stringify([token]));
  }
  await RedisCache.hsetAsync('JWT', token, JSON.stringify(user));
}

export default new CheckInviteCode();
