import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkA<PERSON>en, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON>per } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");

class GeneralDataList implements TypeAPIHandler {

  url = "/api/users/:userId/editor-images";
  method = "GET";
  apiSchema = {
    params: Joi.object({
      userId: Joi.string().required(),
    })
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    // checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { userId } = request.params;
    const u = await DB.UserEditorImage.findOne({
      where: {
        userId,
      }
    });
    if (!u) return { success: true, data: [] };
    return {
      success: true,
      data: u.images
    }
  };
}

export default new GeneralDataList();