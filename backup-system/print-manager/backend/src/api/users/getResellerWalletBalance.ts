import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, checkAuthen, validateRequest, checkAdmin } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON>elper } from "helpers";
import Joi = require("joi");
import { ERROR } from "const";
import Stripe from 'stripe';
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, { apiVersion: null });

class GetResellerSampleToken implements TypeAPIHandler {

  url = "/api/users/:resellerId/wallet-balance";
  method = "GET";
  apiSchema = {
    params: Joi.object({
      resellerId: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
    checkAdmin,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const resellerId = request.params.resellerId;
    const reseller = await DB.User.findByPk(resellerId);
    const stripeId = reseller?.resellerStripeId;
    if (!stripeId) {
      return {
        success: true,
        data: {
          amount: 0,
          formatted: '£0.00',
        }
      }
    }
    const balances = await stripe.customers.retrieve(stripeId, {
      expand: ['cash_balance'],
    });
    // @ts-ignore
    const _balance = (balances.balance * -1) || 0;
    const walletBalance = (_balance) / 100;
    reseller.walletBalance = walletBalance;
    console.log(`Updaing reseller ${reseller.email} wallet balance to £${walletBalance}`);
    await reseller.save();

    return {
      success: true,
      data: {
        amount: walletBalance,
        formatted: '£' + VarHelper.formatBalance(walletBalance)
      },
    }
  };
}

export default new GetResellerSampleToken();