import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, checkAuthen, validateRequest, checkAdmin } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON><PERSON> } from "helpers";
import Joi = require("joi");
import { ERROR } from "const";
import { checkReseller } from "api/api-middlewares/authen";
import { x } from "joi";
import { Op } from "db/DB.Postgres";

class ListResellerUser implements TypeAPIHandler {

  url = "/api/users/resellers-user";
  method = "GET";
  apiSchema = {
    query: Joi.object({
      page: Joi.number(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
    checkReseller,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const user = request.user;
    let { page } = request.query;

    if (!page) page = 1;

    const PAGE_SIZE = 10;
    const result = await DB.User.findAndCountAll({
      where: {
        role: 'user',
        [Op.or]: [
          {
            resellerId: user.id
          }, 
          {
            resellerId: user.resellerId 
          }
      ]
      },
      include: {model: DB.OnlineStore,as: 'onlineStore'},
      order: [
        ['updatedAt', 'DESC'],
      ],
      offset: (page - 1) * PAGE_SIZE,
      limit: PAGE_SIZE,
    });
    return {
      success: true,
      data: {
        list: result.rows,
        total: result.count,
        hasNext: page * PAGE_SIZE < result.count,
      },
    }
  };
}

export default new ListResellerUser();
