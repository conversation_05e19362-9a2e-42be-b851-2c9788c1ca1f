import { <PERSON>AP<PERSON><PERSON><PERSON><PERSON> } from 'type';
import { validateRequest, combineMiddlewares } from '../api-middlewares'
import { ERROR } from 'const';
import { DB } from 'db';
import Joi = require("joi");
import { Op } from 'sequelize';

class VerifyForgotPassword implements TypeAPIHandler {
  url = '/api/users/verify-forgot-password';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      code: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
  ]);

  handler = async (request) => {
    const { code } = request.body;

    const user = await DB.User.findOne({
      where: {
        otherData: {
          [Op.like]: `%${code}%`,
        },
      }
    });

    if (!user) throw new Error(ERROR.NOT_EXISTED);
    return {
      success: true,
    }
  }
}

export default new VerifyForgotPassword();
