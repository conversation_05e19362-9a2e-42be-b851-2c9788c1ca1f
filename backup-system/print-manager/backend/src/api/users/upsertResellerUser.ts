import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON><PERSON>, checkAdmin, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { <PERSON> } from "db";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ar<PERSON><PERSON><PERSON> } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");
import { checkReseller } from "api/api-middlewares/authen";

class UpsertResellerUser implements TypeAPIHandler {

  url = "/api/users/resellers-user";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      id: Joi.string(),
      firstName: Joi.string(),
      lastName: Joi.string(),
      email: Joi.string(),
      password: Joi.string(),
      photoUrl: Joi.string(),
      addressLine1: Joi.string().allow(''),
      addressLine2: Joi.string().allow(''),
      town: Joi.string().allow(''),
      country: Joi.string().allow(''),
      postCode: Joi.string().allow(''),
      otherData: Joi.any(),
      onlineStoreId: Joi.string().allow(''),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
    checkReseller,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { body, user } = request;
    if (request.user?.role !== 'reseller' && request.user.id !== body.id) throw new Error(ERROR.PERMISSION_DENIED);
    if (body.password) {
      body.password = await AuthenHelper.hashPassword(body.password);
    }
    // UPDATE EXISTING
    if (body.id) {
      const find = await DB.User.findByPk(request.body.id);
      if (!!find) {
        for (let key in body) {
          if (['role'].includes(key)) continue;
          find[key] = body[key];
        }
        await find.save();
        const user = await DB.User.findByPk(request.body.id,{
          include: {model: DB.OnlineStore,as: 'onlineStore'},
        });
        const publicInfo = Object.assign(JSON.parse(JSON.stringify(user)), { password: undefined });
        return { success: true, data: publicInfo };
      }
    }
    // CREATE NEW
    const existed = await DB.User.findOne({
      where: {
        email: body.email,
      }
    });
    if (existed) throw new Error(ERROR.EMAIL_ALREADY_EXISTED);

    const data = await DB.User.create({
      id: VarHelper.genId(),
      ...body,
      resellerId: request.user.resellerId ?? request.user.id,
      role: 'user',
    }, {
      include: {model: DB.OnlineStore,as: 'onlineStore'},
    });

    const publicInfo = Object.assign(JSON.parse(JSON.stringify(data)), { password: undefined });

    return {
      success: true,
      data: publicInfo,
    }
  };
}

export default new UpsertResellerUser();
