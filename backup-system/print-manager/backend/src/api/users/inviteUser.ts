import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkA<PERSON>en, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { checkReseller } from "api/api-middlewares/authen";
import Joi = require("joi");
import { DB } from "db";
import { VarHelper } from "helpers";
import MailHelper from "helpers/MailHelper";
import { genEmailHtml } from "helpers/email-templates";

class InviteUser implements TypeAPIHandler {

  url = "/api/users/invite";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      emails: Joi.array().items(Joi.string()),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
    checkReseller,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { body } = request;
    const emails = body?.emails || [];

    const errors = [];
    await Promise.all(
      emails.map(async e => {
        const email = e.toLowerCase();
        const find = await DB.User.findOne({
          where: { email },
        });
        if (!find || find?.otherData?.inviteToken) {
          const inviteToken = find?.otherData?.inviteToken || VarHelper.genId();
          await MailHelper.sendSMTPEmail({
            to: email,
            cc: [],
            subject: "You have an invitation",
            html: genEmailHtml({
              title: "Invitation",
              message: `You have received an invitation to the Bottled Goose CMS.<br/>Click this link to join:`,
              link: `https://${process.env.DEV ? 'bg-dev.' : 'bg-production.'}bottledgoose.co.uk/invite?code=${inviteToken}&email=${email}`,
            }),
          })
          if (!find) {
            await DB.User.create({
              id: VarHelper.genId(),
              email,
              firstName: "",
              lastName: "",
              password: "",
              otherData: { inviteToken },
              resellerId: request.user.resellerId || request.user.id,
              role: 'user',
            });
          }
        } else {
          errors.push(`An account with email ${email} already exists`);
        }
      })
    )

    return {
      success: true,
      errors,
    }
  };
}

export default new InviteUser();
