import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON><PERSON>, checkAuthenOptional, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { <PERSON>ar<PERSON><PERSON><PERSON> } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");
import { generatePdf } from '../pdf/generatePdf';
import { checkReseller } from "api/api-middlewares/authen";

const moment = require('moment');

class NewOrder implements TypeAPIHandler {

  url = "/api/bg-services/:id/new-order";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      productName: Joi.string().required(),
      quantity: Joi.number().required(),
      width: Joi.number().required(),
      height: Joi.number().required(),
      orderId: Joi.string().required(),
      customer: Joi.object({
        first_name: Joi.string(),
        last_name: Joi.string(),
        email: Joi.string(),
        phone: Joi.string(),
      }).required(),
      shipping_address: Joi.object({
        first_name: Joi.string(),
        last_name: Joi.string(),
        address1: Joi.string(),
        address2: Joi.string(),
        phone: Joi.string(),
        city: Joi.string(),
        zip: Joi.string(),
        country_code: Joi.string(),
      }).required(),
      artworkUrls: Joi.array().items(Joi.string()),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
    checkReseller,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { body } = request;
    
    // if (request.user.role !== 'reseller' && request.user.id !== request.params.id) throw new Error(ERROR.PERMISSION_DENIED);

    const user = await DB.User.findByPk(request.user.id);

    const printJob = await DB.PrintJob.create({
      id: VarHelper.genId(),
      quantity: body.quantity,
      clientId: user.id,
      productName: body.productName,
      productId: '',
      designId: '',
      productVariantionName: 'Var + No Mirr',
      previewUrl: body.artworkUrls[0],
      artworkUrls: body.artworkUrls,
      data: {
        orderId: body.orderId,
        useBGService: true,
        product: {
          physicalWidth: body.width,
          physicalHeight: body.height,
          printAreas: [
            { top: 0, left: 0, width: body.width, height: body.height },
          ],
        },
        customer: body.customer, // shopify object
        shipping_address: body.shipping_address, // shopify object
      },
      isPaid: false,
      isPrinted: false,
      isRePrinted: false,
      isPDFDownloaded: false,
    });

    // create dowwnload
    const download = await DB.Download.create({
      id: printJob.id,
      resellerId: user.id,
      productId: '',
      designId: '',
      linkedOrderId: '',
      variationName: printJob.productVariantionName || '',
      pdf: '',
    });
    printJob.data.pdfNamePrefix = `${VarHelper.fourDigitsNumber(download.queueId)}-${body.productName.replace(/\s/g, '_').toUpperCase()}-${printJob.quantity}-B`;
    printJob.readyForPrint = true;
    await printJob.save();

    console.log('PRINT JOB', printJob.id);
    console.log(JSON.stringify(printJob));
    const bgServices = user.otherData?.bgServices || {};

    (async () => {
      if (bgServices.webhookUrl) {
        fetch(bgServices.webhookUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...bgServices.authenHeaders,
          },
          body: JSON.stringify({
            orderId: body.orderId,
            status: 'received',
            timestamp: moment().format(),
          }),
        })
      }
    })();

    // generate pdf in background
    (async () => {
      try {
        await generatePdf({
          id: printJob.id,
          designId: printJob.designId,
          images: printJob.artworkUrls,
          data: printJob.data,
          forceVarnish: true,
        });
      } catch (err) {
        console.log('ERROR generatePdf', err);
        if (bgServices.webhookUrl) {
          fetch(bgServices.webhookUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              ...bgServices.authenHeaders,
            },
            body: JSON.stringify({
              orderId: body.orderId,
              status: 'error',
              timestamp: moment().format(),
            }),
          })
        }
      }
    })();

    return {
      success: true,
    }
  };
}

export default new NewOrder();