import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import Joi = require("joi");
import { checkAdmin, checkAuthen } from "api/api-middlewares/authen";
import { Op } from "sequelize";

class ListOrderLogs implements TypeAPIHandler {

  url = "/api/orders-log";
  method = "GET";
  apiSchema = {
    query: Joi.object({
      page: Joi.number(),
      search: Joi.string().allow(''),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
    checkAdmin,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { page, search } = request.query;
    const user = request.user;

    const PAGE_SIZE = 20;
    const result = await DB.Log.findAndCountAll({
      where: {
        ...(search ? {
          body: {
            [Op.like]: `%${search}%`,
          },
        } : {}),
      },
      order: [
        ['createdAt', 'DESC'],
      ],
      offset: (page - 1) * PAGE_SIZE,
      limit: PAGE_SIZE,
    });

    return {
      success: true,
      data: {
        list: result.rows,
        total: result.count,
      },
    }
  };
}

export default new ListOrderLogs();
