import { DB } from "db"
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "helpers"
import { TUser } from "type"

export const saveLog = async (user: TUser, params: {
  type: string, data: any,
  oldData: any, newData: any
}) => {
  const changes = {}
  for (const key in params.newData) {
    if (params.oldData?.[key] !== params.newData?.[key]) {
      changes[key] = {
        from: params.oldData?.[key],
        to: params.newData?.[key]
      }
    }
  }
  await DB.Log.create({
    id: VarHelper.genId(),
    type: params.type,
    userId: user.id,
    body: {
      ...(params.data || {}),
      changes,
    },
    otherData: {
      user: {
        email: user?.email,
        name: [user?.firstName, user?.lastName].filter(Boolean).join(' '),
      }
    }
  })
}
