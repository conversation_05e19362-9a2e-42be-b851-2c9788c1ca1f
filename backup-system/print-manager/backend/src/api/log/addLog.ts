import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkAuthen, combineMiddlewares, validateRequest } from "api/api-middlewares";
import Joi = require("joi");
import { saveLog } from "./utils";

class AddLog implements TypeAPIHandler {

  url = "/api/log";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      type: Joi.string(),
      data: Joi.any(),
      oldData: Joi.any(),
      newData: Joi.any(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    await saveLog(request.user, {
      type: request.body.type,
      data: request.body.data,
      oldData: request.body.oldData || {},
      newData: request.body.newData || {},
    })

    return {
      success: true,
    }
  };
}

export default new AddLog();
