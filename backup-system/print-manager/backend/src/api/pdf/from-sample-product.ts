import { <PERSON>AP<PERSON><PERSON>and<PERSON> } from 'type';
import { validateRequest, combineMiddlewares, receiveFileAndFields, IModifiedBody, TFileField, checkAuthen, checkAuthenOptional } from '../api-middlewares'
import { ERROR } from 'const';
import { DB, InMemory as RedisCache } from 'db';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FileHelper, VarHelper } from 'helpers';
import Joi = require("joi");
import { MM_TO_PIXEL } from 'const';
import { generatePngMultipleUrl, TEachImage } from './generatePngMultipleUrl';


const PDFDocument = require('pdfkit');
const SVGtoPDF = require('svg-to-pdfkit');
const fs = require('fs');
const path = require('path');

class FromImage implements TypeAPIHandler {
  url = '/api/pdf/from-sample-prodcut';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      physicalWidth: Joi.number().required(),
      physicalHeight: Joi.number().required(),
      printAreas: Joi.array().items({
        width: Joi.number().required(),
        height: Joi.number().required(),
        top: Joi.number().required(),
        left: Joi.number().required(),
      }),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
  ]);

  handler = async (request, reply) => {
    const { physicalWidth, physicalHeight, printAreas } = request.body;

    const randomImages = await VarHelper.getRandomImages('landscape nature', printAreas.length);

    // const INCH_TO_PIXEL = 558.4155022831 / (5.8 * 1.337398374); // 71.9894212137
    // const INCH_TO_PIXEL = 71.9894212137;

    const promiseArr = randomImages.map(async (val, index) => {
      try {
        const imageUrl = randomImages[index].url;
        return {
          url: imageUrl,
          width: printAreas[index].width* MM_TO_PIXEL,
          height: printAreas[index].height* MM_TO_PIXEL,
          top: printAreas[index].top* MM_TO_PIXEL,
          left: printAreas[index].left* MM_TO_PIXEL,
        }
      } catch(err) {}
    })
    let images : Array<TEachImage> = await Promise.all(promiseArr);
    images = images.filter(val => !!val);

    const imageUrl = await generatePngMultipleUrl(
      physicalWidth * MM_TO_PIXEL,
      physicalHeight * MM_TO_PIXEL,
      images,
      `from-sample-prodcut-${new Date().getTime()}.png`,
    );
    
    return {
      success: true,
      data: imageUrl,
    }
  }
}

export default new FromImage();
