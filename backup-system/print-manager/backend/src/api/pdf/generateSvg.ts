export const generateSvg = (base64, imageWidth : number, imageHeight : number) => {
  const ratio = 5.83 / 4.38;
  const imageX = (419.53 - imageWidth) * ratio / 2 ;
  const imageY = (595.28 - imageHeight) * ratio / 2;
  const svgWidth = 419.53 * ratio;
  const svgHeight = 595.28 * ratio;
  return `
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 ${svgWidth} ${svgHeight}" width="${svgWidth}" height="${svgHeight}">
    <!-- <rect id="debug" width="${svgWidth}" height="${svgHeight}" fill="pink" /> -->
    <mask id="myMask">
      <image
        x="${imageX}"
        y="${imageY}"
        width="${imageWidth * ratio}"
        height="${imageHeight * ratio}"
        style="filter: brightness(0%) invert(1);"
        href="data:image/png;base64,${base64}"></image>
    </mask>
    <image
      x="${imageX}"
      y="${imageY}"
      width="${imageWidth * ratio}"
      height="${imageHeight * ratio}"
      href="data:image/png;base64,${base64}"></image>
    <rect
      x="${imageX}"
      y="${imageY}"
      width="${imageWidth * ratio}"
      height="${imageHeight * ratio}"
      fill="Spot1"
      mask="url(#myMask)" />
    <rect
      x="${imageX}"
      y="${imageY}"
      width="${imageWidth * ratio}"
      height="${imageHeight * ratio}"
      fill="Spot2"
      mask="url(#myMask)" />
    </svg>
  `
};