import { TypeAP<PERSON>Hand<PERSON> } from 'type';
import { validateRequest, combineMiddlewares, receiveFileAndFields, IModifiedBody, TFileField, checkAuthen, checkAuthenOptional } from '../api-middlewares'
import { ERROR, MM_TO_PIXEL } from 'const';
import { DB, InMemory as RedisCache } from 'db';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Helper, AWSHelper, Polotno, VarHelper } from 'helpers';
import Joi = require("joi");
import { INCH_TO_PIXEL } from 'const';
import { generatePngMultipleUrl, TEachImage } from './generatePngMultipleUrl';
import { MATCH_SHOPIFY_PRODUCTS, genArtworkUrl, genPreviewUrl } from '../online-stores/partner-in-wine/index';
import { generatePdf } from './generatePdf';
const PDFDocument = require('pdfkit');
const SVGtoPDF = require('svg-to-pdfkit');
const fs = require('fs');
const path = require('path');
const axios = require('axios');

class FromPrintJob implements TypeAPIHandler {
  url = '/api/pdf/from-print-job-pts';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      id: Joi.string().required(),
      designId: Joi.string().allow(''),
      clientId: Joi.string().allow(''),
      images: Joi.array().items(Joi.string()).required(),
      headless: Joi.boolean(),
      data: Joi.object({
        product: Joi.object({

          physicalWidth: Joi.number().required(),
          physicalHeight: Joi.number().required(),
          printAreas: Joi.array().items({
            width: Joi.number().required(),
            height: Joi.number().required(),
            top: Joi.number().required(),
            left: Joi.number().required(),
          }),


        }).required()
      }).required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthenOptional,
  ]);

  handler = async (request, reply) => {
    const { id, designId, data, headless, clientId } = request.body;
    console.log('{ id, designId, data, headless, clientId }', { id, designId, data, headless, clientId });
    const printJob = await DB.PrintJob.findByPk(id);
    if (!printJob) throw new Error('Print Job not found');

    const checkPreDownload = await DB.Download.findOne({
      where: {
        id,
      }
    });
    console.log('checkPreDownload', checkPreDownload);
    if (checkPreDownload && checkPreDownload.pdf) {
      const thePDF = !!printJob.data.pdfNamePrefix ? printJob.data.pdfNamePrefix + '.pdf' : 'pdf-' + new Date().getTime() + '.pdf';
      await FileHelper.downloadFile(checkPreDownload.pdf, thePDF)
      const buffer = fs.readFileSync(thePDF);
      reply.type('application/pdf').send(buffer);
      setTimeout(() => {
        fs.unlink(thePDF, () => { });
      }, 10000);
      return;
    }
    let images = request.body.images || [];
    if (!images || images.length === 0) {
      const designData = printJob.data?.design;
      if (!designData) throw new Error('Empty design data');
      const promiseArr = designData.pages.map(async (p, i) => {
        try {
          const modifiedJson = {
            ...design,
            width: p.width,
            height: p.height,
            pages: [p],
          };
          const s3Url = await Polotno.jsonToS3(modifiedJson, `polotno-image/${designData.name}-${i}-${new Date().getTime()}.png`);
          images.push(s3Url);
        } catch (err) { }
      });
      await Promise.all(promiseArr);
    }

    const printAreas = data.product.printAreas;


    if (images.length !== printAreas.length) throw new Error('Number of artwork and number of print area not match');
    if (!data.product?.physicalWidth || !data.product?.physicalHeight) {
      throw new Error('Invalid artboard sizes');
    }

    console.log('data.product?.physicalWidth', data.product?.physicalWidth);
    console.log('data.product?.physicalHeight', data.product?.physicalHeight);

    if (clientId === 'partnet-in-wine') {
      let download = await DB.Download.findOne({
        where: {
          id: printJob.id,
          resellerId: 'partnet-in-wine',
        }
      });
      if (!download) {
        download = await DB.Download.create({
          id: printJob.id,
          resellerId: 'partnet-in-wine',
          productId: printJob.productId,
          designId: '',
          linkedOrderId: '',
          variationName: printJob.productVariantionName || '',
          pdf: '',
        });
      }

      const { s3Url } = await generatePdf({
        id: printJob.id,
        designId: printJob.designId,
        images: printJob.artworkUrls,
        data: printJob.data,
        forceVarnish: true,
        piw: true,
      });
      const filePath = `pdf-${new Date().getTime()}.pdf`;
      await FileHelper.downloadFile(s3Url, filePath);
      const buffer = fs.readFileSync(filePath);
      reply.type('application/pdf').send(buffer);
      // remove output pdf
      setTimeout(() => {
        fs.unlink(filePath, () => { });
      }, process.env.DEV ? 3000 : 15 * 60 * 1000);
      return;
    }


    const design = await DB.Design.findByPk(designId);
    if (!design) throw new Error('Design not found');

    const varnish = !!design.data?.settings?.varnish;



    // let imageUrl;
    // if (images.length === 1) {
    //   imageUrl = images[0]
    // } else {
    //   const promiseArr = images.map(async (val, index) => {
    //     try {
    //       const imageUrl = val;
    //       console.log('imageUrl', imageUrl);
    //       return {
    //         url: imageUrl,
    //         width: printAreas[index].width * MM_TO_PIXEL,
    //         height: printAreas[index].height * MM_TO_PIXEL,
    //         top: printAreas[index].top * MM_TO_PIXEL,
    //         left: printAreas[index].left * MM_TO_PIXEL,
    //       }
    //     } catch(err) {}
    //   })
    //   let printImages : Array<TEachImage> = await Promise.all(promiseArr);
    //   printImages = printImages.filter(val => !!val);

    //   imageUrl = await generatePngMultipleUrl(
    //     data.product.physicalWidth * MM_TO_PIXEL,
    //     data.product.physicalHeight * MM_TO_PIXEL,
    //     printImages,
    //     `print-job-${id}.png`,
    //   );
    // }

    const promiseArr = images.map(async (val, index) => {
      try {
        const imageUrl = val;
        console.log('imageUrl', imageUrl);
        return {
          url: imageUrl,
          width: printAreas[index].width * MM_TO_PIXEL,
          height: printAreas[index].height * MM_TO_PIXEL,
          top: printAreas[index].top * MM_TO_PIXEL,
          left: printAreas[index].left * MM_TO_PIXEL,
        }
      } catch (err) { }
    })
    let printImages: Array<TEachImage> = await Promise.all(promiseArr);
    printImages = printImages.filter(val => !!val);

    const imageUrl = await generatePngMultipleUrl(
      data.product.physicalWidth * MM_TO_PIXEL,
      data.product.physicalHeight * MM_TO_PIXEL,
      printImages,
      `print-job-${id}.png`
    );

    console.log('imageUrl', imageUrl);
    console.log('to be send', {
      url: imageUrl,
      varnish,
    });

    const productId = printJob.productId;
    const product = !productId ? null : await DB.Product.findByPk(productId);
    const dropletUrl = product?.data?.dropletUrl;

    // remove dropletUrl
    const downloadConfig = {
      url: imageUrl,
      apiPath: '/custom-sizes',
      // folderName: `size_${product.physicalWidth}x${product.physicalHeight}`,
      // dropletUrl: product.dropletUrl,
      // dropletName: `size_${product.physicalWidth}x${product.physicalHeight}`,
      noDroplet: true,
      scriptSize: {
        width: product.physicalWidth,
        height: product.physicalHeight,
        dpi: product.dpi || VarHelper.calculateDPI(product.physicalWidth, product.physicalHeight),
      },
    }

    console.log('PDF download config', downloadConfig);

    // const thePDF = 'pdf-'+new Date().getTime() +'.pdf';
    const thePDF = !!printJob.data.pdfNamePrefix ? printJob.data.pdfNamePrefix + '.pdf' : 'pdf-' + new Date().getTime() + '.pdf';

    const s3Url = await FileHelper.getPDFFromLoadBalance(downloadConfig);

    if (headless) {
      reply.type('application/json').send(JSON.stringify({
        headless: true,
      }));
    } else {
      await FileHelper.downloadFile(s3Url, thePDF);
      const buffer = fs.readFileSync(thePDF);
      reply.type('application/pdf').send(buffer);
    }

    // upload to S3 if file valids
    const pData = {
      ...printJob.data,
      pdf: s3Url,
    }
    printJob.data = pData;
    const download = await DB.Download.findOne({
      where: {
        id: printJob.id
      }
    });
    if (download) {
      download.pdf = s3Url;
      await download.save();
    }

    printJob.isPrinted = false;
    printJob.isPDFDownloaded = true;
    await printJob.save();

    // remove output pdf
    setTimeout(() => {
      fs.unlink(thePDF, () => { });
    }, process.env.DEV ? 3000 : 15 * 60 * 1000);
  }
}

export default new FromPrintJob();
