import { <PERSON>AP<PERSON><PERSON>and<PERSON> } from 'type';
import { validateRequest, combineMiddlewares } from '../api-middlewares'
import axios from 'axios';
import Joi = require("joi");

class GenerateCandleTemplate implements TypeAPIHandler {
  url = '/api/pdf/generate-candle-template';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      scentName: Joi.string().allow(''),
      templateUrl: Joi.string().allow(''),
      address: Joi.string().allow(''),
      companyLogo: Joi.string().allow(''),
      companyName: Joi.string().allow(''),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
  ]);

  handler = async (request, reply) => {
    const { body } = request;
    const { scentName, templateUrl, address, companyName, companyLogo } = body;

    const response: any = await axios.request({
      url: "https://puppeteer.personify.tech/api/candle-template/genCandleTemplate",
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
      },
      data: JSON.stringify({
        templateUrl,
        name: scentName,
        address,
        companyName,
        logo: companyLogo,
        orderNumber: "1234",
        skipSplitAddress: true,
      }),
    })

    return {
      success: true,
      data: response.data?.data,
    }
  }
}

export default new GenerateCandleTemplate();
