import { <PERSON>AP<PERSON><PERSON><PERSON><PERSON> } from 'type';
import { validateRequest, combineMiddlewares } from '../api-middlewares'
import { MM_TO_PIXEL } from 'const';
import { DB } from 'db';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>per, VarHelper } from 'helpers';
import { generatePngMultipleUrl, TEachImage } from './generatePngMultipleUrl';
import Joi = require("joi");
import { generatePdf } from './generatePdf';
const probe = require('probe-image-size');

class SendRequestGeneratePdfLineItem implements TypeAPIHandler {
  url = '/api/pdf/send-request-generate-pdf-line-item';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      lineId: Joi.string(),
      printJobId: Joi.string(),
      designRenderId: Joi.string(),
    }),
  }

  preHandler = combineMiddlewares([
    // receiveFileAndFields,
    validateRequest(this.apiSchema),
  ]);

  handler = async (request, reply) => {
    const { body } = request;
    const { lineId, designRenderId } = body;

    let printJobId = body?.printJobId;

    let imageToCheckQuality;
    let pdfUrl = '';
    if (printJobId) {
      const printJob = await DB.PrintJob.findByPk(printJobId);
      if (!printJob) throw new Error('Print Job not found');
      let images = printJob.artworkUrls;
      if (images[0]) {
        imageToCheckQuality = images[0];
      }
      const data = printJob.data;
      let printAreas = data.product?.printAreas;
      if (!printAreas?.length && printJob.productId) {
        const product = await DB.Product.findByPk(printJob.productId);
        if (product) {
          printAreas = product.printAreas;
        }
      }
      if (!printAreas?.length) {
        printAreas = await Promise.all(images.map(async img => {
          const imgSize = await probe(img)
          return {
            width: imgSize.wUnits === 'px' ? imgSize.width / MM_TO_PIXEL : imgSize.width,
            height: imgSize.hUnits === 'px' ? imgSize.height / MM_TO_PIXEL : imgSize.height,
            top: 0,
            left: 0,
          }
        }))
      } else {
        if (images.length !== printAreas.length) throw new Error('Number of artwork and number of print area not match');
      }
      let physicalWidth = data.product?.physicalWidth;
      let physicalHeight = data.product?.physicalHeight;
      if (!physicalWidth || !physicalHeight) {
        physicalWidth = printAreas[0]?.width;
        physicalHeight = printAreas[0]?.height;
      }
      const design = await DB.Design.findByPk(printJob.designId);
      if (!design) throw new Error('Design not found');
      const varnish = !!design.data?.settings?.varnish;
      const promiseArr = images.map(async (val, index) => {
        try {
          const imageUrl = val;
          console.log('imageUrl', imageUrl);
          return {
            url: imageUrl,
            width: printAreas[index].width * MM_TO_PIXEL,
            height: printAreas[index].height * MM_TO_PIXEL,
            top: printAreas[index].top * MM_TO_PIXEL,
            left: printAreas[index].left * MM_TO_PIXEL,
          }
        } catch (err) { }
      })
      let printImages: Array<TEachImage> = await Promise.all(promiseArr);
      printImages = printImages.filter(val => !!val);
      const imageUrl = await generatePngMultipleUrl(
        physicalWidth * MM_TO_PIXEL,
        physicalHeight * MM_TO_PIXEL,
        printImages,
        `print-job-${printJobId}.png`
      );
      const productId = printJob.productId;
      const product = !productId ? null : await DB.Product.findByPk(productId);

      // remove dropletUrl
      const downloadConfig = {
        url: imageUrl,
        apiPath: '/custom-sizes',
        // folderName: `size_${product.physicalWidth}x${product.physicalHeight}`,
        // dropletUrl: product.dropletUrl,
        // dropletName: `size_${product.physicalWidth}x${product.physicalHeight}`,
        noDroplet: true,
        scriptSize: {
          width: product.physicalWidth,
          height: product.physicalHeight,
          dpi: product.dpi || VarHelper.calculateDPI(product.physicalWidth, product.physicalHeight),
        },
      };

      pdfUrl = await FileHelper.getPDFFromLoadBalance(downloadConfig);
      const pData = {
        ...printJob.data,
        pdf: pdfUrl,
      }
      printJob.data = pData;

      await printJob.save();
    }

    if (designRenderId && (!printJobId || !pdfUrl)) {
      const design = await DB.Design.findByPk(designRenderId);
      if (!design) throw new Error('Design not found');
      console.log("Generate PDF - design.data", design.data);

      // @ts-ignore
      if (design.data.preRenderPDF && design.data.preRenderUpdatedAt && design.updatedAt == design.data.preRenderUpdatedAt) {
        pdfUrl = design.data.preRenderPDF;
        if (design.data.artworkUrls?.[0]) {
          imageToCheckQuality = design.data.artworkUrls?.[0];
        }
      } else {
        const product = await DB.Product.findByPk(design.productId);
        if (!product) throw new Error('Product not found');
        const printJob = await DB.PrintJob.create({
          id: VarHelper.genId(),
          designId: design.id,
          quantity: 0,
          clientId: 'bottled-goose',
          productId: product.id,
          productVariantionName: '',
          previewUrl: design.image,
          artworkUrls: design.data.artworkUrls,
          productName: `${design.name} - Design Render`,
          data: {
            product: {
              physicalHeight: product.physicalHeight,
              physicalWidth: product.physicalWidth,
              printAreas: product.printAreas,
            }
          },
          isPaid: false,
          isPrinted: false,
          isPDFDownloaded: false,
          isRePrinted: false,
        });
        printJobId = printJob.id;
        console.log('Design Render Print Job', printJobId);
        const { s3Url } = await generatePdf({
          id: printJob.id,
          designId: printJob.designId,
          images: printJob.artworkUrls,
          data: printJob.data,
          forceVarnish: false,
        });
        pdfUrl = s3Url;
        if (printJob.artworkUrls?.[0]) {
          imageToCheckQuality = printJob.artworkUrls?.[0];
        }
        const data = {
          ...design.data,
          preRenderPDF: pdfUrl,
        }
        design.data = data;
        await design.save();
        design.data = {
          ...design.data,
          preRenderPDF: pdfUrl,
          // @ts-ignore
          preRenderUpdatedAt: design.updatedAt,
        };
        await design.save({
          silent: true,
        });
      }

    }

    let imageQuality;
    console.log("Generate PDF - check image quality", imageToCheckQuality);
    if (imageToCheckQuality) {
      try {
        imageQuality = await AWSHelper.detectModerationLabel(imageToCheckQuality);
      } catch (error) {
        console.log("Generate PDF - check image quality error", error);
      }
    }

    return {
      success: true,
      data: {
        pdfUrl,
        lineId,
        printJobId,
        imageQuality,
      }
    }
  }
}

export default new SendRequestGeneratePdfLineItem();
