import { MM_TO_PIXEL } from "const";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "helpers";

const sharp = require('sharp');
const axios = require('axios');
const fs = require('fs');

// const json = {
// 	"width": 1080,
// 	"height": 1080,
// 	"fonts": [],
// 	"pages": [{
// 		"id": "MaGeJ3Jw8V",
// 		"children": [{
// 			"id": "e3MJPiYu-e",
// 			"type": "image",
// 			"name": "",
// 			"opacity": 1,
// 			"animations": [],
// 			"visible": true,
// 			"selectable": true,
// 			"removable": true,
// 			"alwaysOnTop": false,
// 			"showInExport": true,
// 			"x": 0,
// 			"y": 505.74018126888217,
// 			"width": 1080,
// 			"height": 68.51963746223565,
// 			"rotation": 0,
// 			"blurEnabled": false,
// 			"blurRadius": 10,
// 			"brightnessEnabled": false,
// 			"brightness": 0,
// 			"sepiaEnabled": false,
// 			"grayscaleEnabled": false,
// 			"shadowEnabled": false,
// 			"shadowBlur": 5,
// 			"shadowOffsetX": 0,
// 			"shadowOffsetY": 0,
// 			"shadowColor": "black",
// 			"shadowOpacity": 1,
// 			"draggable": true,
// 			"resizable": true,
// 			"contentEditable": true,
// 			"styleEditable": true,
// 			"src": "https://print-manager-media.s3.eu-west-1.amazonaws.com/Screenshot%202023-06-20%20at%2017-clj4dqvxe00003b6v6jix4amu.36",
// 			"cropX": 0,
// 			"cropY": 0,
// 			"cropWidth": 1,
// 			"cropHeight": 1,
// 			"cornerRadius": 0,
// 			"flipX": false,
// 			"flipY": false,
// 			"clipSrc": "",
// 			"borderColor": "black",
// 			"borderSize": 0,
// 			"keepRatio": false
// 		}],
// 		"width": 2108.976377952756,
// 		"height": 1148.0314960629921,
// 		"background": "white",
// 		"bleed": 0
// 	}],
// 	"unit": "px",
// 	"dpi": 72
// }

export const generateCustomArtwork = async ({ width, height, imageUrl } : any) : Promise<any> => {
  const filePath = 'custom-art-work-' + new Date().getTime() + '.png';
  const res = await axios({
    method: 'get',
    url: imageUrl,
    responseType: 'stream',
  });
  await sharp(res.data)
    .resize(width * MM_TO_PIXEL , height * MM_TO_PIXEL, {
      fit: 'cover',
    })
    .png()
    .toFile(filePath);
  const uploadedUrl = await AWSHelper.upload({
    key: `bg-custom-artwork/${filePath}`,
    filePath,
  });
  fs.unlinkSync(filePath);
  return uploadedUrl;
};