import { <PERSON>AP<PERSON><PERSON><PERSON><PERSON> } from 'type';
import { validateRequest, combineMiddlewares, receiveFileAndFields, IModifiedBody, TFileField } from '../api-middlewares'
import { ERROR } from 'const';
import { DB, InMemory as RedisCache } from 'db';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FileHelper } from 'helpers';
import Joi = require("joi");
const fs = require('fs');
const axios = require('axios');

class PTSLoadBalance implements TypeAPIHandler {
  url = '/api/pdf/pts-load-balance';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      url: Joi.string().required(),
      apiPath: Joi.string().required(),
      varnish: Joi.boolean(),
      piw: Joi.boolean(),
    }),
  }

  preHandler = combineMiddlewares([
    // receiveFileAndFields,
    // validateRequest(this.apiSchema),
  ]);

  handler = async (request, reply) => {
    const { body } = request;
    const { apiPath, ...other } = body;
    const thePDF = '/tmp/pdf-' + new Date().getTime() + '.pdf';

    const ptsServers = [
      { name: 'PTS 5', url: 'http://pts5.personify.tech', isProcessing: false},
      { name: 'PTS 1', url: 'http://pts.personify.tech', isProcessing: false },
    ];

    
    await Promise.all(ptsServers.map(async (sv, index) => {
      try {
        const res = await axios.request({
          url: sv.url + '/is-proccessing',
          method: 'get',
        });
        ptsServers[index]['isProcessing'] = res.data['isProcessing'];
      } catch(err) {}
    }));

    const availableServers = [
      ...ptsServers.filter(sv => !sv.isProcessing),
      ...ptsServers.filter(sv => sv.isProcessing),
    ];
    console.log('AVAILABLE SERVERS', availableServers);
    let s3Url = '';
    for (let i=0; i<availableServers.length; i++) {
      const sv = availableServers[i];
      try {
        console.log('DOWNLOADING PDF FROM', sv.name);
        console.log('URL', sv.url + apiPath);
        console.log('BODY', JSON.stringify(other));
        const apiCall = await axios.request({
          url: sv.url + apiPath,
          method: 'post',
          headers: {
            'Authorization': 'kgmdqtveva',
            'Content-Type': 'application/json'
          },
          data: JSON.stringify({
            ...other,
          }),
        });
        s3Url = apiCall.data.s3Url;
        if (s3Url) break;
      } catch(err) {
        // alert message
        axios.request({
          url: `https://chat.personify.tech/alert-service-down?name=${sv.name}&url=${sv.url}`,
          method: 'post',
        });
      }
    }
    
    return { success: true, data: s3Url };
  }
}

export default new PTSLoadBalance();
