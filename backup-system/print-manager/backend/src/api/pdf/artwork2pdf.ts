import { <PERSON>AP<PERSON><PERSON>and<PERSON> } from 'type';
import { validateRequest, combineMiddlewares, receiveFileAndFields, IModifiedBody, TFileField } from '../api-middlewares'
import { ERROR } from 'const';
import { DB, InMemory as RedisCache } from 'db';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>per, <PERSON>ar<PERSON>elper } from 'helpers';
import Joi = require("joi");
const fs = require('fs');
const axios = require('axios');

class FromImage implements TypeAPIHandler {
  url = '/api/pdf/artwork2pdf';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      url: Joi.string().required(),
      productType: Joi.string(),
      productId: Joi.string(),
    }),
  }

  preHandler = combineMiddlewares([
    // receiveFileAndFields,
    validateRequest(this.apiSchema),
  ]);

  handler = async (request, reply) => {
    const { body } = request;
    const { url, productType, productId } = body;

    console.log('/api/pdf/artwork2pdf');

    const product = await DB.Product.findByPk(productId);
    if (!product) throw new Error('Product not found');
    // if (!product.dropletUrl) throw new Error('Droplet not found');
    const payload = {
      url,
      apiPath: '/custom-sizes',
      folderName: `size_${product.physicalWidth}x${product.physicalHeight}`,
      dropletUrl: product.dropletUrl,
      dropletName: `size_${product.physicalWidth}x${product.physicalHeight}`,
      noDroplet: true,
      scriptSize: {
        width: product.physicalWidth,
        height: product.physicalHeight,
        dpi: product.dpi || VarHelper.calculateDPI(product.physicalWidth, product.physicalHeight),
      },
    };
    console.log('/api/pdf/artwork2pdf', payload);
    const s3Url = await FileHelper.getPDFFromLoadBalance(payload);

    return { success: !!s3Url, data: s3Url };

  }
}

export default new FromImage();
