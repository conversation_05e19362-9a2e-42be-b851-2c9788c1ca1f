import { TypeAP<PERSON>Handler } from 'type';
import { validateRequest, combineMiddlewares } from '../api-middlewares'
import Joi = require("joi");
import { DB } from 'db';
import { VarHelper } from 'helpers';
import axios from 'axios';

export const requestGenerateCandleSticker = async (params: {
  items: Array<any>, orderNumber: number, storeId: string, callbackUrl: string
}) => {
  const { items, orderNumber, storeId, callbackUrl } = params;
  const requestData = [];
  let error;
  let results = [];
  if (Array.isArray(items) && items.length > 0) {
    results = await Promise.all(items?.map(async v => {
      let _printJob;
      const design = await (async () => {
        if (v.designRenderId) return await DB.Design.findByPk(v.designRenderId);
        if (v.printJobId) {
          const printJob = await DB.PrintJob.findByPk(v.printJobId);
          if (!!printJob) {
            _printJob = printJob;
            return await DB.Design.findByPk(printJob.designId);
          }
        }
      })();

      if (!design) {
        error = `Design not found: ${JSON.stringify(v)}`
        return;
      }

      const product = await DB.Product.findByPk(design.productId);
      if (!product) {
        error = `Product not found: ${design.productId}`
        return;
      }

      if (!!product.data?.candleTemplate) {
        const scentVariantOpts = product?.data?.scentVariants?.split(/,|\n/).filter(i => i.trim()).map(i => ({
          label: i,
          value: i,
        }));
        const packingSlip = await DB.PackingSlip.findOne({
          where: storeId ? {
            storeId,
          } : {
            resellerId: design.createdByUserId,
          }
        });
        const resellerInfo = await DB.User.findOne({
          where: {
            id: design.createdByUserId,
          }
        })
        const candleInfo = _printJob?.data?.candle || design?.data?.candle || {};
        const {
          scentName, defaultScentName, companyName, logo,
          address, country, address2, state, postCode,
        } = VarHelper.getDefaultCandleStickerInfo({
          candleInfo,
          product,
          user: resellerInfo,
          packingSlip: storeId ? packingSlip : undefined,
        });
        let combineAddress = [country, address, address2, state, postCode].filter(Boolean).join("\n");
        if (combineAddress.split("\n").length > 7) {
          const splitedAddress = combineAddress.split("\n");
          const removeLinesCount = splitedAddress.length - 7;
          const firstPart = splitedAddress.slice(0, -removeLinesCount);
          const lastPart = splitedAddress.slice(-removeLinesCount);
          combineAddress = [
            firstPart.join(" "),
            ...lastPart
          ].join("\n");
        }
        requestData.push({
          templateUrl: product.data.candleTemplate,
          name: scentName || defaultScentName || scentVariantOpts?.[0]?.value,
          address: combineAddress,
          companyName,
          logo: logo || packingSlip?.companyLogo,
          lineId: v.id,
          orderNumber,
        })
        return true;
      }
      return undefined;
    }))
  }
  if (!results.some(Boolean) || error) {
    if (!callbackUrl) return;
    axios.request({
      url: callbackUrl,
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
      },
      data: JSON.stringify({ pdfs: [], error }),
    })
    return;
  }

  axios.request({
    url: "https://puppeteer.personify.tech/api/candle-template/genCandleTemplate",
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify({
      infos: requestData,
      callbackUrl,
      authToken: process.env.MICRO_API_TOKEN,
      skipSplitAddress: true,
    }),
  }).catch(err => {
    console.log("sendRequestGenerateCandleSticker", err);
  })
}

class SendRequestGenerateCandleSticker implements TypeAPIHandler {
  url = '/api/pdf/request-generate-candle-sticker';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      items: Joi.array().items(Joi.any()),
      orderNumber: Joi.any(),
      storeId: Joi.any(),
      callbackUrl: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
  ]);

  handler = async (request, reply) => {
    const { body } = request;

    const { items, callbackUrl, orderNumber, storeId } = body;
    reply.status(200).send({
      success: true,
      data: { message: 'Request received' },
    });

    await requestGenerateCandleSticker({
      items, orderNumber, storeId, callbackUrl,
    })
  }
}

export default new SendRequestGenerateCandleSticker();
