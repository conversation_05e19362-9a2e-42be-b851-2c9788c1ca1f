import { TypeAP<PERSON>Hand<PERSON> } from 'type';
import { validateRequest, combineMiddlewares, receiveFileAndFields, IModifiedBody, TFileField, checkAuthen, checkAuthenOptional } from '../api-middlewares'
import { ERROR, MM_TO_PIXEL } from 'const';
import { DB, InMemory as RedisCache } from 'db';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>elper, AWSHelper, VarHelper } from 'helpers';
import Joi = require("joi");
import { INCH_TO_PIXEL } from 'const';
import { generatePngMultipleUrl, TEachImage } from './generatePngMultipleUrl';

const PDFDocument = require('pdfkit');
const SVGtoPDF = require('svg-to-pdfkit');
const fs = require('fs');
const axios = require('axios');

export const generatePdf = async ({ id, designId, images, data, forceVarnish, piw } : any) => {
  if (!images || images.length === 0) throw new Error('This is an empty print job');

  const printAreas = data.product.printAreas;

  if (images.length !== printAreas.length) throw new Error('Number of artwork and number of print area not match');
  if (!data.product?.physicalWidth || !data.product?.physicalHeight) {
    throw new Error('Invalid artboard sizes');
  }

  const printJob = await DB.PrintJob.findByPk(id);
  if (!printJob) throw new Error('Print Job not found: ' + id);

  

  const varnish = await (async() => {
    if(forceVarnish) return 100;
    if (typeof data.design?.settings?.varnishPercentage === 'number') return data.design?.settings?.varnishPercentage;
    if (data.design?.settings?.varnish) return 100;
    const design = await DB.Design.findByPk(designId);
    if (!design) throw new Error('Design not found');
    return !!design.data?.settings?.varnish ? 100 : 0;
  })();

  const promiseArr = images.map(async (val, index) => {
    try {
      const imageUrl = val;
      console.log('imageUrl', imageUrl);
      return {
        url: imageUrl,
        width: printAreas[index].width * MM_TO_PIXEL,
        height: printAreas[index].height * MM_TO_PIXEL,
        top: printAreas[index].top * MM_TO_PIXEL,
        left: printAreas[index].left * MM_TO_PIXEL,
      }
    } catch (err) { }
  })
  let printImages: Array<TEachImage> = await Promise.all(promiseArr);
  printImages = printImages.filter(val => !!val);

  const imageUrl = await generatePngMultipleUrl(
    data.product.physicalWidth * MM_TO_PIXEL,
    data.product.physicalHeight * MM_TO_PIXEL,
    printImages,
    `print-job-${id}.png`,
  );

  console.log('imageUrl', imageUrl);
  console.log('to be send', {
    url: imageUrl,
    varnish,
    piw: !!piw,
  });

  const productId = printJob.productId;
  console.log('productId', productId)
  const product = !productId ? null : await DB.Product.findByPk(productId);
  
  // remove dropletUrl
  const downloadConfig = {
    url: imageUrl,
    apiPath: '/custom-sizes',
    // folderName: `size_${product.physicalWidth}x${product.physicalHeight}`,
    // dropletUrl: product.dropletUrl,
    // dropletName: `size_${product.physicalWidth}x${product.physicalHeight}`,
    noDroplet: true,
    scriptSize: {
      width: product.physicalWidth,
      height: product.physicalHeight,
      dpi: product.dpi || VarHelper.calculateDPI(product.physicalWidth, product.physicalHeight),
    },
  }
  console.log('PDF download config', downloadConfig);

  // const thePDF = 'pdf-'+new Date().getTime() +'.pdf';
  const thePDF = !!printJob.data.pdfNamePrefix ? printJob.data.pdfNamePrefix + '.pdf' : 'pdf-' + new Date().getTime() + '.pdf';
  const s3Url = await FileHelper.getPDFFromLoadBalance(downloadConfig);

  const printJobData = Object.assign({}, printJob.data);
  printJobData.pdf = s3Url;
  printJob.data = printJobData;
  printJob.save();
  const download = await DB.Download.findOne({
    where: {
      id: printJob.id
    }
  });
  if (download) {
    download.pdf = s3Url;
    await download.save();
  }

  return {
    filePath: thePDF,
    s3Url,
  }

}