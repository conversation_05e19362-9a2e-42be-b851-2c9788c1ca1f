import { MM_TO_INCH, MM_TO_PIXEL } from "const";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "helpers";

const sharp = require("sharp")
const fs = require("fs")
const puppeteer = require('puppeteer');
// const { convertFile } = require('convert-svg-to-png');

export type TEachImage = {
  url: string,
  width: number,
  height: number,
  top: number,
  left: number,
}

export const generatePngMultipleUrl = async (containerWidth : number, containerHeight : number, images: Array<TEachImage>, toBeSavedFileNameInS3: string) => {
  const ratio = 1;
  // const ratio = 5.83 / 4.38;
  const svgWidth = containerWidth * ratio;
  const svgHeight = containerHeight * ratio;

  const renderImageDetail = (image : TEachImage, index: number) => {
    const imageX = image.left * ratio;
    const imageY = image.top * ratio;
    return `
      <image
        x="${imageX}"
        y="${imageY}"
        width="${image.width * ratio}"
        height="${image.height * ratio}"
        preserveAspectRatio="xMidYMid slice"
        href="${image.url}">
      </image>
    `
  };

  const greyDot = `
    <rect
      x="0"
      y="0"
      rx="100%"
      width="${0.2 * MM_TO_PIXEL * ratio}"
      height="${0.2 * MM_TO_PIXEL * ratio}"
      fill="#f2fcff"
    ></rect>
    <rect
      x="100%"
      y="100%"
      rx="100%"
      width="${0.2 * MM_TO_PIXEL * ratio}"
      height="${0.2 * MM_TO_PIXEL * ratio}"
      transform="translate(-${0.2 * MM_TO_PIXEL * ratio} -${0.2 * MM_TO_PIXEL * ratio})"
      fill="#f2fcff"
    ></rect>
  `

  const svgString = `
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 ${svgWidth} ${svgHeight}" width="${svgWidth}" height="${svgHeight}">
      ${images.map((val, i) => renderImageDetail(val, i)).join('\n')}
      ${greyDot}
    </svg>
  `;
  const fileName = 'tempSVGFile-' + new Date().getTime();
  const tempSVGFile = fileName + '.svg';
  const tempPNGFile = fileName + '.png';
  fs.writeFileSync(tempSVGFile, svgString);

  const convertToPNG = async () => {
    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox'],
    });
    const page = await browser.newPage();
    await page.setDefaultNavigationTimeout(0);
    const html = `
      <html>
        <head>
          <style>
            html, body {
              background-color: transparent;
              margin: 0;
              padding: 0;
            }
          </style>
        </head>
        <body>${svgString}</body>
      </html>
    `;
    await page.setContent(html);
    await page.setViewport({ width: Math.round(svgWidth), height: Math.round(svgHeight) });
    await page.screenshot({ path: tempPNGFile, omitBackground: true });
    await browser.close();
  }
  await convertToPNG();
  const s3Url = await AWSHelper.upload({
    key: `print-prepare/${toBeSavedFileNameInS3}`,
    filePath: tempPNGFile,
  })
  // const tempPNGBase64 = await FileHelper.fileToBase64(tempPNGMaskFile);

  // fs.unlink(tempPNGMaskFile, () => {});

  return s3Url;
};