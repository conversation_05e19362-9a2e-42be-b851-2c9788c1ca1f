import { <PERSON>AP<PERSON><PERSON><PERSON><PERSON> } from 'type';
import { validateRequest, combineMiddlewares, receiveFileAndFields, IModifiedBody, TFileField, checkAuthen, checkAuthenOptional } from '../api-middlewares'
import { ERROR, MM_TO_PIXEL } from 'const';
import { DB, InMemory as RedisCache } from 'db';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FileHelper } from 'helpers';
import Joi = require("joi");
import { INCH_TO_PIXEL } from 'const';
import { generatePngMultipleUrl, TEachImage } from './generatePngMultipleUrl';

const PDFDocument = require('pdfkit');
const SVGtoPDF = require('svg-to-pdfkit');
const fs = require('fs');
const path = require('path');
const axios = require('axios');

class FromPrintJob implements TypeAPIHandler {
  url = '/api/pdf/from-print-job-data-pts';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      images: Joi.array().items(Joi.string()).required(),
      data: Joi.object({
        product: Joi.object({

          physicalWidth: Joi.number().required(),
          physicalHeight: Joi.number().required(),
          printAreas: Joi.array().items({
            width: Joi.number().required(),
            height: Joi.number().required(),
            top: Joi.number().required(),
            left: Joi.number().required(),
          }),


        }).required()
      }).required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthenOptional,
  ]);

  handler = async (request, reply) => {
    const { id, designId, images, data } = request.body;

    if (!images || images.length === 0) throw new Error('This is an empty print job');
    
    const printAreas = data.product.printAreas;

    if (images.length !== printAreas.length) throw new Error('Number of artwork and number of print area not match');
    if (!data.product?.physicalWidth || !data.product?.physicalHeight) {
      throw new Error('Invalid artboard sizes');
    }

    const varnish = false;

    const promiseArr = images.map(async (val, index) => {
      try {
        const imageUrl = val;
        console.log('imageUrl', imageUrl);
        return {
          url: imageUrl,
          width: printAreas[index].width * MM_TO_PIXEL,
          height: printAreas[index].height * MM_TO_PIXEL,
          top: printAreas[index].top * MM_TO_PIXEL,
          left: printAreas[index].left * MM_TO_PIXEL,
        }
      } catch(err) {}
    })
    let printImages : Array<TEachImage> = await Promise.all(promiseArr);
    printImages = printImages.filter(val => !!val);

    const imageUrl = await generatePngMultipleUrl(
      data.product.physicalWidth * MM_TO_PIXEL,
      data.product.physicalHeight * MM_TO_PIXEL,
      printImages,
      `print-job-${id}.png`
    );
    
    console.log('imageUrl', imageUrl);
    console.log('to be send', {
      url: imageUrl,
      varnish,
    });
    
    const thePDF = 'pdf-'+new Date().getTime() +'.pdf';

    const s3Url = await FileHelper.getPDFFromLoadBalance({
      url: imageUrl,
      varnish,
      apiPath: '/device2-process-pdf',
    })


    await FileHelper.downloadFile(s3Url, thePDF);

    const buffer = fs.readFileSync(thePDF);
    reply.type('application/pdf').send(buffer);

    // remove output pdf
    setTimeout(() => {
      fs.unlink(thePDF, () => {});
    }, process.env.DEV ? 3000 : 15 * 60 * 1000);


  }
}

export default new FromPrintJob();
