import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "helpers";

const sharp = require("sharp")
const fs = require("fs")
const puppeteer = require('puppeteer');

export type TEachImage = {
  base64: string,
  width: number,
  height: number,
  top: number,
  left: number,
}

export const generatePngMultiple = async (containerWidth : number, containerHeight : number, images: Array<TEachImage>, toBeSavedFileNameInS3: string) => {
  const ratio = 5.83 / 4.38;
  const svgWidth = containerWidth * ratio;
  const svgHeight = containerHeight * ratio;

  const renderImageDetail = (image : TEachImage, index: number) => {
    const imageX = image.left * ratio;
    const imageY = image.top * ratio;
    return `
      <image
        x="${imageX}"
        y="${imageY}"
        width="${image.width * ratio}"
        height="${image.height * ratio}"
        href="data:image/png;base64,${image.base64}"></image>
    `
  };

  const svgString = `
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 ${svgWidth} ${svgHeight}" width="${svgWidth}" height="${svgHeight}">
      <!-- <rect id="debug" width="${svgWidth}" height="${svgHeight}" fill="pink" /> -->
      ${images.map((val, i) => renderImageDetail(val, i)).join('\n')}
    </svg>
  `;
  const tempPNGFile = 'tempSVGFile-' + new Date().getTime() + '.png';

  const convertToPNG = async () => {
    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox'],
    });
    const page = await browser.newPage();
    const html = `
      <html>
        <head>
          <style>
            html, body {
              background-color: transparent;
              margin: 0;
              padding: 0;
            }
          </style>
        </head>
        <body>${svgString}</body>
      </html>
    `;
    await page.setContent(html);
    await page.setViewport({ width: Math.floor(svgWidth), height: Math.floor(svgHeight) });
    await page.screenshot({ path: tempPNGFile, omitBackground: true });
    await browser.close();
  }
  await convertToPNG();
  const s3Url = await AWSHelper.upload({
    key: `print-prepare/${toBeSavedFileNameInS3}`,
    filePath: tempPNGFile,
  })
  // const tempPNGBase64 = await FileHelper.fileToBase64(tempPNGMaskFile);

  // fs.unlink(tempPNGMaskFile, () => {});

  return s3Url;
};