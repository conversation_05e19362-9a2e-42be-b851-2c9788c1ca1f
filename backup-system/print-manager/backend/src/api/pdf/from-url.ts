import { TypeAP<PERSON>Hand<PERSON> } from 'type';
import { validateRequest, combineMiddlewares, receiveFileAndFields, IModifiedBody, TFileField, checkAuthen, checkAuthenOptional } from '../api-middlewares'
import { ERROR } from 'const';
import { DB, InMemory as RedisCache } from 'db';
import { <PERSON>then<PERSON><PERSON><PERSON>, FileHelper } from 'helpers';
import Joi = require("joi");
import { generateSvg } from './generateSvg';


const PDFDocument = require('pdfkit');
const SVGtoPDF = require('svg-to-pdfkit');
const fs = require('fs');
const path = require('path');

class FromImage implements TypeAPIHandler {
  url = '/api/pdf/from-url';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      printJobId: Joi.string(),
      url: Joi.string().required(),
      width: Joi.number().required(),
      height: Joi.number().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthenOptional,
  ]);

  handler = async (request, reply) => {
    const { printJobId, url, width, height } = request.body;


    const filePath = new Date().getTime() + path.basename(url);
    await FileHelper.downloadFile(url, filePath);
    const base64 = FileHelper.fileToBase64(filePath);
    
    // http://pdfkit.org/docs/paper_sizes.html
    // A5 = 419.53 x 595.28
    const A5_SIZE = {
      width: 419.53,
      height: 595.28,
    }
    // const doc = new PDFDocument({size: 'A5'});
    const doc = new PDFDocument({size: [A5_SIZE.width, A5_SIZE.height]});
    // adding spot colors
    const RED_CMYK = [0, 100, 100, 0];
    doc.addSpotColor('Spot1', ...RED_CMYK);
    const BLUE_CMYK = [88, 77, 0, 0]
    doc.addSpotColor('Spot2', ...BLUE_CMYK);

    const svg = generateSvg(base64, width, height);
    // fs.writeFileSync('svg-'+new Date().getTime()+'.svg', svg);

    const outputName = `output-${Math.random()}-${new Date().getTime()}.pdf`;
    const fileStream = fs.createWriteStream(outputName)
    doc.pipe(fileStream);

    SVGtoPDF(doc, svg, 0, 0, {
      width: A5_SIZE.width,
      height: A5_SIZE.height,
    });
    doc.end();

    const onEnd = () => new Promise((resolve) => {
      fileStream.on('finish', () => {
        resolve(undefined);
      });
    })
    await onEnd();
    const buffer = fs.readFileSync(outputName);
    reply.type('application/pdf').send(buffer);

    // remove output pdf
    setTimeout(() => {
      fs.unlink(outputName, () => {});
      fs.unlink(filePath, () => {});
    }, process.env.DEV ? 3000 : 15 * 60 * 1000);

    if (!!printJobId && printJobId !== '2022') {
      const printJob = await DB.PrintJob.findByPk(printJobId);
      if (printJob) {
        printJob.isPrinted = true;
        printJob.isPDFDownloaded = true;
        await printJob.save();
      }
    }
  }
}

export default new FromImage();
