import { TypeAP<PERSON><PERSON>and<PERSON> } from 'type';
import { validateRequest, combineMiddlewares, receiveFileAndFields, IModifiedBody, TFileField, checkAuthen, checkAuthenOptional } from '../api-middlewares'
import { ERROR, MM_TO_PIXEL } from 'const';
import { DB, InMemory as RedisCache } from 'db';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FileHelper } from 'helpers';
import Joi = require("joi");
import { generateSvgMultiple, TEachImage } from './generateSvgMultiple';
import { INCH_TO_PIXEL } from 'const';

const PDFDocument = require('pdfkit');
const SVGtoPDF = require('svg-to-pdfkit');
const fs = require('fs');
const path = require('path');

class FromPrintJob implements TypeAPIHandler {
  url = '/api/pdf/from-print-job';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      id: Joi.string(),
      images: Joi.array().items(Joi.string()).required(),
      data: Joi.object({
        product: Joi.object({

          physicalWidth: Joi.number().required(),
          physicalHeight: Joi.number().required(),
          printAreas: Joi.array().items({
            width: Joi.number().required(),
            height: Joi.number().required(),
            top: Joi.number().required(),
            left: Joi.number().required(),
          }),


        }).required()
      }).required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthenOptional,
  ]);

  handler = async (request, reply) => {
    const { id, images, data } = request.body;

    const printAreas = data.product.printAreas;

    const promiseArr = images.map(async (val, index) => {
      try {
        const imageUrl = val;
        console.log('imageUrl', imageUrl);
        const randomName = 'image-'+new Date().getTime() +'.jpg';
        await FileHelper.downloadFile(imageUrl, randomName);
        const base64 = FileHelper.fileToBase64(randomName);
        // console.log('base64', base64);
        setTimeout(() => {
          fs.unlink(randomName, () => {});
        }, 3000);
        return {
          base64,
          width: printAreas[index].width * MM_TO_PIXEL,
          height: printAreas[index].height * MM_TO_PIXEL,
          top: printAreas[index].top * MM_TO_PIXEL,
          left: printAreas[index].left * MM_TO_PIXEL,
        }
      } catch(err) {}
    })
    let printImages : Array<TEachImage> = await Promise.all(promiseArr);
    printImages = printImages.filter(val => !!val);
    
    
    const PAGE_SIZE = {
      width: data.product.physicalWidth * MM_TO_PIXEL,
      height: data.product.physicalHeight * MM_TO_PIXEL,
    }
    // const doc = new PDFDocument({size: 'A5'});
    const doc = new PDFDocument({size: [PAGE_SIZE.width, PAGE_SIZE.height]});
    // adding spot colors
    const RED_CMYK = [0, 100, 100, 0];
    doc.addSpotColor('Spot1', ...RED_CMYK);
    const BLUE_CMYK = [88, 77, 0, 0]
    doc.addSpotColor('Spot2', ...BLUE_CMYK);

    const svg = await generateSvgMultiple(
      data.product.physicalWidth * MM_TO_PIXEL,
      data.product.physicalHeight * MM_TO_PIXEL,
      printImages,
    );
    // fs.writeFileSync('svg-'+new Date().getTime()+'.svg', svg);

    const outputName = `output-${Math.random()}-${new Date().getTime()}.pdf`;
    const fileStream = fs.createWriteStream(outputName)
    doc.pipe(fileStream);

    SVGtoPDF(doc, svg, 0, 0, {
      width: PAGE_SIZE.width,
      height: PAGE_SIZE.height,
    });
    doc.end();

    const onEnd = () => new Promise((resolve) => {
      fileStream.on('finish', () => {
        resolve(undefined);
      });
    })
    await onEnd();
    const buffer = fs.readFileSync(outputName);
    reply.type('application/pdf').send(buffer);

    // remove output pdf
    setTimeout(() => {
      fs.unlink(outputName, () => {});
    }, process.env.DEV ? 3000 : 15 * 60 * 1000);

    if (!!id) {
      const printJob = await DB.PrintJob.findByPk(id);
      if (printJob) {
        printJob.isPrinted = true;
        printJob.isPDFDownloaded = true;
        await printJob.save();
      }
    }
  }
}

export default new FromPrintJob();
