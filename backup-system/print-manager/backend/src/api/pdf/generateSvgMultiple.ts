import { <PERSON><PERSON>elper } from "helpers";

const sharp = require("sharp")
const fs = require("fs")
const puppeteer = require('puppeteer');

export type TEachImage = {
  base64: string,
  width: number,
  height: number,
  top: number,
  left: number,
}

export const generateSvgMultiple = async (containerWidth : number, containerHeight : number, images: Array<TEachImage>) => {
  const ratio = 5.83 / 4.38;
  const svgWidth = containerWidth * ratio;
  const svgHeight = containerHeight * ratio;

  const renderImageMask = (image : TEachImage, index: number) => {
    const imageX = image.left * ratio;
    const imageY = image.top * ratio;
    return `
      <mask id="image-mask-${index}">
        <image
          x="${imageX}"
          y="${imageY}"
          width="${image.width * ratio}"
          height="${image.height * ratio}"
          style="filter: brightness(0%) invert(1);"
          href="data:image/png;base64,${image.base64}"></image>
      </mask>
    `;
  }
  const renderImageDetail = (image : TEachImage, index: number) => {
    const imageX = image.left * ratio;
    const imageY = image.top * ratio;
    return `
      <image
        x="${imageX}"
        y="${imageY}"
        width="${image.width * ratio}"
        height="${image.height * ratio}"
        href="data:image/png;base64,${image.base64}"></image>
    `
  };
  const renderMaskColors = (image : TEachImage, index: number) => {
    const imageX = image.left * ratio;
    const imageY = image.top * ratio;
    return `
      <rect
        x="${imageX}"
        y="${imageY}"
        width="${image.width * ratio}"
        height="${image.height * ratio}"
        fill="#ffffff"
        mask="url(#image-mask-${index})" />
    `;
  }

  const maskSvgString = `
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 ${svgWidth} ${svgHeight}" width="${svgWidth}" height="${svgHeight}">
      <!-- <rect id="debug" width="${svgWidth}" height="${svgHeight}" fill="pink" /> -->
      ${images.map((val, i) => renderImageMask(val, i)).join('\n')}
      ${images.map((val, i) => renderMaskColors(val, i)).join('\n')}
    </svg>
  `;
  const tempPNGMaskFile = 'tempSVGMaskFile-' + new Date().getTime() + '.png';

  const convertMaskToPNG = async () => {
    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox'],
    });
    const page = await browser.newPage();
    const html = `
      <html>
        <head>
          <style>
            html, body {
              background-color: transparent;
              margin: 0;
              padding: 0;
            }
          </style>
        </head>
        <body>${maskSvgString}</body>
      </html>
    `;
    await page.setContent(html);
    await page.setViewport({ width: Math.floor(svgWidth), height: Math.floor(svgHeight) });
    await page.screenshot({ path: tempPNGMaskFile, omitBackground: true });
    await browser.close();
  }
  await convertMaskToPNG();
  const tempPNGBase64 = await FileHelper.fileToBase64(tempPNGMaskFile);

  fs.unlink(tempPNGMaskFile, () => {});

  return `
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 ${svgWidth} ${svgHeight}" width="${svgWidth}" height="${svgHeight}">
      <!-- <rect id="debug" width="${svgWidth}" height="${svgHeight}" fill="pink" /> -->
      <mask id="image-mask-main">
        <image
          x="0"
          y="0"
          width="${svgWidth}"
          height="${svgHeight}"
          href="data:image/png;base64,${tempPNGBase64}"></image>
      </mask>
      <g class="spot1">
        <rect
          x="0"
          y="0"
          width="${svgWidth}"
          height="${svgHeight}"
          fill="Spot1"
          mask="url(#image-mask-main)" />
      </g>
      
      <g class="spot2">
        <rect
          x="0"
          y="0"
          width="${svgWidth}"
          height="${svgHeight}"
          fill="Spot2"
          mask="url(#image-mask-main)" />
      </g>

      <g class="main-content">
        ${images.map((val, i) => renderImageDetail(val, i)).join('\n')}
      </g>
    </svg>
  `;
};