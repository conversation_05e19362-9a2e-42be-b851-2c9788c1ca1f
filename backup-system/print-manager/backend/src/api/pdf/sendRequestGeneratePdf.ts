import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'type';
import { validateRequest, combineMiddlewares } from '../api-middlewares'
import { MM_TO_PIXEL } from 'const';
import { DB } from 'db';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, VarHelper } from 'helpers';
import { generatePngMultipleUrl, TEachImage } from './generatePngMultipleUrl';
import Joi = require("joi");
import { generateCustomArtwork } from './generatePdfFromCustomArtwork';
import { generatePdf } from './generatePdf';
const axios = require('axios');
const probe = require('probe-image-size');

class SendRequestGeneratePdf implements TypeAPIHandler {
  url = '/api/pdf/send-request-generate-pdf';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      items: Joi.array().items(Joi.object({
        id: Joi.number(),
        printJobId: Joi.string(),
        customArtworkData: Joi.object({
          customArtwork: Joi.string(),
          designId: Joi.string(),
        }),
        designRenderId: Joi.string(),
        isITLuggage: Joi.boolean(),
        isBlankPdf: Joi.boolean(),
      })),
      callbackUrl: Joi.string().allow(''),
      returnUrl: Joi.boolean(),
    }),
  }

  preHandler = combineMiddlewares([
    // receiveFileAndFields,
    validateRequest(this.apiSchema),
  ]);

  handler = async (request, reply) => {
    const { body } = request;
    console.log('SendRequestGeneratePdf', body);
    const { items, callbackUrl, returnUrl } = body;
    if (!returnUrl) {
      reply.status(200).send({
        success: true,
        data: { message: 'Request received' },
      });
    }

    const pdfs = [];
    let errorMessage;
    try {
      for (let i = 0; i < items.length; i++) {
        const { customArtworkData, id, designRenderId, isITLuggage, isBlankPdf } = items[i];
        let imageToCheckQuality;
        let pdfUrl = '';
        let printJobId = items[i].printJobId;

        if (isBlankPdf) {
          pdfUrl = "https://print-manager-media.s3.eu-west-1.amazonaws.com/puppeteer/1752754577749.pdf"
          pdfs.push({
            pdfUrl,
            lineId: id,
            printJobId,
            imageQuality: null,
          })
          continue;
        }

        if (printJobId) {
          const printJob = await DB.PrintJob.findByPk(printJobId);
          if (!printJob) throw new Error('Print Job not found 2: ' + printJobId);

          let images = printJob.artworkUrls;
          if (images[0]) {
            imageToCheckQuality = images[0];
          }
          const data = printJob.data;
          let printAreas = data.product?.printAreas;
          if (!printAreas?.length && printJob.productId) {
            const product = await DB.Product.findByPk(printJob.productId);
            if (product) {
              printAreas = product.printAreas;
            }
          }
          if (!printAreas?.length) {
            printAreas = await Promise.all(images.map(async img => {
              const imgSize = await probe(img)
              return {
                width: imgSize.wUnits === 'px' ? imgSize.width / MM_TO_PIXEL : imgSize.width,
                height: imgSize.hUnits === 'px' ? imgSize.height / MM_TO_PIXEL : imgSize.height,
                top: 0,
                left: 0,
              }
            }))
          } else {
            if (images.length !== printAreas.length) throw new Error('Number of artwork and number of print area not match');
          }
          let physicalWidth = data.product?.physicalWidth;
          let physicalHeight = data.product?.physicalHeight;
          if (!physicalWidth || !physicalHeight) {
            physicalWidth = printAreas[0]?.width;
            physicalHeight = printAreas[0]?.height;
          }

          const designId = isITLuggage || !printJob?.designId ? designRenderId : printJob?.designId;
          const design = await DB.Design.findByPk(designId);
          if (!design) throw new Error('Design not found');
          const varnish = !!design.data?.settings?.varnish;
          const promiseArr = images.map(async (val, index) => {
            try {
              const imageUrl = val;
              console.log('imageUrl', imageUrl);
              return {
                url: imageUrl,
                width: printAreas[index].width * MM_TO_PIXEL,
                height: printAreas[index].height * MM_TO_PIXEL,
                top: printAreas[index].top * MM_TO_PIXEL,
                left: printAreas[index].left * MM_TO_PIXEL,
              }
            } catch (err) { }
          })
          let printImages: Array<TEachImage> = await Promise.all(promiseArr);
          printImages = printImages.filter(val => !!val);
          const imageUrl = await generatePngMultipleUrl(
            physicalWidth * MM_TO_PIXEL,
            physicalHeight * MM_TO_PIXEL,
            printImages,
            `print-job-${printJobId}.png`
          );
          console.log('imageUrl', imageUrl);
          console.log('to be send', {
            url: imageUrl,
            varnish,
          });

          const productId = printJob?.productId || design?.productId;
          console.log('productId', productId)
          const product = !productId ? null : await DB.Product.findByPk(productId);
          const dropletUrl = product?.dropletUrl;
          console.log('dropletUrl', dropletUrl);

          // remove dropletUrl
          const downloadConfig = {
            url: imageUrl,
            apiPath: '/custom-sizes',
            // folderName: `size_${product.physicalWidth}x${product.physicalHeight}`,
            // dropletUrl: product.dropletUrl,
            // dropletName: `size_${product.physicalWidth}x${product.physicalHeight}`,
            noDroplet: true,
            scriptSize: {
              width: product.physicalWidth,
              height: product.physicalHeight,
              dpi: product.dpi || VarHelper.calculateDPI(product.physicalWidth, product.physicalHeight),
            },
          };
          console.log('PDF download config', downloadConfig);
          const thePDF = !!printJob.data.pdfNamePrefix ? printJob.data.pdfNamePrefix + '.pdf' : 'pdf-' + new Date().getTime() + '.pdf';
          console.log('thePDF', thePDF);
          pdfUrl = await FileHelper.getPDFFromLoadBalance(downloadConfig);
          const pData = {
            ...printJob.data,
            pdf: pdfUrl,
          }
          printJob.data = pData;
          console.log('printJob.data.pdf', printJob.data.pdf);
          await printJob.save();
        }

        if (customArtworkData && customArtworkData.customArtwork) {
          const customArtworkUrl = `https://print-manager-media.s3.eu-west-1.amazonaws.com/bg-custom-artwork/${customArtworkData.customArtwork}`;
          const design = await DB.Design.findByPk(customArtworkData.designId);
          if (!design) throw new Error('Design not found');
          const product = await DB.Product.findByPk(design.productId);
          if (!product) throw new Error('Product not found');
          const artworkUrl = await generateCustomArtwork({
            width: product.physicalWidth,
            height: product.physicalHeight,
            imageUrl: customArtworkUrl,
          });
          const printJob = await DB.PrintJob.create({
            id: VarHelper.genId(),
            designId: customArtworkData.designId,
            quantity: 0,
            clientId: 'bottled-goose',
            productId: product.id,
            productVariantionName: '',
            previewUrl: customArtworkUrl,
            artworkUrls: [artworkUrl],
            productName: `${design.name} - Custom Artwork`,
            data: {
              product: {
                physicalHeight: product.physicalHeight,
                physicalWidth: product.physicalWidth,
                printAreas: product.printAreas,
              }
            },
            isPaid: false,
            isPrinted: false,
            isPDFDownloaded: false,
            isRePrinted: false,
          });
          printJobId = printJob.id;
          const { s3Url } = await generatePdf({
            id: printJob.id,
            designId: printJob.designId,
            images: printJob.artworkUrls,
            data: printJob.data,
            forceVarnish: false,
          });
          if (printJob.artworkUrls?.[0]) {
            imageToCheckQuality = printJob.artworkUrls?.[0];
          }
          pdfUrl = s3Url;
        }

        if (designRenderId && (!printJobId || !pdfUrl)) {
          const design = await DB.Design.findByPk(designRenderId);
          if (!design) throw new Error('Design not found');
          console.log("Generate PDF - design.data", design.data);

          // @ts-ignore
          if (design.data.preRenderPDF && design.data.preRenderUpdatedAt && design.updatedAt == design.data.preRenderUpdatedAt) {
            pdfUrl = design.data.preRenderPDF;
            if (design.data.artworkUrls?.[0]) {
              imageToCheckQuality = design.data.artworkUrls?.[0];
            }
          } else {
            const product = await DB.Product.findByPk(design.productId);
            if (!product) throw new Error('Product not found');
            const printJob = await DB.PrintJob.create({
              id: VarHelper.genId(),
              designId: design.id,
              quantity: 0,
              clientId: 'bottled-goose',
              productId: product.id,
              productVariantionName: '',
              previewUrl: design.image,
              artworkUrls: design.data.artworkUrls,
              productName: `${design.name} - Design Render`,
              data: {
                product: {
                  physicalHeight: product.physicalHeight,
                  physicalWidth: product.physicalWidth,
                  printAreas: product.printAreas,
                }
              },
              isPaid: false,
              isPrinted: false,
              isPDFDownloaded: false,
              isRePrinted: false,
            });
            printJobId = printJob.id;
            console.log('Design Render Print Job', printJobId);
            const { s3Url } = await generatePdf({
              id: printJob.id,
              designId: printJob.designId,
              images: printJob.artworkUrls,
              data: printJob.data,
              forceVarnish: false,
            });
            pdfUrl = s3Url;
            if (printJob.artworkUrls?.[0]) {
              imageToCheckQuality = printJob.artworkUrls?.[0];
            }
            const data = {
              ...design.data,
              preRenderPDF: pdfUrl,
            }
            design.data = data;
            await design.save();
            design.data = {
              ...design.data,
              preRenderPDF: pdfUrl,
              // @ts-ignore
              preRenderUpdatedAt: design.updatedAt,
            };
            await design.save({
              silent: true,
            });
          }
        }

        let imageQuality;
        console.log("Generate PDF - check image quality", imageToCheckQuality);
        if (imageToCheckQuality) {
          try {
            imageQuality = await AWSHelper.detectModerationLabel(imageToCheckQuality);
          } catch (error) {
            console.log("Generate PDF - check image quality error", error);
          }
        }

        pdfs.push({
          pdfUrl,
          lineId: id,
          printJobId,
          imageQuality,
        })
      }
    } catch (error) {
      console.log("Generate PDF - error", error);
      errorMessage = error.message || JSON.stringify(error);
    }
    console.log("Generate PDF callback data", pdfs, errorMessage);
    if (callbackUrl) {
      axios.request({
        url: callbackUrl,
        method: 'post',
        headers: {
          'Content-Type': 'application/json',
          'X-Auth-Token': process.env.MICRO_API_TOKEN,
        },
        data: JSON.stringify({ pdfs, error: errorMessage }),
      })
    }
    if (returnUrl) {
      reply.status(200).send({
        success: true,
        data: { pdfs, error: errorMessage },
      });
    }
  }
}

export default new SendRequestGeneratePdf();
