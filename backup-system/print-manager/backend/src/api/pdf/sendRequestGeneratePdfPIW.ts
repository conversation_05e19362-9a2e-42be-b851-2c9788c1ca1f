import { <PERSON>AP<PERSON><PERSON><PERSON><PERSON> } from 'type';
import { validateRequest, combineMiddlewares } from '../api-middlewares'
import { DB } from 'db';
import { VarHelper } from 'helpers';
import Joi = require("joi");
import { generatePdf } from './generatePdf';
const axios = require('axios');

class SendRequestGeneratePdfPIW implements TypeAPIHandler {
  url = '/api/pdf/send-request-generate-pdf-piw';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      items: Joi.array(),
      customer: Joi.object(),
      callbackUrl: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    // receiveFileAndFields,
    validateRequest(this.apiSchema),
  ]);

  handler = async (request, reply) => {
    const { body } = request;
    console.log('SendRequestGeneratePdf', body);
    const { customer, items, callbackUrl } = body;
    reply.status(200).send({
      success: true,
      data: { message: 'Request received' },
    });

    const pdfs = [];

    for (let i = 0; i < items.length; i++) {
      const variant = items[i];
      const product = variant.product || {};
      const printJob = await DB.PrintJob.create({
        id: VarHelper.genId(),
        quantity: variant.quantity,
        clientId: 'partnet-in-wine',
        productName: `${variant.name} - ${customer.last_name}`,
        productId: product.id,
        designId: '',
        productVariantionName: 'Var + No Mirr',
        previewUrl: variant.previewUrl,
        artworkUrls: [variant.artworkUrl],
        data: {
          orderId: variant.orderId,
          variantId: variant.id,
          product: {
            physicalWidth: product.physicalWidth,
            physicalHeight: product.physicalHeight,
            printAreas: product.printAreas,
          },
          customer: variant.customer, // shopify object
          shipping_address: variant.shippingAddress, // shopify object
        },
        isPaid: false,
        isPrinted: false,
        isRePrinted: false,
        isPDFDownloaded: false,
      })
      // create dowwnload
      const download = await DB.Download.create({
        id: printJob.id,
        resellerId: 'partnet-in-wine',
        productId: product.id,
        designId: '',
        linkedOrderId: '',
        variationName: printJob.productVariantionName || '',
        pdf: '',
      });
      printJob.data.pdfNamePrefix = `${VarHelper.fourDigitsNumber(download.queueId)}-${variant.name.replace(/\s/g, '_').toUpperCase()}-${printJob.quantity}-B`;
      printJob.readyForPrint = true;
      await printJob.save();

      const { s3Url } = await generatePdf({
        id: printJob.id,
        designId: printJob.designId,
        images: printJob.artworkUrls,
        data: printJob.data,
        forceVarnish: true,
        piw: true,
      });
      pdfs.push({
        pdfUrl: s3Url,
        lineId: variant.id,
      });
    }
    if (!callbackUrl) return;
    axios.request({
      url: callbackUrl,
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
        'X-Auth-Token': process.env.MICRO_API_TOKEN,
      },
      data: JSON.stringify({ pdfs }),
    })
  }
}

export default new SendRequestGeneratePdfPIW();
