import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkA<PERSON>en, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import Joi = require("joi");

class GetSurveyForm implements TypeAPIHandler {
  url = "/api/survey/get-form/:type";
  method = "GET";
  apiSchema = {
    params: Joi.object({
      type: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { params, user } = request;
    const { type } = params;

    const surveyFormModel = await DB.GeneralData.findOne({
      where: {
        type: 'survey-form',
        field1: type,
      },
      raw: true,
    });

    const existedAnwser = await DB.SurveyAnwser.findOne({
      where: {
        userId: user.id,
        type,
      },
    });
    
    return {
      success: true,
      data: {
        ...surveyFormModel,
        formType: type,
      },
      anwser: existedAnwser,
    }
  };
}

export default new GetSurveyForm();
