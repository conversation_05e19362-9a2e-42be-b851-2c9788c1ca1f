import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkAd<PERSON>, checkA<PERSON>en, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { VarHelper } from "helpers";
import Joi = require("joi");

class UpdateSurveyForm implements TypeAPIHandler {
  url = "/api/survey/update-form";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      type: Joi.string().required(),
      data: Joi.string(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
    checkAdmin,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { body } = request;
    const { data, type } = body;

    let objData = {};
    try {
      objData = JSON.parse(data);
    } catch (error) {
    }

    if (!Object.keys(objData).length) {
      throw new Error('Invalid form data');
    }

    const formExisted = await DB.GeneralData.findOne({
      where: {
        type: 'survey-form',
        field1: type,
      },
    });
    if (!!formExisted?.id) {
      await DB.GeneralData.update({
        data: objData,
      }, {
        where: {
          type: 'survey-form',
          field1: type,
        },
      });
    } else {
      await DB.GeneralData.create({
        id: VarHelper.genId(),
        type: 'survey-form',
        name: 'survey-form',
        field1: type,
        field2: '',
        userId: '1',
        data: objData,
        publicPermission: {
          c: false, r: false, u: false, d: false,
        }
      })
    }

    return {
      success: true,
    }
  };
}

export default new UpdateSurveyForm();
