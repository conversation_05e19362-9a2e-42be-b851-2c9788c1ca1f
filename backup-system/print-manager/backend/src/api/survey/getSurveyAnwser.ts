import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkAdmin, checkAuthen, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import Joi = require("joi");
import { Op, Sequelize } from "sequelize";

class GetSurveyAnwser implements TypeAPIHandler {
  url = "/api/survey/get-anwser";
  method = "GET";
  apiSchema = {
    query: Joi.object({
      offset: Joi.number().required(),
      limit: Joi.number().required(),
      sortField: Joi.string().allow(''),
      sortOrder: Joi.string().allow(''),
      keyword: Joi.string().allow(''),
      type: Joi.string().required(),
      getAll: Joi.boolean().default(false),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
    checkAdmin,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { offset, limit, sortField, sortOrder, keyword, type, getAll } = request.query;

    if (getAll) {
      const surveyFormModel = await DB.SurveyAnwser.findAll({
        include: [{
          model: DB.User,
          required: true,
          attributes: ['id', 'email', 'firstName', 'lastName', 'accountName'],
        }],
        where: {
          type
        },
      });
      return {
        success: true,
        data: {
          rows: surveyFormModel,
          count: surveyFormModel.length,
        }
      }
    }
  
    const surveyFormModel = await DB.SurveyAnwser.findAndCountAll({
      include: [{
        model: DB.User,
        required: true,
        attributes: ['id', 'email', 'firstName', 'lastName', 'accountName'],
        where: keyword ? {
          [Op.or]: [
            { email: { [Op.iLike]: `%${keyword}%` } },
            { firstName: { [Op.iLike]: `%${keyword}%` } },
            { lastName: { [Op.iLike]: `%${keyword}%` } },
            { accountName: { [Op.iLike]: `%${keyword}%` } },
            Sequelize.literal(`CONCAT("firstName", ' ', "lastName") ILIKE '%${keyword}%'`)
          ]
        } : undefined
      }],
      where: {
        type
      },
      limit: limit || 20,
      offset: offset || 0,
      order: [
        [sortField || 'updatedAt', sortOrder || 'DESC']
      ],
    });

    return {
      success: true,
      data: surveyFormModel
    }
  };
}

export default new GetSurveyAnwser();
