import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TSurveyFormModel } from "type";
import { check<PERSON><PERSON><PERSON>, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import Joi = require("joi");
import { Var<PERSON><PERSON>per } from "helpers";

class UpdateSurveyForm implements TypeAPIHandler {
  url = "/api/survey/submit-answer";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      type: Joi.string().required(),
      data: Joi.object(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { body } = request;
    const { type, data } = body;

    const surveyForm = await DB.GeneralData.findOne({
      where: {
        type: 'survey-form',
        field1: type,
      },
    });

    if (!surveyForm) {
      throw new Error('Survey form not found');
    }

    let surveyFormModel: TSurveyFormModel = surveyForm.data;
    const questions = surveyFormModel.pages.flatMap(page => page.elements).map(i => i.name);
    const anwsers = {}

    Object.keys(data).forEach(key => {
      const questionIdx = questions.indexOf(key);
      if (questionIdx !== -1) {
        anwsers[`field${questionIdx + 1}`] = Array.isArray(data[key]) 
          ? (typeof data[key][0] === 'object' 
              ? JSON.stringify(data[key])
              : data[key].join(', '))
          : String(data[key]);
      }
    });

    await DB.SurveyAnwser.create({
      id: `sa-${VarHelper.genId()}`,
      userId: request.user.id,
      type,
      ...anwsers,
      data,
    })

    return {
      success: true,
    }
  };
}

export default new UpdateSurveyForm();
