import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, checkAuthen, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON><PERSON> } from "helpers";
import Joi = require("joi");
import { ERROR } from "const";
import { Op } from "sequelize";
import { checkReseller } from "api/api-middlewares/authen";

class ListStickers implements TypeAPIHandler {

  url = "/api/stickers";
  method = "GET";
  apiSchema = {
    query: Joi.object({
      page: Joi.number(),
      resellerId: Joi.string().allow(''),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
    checkReseller
  ]);

  handler = async (request, reply) => {
    const user = request.user;
    let { page, resellerId } = request.query;

    if (!page) page = 1;

    const PAGE_SIZE = 1000;
    const result = await DB.Sticker.findAndCountAll({
      where: resellerId,
      order: [
        ['updatedAt', 'DESC'],
      ],
      offset: (page - 1) * PAGE_SIZE,
      limit: PAGE_SIZE,
    });

    return {
      success: true,
      data: {
        list: result.rows,
        total: result.count,
        hasNext: page * PAGE_SIZE < result.count,
      },
    }
  };
}

export default new ListStickers();