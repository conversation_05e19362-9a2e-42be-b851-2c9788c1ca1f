import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON><PERSON>, checkAuthenOptional, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON>el<PERSON> } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");
import { checkReseller } from "api/api-middlewares/authen";

class UpsertSticker implements TypeAPIHandler {

  url = "/api/sticker";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      urls: Joi.array().items(Joi.string()),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthenOptional,
    checkReseller
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { body, user } = request;
    body.urls.map(async x=>{
      await DB.Sticker.create({
        id: VarHelper.genId(),
        url:x,
        resellerId: user.resellerId ?? user.id,
      });
    })

    return {
      success: true,
    }
  };
}

export default new UpsertSticker();