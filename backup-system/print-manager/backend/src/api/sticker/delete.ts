import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, checkAuthen, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON><PERSON> } from "helpers";
import Joi = require("joi");
import { ERROR } from "const";
import { checkReseller } from "api/api-middlewares/authen";

class DeleteProduct implements TypeAPIHandler {

  url = "/api/sticker/:id";
  method = "DELETE";
  apiSchema = {
    params: Joi.object({
      id: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
    checkReseller
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { id } = request.params;
    const user = request.user;
    
    await DB.Sticker.destroy({
      where: {
        id,
      }
    });

    return {
      success: true,
    }
  };
}

export default new DeleteProduct();