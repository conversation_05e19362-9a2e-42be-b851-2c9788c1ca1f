import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON>en, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON><PERSON> } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");
import { listOrder, listPipeline, rerunJob } from "api/bg/services";

export const rerunCandleStickerJob = async (updateJobQueue: { jobId: string, pipelineId: string }[]) => {
  for (const job of updateJobQueue) {
    if (job?.jobId && job?.pipelineId) {
      await rerunJob({
        jobId: job.jobId,
        pipelineId: Number(job.pipelineId),
        thisJobOnly: true,
      })
      console.log(`Rerun job request sent for jobId: ${job.jobId}, pipelineId: ${job.pipelineId}`);
    }
  }
}

export const reGenerateCandleStickers = async (clientId: string | number, designId?: string) => {
  const params: any = {
    supported: true,
    offset: 0,
    limit: 999,
    clientId,
    stage: "Pre Production,In Production",
    dateType: 'UpdatedAt',
  }
  let rerunJobs = {};
  let rerunPipelines = {};
  const res = await listOrder({
    ...params,
  })
  let orders = res.withPipelines;
  for (let i = 0; i < orders.length; i++) {
    if (orders[i]?.Pipelines?.length) continue;
    const resPipeline = await listPipeline({
      limit: 10,
      offset: 0,
      orderId: orders[i]?.['Order ID'],
    })
    orders[i] = {
      ...orders[i],
      Pipelines: resPipeline?.rows
    }
  }
  if (!orders?.length) return false;
  if (designId) {
    orders = orders.filter(o => o["Raw Data"]?.line_items?.some(lineItem => {
      return lineItem?.properties?.find(property => property?.name === "BG Product Number" && property?.value === designId)
    }))
  }
  const updateJobs = orders.map(o => {
    let order = { ...o };
    if (order.Pipelines.length === 0) return;
    const last = order.Pipelines[order.Pipelines.length - 1];
    if (!last?.Jobs) return;
    if (rerunPipelines[last.Id]) return;
    const jobId = Object.keys(last.Jobs).find(id => last.Jobs?.[id]?.Title === "Generate Candle Sticker")
    if (rerunJobs[jobId]) return;
    if (jobId) {
      rerunJobs[jobId] = true;
      rerunPipelines[last.Id] = true;
      const pipelineId = last.Id;
      return {
        jobId,
        pipelineId,
      }
    }
  }).filter(Boolean);
  console.log("rerunCandleStickerJob", updateJobs);
  rerunCandleStickerJob(updateJobs);
  return updateJobs.length > 0;
}

class UpsertPackingSlip implements TypeAPIHandler {

  url = "/api/packing-slips";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      id: Joi.string().allow(''),
      storeId: Joi.string().allow(''),
      companyName: Joi.string().allow(''),
      phone: Joi.string().allow(''),
      email: Joi.string().allow(''),
      address: Joi.string().allow(''),
      companyLogo: Joi.string().allow(''),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { body } = request;

    // UPDATE EXISTING
    const find = await DB.PackingSlip.findOne({
      where: body.id ? {
        id: body.id
      } : {
        storeId: body.storeId
      }
    });

    if (body.storeId) {
      const findStore = await DB.OnlineStore.findByPk(body.storeId)
      if (!findStore) throw new Error(ERROR.NOT_EXISTED);
      if (request.user.id !== findStore.resellerId && request.user.resellerId !== findStore.resellerId) {
        throw new Error(ERROR.PERMISSION_DENIED);
      }
    }

    if (!!find) {
      if (request.user.id !== find.resellerId && request.user.resellerId !== find.resellerId) {
        throw new Error(ERROR.PERMISSION_DENIED);
      }
      for (let key in body) {
        find[key] = body[key];
      }
      await find.save();
      const updateCandleSticker = await reGenerateCandleStickers(find.resellerId);

      return { success: true, data: find, updateCandleSticker };
    }

    // CREATE NEW
    const data = await DB.PackingSlip.create({
      id: VarHelper.genId(),
      resellerId: request.user.resellerId || request.user.id,
      ...body
    });

    return {
      success: true,
      data: data,
    }
  };
}

export default new UpsertPackingSlip();
