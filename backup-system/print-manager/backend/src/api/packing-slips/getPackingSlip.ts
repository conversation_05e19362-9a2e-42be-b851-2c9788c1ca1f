import { <PERSON>AP<PERSON><PERSON><PERSON><PERSON> } from "type";
import { combineMiddlewares, checkAuthen, validateRequest } from "../api-middlewares";
import Joi = require("joi");
import { ERROR } from "const";
import { DB } from "db";

class GetPackingSlip implements TypeAPIHandler {
  url = "/api/get-packing-slips";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      id: Joi.string().allow(''),
      storeId: Joi.string().allow(''),
      resellerId: Joi.string().allow(''),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    // checkAuthen,
  ]);

  handler = async (request, reply) => {
    const { id, storeId, resellerId } = request.body;

    let whereQuery: any = { id };
    if (storeId) whereQuery = { storeId };
    if (resellerId) whereQuery = { resellerId };

    const find = await DB.PackingSlip.findOne({
      where: whereQuery
    });
    if (!find) throw new Error(ERROR.NOT_EXISTED);
    // if (request.user.id !== find.resellerId && request.user.resellerId !== find.resellerId) {
    //   throw new Error(ERROR.PERMISSION_DENIED);
    // }

    return {
      success: true,
      data: find,
    };

  };
}

export default new GetPackingSlip();
