import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON><PERSON>, checkAuthenOptional, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON>elper } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");

class GeneralDataList implements TypeAPIHandler {

  url = "/api/general-data/:type/:id";
  method = "GET";
  apiSchema = {
    params: Joi.object({
      id: Joi.string().required(),
      type: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthenOptional,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const find = await DB.GeneralData.findOne({
      where: {
        id: request.params.id,
        type: request.params.type,
      },
    });
    if (find.userId !== request.user?.id && !find.publicPermission.r) {
      throw new Error(ERROR.PERMISSION_DENIED);
    }
    return {
      success: true,
      data: find,
    }
  };
}

export default new GeneralDataList();