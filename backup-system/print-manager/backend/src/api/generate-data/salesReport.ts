import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkAuthenOptional, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { ERROR } from 'const';
import Joi = require("joi");

class SalesReport implements TypeAPIHandler {

  url = "/api/sales-report";
  method = "GET";
  apiSchema = {
    params: Joi.object({
      limit: Joi.number(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthenOptional,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { limit = 10 } = request.params;
    const find = await DB.GeneralData.findOne({
      where: {
        type: 'reseller-sales-report',
      },
    });
    if (!find) {
      throw new Error(ERROR.NOT_EXISTED);
    }
    const { designIdQuantity, productIdQuantity } = find.data;

    let designIdQuantityList = Object.keys(designIdQuantity)
      .map(designId => ({
        id: designId,
        name: '',
        image: '',
        quantity: designIdQuantity[designId],
      }))
      .sort((a, b) => b.quantity - a.quantity)
      .slice(0, limit);

    let productIdQuantityList = Object.keys(productIdQuantity)
      .map(productId => ({
        id: productId,
        name: '',
        image: '',
        quantity: productIdQuantity[productId],
      }))
      .sort((a, b) => b.quantity - a.quantity)
      .slice(0, limit);

    const designs = await DB.Design.findAll({
      where: {
        id: designIdQuantityList.map(d => d.id),
      }
    });

    const products = await DB.Product.findAll({
      where: {
        id: productIdQuantityList.map(p => p.id),
      }
    });

    designIdQuantityList = designIdQuantityList.map(d => {
      const design = designs.find(design => design.id === d.id);
      d.name = design?.name;
      d.image = design?.image;
      return d;
    });

    productIdQuantityList = productIdQuantityList.map(p => {
      const product = products.find(product => product.id === p.id);
      p.name = product?.name;
      p.image = product?.image;
      return p;
    });

    return {
      success: true,
      data: {
        designIdQuantityList,
        productIdQuantityList,
      },
    }
  };
}

export default new SalesReport();