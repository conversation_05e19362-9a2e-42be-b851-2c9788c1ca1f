import { DB, InMemory as RedisCache } from 'db';
import { ERROR } from 'const';
import RolesHelper from '../../helpers/RolesHelper';
const moment = require('moment');

export const checkAuthen = async function (request, reply, next) {
  var jwt = request.headers['Authorization'] || request.headers['authorization'];
  if (!jwt) {
    reply.send({ success: false, error: ERROR.NOT_AUTHEN });
    return reply;
  }
  var userInfo = await RedisCache.hgetAsync('JWT', jwt);
  if (userInfo) {
    userInfo = JSON.parse(userInfo);
    request.user = userInfo;
    if (!request.user.adminInspect) {
      DB.User.update({
        loggedInAt: moment().toISOString()
      }, {
        where: {
          id: userInfo.id
        }
      })
    }
    return next();
  }

  reply.send({ success: false, error: ERROR.NOT_AUTHEN });
  return reply;
};

export const checkAuthenMicroApi = async (request, reply, next) => {
  let xAuthToken = request.headers['X-Auth-Token'] || request.headers['x-auth-token'];
  if (xAuthToken && xAuthToken === "P8UeTt92HS98") {
    return next();
  }
  var jwt = request.headers['Authorization'] || request.headers['authorization'];
  if (!jwt) {
    reply.send({ success: false, error: ERROR.NOT_AUTHEN });
    return reply;
  }
  var userInfo = await RedisCache.hgetAsync('JWT', jwt);
  if (userInfo) {
    userInfo = JSON.parse(userInfo);
    request.user = userInfo;
    return next();
  }

  reply.send({ success: false, error: ERROR.NOT_AUTHEN });
  return reply;
}

export const checkAuthenOptional = async function (request, reply, next) {
  var jwt = request.headers['Authorization'] || request.headers['authorization'];
  if (!jwt) return next();
  var userInfo = await RedisCache.hgetAsync('JWT', jwt);
  if (userInfo) {
    userInfo = JSON.parse(userInfo);
    request.user = userInfo;
    return next();
  }
};

export const checkAdmin = async function (request, reply, next) {
  if (!request.user) {
    reply.send({ success: false, error: ERROR.NOT_AUTHEN });
    return reply;
  }
  if (request.user.role !== 'admin') {
    if (request.user.adminInspect) {
      let adminInfo = await RedisCache.hgetAsync('JWT', request.user.adminInspect);
      if (adminInfo) {
        adminInfo = JSON.parse(adminInfo);
        request.admin = adminInfo;
        return next();
      }
    }
    reply.send({ success: false, error: ERROR.PERMISSION_DENIED });
    return reply;
  }
  return next();
};

export const checkReseller = async function (request, reply, next) {
  if (!request.user) {
    reply.send({ success: false, error: ERROR.NOT_AUTHEN });
    return reply;
  }
  if (request.user.role === 'admin') {
    reply.send({ success: false, error: ERROR.PERMISSION_DENIED });
    return reply;
  }
  if (request.user.role !== 'reseller' && (request.user.role === 'user' && !request.user.resellerId)) {
    reply.send({ success: false, error: ERROR.PERMISSION_DENIED });
    return reply;
  }
  return next();
};
