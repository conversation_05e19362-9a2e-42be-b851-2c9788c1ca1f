import { TRequestUser, TypeAPIH<PERSON><PERSON> } from "type";
import { combineMiddlewares, validateRequest } from ".";
import { checkAuthenMicroApi } from "./authen";

type TOptions = {
  url: string;
  method: string;
  apiSchema?: any;
  checkAuthen?: boolean;
}

export const nextjs2api = (handler, options : TOptions) => {
  class NewApi implements TypeAPIHandler {
    
    url = options.url;

    method = options.method;
    apiSchema = options.apiSchema;

    preHandler = combineMiddlewares([
      // options.apiSchema ? validateRequest(options.apiSchema) : null,
      options.checkAuthen ? checkAuthenMicroApi : null,
    ].filter(Boolean));

    handler = async (request, reply) => {
      const res = {
        json: (obj) => {
          reply.send(obj);
        },
      }
      await handler(request, res);
    }
  }

  return new NewApi();
}
