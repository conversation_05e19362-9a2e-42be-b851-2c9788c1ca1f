export { sampleAddData, sampleReject } from "./sample";
export { checkAuthen, checkAuthenOptional, checkAdmin } from "./authen";
export { receiveFileAndFields, IModifiedBody, TFileField } from "./receiveFileAndFields";
export { validateRequest } from "./validateRequest";

export const combineMiddlewares = (arr) => async (request, reply) => {
  let stopStep = 0;
  const done = () => undefined;
  const next = (i) => {
    stopStep = i + 1;
  };
  for (let i = 0; i < arr.length; i++) {
    // console.log(i, stopStep);
    if (stopStep !== i) return done();
    const thisMiddleware = arr[i];
    await thisMiddleware(
      request,
      reply,
      i === arr.length - 1 ? done : () => next(i)
    );
  }
};

export { nextjs2api } from './nextjs2api'