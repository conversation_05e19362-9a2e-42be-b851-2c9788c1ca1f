import { VarHelper } from 'helpers';

export const validateRequest = (schema) => (request, reply, next) => {
  if (!schema) return next();
  if(schema.body) {
    const errorBody = VarHelper.validateVar(request.body, schema.body);
    if (!!errorBody) {
      reply.send({
        success: false,
        error: errorBody.details.map((val) => val.message).join(", "),
        errorDetails: errorBody.details,
      });
      return reply;
    }
  }
  if (schema.params) {
    const errorParams = VarHelper.validateVar(request.params, schema.params);
    if (!!errorParams) {
      reply.send({
        success: false,
        error: errorParams.details.map((val) => val.message).join(", "),
        errorDetails: errorParams.details,
      });
      return reply;
    }
  }
  if (schema.query) {
    const errorQuery = VarHelper.validateVar(request.query, schema.query);
    if (!!errorQuery) {
      reply.send({
        success: false,
        error: errorQuery.details.map((val) => val.message).join(", "),
        errorDetails: errorQuery.details,
      });
      return reply;
    }
  }
  return next();
}