import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkAdmin, checkAuthen, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON><PERSON> } from "helpers";
import Joi = require("joi");

class UpdateFAQs implements TypeAPIHandler {
  url = "/api/settings/update-faqs";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      data: Joi.any(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
    checkAdmin,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { body } = request;
    const { data } = body;

    const formExisted = await DB.GeneralData.findOne({
      where: {
        type: 'faq-questions',
      },
    });
    if (!!formExisted?.id) {
      await DB.GeneralData.update({
        data,
      }, {
        where: {
          type: 'faq-questions',
        },
      });
    } else {
      await DB.GeneralData.create({
        id: VarHelper.genId(),
        type: 'faq-questions',
        name: 'faq-questions',
        field1: '',
        field2: '',
        userId: '1',
        data,
        publicPermission: {
          c: false, r: false, u: false, d: false,
        }
      })
    }

    return {
      success: true,
    }
  };
}

export default new UpdateFAQs();
