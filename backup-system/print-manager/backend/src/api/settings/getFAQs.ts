import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkAuthen, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import Joi = require("joi");

class GetFAQs implements TypeAPIHandler {
  url = "/api/settings/get-faqs";
  method = "GET";
  apiSchema = {}

  preHandler = combineMiddlewares([
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {

    const faqs = await DB.GeneralData.findOne({
      where: {
        type: 'faq-questions',
      },
      raw: true,
    });

    return {
      success: true,
      data: faqs,
    }
  };
}

export default new GetFAQs();
