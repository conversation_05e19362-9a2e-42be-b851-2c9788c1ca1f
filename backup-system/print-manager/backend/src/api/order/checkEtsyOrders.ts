import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TypeAPIHand<PERSON> } from 'type';
import { combineMiddlewares } from '../api-middlewares'
import { DB } from 'db';
import Joi = require('joi');
import Etsy from 'helpers/EtsyHelper';
import { OnlineStoreModel } from 'db/Schema.OnlineStore';

const setupEtsyClient = async (store: OnlineStoreModel) => {
  const { etsyAccessToken, etsyRefreshToken } = store.data || {};
  let needRefreshToken = Date.now() - new Date(store.updatedAt).getTime() > 24 * 60 * 60 * 1000;
  let etsy = new Etsy(etsyAccessToken, etsyRefreshToken);
  if (needRefreshToken) {
    const newTokens = await etsy.getNewToken();
    if (newTokens?.accessToken) {
      store.data = {
        ...store.data,
        etsyAccessToken: newTokens.accessToken,
        etsyRefreshToken: newTokens.refreshToken,
      }
      etsy = new Etsy(newTokens.accessToken, newTokens.refreshToken);
      await store.save();
    }
  }
  return etsy;
}

const checkEtsyOrdersOfEachStore = async (orders: TCMSOrder[], storeId: string) => {
  const store = await DB.OnlineStore.findByPk(storeId);
  if (!store) return;
  const { etsyAccessToken, etsyRefreshToken } = store.data || {};
  if (!etsyAccessToken || !etsyRefreshToken) {
    console.log(`Etsy access token or refresh token not found - ${store.url}`);
    return;
  }
  const etsy = await setupEtsyClient(store)
  await new Promise(resolve => setTimeout(resolve, 500));
  const shopInfo = await etsy.getMe();
  const shopId = shopInfo?.shop_id;
  if (!shopId) {
    return;
  }
  let invalidOrders = [];
  for (let i = 0; i < orders.length; i++) {
    const receipt = await etsy.getReceiptById({ id: Number(orders[i]['Order Source ID']), shopId })
    if (!receipt) {
      invalidOrders.push(orders[i]);
    }
  }
  return invalidOrders;
}

class CheckEtsyOrders implements TypeAPIHandler {
  url = '/api/order/check-etsy-orders';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      orders: Joi.array().required(),
    }),
  }

  preHandler = combineMiddlewares([
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { orders } = request.body;

    const ordersByStore = orders.reduce((acc, order) => {
      const storeId = order['Store ID'];
      if (!acc[storeId]) {
        acc[storeId] = [];
      }
      acc[storeId].push(order);
      return acc;
    }, {});

    let invalidOrders = [];
    for (const storeId in ordersByStore) {
      const invalids = await checkEtsyOrdersOfEachStore(ordersByStore[storeId], storeId);
      if (invalids?.length) {
        invalidOrders.push(...invalids);
      }
    }

    return {
      success: true,
      invalids: invalidOrders,
    }
  }
}

export default new CheckEtsyOrders();
