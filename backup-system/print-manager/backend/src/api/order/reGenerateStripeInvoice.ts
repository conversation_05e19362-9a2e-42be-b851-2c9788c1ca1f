import { TRequestUser, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'type';
import { checkAdmin, checkAuthen, combineMiddlewares } from '../api-middlewares'
import { DB } from 'db';
import Joi = require('joi');
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>per, VarHelper } from 'helpers';
import Stripe from 'stripe';
import { DISCOUNT_RATE, EUROPEAN_COUNTRIES, TAX_ONLY_RATE, shippingPrice, shippingPriceEurope, shippingPriceTheRestOfTheWorld } from 'api/payment/utils';
import Bg from 'api/bg/@utils/Bg';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, { apiVersion: null });

class RecreateStripeInvoice implements TypeAPIHandler {
  url = '/api/order/recreate-stripe-invoice';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      orderId: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    check<PERSON><PERSON><PERSON>,
    checkAdmin,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { orderId } = request.body;

    // Find the order
    const order = await Bg.Order.findByOrderID(orderId);

    if (!order) {
      throw new Error('Order not found');
    }

    // Find the invoice
    const invoice = await DB.Invoice.findOne({
      where: {
        orderId: orderId,
      }
    });

    if (!invoice) {
      throw new Error('Invoice not found');
    }

    // If invoice already has a Stripe invoice ID, check if it exists
    if (invoice.data?.stripeInvoiceID) {
      try {
        const existingStripeInvoice = await stripe.invoices.retrieve(invoice.data.stripeInvoiceID);
        if (existingStripeInvoice) {
          return {
            success: true,
            message: 'Stripe invoice already exists',
            data: existingStripeInvoice
          };
        }
      } catch (error) {
        // If we get here, the Stripe invoice doesn't exist or is invalid
        console.log('Existing Stripe invoice not found, will create new one');
      }
    }

    // Get the reseller info
    const fullUser = await DB.User.findByPk(order['Client ID']);
    if (!fullUser?.resellerStripeId) {
      throw new Error('Reseller Stripe ID not found');
    }

    // Recreate the Stripe invoice
    const itemPrefix = `#${order['Order Number']}`;
    let stripeInvoice;

    try {
      // Truncate product names to stay within Stripe's 500-character limit
      const productNames = invoice.lineItems?.map(i => i.name) || [];
      const productNamesStr = VarHelper.formatProductNamesForStripe(productNames);

      // Create new Stripe invoice
      stripeInvoice = await stripe.invoices.create({
        customer: fullUser.resellerStripeId,
        auto_advance: false,
        metadata: {
          orderId: String(order['Order ID']),
          orderNumber: String(order['Order Number']),
          productName: productNamesStr,
          paymentMethod: "Wallet Credit",
        },
        pending_invoice_items_behavior: "exclude",
      });

      // Add product items
      let totalProductsPrice = 0;
      for (const item of invoice.lineItems || []) {
        const price = invoice.prices?.[String(item.id)];
        if (!price) continue;

        const newPrice = await stripe.prices.create({
          currency: 'gbp',
          product_data: {
            name: `${itemPrefix} ${item.name}`,
          },
          unit_amount: Math.round(Number(price) * 100),
        });

        const productInvoiceLine = await stripe.invoiceItems.create({
          invoice: stripeInvoice.id,
          customer: fullUser.resellerStripeId,
          price: newPrice.id,
          quantity: item.quantity,
          metadata: {
            printJobId: item.properties?.find(p => p.name === 'Print Job')?.value,
            designId: item.properties?.find(p => p.name === 'BG Product Number')?.value,
            productId: item.properties?.find(p => p.name === 'BG Product Library')?.value,
          }
        });

        totalProductsPrice += Math.round(Number(price) * 100) * item.quantity;
      }

      // Add shipping fee
      const shippingFee = (() => {
        const address = invoice.shippingAddress;
        if (address?.country === 'United Kingdom') {
          return shippingPrice.RM48 || 0;
        }
        if (EUROPEAN_COUNTRIES.includes(address?.country)) {
          return shippingPriceEurope.RM48 || 0;
        }
        return shippingPriceTheRestOfTheWorld.RM48 || 0;
      })();

      const _shippingPrice = await stripe.prices.create({
        currency: 'gbp',
        product_data: {
          name: itemPrefix + ` Shipping fee`,
        },
        unit_amount: shippingFee * 100,
      });

      await stripe.invoiceItems.create({
        invoice: stripeInvoice.id,
        customer: fullUser.resellerStripeId,
        price: _shippingPrice.id,
        quantity: 1,
      });

      // Add VAT if applicable
      const shouldIncludeVAT = invoice.shippingAddress?.country === "United Kingdom";
      if (shouldIncludeVAT) {
        const shippingFeeTax = shippingFee * TAX_ONLY_RATE * 100;
        const _taxPrice = await stripe.prices.create({
          currency: 'gbp',
          product_data: {
            name: itemPrefix + ' VAT (20%)',
          },
          unit_amount: Math.round(totalProductsPrice * TAX_ONLY_RATE + shippingFeeTax),
        });

        await stripe.invoiceItems.create({
          invoice: stripeInvoice.id,
          customer: fullUser.resellerStripeId,
          price: _taxPrice.id,
          quantity: 1,
        });
      }

      // Download and upload invoice PDF
      let stripePdfUrl;
      if (stripeInvoice.invoice_pdf) {
        const tempFilePath = `/tmp/invoice-${stripeInvoice.id}.pdf`;
        await FileHelper.downloadFile(stripeInvoice.invoice_pdf, tempFilePath);
        stripePdfUrl = await AWSHelper.upload({
          key: `bg/invoices/${stripeInvoice.id}.pdf`,
          filePath: tempFilePath,
        });
      }

      // Update invoice with new Stripe info
      invoice.data = {
        ...invoice.data,
        invoicePdf: `${process.env.BACKEND_CMS_URL}/api/order/gen-invoice/${order["Order ID"]}`,
        stripeInvoiceUrl: stripePdfUrl,
        stripeInvoiceID: stripeInvoice.id,
      };

      await invoice.save();

      return {
        success: true,
        message: 'Stripe invoice recreated successfully',
        data: stripeInvoice
      };

    } catch (error) {
      console.error('Error recreating Stripe invoice:', error);
      throw new Error(`Failed to recreate Stripe invoice: ${error.message}`);
    }
  }
}

export default new RecreateStripeInvoice(); 