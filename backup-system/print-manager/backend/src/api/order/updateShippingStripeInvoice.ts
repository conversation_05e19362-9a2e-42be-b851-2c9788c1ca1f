import { TRe<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>and<PERSON> } from 'type';
import { checkAdmin, checkAuthen, combineMiddlewares } from '../api-middlewares'
import { DB } from 'db';
import Joi = require('joi');
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, FileHelper } from 'helpers';
import Stripe from 'stripe';
import { EUROPEAN_COUNTRIES, SHIPPING_SERVICES } from 'api/payment/utils';
import Bg from 'api/bg/@utils/Bg';
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, { apiVersion: null });

class UpdateShippingStripeInvoice implements TypeAPIHandler {
  url = '/api/order/update-shipping-service';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      orderId: Joi.string().required(),
      shippingService: Joi.string().optional(),
    }),
  }

  preHandler = combineMiddlewares([
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { orderId, shippingService } = request.body;
    await Bg.initDB()
    const order = await Bg.Order.findByOrderID(orderId);
    if (!order) {
      return {
        success: false,
        message: 'Order not found',
      }
    }

    if (!shippingService) {
      return {
        success: true,
      }
    }

    let invoice = await DB.Invoice.findOne({
      where: {
        orderId: order['Order ID'],
      }
    });

    if (!invoice?.id) throw new Error('Invoice not found');
    const fullUser = await DB.User.findByPk(order['Client ID']);
    if (!fullUser?.resellerStripeId) throw new Error('Reseller Stripe ID not found');

    invoice.data = {
      ...(invoice.data || {}),
      shippingService,
    };

    let stripeInvoice: Stripe.Invoice;
    if (invoice.data?.stripeInvoiceID) {
      stripeInvoice = await stripe.invoices.retrieve(invoice.data?.stripeInvoiceID);
    }
    if (!stripeInvoice) {
      throw new Error('Stripe Invoice not found');
    }

    let newShippingFee = SHIPPING_SERVICES[shippingService]?.price || 0;
    const shippingAddress = order?.['Raw Data']?.shipping_address;
    const isUK = shippingAddress?.country === 'United Kingdom';
    const isEurope = EUROPEAN_COUNTRIES.includes(shippingAddress?.country);
    const isGlobal = !isEurope && !isUK;
    if (isEurope) {
      newShippingFee = SHIPPING_SERVICES[shippingService]?.priceEurope || 0;
    }
    if (isGlobal) {
      newShippingFee = SHIPPING_SERVICES[shippingService]?.priceGlobal || 0;
    }
    if (!newShippingFee) {
      throw new Error('Shipping service not found');
    }

    const shippingLine = stripeInvoice.lines.data.find(i => i.description.includes('Shipping fee'));
    if (shippingLine?.price?.unit_amount === newShippingFee) {
      return {
        success: true,
      }
    }

    // delete old shipping line
    await stripe.invoiceItems.del(shippingLine.id);

    const itemPrefix = `#${order['Order Number']}`;

    const _shippingPrice = await stripe.prices.create({
      currency: 'gbp',
      product_data: {
        name: itemPrefix + ` Shipping fee`,
      },
      unit_amount: newShippingFee * 100,
    });
    await stripe.invoiceItems.create({
      invoice: stripeInvoice.id,
      customer: fullUser.resellerStripeId,
      price: _shippingPrice.id,
      quantity: 1,
    });

    // update DB shipping fee
    invoice.data = {
      ...(invoice.data || {}),
      shippingService,
      shippingFee: newShippingFee,
    }
    const taxLine = stripeInvoice.lines.data.find(i => i.description.includes('VAT (20%)'));
    if (taxLine) {
      await stripe.invoiceItems.del(taxLine.id);

      const diffShippingFee = newShippingFee * 100 - shippingLine.price.unit_amount;
      const newTaxPrice = taxLine.price.unit_amount + diffShippingFee;

      const _taxPrice = await stripe.prices.create({
        currency: 'gbp',
        product_data: {
          name: itemPrefix + ' VAT (20%)',
        },
        unit_amount: newTaxPrice,
      });
      await stripe.invoiceItems.create({
        invoice: stripeInvoice.id,
        customer: fullUser.resellerStripeId,
        price: _taxPrice.id,
        quantity: 1,
      });
      invoice.taxes = newTaxPrice;
    }

    // update DB invoice total
    stripeInvoice = await stripe.invoices.retrieve(stripeInvoice.id);
    invoice.total = stripeInvoice.total / 100;
    console.log("[createInvoice] stripeInvoice.total", stripeInvoice.total);

    const stripeInvoiceID = stripeInvoice.id
    let stripePdfUrl
    if (stripeInvoice.invoice_pdf) {
      const tempFilePath = `/tmp/invoice-${stripeInvoiceID}.pdf`;
      try {
        await FileHelper.downloadFile(stripeInvoice.invoice_pdf, tempFilePath);
        stripePdfUrl = await AWSHelper.upload({
          key: `bg/invoices/${stripeInvoiceID}.pdf`,
          filePath: tempFilePath,
        });
      } catch (error) {
        console.log("[createInvoice] downloadInvoicePdfError", error);
      }
    }

    invoice.data = {
      ...invoice.data,
      invoicePdf: `${process.env.BACKEND_CMS_URL}/api/order/gen-invoice/${order["Order ID"]}`,
      stripeInvoiceUrl: stripePdfUrl,
      stripeInvoiceID,
    }

    await invoice.save();

    const newOrder = await Bg.Order.updateByOrderId(orderId, {
      "Other Data": {
        ...(order["Other Data"] || {}),
        shippingService,
      }
    })

    return {
      success: true,
      data: newOrder,
    }
  }
}

export default new UpdateShippingStripeInvoice();
