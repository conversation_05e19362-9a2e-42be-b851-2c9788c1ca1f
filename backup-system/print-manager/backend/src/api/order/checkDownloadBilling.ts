import { <PERSON>AP<PERSON><PERSON>and<PERSON> } from 'type';
import { validateRequest, combineMiddlewares, receiveFileAndFields, IModifiedBody, TFileField, checkA<PERSON><PERSON> } from '../api-middlewares'
import { ERROR } from 'const';
import { DB, InMemory as RedisCache } from 'db';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FileHelper } from 'helpers';
import Joi = require("joi");

class CheckDownloadBilling implements TypeAPIHandler {
  url = '/api/orders/check-download-billings/:productId';
  method = 'POST';
  apiSchema = {
    params: Joi.object({
        quantity: Joi.number(),
        productId: Joi.string(),
        variantName: Joi.string().allow(''),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request, reply) => {
    const { productId, variantName } = request.params;
    if (!productId) throw new Error('Invalid productId');
    const resellerId = request.user?.id;
    if (!resellerId) throw new Error('Reseller ID not found');

    const orders = await DB.Order.findAll({
        where: {
            didUseAllDownload: false,
            resellerId,
            productId,
        },
    });
    const product = await DB.Product.findByPk(productId);
    let linkedOrders = [];
    const needToPay = (() => {
        if (!product) return false;
        if (!product.variations || product.variations.length === 0) return false;

        let areAllVariationsPrice0 = true;
        for (let i=0; i<product.variations.length; i++) {
            var variation = product.variations[i];
            if (!areAllVariationsPrice0) break;
            for (let j=0; j<variation.prices.length; j++) {
                if (variation.prices[j].price != 0) {
                    areAllVariationsPrice0 = false;
                    break;
                }
            }
        }
        if (areAllVariationsPrice0) return false;

        if (orders.length === 0) return true;
        
        const defaultVariationName = product.variations[0].variant;
        const variationName = variantName || defaultVariationName;
        const finds = orders.filter(v => v.variationName === variationName);
        linkedOrders = finds;
        if (finds.length === 0) return true;
        let availableQuantity = (() => {
            let value = 0;
            finds.forEach((f) => {
                let thisFindValue = f.amount;
                Object.keys(f.downloadIds || {}).forEach((v) => {
                    thisFindValue -= f.downloadIds[v] || 0;
                });
                value += thisFindValue;
            })
            return value;
        })();
        if (availableQuantity < variantName) return true;
        return false;
    })();
    return {
        success: true,
        data: {
            orders,
            needToPay,
            linkedOrders,
        }
    }
  }
}

export default new CheckDownloadBilling();
