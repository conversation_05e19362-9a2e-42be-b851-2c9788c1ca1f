import { <PERSON>AP<PERSON><PERSON>and<PERSON> } from 'type';
import { validateRequest, combineMiddlewares, receiveFileAndFields, IModifiedBody, TFileField, checkAuthen } from '../api-middlewares'
import { ERROR } from 'const';
import { DB, InMemory as RedisCache } from 'db';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ar<PERSON><PERSON>per } from 'helpers';
import Joi = require("joi");
import { Op } from 'sequelize';

class MarkOrderComplete implements TypeAPIHandler {
  url = '/api/orders/mark-order-complete';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      printJobId: Joi.string(),
      amount: Joi.number(),
      fulfillAmount: Joi.number(),
      price: Joi.number(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request, reply) => {
    const { printJobId, amount, price, fulfillAmount } = request.body;
    const findOrder = await DB.Order.findOne({
      where: {
        downloadIds: {
          [Op.like]: `%${printJobId}%`,
        }
      }
    });
    if (!!findOrder) return { success: true, data: findOrder };

    const printJob = await DB.PrintJob.findByPk(printJobId);

    const order = await DB.Order.create({
      id: VarHelper.genId(),
      productId: printJob.productId,
      price,
      amount,
      downloadIds: {
        [printJobId]: fulfillAmount,
      },
      resellerId: request.user.resellerId || request.user.id,
      variationName: printJob.productVariantionName,
      didUseAllDownload: amount === fulfillAmount,
      status: 'PAID',
    });

    printJob.isPaid = true;
    await printJob.save();

    return { success: true, data: order };
  }
}

export default new MarkOrderComplete();
