import { TRequestUser, TypeAPIHandler } from 'type';
import { combineMiddlewares } from '../api-middlewares'
import { DB } from 'db';
import Joi = require('joi');

class UpdateCustomerAddress implements TypeAPIHandler {
  url = '/api/order/update-customer-address';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      orderId: Joi.string().required(),
      address: Joi.object().required(),
    }),
  }

  preHandler = combineMiddlewares([
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { orderId, address } = request.body;

    const invoice = await DB.Invoice.findOne({
      where: {
        orderId,
      }
    });

    if (!invoice?.id) {
      return {
        success: false,
      }
    }
    invoice.shippingAddress = address;
    await invoice.save();

    return {
      success: true,
      data: invoice,
    }
  }
}

export default new UpdateCustomerAddress();
