import { <PERSON>AP<PERSON><PERSON><PERSON><PERSON> } from 'type';
import { validateRequest, combineMiddlewares } from '../api-middlewares'
import { DB } from 'db';
import Joi = require("joi");
import Stripe from 'stripe';
import { VarHelper } from 'helpers';
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, { apiVersion: null });
const moment = require('moment');
const fs = require('fs');
var { Liquid } = require('liquidjs');
var engine = new Liquid();

const LIQUID_TEMPLATE = `
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <style type="text/css">
    * {
      margin: 0;
      padding: 0;
      text-indent: 0;
    }
    body {
      padding: 12px;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      width: {{ viewWidth }};
      height: {{ viewHeight }};
    }
    .logo {
      margin-left: 8pt;
    }
    .header-container {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }
    .normal-text {
      text-indent: 0pt;
      line-height: 11pt;
      text-align: left;
    }
    .row {
      display: flex;
      flex-direction: row;
    }
    .s1 {
      color: black;
      font-family: Calibri, sans-serif;
      font-style: normal;
      font-weight: normal;
      text-decoration: none;
      font-size: 24pt;
    }
    .main-content {
      display: flex;
      flex: 1;
      flex-direction: column;
      justify-content: center;
    }
    .total-container {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
    }
    .devider {
      height: 1px;
      background-color: black;
      width: 250pt;
      margin-top: 8pt;
      margin-bottom: 8pt;
    }
    .p,
    p {
      color: black;
      font-family: Calibri, sans-serif;
      font-style: normal;
      font-weight: normal;
      text-decoration: none;
      font-size: 9pt;
      margin: 0pt;
    }
    h2 {
      color: black;
      font-family: Calibri, sans-serif;
      font-style: normal;
      font-weight: bold;
      text-decoration: none;
      font-size: 9pt;
    }
    .s2 {
      color: black;
      font-family: Calibri, sans-serif;
      font-style: normal;
      font-weight: bold;
      text-decoration: none;
      font-size: 9pt;
    }
    .s3 {
      color: black;
      font-family: Calibri, sans-serif;
      font-style: normal;
      font-weight: normal;
      text-decoration: none;
      font-size: 9pt;
    }
    .s4 {
      color: black;
      font-family: "Times New Roman", serif;
      font-style: normal;
      font-weight: normal;
      text-decoration: none;
      font-size: 9pt;
    }
    h1 {
      color: black;
      font-family: Calibri, sans-serif;
      font-style: normal;
      font-weight: bold;
      text-decoration: none;
      font-size: 10.5pt;
    }
    .s5 {
      color: #333;
      font-family: Calibri, sans-serif;
      font-style: normal;
      font-weight: normal;
      text-decoration: none;
      font-size: 8pt;
    }
    table,
    tbody {
      vertical-align: top;
      overflow: visible;
    }
    body {
      box-sizing: border-box;
    }
    @media screen {
      body {
        padding: 40px;
      }
    }
  </style>
</head>
<body>
  <div class="row">
    {% if logo1 != blank %}
      <img class="logo" width="auto" height="50" src="{{ logo1 }}" />
    {% endif %}
    {% if logo2 != blank %}
      <img class="logo" width="auto" height="50" src="{{ logo2 }}" />
    {% endif %}
    {% if logo3 != blank %}
      <img class="logo" width="auto" height="50" src="{{ logo3 }}" />
    {% endif %}
  </div>
  <p><br /></p>
  <div class="header-container">
    <div>
      <img src="https://print-manager-media.s3.eu-west-1.amazonaws.com/bg/invoice_logo_cropped.png" style="height: 120px; width: auto; " />
      <p class="s1" style="padding-top: 1pt;padding-left: 5pt;text-indent: 0pt;text-align: left;">TAX INVOICE</p>
      <p style="padding-top: 4pt;padding-left: 33pt;text-indent: 0pt;text-align: left;">{{ customerName }}</p>
      <p style="padding-top: 2pt;padding-left: 33pt;text-indent: 0pt;line-height: 118%;text-align: left;">{{ billingAddress }}</p>
    </div>
    <div class="row">
      <div style="margin-right: 12px; min-width: 150px">
        <h2 style="padding-top: 3pt;" class="normal-text">Invoice Date</h2>
        <p class="normal-text">{{ invoiceDate }}</p>
        <h2 style="padding-top: 6pt;" class="normal-text">Invoice Number</h2>
        <p class="normal-text">{{ invoiceNumber }}</p>
        <h2 style="padding-top: 6pt;" class="normal-text">Reference
        </h2>
        <p class="normal-text">Order {{ orderId }}</p>
        <h2 style="padding-top: 6pt;" class="normal-text">VAT Number
        </h2>
        <p class="normal-text">{{ vatNumber }}</p>
      </div>
      <div style="margin-right: 12px; min-width: 150px">
        <p style="padding-top: 4pt;padding-left: 5pt;text-indent: 0pt;line-height: 118%;text-align: left;">{{ bgAddress }}</p>
      </div>
    </div>
  </div>

  <p><br /></p>
  <div class="main-content">
    <table style="border-collapse:collapse" cellspacing="0">
      <tr style="height:16pt">
        <td style="border-bottom-style:solid;border-bottom-width:1pt">
          <p class="s2" style="padding-left: 2pt;text-indent: 0pt;line-height: 9pt;text-align: left;">Description</p>
        </td>
        <td style="width:152pt;border-bottom-style:solid;border-bottom-width:1pt">
          <p class="s2" style="padding-right: 22pt;text-indent: 0pt;line-height: 9pt;text-align: right;">Quantity</p>
        </td>
        <td style="width:58pt;border-bottom-style:solid;border-bottom-width:1pt">
          <p class="s2" style="padding-right: 16pt;text-indent: 0pt;line-height: 9pt;text-align: right;">VAT</p>
        </td>
        <td style="width:68pt;border-bottom-style:solid;border-bottom-width:1pt">
          <p class="s2" style="padding-right: 2pt;text-indent: 0pt;line-height: 9pt;text-align: right;">Amount GBP</p>
        </td>
      </tr>
      {% for lineItem in lineItems %}
        <tr style="height:33pt">
          <td
            style="border-top-style:solid;border-top-width:1pt;border-bottom-style:solid;border-bottom-width:1pt;border-bottom-color:#CCCCCC">
            <p class="s3" style="padding-top: 4pt;padding-left: 2pt;padding-right: 38pt;text-indent: 0pt;text-align: left;">
              {{ lineItem.description }}
              {% if lineItem.originalPrice %}
                <span style="color: red;">(Sale £̶{{ lineItem.originalPrice }})</span>
              {% endif %}
            </p>
          </td>
          <td
            style="width:152pt;border-top-style:solid;border-top-width:1pt;border-bottom-style:solid;border-bottom-width:1pt;border-bottom-color:#CCCCCC">
            <p class="s3" style="padding-top: 4pt;padding-right: 22pt;text-indent: 0pt;text-align: right;">
            {{ lineItem.quantity }}
            </p>
          </td>
          <td
            style="width:58pt;border-top-style:solid;border-top-width:1pt;border-bottom-style:solid;border-bottom-width:1pt;border-bottom-color:#CCCCCC">
            <p class="s3" style="padding-top: 4pt;padding-right: 13pt;text-indent: 0pt;text-align: right;">
            {{ lineItem.vat }}
            </p>
          </td>
          <td
            style="width:68pt;border-top-style:solid;border-top-width:1pt;border-bottom-style:solid;border-bottom-width:1pt;border-bottom-color:#CCCCCC">
            <p class="s3" style="padding-top: 4pt;padding-right: 2pt;text-indent: 0pt;text-align: right;">
            {{ lineItem.amount }}
            </p>
          </td>
        </tr>
      {% endfor %}
      <tr style="height:16pt">
        <td style="border-top-style:solid;border-top-width:1pt;border-top-color:#CCCCCC">
          <p style="text-indent: 0pt;text-align: left;"><br /></p>
        </td>
        <td style="width:152pt;border-top-style:solid;border-top-width:1pt;border-top-color:#CCCCCC">
          <p style="text-indent: 0pt;text-align: left;"><br /></p>
        </td>
      </tr>
    </table>
    <p style="text-indent: 0pt;text-align: left;"><br /></p>

    <div class="total-container" style="padding-right: 2pt;">
      <div class="row">
        <p class="normal-text">TOTAL VAT 20%</p>
        <p style="width: 80pt; text-align: right">{{ vatTotal }}</p>
      </div>
      <div class="devider"></div>
      <div class="row">
        <p class="normal-text" style="font-weight: bold;">TOTAL GBP</p>
        <p style="width: 80pt; text-align: right; font-weight: bold;">{{ total }}</p>
      </div>
    </div>

    <h1 style="padding-top: 33pt;padding-left: 5pt;text-indent: 0pt;text-align: left;">Due Date: {{ dueDate }}</h1>
    <p style="padding-left: 5pt;text-indent: 0pt;text-align: left;">
      Invoices can be paid to Proper Goose Ltd via BACS. Account number- ******** Sort code- 30-65-41
    </p>
  </div>
  <img width="100%" height="13"
    src="data:image/png;base64,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" />
  <div class="header-container" style="margin-top: 5pt;">
    <div>
      <p class="s1" style="padding-left: 8pt;text-indent: 0pt;text-align: left;">PAYMENT ADVICE</p>
      <p style="padding-top: 24pt;padding-left: 53pt;text-indent: -17pt;line-height: 118%;text-align: left;">To: {{ billingAddress }}</p>
      <p style="text-indent: 0pt;text-align: left;"><br /></p>
    </div>
    <div>
      <div class="row">
        <p class="normal-text" style="font-weight: bold; width: 90pt">Customer</p>
        <p>{{ customerName }}</p>
      </div>
      <div class="row">
        <p class="normal-text" style="font-weight: bold; width: 90pt">Invoice Number</p>
        <p>{{ invoiceNumber }}</p>
      </div>
      <div class="devider" style="background-color: gray;"></div>
      <div class="row">
        <p class="normal-text" style="font-weight: bold; width: 90pt">Amount Due</p>
        <p style="font-weight: bold;">{{ amountDue }}</p>
      </div>
      <div class="row">
        <p class="normal-text" style="font-weight: bold; width: 90pt">Due Date </p>
        <p>{{ dueDate }}</p>
      </div>
      <div class="devider" style="background-color: gray;"></div>
      <h2 class="normal-text" style="font-weight: bold;">Amount Enclosed</h2>
      <div class="devider"></div>
      <p class="s5" style="padding-top: 3pt;padding-left: 104pt;text-indent: 0pt;text-align: left;">Enter the amount you are paying above</p>
    </div>
  </div>
  <p style="text-indent: 0pt;text-align: left; padding-top: 10pt; font-size: 7pt;">Company Registration No: 09119257. Registered Office: Attention: Nicola Parr, 85 Island Farm Road, West Molesey, Surrey, KT8 2LN, United Kingdom</p>
</body>
</html>
`

class OrderInvoice implements TypeAPIHandler {
  url = '/api/order/gen-invoice/:orderId';
  method = 'GET';
  apiSchema = {
    params: Joi.object({
      orderId: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
  ]);

  handler = async (request, reply) => {
    const { orderId } = request.params;

    const returnHTML = (html) => {
      reply.type('text/html').send(html);
    }

    const debugMode = false;
    const savedOrderJson = !debugMode ? null : fs.existsSync(`order_${orderId}.json`) ? JSON.parse(fs.readFileSync(`order_${orderId}.json`, 'utf8')) : null;

    const invoice = savedOrderJson ? savedOrderJson : await DB.Invoice.findOne({
      where: {
        orderId,
      }
    });
    debugMode && fs.writeFileSync(`order_${orderId}.json`, JSON.stringify(invoice, null, 2));

    if (!invoice?.id) {
      returnHTML('Order not found');
      return;
    }

    if (!invoice?.data?.stripeInvoiceID) {
      returnHTML('Order not paid, invoice is unavailable');
      return;
    }

    const stripeInvoice = await stripe.invoices.retrieve(invoice?.data?.stripeInvoiceID);
    // console.log('stripeInvoice', stripeInvoice);
    debugMode && fs.writeFileSync(`stripeInvoice_${invoice?.data?.stripeInvoiceID}.json`, JSON.stringify(stripeInvoice, null, 2));

    const renderAddress = (data: string[]) => {
      if (!data) return '';
      return data.filter(Boolean).join('<br />');
    }

    // stripeInvoice.lines.data can have other items from other order, we will only take the items from this order
    let stripeRelatedLines = (() => {
      // new update, invoice item have prefix for the ease of filtering
      const itemPrefix = `#${invoice.orderNumber} `;
      const filterWithPrefix = stripeInvoice.lines.data.filter(v => v.description.startsWith(itemPrefix));
      if (filterWithPrefix.length > 0) return filterWithPrefix;

      // old update, invoice does not have prefix, we will try to match the title
      const arr = [];
      const allLineTitlesOfOrder = invoice.lineItems.map(v => {
        // v.name but remove all special characters, keep spaces
        return v.name.replace(/[‘’“”]/g, '');
      });
      console.log('allLineTitlesOfOrder', allLineTitlesOfOrder);
      const stripeLines = stripeInvoice.lines.data.reverse();
      

      // in case of duplicate titles, we will check from the index to verify the order of title
      const checkFromIndex = (title, indexStripe, indexOrder) => {
        console.log('checkFromIndex', title, indexStripe, indexOrder);
        // if therre is only one occcurance return true
        const count = stripeLines.filter(v => v.description === title).length;
        if (count === 1) return true;

        let cursor = indexStripe + 1;
        let cursorOrder = indexOrder + 1;
        if (!allLineTitlesOfOrder[cursorOrder]) return true;
        if (stripeLines[cursor].description.includes('Shipping fee')) {
          cursor++;
        }
        if (stripeLines[cursor].description.includes('VAT')) {
          cursor++;
        }
        if (stripeLines[cursor].description !== allLineTitlesOfOrder[cursorOrder] && !!allLineTitlesOfOrder[cursorOrder]) {
          return false;
        } else if (!allLineTitlesOfOrder[cursorOrder]) {
          return true;
        } else {
          return checkFromIndex(allLineTitlesOfOrder[cursorOrder], cursor, cursorOrder);
        }
      }

      // for (let i = 0; i < stripeLines.length; i++) {
      //   const line = stripeLines[i];
      //   for (let j = 0; j< allLineTitlesOfOrder.length; j++) {
      //     const title = allLineTitlesOfOrder[j];
      //     console.log('title', title);
      //     if (line.description === title) {
      //       console.log('match', title);
      //       if (checkFromIndex(title, i, j)) {
      //         arr.push(line);
      //         break;
      //       }
      //     }
      //   }
      // }
      const firstTitle = allLineTitlesOfOrder[0];
      const titleIndexs = stripeLines.map((v, vIndex) => ({ index: vIndex, title: v.description })).filter(v => v.title === firstTitle);
      console.log('titleIndexs', titleIndexs);
      // titleIndexs.forEach(v => {
      //   if (checkFromIndex(firstTitle, v.index, 0)) {
      //     arr.push(stripeLines[v.index]);
      //   }
      // })
      let startStripeIndex = -1;
      for (let i=0; i<titleIndexs.length; i++) {
        const v = titleIndexs[i];
        if (checkFromIndex(firstTitle, v.index, 0)) {
          startStripeIndex = v.index;
          break;
        }
      }
      if (startStripeIndex === -1) return [];


      console.log('startStripeIndex', startStripeIndex);

      let cursor = startStripeIndex;
      let cursorOrder = 0;
      let nextOrderItem = allLineTitlesOfOrder[cursorOrder];
    //   while (!!nextOrderItem && !!stripeLines[cursor]) {
    //     console.log('stripeLines[cursor]', stripeLines[cursor]);
    //     arr.push(stripeLines[cursor]);
    //     cursor++;
    //     if (!!stripeLines[cursor] && stripeLines[cursor].description.includes('Shipping fee')) {
    //       arr.push(stripeLines[cursor]);
    //       cursor++;
    //     }
    //     if (!!stripeLines[cursor] && stripeLines[cursor].description.includes('VAT')) {
    //       arr.push(stripeLines[cursor]);
    //       cursor++;
    //     }
    //     cursorOrder++;
    //   }

    //   return arr.reverse();
    // })();
      allLineTitlesOfOrder.forEach((title, index) => {
        const stripeLine = stripeLines[cursor];
        if (!!stripeLine) arr.push(stripeLine);
        cursor++;
        if (!!stripeLines[cursor] && stripeLines[cursor].description.includes('Shipping fee')) {
          arr.push(stripeLines[cursor]);
          cursor++;
        }
        if (!!stripeLines[cursor] && stripeLines[cursor].description.includes('VAT')) {
          arr.push(stripeLines[cursor]);
          cursor++;
        }
      });

      return arr.reverse();
  })();
    if (stripeRelatedLines.length === 0) {
      stripeRelatedLines = stripeInvoice.lines.data.reverse();
    }

    const lines = (() => {
      const list = stripeRelatedLines;
      // const list = stripeInvoice.lines.data.reverse();
      // .filter(v => !v.description?.includes('VAT')).map((line) => {
      //   return {
      //     description: line.description,
      //     amount: line.amount / 100,
      //     quantity: line.quantity,
      //     vat: '20%'
      //   }
      // });
      const arr = [];
      let lastIndexWithVAT = -1;
      let lastVATOrderNumber;
      // console.log('original list: ' + list.length, list);
      for (let i = 0; i < list.length; i++) {
        const line = list[i];
        const orderNumber = line.description.startsWith('#') ? line.description.split(' ')[0].replace('#', '') : undefined;
        if (line.description?.includes('VAT')) {
          // console.log('VAT line lastIndexWithVAT', lastIndexWithVAT);
          // detect order number in the description, format: #1234 VAT (20%)
          lastVATOrderNumber = orderNumber;
          lastIndexWithVAT = i;
          continue;
        }
        const originalPrice = line?.metadata?.originalPrice || line?.price?.metadata?.originalPrice;
        arr.push({
          description: line.description,
          originalPrice: originalPrice ? VarHelper.formatOriginalPrice(originalPrice) : undefined,
          amount: line.amount / 100,
          quantity: line.quantity,
          vat: (() => {
            if (lastIndexWithVAT !== -1 && lastIndexWithVAT === i - 1) return '20%';
            if (orderNumber === lastVATOrderNumber) return '20%';
            const previousItem = arr[arr.length -  1];
            if (!previousItem) return '';
            // console.log('previousItem', previousItem);
            // @ts-ignore
            if (previousItem.description.includes('Shipping fee') && previousItem.vat === '20%') return '20%';
            return '';
          })(),
        });
      }
      return arr.reverse();
    })();

    // console.log('lines', lines);
    const totalVAT = stripeRelatedLines.filter(v => v.description?.includes('VAT')).reduce((acc, v) => acc + v.amount, 0) / 100;
    const total = stripeRelatedLines.reduce((acc, v) => acc + v.amount, 0);

    const displayData = {
      viewWidth: "100%",
      viewHeight: "100%",
      customerInfo: invoice.customerInfo,
      customerName: [invoice.customerInfo?.first_name, invoice.customerInfo?.last_name].filter(Boolean).join(" "),
      invoiceNumber: invoice.id,
      orderNumber: invoice.orderNumber,
      orderId: invoice.orderId,
      vatNumber: "196504680",
      bgAddress: renderAddress([
        'Proper Goose Ltd',
        '85 Island Farm Road',
        'West Molesey',
        'KT8 2LN',
        '020 8941 3481',
      ]),
      billingAddress: renderAddress([
        invoice.shippingAddress?.address1,
        invoice.shippingAddress?.address2,
        invoice.shippingAddress?.city,
        invoice.shippingAddress?.province,
        invoice.shippingAddress?.zip,
        invoice.shippingAddress?.country,
      ]),
      amountDue: stripeInvoice.status === 'paid' ? 'PAID' : total / 100,
      total: total / 100,
      vatTotal: totalVAT,
      subTotal: "",
      invoiceDate: invoice.createdAt ? moment(invoice.createdAt).format("DD-MM-YYYY HH:mm") : "",
      dueDate: invoice.paidAt ? moment(invoice.paidAt).format("DD-MM-YYYY HH:mm") : "",
      lineItems: lines
    };

    const html = await engine.parseAndRender(LIQUID_TEMPLATE, displayData);

    returnHTML(html);
  }
}

export default new OrderInvoice();
