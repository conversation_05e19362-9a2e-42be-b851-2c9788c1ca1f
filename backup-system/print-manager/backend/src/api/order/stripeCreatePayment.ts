import { <PERSON>AP<PERSON><PERSON><PERSON><PERSON> } from 'type';
import { validateRequest, combineMiddlewares, receiveFileAndFields, IModifiedBody, TFileField, checkAuth<PERSON> } from '../api-middlewares'
import { ERROR } from 'const';
import { DB, InMemory as RedisCache } from 'db';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FileHelper } from 'helpers';
import Joi = require("joi");
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

class StripeCreatePayment implements TypeAPIHandler {
  url = '/api/orders/create-payment';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      amount: Joi.number(),
      productId: Joi.string(),
      variationName: Joi.string(),
      variationAmount: Joi.number(),
      variationPrice: Joi.number(),
      paymentMethodId: Joi.string(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request, reply) => {
    const { amount, productId, variationName, variationAmount, variationPrice, paymentMethodId } = request.body;
    const fullUser = await DB.User.findByPk(request.user.id);
    const resellerName = fullUser.accountName || [fullUser.firstName, fullUser.lastName].filter(Boolean).join(' ');
    if (!fullUser.resellerStripeId) {
      const customer = await stripe.customers.create({
        name: resellerName,
        email: fullUser.email,
      });
      fullUser.resellerStripeId = customer.id;
      await fullUser.save();
      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(amount * 100), // in cents
        currency: 'gbp',
        payment_method_types: ['card'],
        metadata: {
          productId,
          variationName,
          amount,
          price: variationPrice,
        },
        customer: fullUser.resellerStripeId,
        setup_future_usage: 'off_session',
      });
      return {
        success: true,
        data: paymentIntent,
      }
    }

    // pay using saved card

    if (!!paymentMethodId && paymentMethodId !== 'new') {
      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(amount * 100), // in cents
        currency: 'gbp',
        customer: fullUser.resellerStripeId,
        payment_method: paymentMethodId,
        off_session: true,
        confirm: true,
      });
      return {
        success: true,
        data: paymentIntent,
      }
    }

    if (!paymentMethodId) {
      const paymentMethods = await stripe.paymentMethods.list({
        customer: fullUser.resellerStripeId,
        type: 'card',
      });
  
      if (paymentMethods.data.length > 0) {
        return {
          success: true,
          extraConfirmStepData: {
            paymentMethods: paymentMethods.data,
          },
          data: null,
        }
      }
    }
    
    // fallback to first time payment

    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount * 100), // in cents
      currency: 'gbp',
      payment_method_types: ['card'],
      metadata: {
        productId,
        variationName,
        amount: variationAmount,
        price: variationPrice,
      },
      customer: fullUser.resellerStripeId,
      setup_future_usage: 'off_session',
    });
    return {
      success: true,
      data: paymentIntent,
    }
  }
}

export default new StripeCreatePayment();
