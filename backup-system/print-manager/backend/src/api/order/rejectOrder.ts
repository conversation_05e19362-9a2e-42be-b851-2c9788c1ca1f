import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'type';
import { validateRequest, combineMiddlewares, checkAuthen, checkAdmin } from '../api-middlewares'
import { DB, InMemory as RedisCache } from 'db';
import Joi = require("joi");
import <PERSON>Helper from 'helpers/MailHelper';
import { Op } from 'sequelize';
import { getOrderRejectedNotiEmailHtml } from 'helpers/email-templates';
import { FinanceHelper, StripeHelper } from 'helpers';
import ShopifyHelper from 'helpers/ShopifyHelper';

const moment = require('moment');

class RejectOrder implements TypeAPIHandler {
  url = '/api/orders/reject';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      orders: Joi.array().items(Joi.any()),
      reason: Joi.string(),
      deleteShopify: Joi.boolean(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    check<PERSON><PERSON><PERSON>,
    checkAdmin,
  ]);

  handler = async (request, reply) => {
    const { orders, reason, deleteShopify } = request.body;
    console.log('rejecting orders...', orders);

    if (deleteShopify) {
      // cancel shopify orders
      for (let i = 0; i < orders?.length; i++) {
        const order = orders[i];
        if (!order?.OrderId || !order?.StoreID || !order?.shopifyOrderId) continue;
        const store = await DB.OnlineStore.findByPk(order.StoreID);
        if (!store?.id || store.type === "etsy") continue;
        ShopifyHelper.cancelOrder({
          storeUrl: store.url,
          token: store.data?.shopifyAccessToken,
          orderId: order.shopifyOrderId,
        })
      }
    }

    const refundInvoices = []

    // refund
    for (let i = 0; i < orders?.length; i++) {
      const orderId = orders[i].OrderId;
      const orderNumber = orders[i].OrderNumber;
      if (!orderId) continue;
      const invoice = await DB.Invoice.findOne({
        where: {
          orderId,
        }
      });
      if (!invoice || invoice.refundAt) continue;
      if (!invoice.paidAt) continue;
      const stripeInvoiceID = invoice.data?.stripeInvoiceID;
      if (!stripeInvoiceID) continue;

      const invoiceDetail = await StripeHelper.getInvoiceDetail(stripeInvoiceID);
      const { total, customer } = invoiceDetail;
      const refundAmount = invoice.data?.amountPaid || total;
      const transaction = await StripeHelper.refundInvoice(customer, refundAmount, {
        stripeInvoiceID,
        bgInvoiceID: invoice.id,
        orderId: invoice.orderId || orderId,
        refundMessage: `Order #${invoice.orderId || orderId} refund. Reason: ${reason}`,
      });
      invoice.refundAt = moment().toISOString();
      const data = Object.assign({}, invoice.data, {
        refundTransaction: transaction.id,
      });
      invoice.data = data;
      await invoice.save();

      await FinanceHelper.reduceUserTotalsAfterRefund({
        userId: invoice.resellerId,
        orderId,
        stripeInvoiceId: stripeInvoiceID,
      });
      refundInvoices.push(invoice);
    }

    const resellers: { [resellerId: string]: any } = {};

    // send email
    const emails = {};
    for (let i = 0; i < orders?.length; i++) {
      const order = orders[i];
      const resellerId = order['Client ID'] || order.resellerId;

      const setting = await (async () => {
        if (resellers[resellerId]) return resellers[resellerId];
        const s = await DB.GeneralData.findOne({
          where: {
            type: 'reseller-settings-emails',
            name: 'Order Rejected',
            field1: resellerId,
          }
        });
        resellers[resellerId] = s;
        return s;
      })();

      const shouldSendEmail = setting?.field2 !== 'false';
      if (!shouldSendEmail) continue;
      const result = await DB.User.findAll({
        where: {
          [Op.or]: [{
            resellerId: order.resellerId,
          }, {
            id: order.resellerId,
          }]
        }
      });
      const resellerUser = result.find(i => i.id === order.resellerId);
      const members = result.filter(i => i.id !== order.resellerId);

      const ordersNumber = emails[order.resellerId]?.orders || [];
      if (!orders.includes(order.OrderNumber)) {
        ordersNumber.push(order.OrderNumber);
      }
      const resellerName = resellerUser.accountName || [resellerUser.firstName, resellerUser.lastName].filter(Boolean).join(' ');
      emails[order.resellerId] = {
        resellerName,
        to: resellerUser?.email || members[0]?.email,
        cc: members?.map(i => i.email),
        orders: ordersNumber,
      }
    }

    await Promise.all(Object.keys(emails).map(async id => {
      await MailHelper.sendSMTPEmail({
        to: emails[id]?.to,
        cc: emails[id]?.cc,
        subject: "Order rejected",
        html: getOrderRejectedNotiEmailHtml({
          resellerName: emails[id]?.resellerName,
          link: `https://${process.env.DEV ? 'dev.bg-production.personify.tech' : 'bg-production.bottledgoose.co.uk'}/list-orders/rejected`,
        }),
      })
    }))

    return {
      success: true,
      data: {
        refundInvoices,
      }
    }
  }
}

export default new RejectOrder();
