import { TRe<PERSON><PERSON><PERSON>, TypeAP<PERSON>Hand<PERSON> } from 'type';
import { check<PERSON><PERSON>en, combineMiddlewares, validateRequest } from '../api-middlewares'
import { DB } from 'db';
import Joi = require('joi');
import { Var<PERSON>elper } from 'helpers';
import { EUROPEAN_COUNTRIES, TAX_ONLY_RATE, shippingPrice, shippingPriceEurope, shippingPriceTheRestOfTheWorld } from 'api/payment/utils';
import { ERROR } from 'const';
const moment = require('moment');

const genStripeLine = ({ price, name, quantity }) => {
  return {
    "amount": price,
    "amount_excluding_tax": price,
    "currency": "gbp",
    "description": name,
    "price": {
      "object": "price",
      "created": new Date().getTime(),
      "currency": "gbp",
      "type": "one_time",
      "unit_amount": price,
      "unit_amount_decimal": String(price),
    },
    "quantity": quantity,
    "type": "invoiceitem",
    "unit_amount_excluding_tax": String(price)
  }
}

class CreateDraftInvoice implements TypeAPIHandler {
  url = '/api/order/draft-invoice';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      productId: Joi.string().required(),
      designId: Joi.string().required(),
      quantity: Joi.number().required(),
      style: Joi.string().allow(''),
      type: Joi.string().required(),
      customAddress: Joi.object().keys({
        email: Joi.string().allow(''),
        first_name: Joi.string().allow(''),
        address1: Joi.string().allow(''),
        country: Joi.string().allow(''),
        town: Joi.string().allow(''),
        zip: Joi.string().allow(''),
        last_name: Joi.string().allow(''),
        address2: Joi.string().allow(''),
        phone: Joi.string().allow(''),
        county: Joi.string().allow(''),
      }),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { body, user } = request;
    const { productId, customAddress, designId, style, quantity, type } = body;
    if (user.role === 'admin') throw new Error(ERROR.PERMISSION_DENIED);
    const resellerId = user.resellerId || user.id;
    const client = await DB.User.findByPk(resellerId);
    const clientName = client.accountName || [client.firstName, client.lastName].filter(Boolean).join(' ');
    const product = await DB.Product.findByPk(productId);
    const orderNumber = 1000 + Math.floor(Math.random() * 1000)

    const customerInfo = {
      email: user.email,
      first_name: customAddress?.firstName,
      last_name: customAddress?.lastName,
      default_address: {
        first_name: customAddress?.first_name || user.firstName,
        address1: customAddress?.address1 || user.addressLine1,
        country: customAddress?.country || user.country,
        town: customAddress?.town || user.town,
        city: customAddress?.town || user.town,
        zip: customAddress?.zip || user.postCode,
        last_name: customAddress?.last_name || user.lastName,
        address2: customAddress?.address2 || user.addressLine2,
        phone: customAddress?.phone || null,
      }
    }
    const rawData = {
      id: new Date().getTime(),
      email: user.email,
      name: `${type}-${orderNumber}`,
      order_number: orderNumber,
      customer: customerInfo,
      current_total_price: "0",
      current_subtotal_price: "0",
      total_price: "0",
      shipping_lines: [],
      line_items: [{
        id: product.id,
        name: product.name + ' ' + style,
        price: "0",
        product_id: product.id,
        quantity,
        title: product.name + ' ' + style,
        total_discount: "0.00",
        properties: [
          type === 'Sample' ? { name: 'Order A Sample', value: 'YES' } : undefined,
          (!type || type === 'Wholesale') ? { name: 'Wholesale', value: 'YES' } : undefined,
          { name: 'BG Product Number', value: designId },
          { name: 'BG Product Library', value: productId },
        ].filter(Boolean),
        grams: product?.data?.weight || 0,
      }],
      shipping_address: {
        first_name: customAddress?.first_name || user.firstName,
        address1: customAddress?.address1 || user.addressLine1,
        country: customAddress?.country || user.country,
        town: customAddress?.town || user.town,
        city: customAddress?.town || user.town,
        zip: customAddress?.zip || user.postCode,
        last_name: customAddress?.last_name || user.lastName,
        address2: customAddress?.address2 || user.addressLine2,
        phone: customAddress?.phone || null,
        name: customAddress?.first_name ? [customAddress?.first_name, customAddress?.last_name].filter(Boolean).join(' ') : `${user.firstName} ${user.lastName}`,
        countryCode: customAddress?.country === 'United Kingdom' ? 'GB' : customAddress?.country === 'United States' ? 'US' : customAddress?.country === 'Canada' ? 'CA' : customAddress?.country === 'Australia' ? 'AU' : 'GB',
      },
      billing_address: {
        first_name: user.firstName,
        address1: user.addressLine1,
        country: user.country,
        zip: user.postCode,
        last_name: user.lastName,
        address2: user.addressLine2,
        town: customAddress?.town || user.town,
        city: customAddress?.town || user.town,
        countryCode: customAddress?.country === 'United Kingdom' ? 'GB' : customAddress?.country === 'United States' ? 'US' : customAddress?.country === 'Canada' ? 'CA' : customAddress?.country === 'Australia' ? 'AU' : 'GB',
      },
      created_at: moment().format('YYYY-MM-DD HH:mm:ssZ'),
      updated_at: moment().format('YYYY-MM-DD HH:mm:ssZ'),
      orderType: type || 'Wholesale',
    }

    const order = {
      'Order ID': 'DR_' + Math.random().toString(36).substring(7),
      'Order Source ID': String(new Date().getTime()),
      'Order Name': `${type}-${orderNumber}`,
      'Order Number': orderNumber,
      'Customer Email': customerInfo.email,
      'Customer Name': [customerInfo.first_name, customerInfo.last_name].filter(Boolean).join(' '),
      'Raw Data': rawData,
      'All Item IDs': rawData.line_items?.map(i => i.id)?.join(', '),
      'All Product Names': rawData.line_items?.map(i => i.name)?.join(', '),
      'Status': 'Pending',
      'Client ID': resellerId,
      'Client Name': clientName,
      'env': process.env.DEV ? 'dev' : 'prod',
      'Stage': 'Pre Production',
      'StageStatus': "Awaiting Payment",
      'OrderType': type || 'Wholesale',
    }

    const discountedPrice = VarHelper.calculateDiscountedPrice({
      price: product.price,
      quantity,
      discountType: product.data?.discountType,
    }, product.packPrices);

    const stripeLines = [
      genStripeLine({
        price: discountedPrice * 100,
        name: product.name,
        quantity,
      })
    ]
    const shippingFee = (() => {
      const address = customerInfo?.default_address;
      if (address?.country === 'United Kingdom') {
        return shippingPrice.RM48 || 0;
      }
      // TODO: handle international shipping fee
      if (EUROPEAN_COUNTRIES.includes(address?.country)) {
        return shippingPriceEurope.RM48 || 0;
      }
      return shippingPriceTheRestOfTheWorld.RM48 || 0;
    })();
    stripeLines.push(genStripeLine({
      price: shippingFee * 100,
      name: "Shipping fee",
      quantity: 1,
    }))
    const shouldIncludeVAT = rawData.shipping_address?.country === "United Kingdom";
    const orderAmount = (discountedPrice * quantity + shippingFee) * 100;
    let totalAmount = orderAmount;

    if (shouldIncludeVAT) {
      const taxAmount = Math.round(orderAmount * TAX_ONLY_RATE);
      stripeLines.push(genStripeLine({
        price: taxAmount,
        name: "VAT (20%)",
        quantity: 1,
      }))
      totalAmount += taxAmount;
    }
    const stripeInvoice = {
      metadata: {
        orderId: String(order['Order ID']),
        orderNumber: String(order['Order Number']),
        productName: product.name,
        paymentMethod: "Wallet Credit",
      },
      lines: {
        data: stripeLines,
      },
      total: totalAmount,
    }

    const draftInvoice = await DB.DraftInvoice.create({
      id: VarHelper.genId(),
      productId,
      designId,
      quantity,
      resellerId,
      status: 'pending',
      type: type || 'Wholesale',
      data: { order, invoice: stripeInvoice },
    })

    return {
      success: true,
      data: draftInvoice,
    }
  }
}

export default new CreateDraftInvoice();
