import { TRequestUser, TUser, TypeAP<PERSON><PERSON>and<PERSON> } from 'type';
import { validateRequest, combineMiddlewares, checkAuthen } from '../api-middlewares'
import { DB, InMemory as RedisCache } from 'db';
import Joi = require("joi");
import { <PERSON>Helper, StripeHelper } from 'helpers';
import ShopifyHelper from 'helpers/ShopifyHelper';

const moment = require('moment');

class CancelOrder implements TypeAPIHandler {
  url = '/api/orders/cancel';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      orders: Joi.array().items(Joi.any()),
      deleteShopify: Joi.boolean(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { orders, deleteShopify } = request.body;
    const refundInvoices = []
    const userResellerId = request.user.resellerId || request.user.id;

    if (deleteShopify) {
      // cancel shopify orders
      for (let i = 0; i < orders?.length; i++) {
        const order = orders[i];
        if (!order?.OrderId || !order?.StoreID || !order?.shopifyOrderId) continue;
        const store = await DB.OnlineStore.findByPk(order.StoreID);
        if (!store?.id || store.type === "etsy") continue;
        ShopifyHelper.cancelOrder({
          storeUrl: store.url,
          token: store.data?.shopifyAccessToken,
          orderId: order.shopifyOrderId,
        })
      }
    }

    // refund
    for (let i = 0; i < orders?.length; i++) {
      const orderId = orders[i].OrderId;
      const orderNumber = orders[i].OrderNumber;
      if (!orderId) continue;
      const resellerId = orders[i].resellerId;
      if (userResellerId !== resellerId) continue;
      const invoice = await DB.Invoice.findOne({
        where: {
          orderId,
        }
      });
      if (!invoice || invoice.refundAt) continue;
      if (!invoice.paidAt) continue;
      const stripeInvoiceID = invoice.data?.stripeInvoiceID;
      if (!stripeInvoiceID) continue;

      const invoiceDetail = await StripeHelper.getInvoiceDetail(stripeInvoiceID);
      const { total, customer } = invoiceDetail;
      const refundAmount = invoice.data?.amountPaid || total;
      const transaction = await StripeHelper.refundInvoice(customer, refundAmount, {
        stripeInvoiceID,
        bgInvoiceID: invoice.id,
        orderId: invoice.orderId || orderId,
        refundMessage: `Order #${invoice.orderId || orderId} refund. Reason: Reseller cancelled`,
      });
      invoice.refundAt = moment().toISOString();
      const data = Object.assign({}, invoice.data, {
        refundTransaction: transaction.id,
      });
      invoice.data = data;

      await invoice.save();

      await FinanceHelper.reduceUserTotalsAfterRefund({
        userId: resellerId,
        orderId: invoice.orderId || orderId,
        stripeInvoiceId: stripeInvoiceID,
      });

      refundInvoices.push(invoice);
    }

    return {
      success: true,
      data: {
        refundInvoices,
      }
    }
  }
}

export default new CancelOrder();
