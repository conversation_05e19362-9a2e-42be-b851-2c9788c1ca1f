import { T<PERSON><PERSON>rde<PERSON>, TRe<PERSON>User, TypeAPIHand<PERSON> } from 'type';
import { combineMiddlewares } from '../api-middlewares'
import { DB } from 'db';
import Joi = require('joi');
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, FileHelper, VarHelper } from 'helpers';
import Stripe from 'stripe';
import { DISCOUNT_RATE, EUROPEAN_COUNTRIES, TAX_ONLY_RATE, shippingPrice, shippingPriceEurope, shippingPriceTheRestOfTheWorld } from 'api/payment/utils';
import MailHelper from 'helpers/MailHelper';
import { getNewOrderNotiEmailHtml } from 'helpers/email-templates';
import { listPipeline, updateOrderStatus, updatePipelineShareData } from 'api/bg/services';
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, { apiVersion: null });

const recentOrders: { [key: string]: number } = {};
const THROTTLE_TIME = 2000;

class UpsertInvoice implements TypeAPIHandler {
  url = '/api/order/invoice';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      order: Joi.any(),
    }),
  }

  preHandler = combineMiddlewares([
  ]);

  handler = async (request: TRequestUser, reply) => {
    const order: TCMSOrder = request.body.order;
    // prevent duplicate request from micro APIs
    const orderId = order['Order ID'];
    const now = new Date().getTime();
    if (recentOrders[orderId]) {
      if (now - recentOrders[orderId] < THROTTLE_TIME) {
        throw new Error("Too many request with the same order");
      }
    }
    recentOrders[order['Order ID']] = now;

    let orderWeight = 0;
    const printJobInfo = order['Raw Data']?.line_items.map(v => {
      const printJobId = v.properties.find(i => i.name === "Print Job")?.value;
      const designId = v.properties.find(val => val.name === 'BG Product Number')?.value;
      const productId = v.properties.find(val => val.name === 'BG Product Library')?.value;
      return {
        id: v.id,
        quantity: v.quantity,
        printJobId,
        designId,
        productId,
        name: v.name,
      }
    }).filter(v => !!v.printJobId || !!v.designId || !!v.productId);

    let orderAmount = 0;
    let productsPrice = {};
    let invoiceProducts = [];
    console.log(`[createInvoice] printJobInfo:`, printJobInfo);

    for (let i = 0; i < printJobInfo.length; i++) {
      const { id, quantity, printJobId, designId, productId, name } = printJobInfo[i];
      try {
        const product = await (async () => {
          if (productId) {
            return await DB.Product.findByPk(productId);
          }
          if (printJobId) {
            const printJob = await DB.PrintJob.findByPk(printJobId);
            if (!!printJob) {
              const { productId } = printJob;
              return await DB.Product.findByPk(productId);
            }
          }
          if (designId) {
            const design = await DB.Design.findByPk(designId);
            if (!!design) {
              const { productId } = design;
              return await DB.Product.findByPk(productId);
            }
          }
        })();
        if (!product) continue;
        console.log(`[createInvoice] product:`, {
          price: product.price,
          quantity,
          discountType: product.data?.discountType,
          packPrices: product.packPrices,
        });
        const discountedPrice = VarHelper.calculateDiscountedPrice({
          price: product.price,
          quantity,
          discountType: product.data?.discountType,
        }, product.packPrices);
        orderAmount += discountedPrice * quantity;
        orderWeight += (product.data?.weight || 1) * quantity;
        productsPrice[String(printJobId || id)] = discountedPrice;
        invoiceProducts.push({
          quantity, printJobId, designId, productId, name, id,
          productPrice: discountedPrice,
          product,
        })
      } catch (err) {
        console.log(`[createInvoice] invoiceProducts_error:`, err);
      }
    }
    const shouldIncludeVAT = order['Raw Data']?.shipping_address?.country === "United Kingdom";
    const amountWithTax = shouldIncludeVAT ? orderAmount * 1.2 : orderAmount;

    let invoice = await DB.Invoice.findOne({
      where: {
        orderId: order['Order ID'],
      }
    });
    console.log(`[createInvoice] existed invoice:`, invoice);
    if (!invoice?.id) {
      invoice = await DB.Invoice.create({
        id: VarHelper.genId(),
        orderId: String(order['Order ID']),
        orderNumber: String(order['Order Number']),
        total: amountWithTax,
        taxes: shouldIncludeVAT ? orderAmount * 0.2 : 0,
        prices: productsPrice,
        data: {
          weight: orderWeight,
          isSampleRequest: !!order['Raw Data']?.is_sample_request,
          isWholesale: order['Raw Data'].orderType === 'Wholesale',
          orderType: order['Raw Data'].orderType,
        },
        resellerId: order['Client ID'],
        store: order['Client Name'],
        lineItems: order['Raw Data'].line_items,
        customerInfo: order['Raw Data'].customer,
        shippingAddress: order['Raw Data'].shipping_address || order['Raw Data'].customer,
      });
      console.log("[createInvoice] upsert invoice success", invoice);
    } else {
      invoice.total = amountWithTax;
      invoice.orderNumber = String(order['Order Number']);
      invoice.taxes = orderAmount * 0.2;
      invoice.prices = productsPrice;
      invoice.data = {
        ...(invoice.data || {}),
        weight: orderWeight,
        isSampleRequest: !!order['Raw Data']?.is_sample_request,
        isWholesale: order['Raw Data'].orderType === 'Wholesale',
        orderType: order['Raw Data'].orderType,
      };
      invoice.store = order['Client Name'];
      invoice.resellerId = order['Client ID'];
      invoice.lineItems = order['Raw Data'].line_items;
      invoice.customerInfo = order['Raw Data'].customer;
    }

    const fullUser = await DB.User.findByPk(order['Client ID']);
    console.log("[createInvoice] resellerStripeId", fullUser?.resellerStripeId);
    if (fullUser?.resellerStripeId) {
      let existedStripeInvoice;
      if (invoice.data?.stripeInvoiceID) {
        existedStripeInvoice = await stripe.invoices.retrieve(invoice.data?.stripeInvoiceID);
      }
      console.log("[createInvoice] invoiceProducts", invoiceProducts);
      console.log("[createInvoice] existedStripeInvoice", existedStripeInvoice);
      if (!existedStripeInvoice && invoiceProducts?.length) {
        // let sampleToken = typeof fullUser?.otherData?.sampleToken === 'number' ? fullUser?.otherData?.sampleToken : 10;
        // const isSampleRequest = order['Raw Data']?.is_sample_request;
        // const shouldDiscount = !isSampleRequest ? false : (sampleToken > 0 ? true : false);
        // if (shouldDiscount) sampleToken--;
        let shouldDiscount = false;
        const itemPrefix = `#${order['Order Number']}`;

        let stripeInvoice: Stripe.Invoice
        // create stripe
        try {
          // Truncate product names to stay within Stripe's 500-character limit
          const productNames = invoiceProducts.map(i => i.name);
          const productNamesStr = VarHelper.formatProductNamesForStripe(productNames);

          stripeInvoice = await stripe.invoices.create({
            customer: fullUser.resellerStripeId,
            auto_advance: false,
            metadata: {
              orderId: String(order['Order ID']),
              orderNumber: String(order['Order Number']),
              productName: productNamesStr,
              paymentMethod: "Wallet Credit",
            },
            pending_invoice_items_behavior: "exclude",
          });
        } catch (error) {
          console.log("[createInvoice] createStripeInvoiceError", error);
        }

        let totalProductsPrice = 0;
        // add items for product
        for (let i = 0; i < invoiceProducts.length; i++) {
          const { name, quantity, productPrice, printJobId, designId, productId, product } = invoiceProducts[i];
          const discountRate = shouldDiscount ? DISCOUNT_RATE : 1;
          const newPrice = await stripe.prices.create({
            currency: 'gbp',
            product_data: {
              name: `${itemPrefix} ${name}`,
            },
            unit_amount: Math.round(Number(productPrice) * discountRate * 100),
          })
          const isDiscountedProduct = !!product.originalPrice && product.originalPrice !== product.price;

          const productInvoiceLine = await stripe.invoiceItems.create(VarHelper.removeUndefinedField({
            invoice: stripeInvoice?.id,
            customer: fullUser.resellerStripeId,
            price: newPrice.id,
            quantity: quantity,
            metadata: {
              printJobId, designId, productId,
              ...(isDiscountedProduct ? {
                originalPrice: product.originalPrice,
              } : {})
            }
          }));
          totalProductsPrice += Math.round(Number(productPrice) * discountRate * 100) * quantity;
          console.log("[createInvoice] productInvoiceLine", productInvoiceLine);
        }

        if (!stripeInvoice) {
          // create stripe
          stripeInvoice = await stripe.invoices.create({
            customer: fullUser.resellerStripeId,
            auto_advance: false,
            metadata: {
              orderId: String(order['Order ID']),
              orderNumber: String(order['Order Number']),
              productName: invoiceProducts.map(i => i.name).join('\n'),
              paymentMethod: "Wallet Credit",
            }
          });
        }
        console.log("[createInvoice] stripeInvoice", stripeInvoice);
        console.log("[createInvoice] stripeInvoice lines", stripeInvoice?.lines?.data);
        const shippingFee = (() => {
          const address = invoice.shippingAddress;
          if (address?.country === 'United Kingdom') {
            return shippingPrice.RM48 || 0;
          }
          // TODO: handle international shipping fee
          if (EUROPEAN_COUNTRIES.includes(address?.country)) {
            return shippingPriceEurope.RM48 || 0;
          }
          return shippingPriceTheRestOfTheWorld.RM48 || 0;
        })();

        const _shippingPrice = await stripe.prices.create({
          currency: 'gbp',
          product_data: {
            name: itemPrefix + ` Shipping fee`,
          },
          unit_amount: shippingFee * 100,
        });
        await stripe.invoiceItems.create({
          invoice: stripeInvoice.id,
          customer: fullUser.resellerStripeId,
          price: _shippingPrice.id,
          quantity: 1,
        });
        // update DB shipping fee
        invoice.data = {
          ...(invoice.data || {}),
          shippingFee: shippingFee,
        }
        if (shouldIncludeVAT) {
          const shippingFeeTax = shippingFee * TAX_ONLY_RATE * 100;
          const _taxPrice = await stripe.prices.create({
            currency: 'gbp',
            product_data: {
              name: itemPrefix + ' VAT (20%)',
            },
            unit_amount: Math.round(totalProductsPrice * TAX_ONLY_RATE + shippingFeeTax),
          });
          await stripe.invoiceItems.create({
            invoice: stripeInvoice.id,
            customer: fullUser.resellerStripeId,
            price: _taxPrice.id,
            quantity: 1,
          });
          // update DB invoice taxes
          invoice.taxes = Math.round(totalProductsPrice * TAX_ONLY_RATE + shippingFeeTax) / 100;
        } else {
          // update DB invoice taxes
          invoice.taxes = 0;
        }

        // update DB invoice total
        stripeInvoice = await stripe.invoices.retrieve(stripeInvoice.id);
        invoice.total = stripeInvoice.total / 100;
        console.log("[createInvoice] stripeInvoice.total", stripeInvoice.total);

        const stripeInvoiceID = stripeInvoice.id
        let stripePdfUrl
        if (stripeInvoice.invoice_pdf) {
          const tempFilePath = `/tmp/invoice-${stripeInvoiceID}.pdf`;
          try {
            await FileHelper.downloadFile(stripeInvoice.invoice_pdf, tempFilePath);
            stripePdfUrl = await AWSHelper.upload({
              key: `bg/invoices/${stripeInvoiceID}.pdf`,
              filePath: tempFilePath,
            });
          } catch (error) {
            console.log("[createInvoice] downloadInvoicePdfError", error);
          }
        }

        invoice.data = {
          ...invoice.data,
          invoicePdf: `${process.env.BACKEND_CMS_URL}/api/order/gen-invoice/${order["Order ID"]}`,
          stripeInvoiceUrl: stripePdfUrl,
          stripeInvoiceID,
        }
      }
    }
    console.log("[createInvoice] saveInvoice", invoice);

    await invoice.save();

    if (fullUser?.otherData?.skipPayment) {
      console.log("[createInvoice] skipping payment", orderId, order.Id);
      const pipeline = await (async () => {
        try {
          const apiCall = await listPipeline({
            limit: 1,
            offset: 0,
            orderId: String(orderId),
          })
          const list = apiCall.rows;
          if (!list || list.length === 0) return;
          return list[0];
        } catch (error) {
          return undefined;
        }
      })();
      console.log("[createInvoice] pipeline", pipeline?.Id);
      if (pipeline?.Id) {
        await updatePipelineShareData({
          data: {
            isPaid: true,
            isAdminApproved: false,
          },
          pipelineId: pipeline.Id
        })
        await updateOrderStatus({
          id: order.Id,
          StageStatus: "Queued For Production"
        })
      }
    }

    // send noti email
    if (invoice.customerInfo?.email && !order['Raw Data']?.is_sample_request) {
      const settings = await DB.GeneralData.findOne({
        where: {
          type: 'reseller-settings-emails',
          name: 'Awaiting accept',
          field1: order['Client ID'],
        }
      });
      const shouldSendEmail = settings?.field2 !== 'false';
      if (shouldSendEmail) {
        await MailHelper.sendSMTPEmail({
          to: fullUser.email,
          subject: "Your order is on its way",
          html: getNewOrderNotiEmailHtml({
            resellerName: order['Client Name'],
            customerName: `${order['Raw Data'].customer?.first_name} ${order['Raw Data'].customer?.last_name}`,
            link: `https://${process.env.DEV ? 'dev.bg-production.personify.tech' : 'bg-production.bottledgoose.co.uk'}/order/${order["Order ID"]}`,
            items: (invoiceProducts || []).map(i => ({
              name: i.name,
              quantity: i.quantity,
            })),
          }),
        })
      }
    }

    return {
      success: true,
      data: invoice,
    }
  }
}

export default new UpsertInvoice();
