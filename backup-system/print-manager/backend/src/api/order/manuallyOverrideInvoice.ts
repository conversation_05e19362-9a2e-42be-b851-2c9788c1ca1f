import { <PERSON><PERSON><PERSON><PERSON>and<PERSON> } from 'type';
import { validateRequest, combineMiddlewares, receiveFileAndFields, IModifiedBody, TFileField, check<PERSON><PERSON><PERSON> } from '../api-middlewares'
import { ERROR, LOG_TYPES } from 'const';
import { DB, InMemory as RedisCache } from 'db';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FileHelper, VarHelper } from 'helpers';
import Joi = require("joi");
import { Op } from 'sequelize';
import { saveLog } from 'api/log/utils';

class ManuallyOverrideInvoice implements TypeAPIHandler {
  url = '/api/orders/manually-override-invoice';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      invoiceId: Joi.string(),
      productPrice: Joi.number(),
      postage: Joi.number(),
      vat: Joi.number(),
      total: Joi.number(),
      manualInvoicePdf: Joi.string().allow(''),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request, reply) => {
    if (request.user.role !== 'admin') throw new Error(ERROR.PERMISSION_DENIED);
    const { invoiceId, productPrice, postage, vat, total, manualInvoicePdf } = request.body;
    const invoice = await DB.Invoice.findByPk(invoiceId);
    if (!invoice) throw new Error(ERROR.NOT_EXISTED);
    saveLog(request.user, {
      type: LOG_TYPES.OVERRIDE_INVOICE,
      data: {
        invoiceId,
      },
      newData: { invoiceId, productPrice, postage, vat, total, manualInvoicePdf },
      oldData: {
        ...invoice.manualInvoiceData,
        manualInvoicePdf: invoice.manualInvoicePdf,
      },
    })

    invoice.manualInvoiceData = {
      productPrice,
      postage,
      vat,
      total,
    };
    if (typeof manualInvoicePdf === 'string') {
      invoice.manualInvoicePdf = manualInvoicePdf;
    }
    await invoice.save();
    return { success: true, invoice };
  }
}

export default new ManuallyOverrideInvoice();
