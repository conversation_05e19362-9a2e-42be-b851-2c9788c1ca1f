import { TE<PERSON><PERSON><PERSON><PERSON><PERSON>, TEts<PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser, TypeAPIHandler } from 'type';
import { checkAdmin, checkAuthen, combineMiddlewares } from '../api-middlewares'
import { DB } from 'db';
import Joi = require('joi');
import Etsy from 'helpers/EtsyHelper';
import { OnlineStoreModel } from 'db/Schema.OnlineStore';

const setupEtsyClient = async (store: OnlineStoreModel) => {
  const { etsyAccessToken, etsyRefreshToken } = store.data || {};
  let needRefreshToken = Date.now() - new Date(store.updatedAt).getTime() > 24 * 60 * 60 * 1000;
  let etsy = new Etsy(etsyAccessToken, etsyRefreshToken);
  if (needRefreshToken) {
    const newTokens = await etsy.getNewToken();
    if (newTokens?.accessToken) {
      store.data = {
        ...store.data,
        etsyAccessToken: newTokens.accessToken,
        etsyRefreshToken: newTokens.refreshToken,
      }
      etsy = new Etsy(newTokens.accessToken, newTokens.refreshToken);
      await store.save();
    }
  }
  return etsy;
}

class GetEtsyReceipts implements TypeAPIHandler {
  url = '/api/order/get-etsy-receipts';
  method = 'GET';
  apiSchema = {
    query: Joi.object({
      storeId: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    checkAuthen,
    checkAdmin,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { storeId } = request.query;

    const store = await DB.OnlineStore.findByPk(storeId);
    if (!store) {
      throw new Error('Store not found');
    }
    const { etsyAccessToken, etsyRefreshToken } = store.data || {};
    if (!etsyAccessToken || !etsyRefreshToken) {
      throw new Error('Etsy access token or refresh token not found');
    }
    const etsy = await setupEtsyClient(store)
    await new Promise(resolve => setTimeout(resolve, 500));
    const shopInfo = await etsy.getMe();
    const shopId = shopInfo?.shop_id;
    if (!shopId) {
      throw new Error('Shop not found');
    }

    const sevenDaysAgo = Math.floor(Date.now() / 1000) - (7 * 24 * 60 * 60);
    const receipts: TEtsyReceipt[] = await etsy.getAllReceipts({ shopId, minCreated: sevenDaysAgo });

    let supportedReceipts = []
    let unsupportedReceipts = []
    for (const receipt of receipts) {
      let failedFlag = false;
      const allListings = await Promise.all(receipt.transactions.map(async (transaction) => {
        try {
          await new Promise(resolve => setTimeout(resolve, 500));
          const listing: TEtsyListing = await etsy.getListing({
            listingId: transaction.listing_id,
            shopId,
          });
          if (!listing) {
            failedFlag = true;
            return;
          }
          if (listing.tags?.some(tag => String(tag).startsWith("d-"))) {
            return listing;
          }
          return listing;
        } catch (error) {
          console.log('getEtsyReceiptsEvery5Min - getListingError', error?.message || JSON.stringify(error));
          failedFlag = true;
          return;
        }
      }));
      if (!failedFlag) {
        if (allListings.filter(Boolean)?.length) {
          supportedReceipts.push({
            ...receipt,
            supportedListings: allListings.filter(Boolean),
          })
        }
      } else {
        unsupportedReceipts.push(receipt)
      }
    }

    return {
      success: true,
      supported: supportedReceipts,
      unsupported: unsupportedReceipts,
    }
  }
}

export default new GetEtsyReceipts();
