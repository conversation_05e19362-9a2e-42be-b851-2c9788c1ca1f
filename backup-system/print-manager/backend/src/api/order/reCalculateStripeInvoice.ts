import { T<PERSON><PERSON><PERSON><PERSON>, TR<PERSON><PERSON><PERSON><PERSON>, TypeAP<PERSON><PERSON><PERSON><PERSON> } from 'type';
import { checkAdmin, checkAuthen, combineMiddlewares } from '../api-middlewares'
import { DB } from 'db';
import Joi = require('joi');
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Var<PERSON><PERSON><PERSON> } from 'helpers';
import Strip<PERSON> from 'stripe';
import { DISCOUNT_RATE, EUROPEAN_COUNTRIES, TAX_ONLY_RATE, shippingPrice, shippingPriceEurope, shippingPriceTheRestOfTheWorld } from 'api/payment/utils';
import Bg from 'api/bg/@utils/Bg';
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, { apiVersion: null });

class ReCalculateStripeInvoice implements TypeAPIHandler {
  url = '/api/order/recalculate-stripe-invoice';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      orderId: Joi.string().required(),
      forceUpdate: Joi.boolean().optional(),
    }),
  }

  preHandler = combineMiddlewares([
    checkA<PERSON>en,
    checkAdmin,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { orderId, forceUpdate } = request.body;
    await Bg.initDB()
    const order = await Bg.Order.findByOrderID(orderId);
    if (!order) {
      return {
        success: false,
        message: 'Order not found',
      }
    }

    let orderWeight = 0;
    const printJobInfo = order['Raw Data']?.line_items.map(v => {
      const printJobId = v.properties.find(i => i.name === "Print Job")?.value;
      const designId = v.properties.find(val => val.name === 'BG Product Number')?.value;
      const productId = v.properties.find(val => val.name === 'BG Product Library')?.value;
      return {
        id: v.id,
        quantity: v.quantity,
        printJobId,
        designId,
        productId,
        name: v.name,
      }
    }).filter(v => !!v.printJobId || !!v.designId || !!v.productId);

    let orderAmount = 0;
    let productsPrice = {};
    let invoiceProducts = [];

    for (let i = 0; i < printJobInfo.length; i++) {
      const { id, quantity, printJobId, designId, productId, name } = printJobInfo[i];
      try {
        const product = await (async () => {
          if (productId) {
            return await DB.Product.findByPk(productId);
          }
          if (printJobId) {
            const printJob = await DB.PrintJob.findByPk(printJobId);
            if (!!printJob) {
              const { productId } = printJob;
              return await DB.Product.findByPk(productId);
            }
          }
          if (designId) {
            const design = await DB.Design.findByPk(designId);
            if (!!design) {
              const { productId } = design;
              return await DB.Product.findByPk(productId);
            }
          }
        })();
        if (!product) continue;
        const discountedPrice = VarHelper.calculateDiscountedPrice({
          price: product.price,
          quantity,
          discountType: product.data?.discountType,
        }, product.packPrices);
        orderAmount += discountedPrice * quantity;
        orderWeight += (product.data?.weight || 1) * quantity;
        productsPrice[String(printJobId || id)] = discountedPrice;
        invoiceProducts.push({
          quantity, printJobId, designId, productId, name, id,
          productPrice: discountedPrice,
          product,
        })
      } catch (err) {
        console.log(`[createInvoice] invoiceProducts_error:`, err);
      }
    }
    const shouldIncludeVAT = order['Raw Data']?.shipping_address?.country === "United Kingdom";
    const amountWithTax = shouldIncludeVAT ? orderAmount * 1.2 : orderAmount;

    let invoice = await DB.Invoice.findOne({
      where: {
        orderId: order['Order ID'],
      }
    });

    if (!invoice?.id) throw new Error('Invoice not found');
    const fullUser = await DB.User.findByPk(order['Client ID']);
    console.log("[createInvoice] resellerStripeId", fullUser?.resellerStripeId);
    if (!fullUser?.resellerStripeId) throw new Error('Reseller Stripe ID not found');
    if (!invoiceProducts?.length) throw new Error('Invoice products not found');

    // Store original values to compare later
    const originalInvoice = {
      total: invoice.total,
      taxes: invoice.taxes,
      prices: { ...invoice.prices },
      data: { ...invoice.data }
    };

    invoice.total = amountWithTax;
    invoice.orderNumber = String(order['Order Number']);
    invoice.taxes = orderAmount * 0.2;
    invoice.prices = productsPrice;
    invoice.data = {
      ...(invoice.data || {}),
      weight: orderWeight,
      isSampleRequest: !!order['Raw Data']?.is_sample_request,
      isWholesale: order['Raw Data'].orderType === 'Wholesale',
      orderType: order['Raw Data'].orderType,
    };
    invoice.store = order['Client Name'];
    invoice.resellerId = order['Client ID'];
    invoice.lineItems = order['Raw Data'].line_items;
    invoice.customerInfo = order['Raw Data'].customer;

    let existedStripeInvoice: Stripe.Invoice;
    if (invoice.data?.stripeInvoiceID) {
      existedStripeInvoice = await stripe.invoices.retrieve(invoice.data?.stripeInvoiceID);
    }
    if (forceUpdate && !existedStripeInvoice) {
      throw new Error('Stripe Invoice not found');
    }

    let existingLines = [];
    let newLines = [];
    if (existedStripeInvoice) {
      // Get existing line items
      existingLines = existedStripeInvoice.lines.data.map(i => ({
        id: i.id,
        name: i.description,
        quantity: i.quantity,
        price: i.price.unit_amount,
      }));

      if (!forceUpdate) {
        // Just log differences if not forcing update
        console.log("[createInvoice] Existing line items:", existingLines);
        console.log("[createInvoice] New products to add:", invoiceProducts);
      } else {
        // Delete all existing line items
        for (const line of existingLines) {
          await stripe.invoiceItems.del(line.id);
          console.warn("trying to delete lines");
        }
      }
    }

    let shouldDiscount = false;
    const itemPrefix = `#${order['Order Number']}`;

    let stripeInvoice: Stripe.Invoice
    // create stripe
    if (!existedStripeInvoice) {
      stripeInvoice = await stripe.invoices.create({
        customer: fullUser.resellerStripeId,
        auto_advance: false,
        metadata: {
          orderId: String(order['Order ID']),
          orderNumber: String(order['Order Number']),
          productName: invoiceProducts.map(i => i.name).join('\n'),
          paymentMethod: "Wallet Credit",
        },
        pending_invoice_items_behavior: "exclude",
      });
    } else {
      stripeInvoice = existedStripeInvoice
    }

    let totalProductsPrice = 0;
    // add items for product
    for (let i = 0; i < invoiceProducts.length; i++) {
      const { name, quantity, productPrice, printJobId, designId, productId, product } = invoiceProducts[i];
      const discountRate = shouldDiscount ? DISCOUNT_RATE : 1;

      if (forceUpdate) {
        const newPrice = await stripe.prices.create({
          currency: 'gbp',
          product_data: {
            name: `${itemPrefix} ${name}`,
          },
          unit_amount: Math.round(Number(productPrice) * discountRate * 100),
        })
        const isDiscountedProduct = !!product.originalPrice && product.originalPrice !== product.price;

        await stripe.invoiceItems.create(VarHelper.removeUndefinedField({
          invoice: stripeInvoice?.id,
          customer: fullUser.resellerStripeId,
          price: newPrice.id,
          quantity: quantity,
          metadata: {
            printJobId, designId, productId,
            ...(isDiscountedProduct ? {
              originalPrice: product.originalPrice,
            } : {})
          }
        }));
      } else {
        newLines.push({
          name: `${itemPrefix} ${name}`,
          quantity,
          price: Math.round(Number(productPrice) * discountRate * 100),
        });
      }
      totalProductsPrice += Math.round(Number(productPrice) * discountRate * 100) * quantity;
    }

    // if (!stripeInvoice) {
    //   // create stripe
    //   stripeInvoice = await stripe.invoices.create({
    //     customer: fullUser.resellerStripeId,
    //     auto_advance: false,
    //     metadata: {
    //       orderId: String(order['Order ID']),
    //       orderNumber: String(order['Order Number']),
    //       productName: invoiceProducts.map(i => i.name).join('\n'),
    //       paymentMethod: "Wallet Credit",
    //     }
    //   });
    // }
    const shippingFee = (() => {
      const address = invoice.shippingAddress;
      if (address?.country === 'United Kingdom') {
        return shippingPrice.RM48 || 0;
      }
      // TODO: handle international shipping fee
      if (EUROPEAN_COUNTRIES.includes(address?.country)) {
        return shippingPriceEurope.RM48 || 0;
      }
      return shippingPriceTheRestOfTheWorld.RM48 || 0;
    })();

    if (forceUpdate) {
      const _shippingPrice = await stripe.prices.create({
        currency: 'gbp',
        product_data: {
          name: itemPrefix + ` Shipping fee`,
        },
        unit_amount: shippingFee * 100,
      });
      await stripe.invoiceItems.create({
        invoice: stripeInvoice.id,
        customer: fullUser.resellerStripeId,
        price: _shippingPrice.id,
        quantity: 1,
      });
    } else {
      newLines.push({
        name: itemPrefix + ` Shipping fee`,
        quantity: 1,
        price: shippingFee * 100,
      });
    }

    // update DB shipping fee
    invoice.data = {
      ...(invoice.data || {}),
      shippingFee: shippingFee,
    }
    if (shouldIncludeVAT) {
      const shippingFeeTax = shippingFee * TAX_ONLY_RATE * 100;
      if (forceUpdate) {
        const _taxPrice = await stripe.prices.create({
          currency: 'gbp',
          product_data: {
            name: itemPrefix + ' VAT (20%)',
          },
          unit_amount: Math.round(totalProductsPrice * TAX_ONLY_RATE + shippingFeeTax),
        });
        await stripe.invoiceItems.create({
          invoice: stripeInvoice.id,
          customer: fullUser.resellerStripeId,
          price: _taxPrice.id,
          quantity: 1,
        });
      } else {
        newLines.push({
          name: itemPrefix + ' VAT (20%)',
          quantity: 1,
          price: Math.round(totalProductsPrice * TAX_ONLY_RATE + shippingFeeTax),
        })
      }
      // update DB invoice taxes
      invoice.taxes = Math.round(totalProductsPrice * TAX_ONLY_RATE + shippingFeeTax) / 100;
    } else {
      // update DB invoice taxes
      invoice.taxes = 0;
    }

    if (forceUpdate) {
      // update DB invoice total
      stripeInvoice = await stripe.invoices.retrieve(stripeInvoice.id);
      invoice.total = stripeInvoice.total / 100;
      console.log("[createInvoice] stripeInvoice.total", stripeInvoice.total);

      const stripeInvoiceID = stripeInvoice.id
      let stripePdfUrl
      if (stripeInvoice.invoice_pdf) {
        const tempFilePath = `/tmp/invoice-${stripeInvoiceID}.pdf`;
        try {
          await FileHelper.downloadFile(stripeInvoice.invoice_pdf, tempFilePath);
          stripePdfUrl = await AWSHelper.upload({
            key: `bg/invoices/${stripeInvoiceID}.pdf`,
            filePath: tempFilePath,
          });
        } catch (error) {
          console.log("[createInvoice] downloadInvoicePdfError", error);
        }
      }

      invoice.data = {
        ...invoice.data,
        invoicePdf: `${process.env.BACKEND_CMS_URL}/api/order/gen-invoice/${order["Order ID"]}`,
        stripeInvoiceUrl: stripePdfUrl,
        stripeInvoiceID,
      }
    }

    if (!forceUpdate) {
      // Return differences without saving
      const differences = {
        total: {
          old: originalInvoice.total,
          new: invoice.total,
          diff: invoice.total - originalInvoice.total
        },
        taxes: {
          old: originalInvoice.taxes,
          new: invoice.taxes,
          diff: invoice.taxes - originalInvoice.taxes
        },
        prices: {
          old: originalInvoice.prices,
          new: invoice.prices
        },
        data: {
          old: originalInvoice.data,
          new: invoice.data
        }
      };

      return {
        success: true,
        differences,
        existingLines,
        newLines,
      };
    }

    await invoice.save();

    return {
      success: true,
      data: invoice,
    }
  }
}

export default new ReCalculateStripeInvoice();
