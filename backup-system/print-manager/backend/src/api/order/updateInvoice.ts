import { TRequestUser, TypeAPIHandler } from 'type';
import { combineMiddlewares } from '../api-middlewares'
import { DB } from 'db';
import Joi = require('joi');

class GetChargeAmountFromLineItems implements TypeAPIHandler {
  url = '/api/order/update-invoice';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      orderId: Joi.string().required(),
      paidAt: Joi.string(),
      fulfilledAt: Joi.string(),
    }),
  }

  preHandler = combineMiddlewares([
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { orderId, paidAt, fulfilledAt } = request.body;

    const invoice = await DB.Invoice.findOne({
      where: {
        orderId,
      }
    });

    if (!invoice?.id) {
      return {
        success: false,
      }
    }
    if (paidAt) invoice.paidAt = paidAt;
    if (fulfilledAt) invoice.fulfilledAt = fulfilledAt;
    await invoice.save();

    return {
      success: true,
      data: invoice,
    }
  }
}

export default new GetChargeAmountFromLineItems();
