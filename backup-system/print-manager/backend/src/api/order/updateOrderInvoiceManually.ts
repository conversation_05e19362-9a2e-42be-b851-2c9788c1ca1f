import { <PERSON>AP<PERSON><PERSON><PERSON><PERSON> } from 'type';
import { validateRequest, combineMiddlewares, checkAuthen, checkAdmin } from '../api-middlewares'
import { DB, InMemory as RedisCache } from 'db';
import Joi = require("joi");
import MailHelper from 'helpers/MailHelper';
import { Op } from 'sequelize';
import { getOrderRejectedNotiEmailHtml } from 'helpers/email-templates';
import { StripeHelper } from 'helpers';

const moment = require('moment');

class UpdateOrderInvoiceManually implements TypeAPIHandler {
  url = '/api/orders/update-invoice-manually';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      id: Joi.string().required(),
      url: Joi.string().allow(''),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
    checkAdmin,
  ]);

  handler = async (request, reply) => {
    const { id, url } = request.body;

    const invoice = await DB.Invoice.findByPk(id);
    invoice.manualInvoicePdf = url;
    await invoice.save();
  
    return {
      success: true,
      data: invoice
    }
  }
}

export default new UpdateOrderInvoiceManually();
