import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'type';
import { validateRequest, combineMiddlewares, checkAuthen } from '../api-middlewares'
import { DB } from 'db';
import Joi = require("joi");
import { Op } from 'sequelize';
import { ERROR } from 'const';
const moment = require('moment');

class OrderInvoices implements TypeAPIHandler {
  url = '/api/order/invoices';
  method = 'GET';
  apiSchema = {
    query: Joi.object({
      month: Joi.string(), // YYYYMM
      resellerId: Joi.string(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request, reply) => {
    const { month, resellerId } = request.query;
    if (!resellerId && request.user.role !== "admin") throw new Error(ERROR.PERMISSION_DENIED);

    const monthMoment = moment(month, "YYYYMM");

    const filterByClient = {};
    if (resellerId) {
      filterByClient["resellerId"] = resellerId;
    }

    const invoices = await DB.Invoice.findAll({
      where: {
        createdAt: {
          [Op.gt]: monthMoment.clone().startOf("month").toDate(),
          [Op.lt]: monthMoment.clone().endOf("month").toDate(),
        },
        ...filterByClient,
      }
    });

    return {
      success: true,
      invoices,
    }
  }
}

export default new OrderInvoices();
