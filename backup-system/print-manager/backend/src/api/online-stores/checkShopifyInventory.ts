import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkAuthen, combineMiddlewares } from "api/api-middlewares";
import { DB } from "db";
import Joi = require("joi");
import ShopifyHelper from "helpers/ShopifyHelper";
import { ERROR } from "const";

class CheckShopifyInventoryAPI implements TypeAPIHandler {
  url = "/api/online-stores/check-shopify-inventory/:id";
  method = "GET";
  apiSchema = {
    params: Joi.object({
      id: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { id } = request.params;

    const design = await DB.Design.findByPk(id);
    if (!design) throw new Error(ERROR.NOT_EXISTED);

    let hasInventoryTracking = false;
    if (design.products) {
      for (const product of design.products) {
        if (!product.storeId) continue;
        const store = await DB.OnlineStore.findByPk(product.storeId);

        if (store.data?.shopifyAccessToken) {
          const res = await ShopifyHelper.getProductInventoryStatus({
            storeUrl: store.url,
            token: store.data?.shopifyAccessToken,
            productId: product.productId,
          });
          if (res.is_inventory_tracked) {
            hasInventoryTracking = true;
            break;
          }
        }
      }
    }

    return {
      success: true,
      hasInventoryTracking,
    }
  };
}

export default new CheckShopifyInventoryAPI();
