import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkAuthen, combineMiddlewares } from "api/api-middlewares";
import { DB } from "db";
import axios from 'axios';
import { Shopify } from 'helpers';
import { checkReseller } from "api/api-middlewares/authen";
import { Op } from "sequelize";
import ShopifyHelper from "helpers/ShopifyHelper";

class ResellerHealthCheck implements TypeAPIHandler {

    url = "/api/online-stores/reseller-health-check";
    method = "GET";

    preHandler = combineMiddlewares([
        checkAuthen,
        checkReseller
    ]);

    handler = async (request: TRequestUser, reply) => {
        const { user } = request;
        // if (user.role !== 'reseller') throw new Error(ERROR.PERMISSION_DENIED);

        const fullUser = await DB.User.findByPk(user.id);

        const clientName = fullUser.accountName || [fullUser.firstName, fullUser.lastName].filter(Boolean).join(' ');

        const stores = await DB.OnlineStore.findAll({
            where: {
                resellerId: user.resellerId || user.id,
                inactive: {
                    [Op.not]: true
                }
            }
        });
        if (stores.length === 0) return { success: true };
        let needAcceptUpdate = false;
        const storeError = []

        for (let i = 0; i < stores.length; i++) {
            const store = stores[i];
            const url = store.url;
            const token = store.data?.shopifyAccessToken;
            if (!token) {
                storeError.push({
                    action: 'SETUP WEBHOOK',
                    error: 'No token',
                    name: store.name,
                    url,
                });
                continue;
            }
            //  SET UP WEBHOOK
            try {
                const axiosRes: any = await axios.request({
                    url: `${url}/admin/api/2023-04/webhooks.json`,
                    method: 'get',
                    headers: {
                        'X-Shopify-Access-Token': token,
                    },
                });
                const webhooks = axiosRes.data.webhooks || [];
                // ORDER WEBHOOK
                await Shopify.upsertOrderWebhook(url, token, [], store, clientName);
                // APP UNINSTALL WEBHOOK
                if (webhooks.filter(val => val.topic === 'apps/uninstalled' && val.address.includes(process.env.BACKEND_CMS_URL)).length === 0) {
                    console.log('create app uninstall webhook');
                    await axios.request({
                        url: `${url}/admin/api/2023-04/webhooks.json`,
                        method: 'post',
                        headers: {
                            'X-Shopify-Access-Token': token,
                            'Content-Type': 'application/json',
                        },
                        data: JSON.stringify({
                            webhook: {
                                "address": `${process.env.BACKEND_CMS_URL}/api/online-stores/${store.id}/uninstall`,
                                "topic": "app/uninstalled",
                                "format": "json"
                            }
                        }),
                    });
                }
            } catch (err) {
                storeError.push({
                    action: 'SETUP WEBHOOK',
                    error: err,
                    name: store.name,
                    url,
                });
            }
            //  SETUP FULFILLMENT SERVICE
            try {
                const apiCall = await Shopify.apiCall(`${url}/admin/api/2023-04/fulfillment_services.json?scope=all`, 'get', token);
                const { fulfillment_services } = apiCall;
                const findBGFulfillment = fulfillment_services.find(v => v.name === "BG Fulfillment");
                if (!findBGFulfillment) {
                    console.log(`${url}/admin/api/2023-04/fulfillment_services.json`, token);
                    const createRes = await Shopify.apiCall(`${url}/admin/api/2023-04/fulfillment_services.json`, 'post', token, {
                        "fulfillment_service": {
                            "name": "BG Fulfillment",
                            "callback_url": `https://${process.env.DEV ? 'bg-dev.' : 'bg-production.'}bottledgoose.co.uk/api/online-stores/shopify-app-fulfillment-service`,
                            "inventory_management": false,
                            "tracking_support": false,
                            "requires_shipping_method": true,
                            "format": "json",
                            "permits_sku_sharing": false,
                            "fulfillment_orders_opt_in": true,
                        }
                    });
                }

            } catch (err) {
                storeError.push({
                    action: 'SETUP FULFILLMENT SERVICE',
                    name: store.name,
                    url,
                    err,
                });
            }

            // CHECK TOKEN SCOPES
            // fulfillment order
            try {
                await ShopifyHelper.apiCall(`${url}/admin/api/2023-04/orders/1111/fulfillment_orders.json`, 'get', token);
            } catch (error) {
                let errorMess = error.response.data.errors;
                if (typeof errorMess !== "string") errorMess = JSON.stringify(errorMess);
                if (errorMess.includes("does not have the required permission")) needAcceptUpdate = true;
            }
        }

        return {
            success: true,
            storeError,
            needAcceptUpdate,
        }
    };
}

export default new ResellerHealthCheck();
