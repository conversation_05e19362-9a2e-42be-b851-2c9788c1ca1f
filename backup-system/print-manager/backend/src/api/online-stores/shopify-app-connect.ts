import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, checkAuthen, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { VarHelper, Shopify } from "helpers";
import Joi = require("joi");
import { ERROR } from "const";
import { checkReseller } from "api/api-middlewares/authen";

class ShopifyAppConnect implements TypeAPIHandler {

  url = "/api/online-stores/shopify-app-connect";
  method = "GET";
  apiSchema = {
    
  }

  preHandler = combineMiddlewares([
    // validateRequest(this.apiSchema),
  ]);

  handler = async (request: TRequestUser, reply) => {
    console.log('HERE');
    const nonce = Shopify.generateNonce();
    console.log('nonce', nonce)
    const uri = Shopify.generateAuthUrl(request.query.shop || 'bottled-goose-dev', nonce);
    reply.redirect(uri);
    return;
  };
}

export default new ShopifyAppConnect();