import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkA<PERSON>en, combineMiddlewares } from "api/api-middlewares";
import { DB } from "db";
import Joi = require("joi");
import ShopifyHelper from "helpers/ShopifyHelper";
import { ERROR } from "const";

class CheckShopifyInventoryAPI implements TypeAPIHandler {
  url = "/api/online-stores/disable-shopify-inventory";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      id: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { id } = request.body;

    const design = await DB.Design.findByPk(id);
    if (!design) throw new Error(ERROR.NOT_EXISTED);

    if (design.products) {
      for (const product of design.products) {
        if (!product.storeId) continue;
        const store = await DB.OnlineStore.findByPk(product.storeId);

        if (store.data?.shopifyAccessToken) {
          const shopifyProduct = await ShopifyHelper.getProductInventoryStatus({
            storeUrl: store.url,
            token: store.data?.shopifyAccessToken,
            productId: product.productId,
          });
          if (!shopifyProduct?.variants?.length) continue;

          for (const variant of shopifyProduct.variants) {
            await ShopifyHelper.disableProductInventoryTracking({
              storeUrl: store.url,
              token: store.data?.shopifyAccessToken,
              productId: product.productId,
              variantId: variant.id,
            });
          }
        }
      }
    }

    return {
      success: true,
    }
  };
}

export default new CheckShopifyInventoryAPI();
