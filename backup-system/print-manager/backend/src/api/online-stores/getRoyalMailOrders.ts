import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON><PERSON>, combineMiddlewares } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON><PERSON> } from "helpers";
import Joi = require("joi");
import axios from 'axios';
import { get, isEqual } from 'lodash';
import { Op } from "sequelize";
const moment = require('moment');

const deleteOldGeneralData = async () => {
  await DB.GeneralData.destroy({
    where: {
      createdAt: {
        [Op.lt]: moment().subtract(30, 'days').toDate(),
      },
      type: 'royal-mail-order',
    },
  });
}

export const fetchRoyalMailOrders = async () => {
  const apiCallRes = await axios.request({
    url: 'https://api.parcel.royalmail.com/api/v1/orders?pageSize=100',
    method: 'get',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer b1eed28a-9beb-4e93-b8c9-5a0083c43bfd',
    },
  })
  const orders = get(apiCallRes, 'data.orders', []);
  await Promise.all(orders.map(async (order) => {
    const existed = await DB.GeneralData.findOne({
      where: {
        type: 'royal-mail-order',
        field1: String(order.orderIdentifier),
      }
    })
    if (existed) {
      if (!isEqual(existed.data, order)) {
        console.log("update RM order");
        await existed.update({
          data: order,
        });
      }
    } else {
      console.log("create RM order");
      await DB.GeneralData.create({
        type: 'royal-mail-order',
        field1: String(order.orderIdentifier),
        field2: String(order.orderReference),
        data: order,
        id: VarHelper.genId(),
        name: 'royal-mail-order',
        userId: '1',
        publicPermission: {
          c: false, r: false, u: false, d: false,
        }
      });
    }
  }));
  await deleteOldGeneralData();
  return orders;
  // {
  //   orderIdentifier: 39781,
  //   orderReference: '82377636',
  //   createdOn: '2024-07-30T16:55:11.6166667',
  //   orderDate: '2024-07-29T07:48:41',
  //   printedOn: '2024-07-31T13:45:35.9979672',
  //   manifestedOn: '2024-07-31T13:49:41.5461937',
  //   shippedOn: '2024-07-31T13:45:35.9979672',
  //   trackingNumber: 'FL055722192GB'
  // },
}

class GetRoyalMailOrdersAPI implements TypeAPIHandler {
  url = "/api/online-stores/get-royal-mail-orders";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      orderId: Joi.any(),
    }),
  }

  preHandler = combineMiddlewares([
    // checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { orderId } = request.body;

    if (orderId) {
      const order = await DB.GeneralData.findOne({
        where: {
          type: 'royal-mail-order',
          [Op.or]: [{
            field1: orderId,
          }, {
            field2: orderId,
          }]
        }
      });
      if (order?.id) {
        return {
          success: true,
          data: order,
        }
      }
    }
    const orders = await fetchRoyalMailOrders();

    return {
      success: true,
      data: orders?.find(i => i.orderIdentifier === orderId || i.orderReference === orderId),
    }
  };
}

export default new GetRoyalMailOrdersAPI();
