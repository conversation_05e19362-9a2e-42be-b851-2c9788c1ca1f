import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkAd<PERSON>, checkA<PERSON><PERSON>, checkAuthenOptional, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");
import { Op } from "sequelize";

const path = require('path'); 
const fs = require('fs'); 

class DispatchToRoyalMail implements TypeAPIHandler {
  url = "/api/online-stores/order-info-from-shopify/:orderId";
  method = "GET";
  apiSchema = {
    params: Joi.object({
      orderId: Joi.string().required(),
    })
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
    checkAdmin,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { orderId } = request.params;

    const log = await DB.GeneralData.findOne({
      where: {
        name: {
          [Op.like]: `shopify-webhook%`,
        },
        data: {
          [Op.like]: `%${orderId}%`,
        }
      },
    })
    if (!log) throw new Error(ERROR.NOT_EXISTED);

    return {
      success: true,
      data: log.data,
    }

  };
}

export default new DispatchToRoyalMail();