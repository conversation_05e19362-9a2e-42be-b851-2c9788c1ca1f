import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkAuthen, combineMiddlewares, validateRequest } from "api/api-middlewares";
import Joi = require("joi");
import axios from 'axios';
import { updateOrderStatus } from "api/bg/services";

const path = require('path');
const fs = require('fs');
const { exec } = require('child_process');

let runCypressTimeout = setTimeout(() => { }, 0)
let lastTimeCrawling = 0

class GetRoyalMailOrderStatus implements TypeAPIHandler {
  url = "/api/online-stores/get-royal-mail-order-status";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      orderIdentifiers: Joi.array().items(Joi.number()),
      trackingNumbers: Joi.array().items(Joi.string()),
      orders: Joi.array().items(Joi.object({
        trackingNumber: Joi.string(),
        orderId: Joi.string(),
        orderIdentifier: Joi.string(),
        status: Joi.string(),
        stageStatus: Joi.string(),
      })),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { orderIdentifiers = [], trackingNumbers = [], orders = [] } = request.body;

    const data: any = {};
    for (let i = 0; i < orderIdentifiers.length; i++) {
      const orderId = orderIdentifiers[i];
      try {
        const apiCallRes = await axios.request({
          url: 'https://api.parcel.royalmail.com/api/v1/orders/' + orderId,
          method: 'get',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer b1eed28a-9beb-4e93-b8c9-5a0083c43bfd',
          },
        })
        data[orderId] = apiCallRes.data;
      } catch (err) {
        console.log(err);
      }
    }

    let rmOrders = [];
    let needCrawling = false;
    if (trackingNumbers.length) {
      try {
        const res = fs.readFileSync(path.join(__dirname, '../../../interceptedRequest.json'), 'utf8');
        const { request } = JSON.parse(res);
        let body = { ...request.body };
        body.paging.pageSize = 500;
        const apiCallRes: any = await axios.request({
          url: request.url,
          method: request.method,
          headers: request.headers,
          data: JSON.stringify(body)
        })
        // rmOrders = orderIdentifiers.map(async orderIdentifier => {
        //   return apiCallRes.data.data.find(rm => String(rm.trackingOrConfirmationNumber) === String(orderIdentifier).trim())
        // })
        trackingNumbers.map(trackingNum => {
          rmOrders.push(apiCallRes.data.data.find(rm => String(rm.trackingOrConfirmationNumber) === String(trackingNum).trim()))
        })
        rmOrders = rmOrders.filter(Boolean)
      } catch (error) {
        const errorMessage = error.response?.data?.message || error?.message || error;
        console.log("getRoyalMailOrderStatus_Error", errorMessage)
        if (new Date().getTime() - lastTimeCrawling > 1000 * 60 * 5) {
          lastTimeCrawling = new Date().getTime()
          clearTimeout(runCypressTimeout)
          runCypressTimeout = setTimeout(() => {
            exec('npx cypress run');
          }, 500)
        }
        needCrawling = true
      }
      if (orders.length) {
        await Promise.all(orders.map(async order => {
          const rmOrder = rmOrders.find(rm => String(rm.trackingOrConfirmationNumber) === String(order.trackingNumber).trim());
          if (rmOrder) {
            const isDelivered = rmOrder?.shippingTrackingStatus === "Delivered";
            if (isDelivered && order.stageStatus !== "Delivered") {
              await updateOrderStatus({
                orderId: order.orderId,
                StageStatus: "Delivered",
              })
            }
          }
        }))
      }
    }

    return {
      success: true,
      data,
      rmOrders,
      needCrawling,
    }
  };
}

export default new GetRoyalMailOrderStatus();
