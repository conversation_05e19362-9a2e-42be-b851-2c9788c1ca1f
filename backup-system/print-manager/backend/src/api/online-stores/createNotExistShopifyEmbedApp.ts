import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON><PERSON>, checkAuthenOptional, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { VarHelper } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");
import { checkReseller } from "api/api-middlewares/authen";
import { Op } from "sequelize";

class CreateNotExistShopifyEmbedApp implements TypeAPIHandler {

  url = "/api/online-stores/upsert-shopify-embed-app";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      name: Joi.string(),
      url: Joi.string(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
    checkReseller
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { body, user } = request;

    const name = body.name;
    const url = !body.url.includes('https://') ? 'https://' + body.url : body.url;


    const find = await DB.OnlineStore.findOne({
      where: {
        type: 'shopify-embed-app',
        resellerId: user.id,
        url,
      }
    });

    console.log('find', name, url, find);

    if (!!find) {
      if (find.name === name) return { success: true };
      find.name = name;
      await find.save();
      return { success: true };
    }

    await DB.OnlineStore.create({
      id: VarHelper.genId(),
      resellerId: user.id,
      url,
      name,
      data: {},
      type: 'shopify-embed-app',
    })

    return {
      success: true,
    }
  };
}

export default new CreateNotExistShopifyEmbedApp();