import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkAuthen, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { ETSY_CLIENT_ID } from "helpers/EtsyHelper";
const CryptoJS = require('crypto-js');

function generateCodeVerifier() {
  const possibleChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
  let codeVerifier = '';
  for (let i = 0; i < 36; i++) {
    const randomIndex = Math.floor(Math.random() * possibleChars.length);
    codeVerifier += possibleChars[randomIndex];
  }
  return codeVerifier;
}

function generateCodeChallenge(codeVerifier) {
  const hash = CryptoJS.SHA256(codeVerifier);
  const base64Hash = hash.toString(CryptoJS.enc.Base64);

  return base64Hash.replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
}

class EtsyRequestConnect implements TypeAPIHandler {

  url = "/api/online-stores/etsy-request-connect";
  method = "GET";
  apiSchema = {

  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const codeVerifier = generateCodeVerifier();
    const codeChallenge = generateCodeChallenge(codeVerifier);

    return {
      success: true,
      data: {
        codeVerifier,
        codeChallenge,
        clientId: ETSY_CLIENT_ID,
      }
    }
  };
}

export default new EtsyRequestConnect();
