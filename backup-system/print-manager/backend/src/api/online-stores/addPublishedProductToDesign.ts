import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON><PERSON>, checkAuthenOptional, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, FileHelper } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");
import { checkReseller } from "api/api-middlewares/authen";
import { Op } from "sequelize";

const path = require('path'); 
const fs = require('fs'); 

class AddPublishedProductToDesign implements TypeAPIHandler {
  url = "/api/online-stores/add-published-product-to-design";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      storeUrl: Joi.string(),
      designId: Joi.string(),
      product: Joi.any(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkA<PERSON>en,
    checkReseller
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { body, user } = request;
    console.log('body', body);
    // if (user.role !== 'reseller') throw new Error(ERROR.PERMISSION_DENIED);

    const design = await DB.Design.findByPk(body.designId);
    if (!design) throw new Error('Design not existed');

    const store = await DB.OnlineStore.findOne({
      where: {
        url: body.storeUrl.includes('https://') ? body.storeUrl : 'https://' + body.storeUrl,
        resellerId: user.id,
        inactive: {
          [Op.not]: true
        }
      }
    })

    if (!store) throw new Error('Store not existed');

    const product = body.product;

    const products = (design.products || []).slice();
    products.push({
      url: `${store.url}/products/${product.handle}`,
      productId: product?.id,
      storeId: store.id,
      productAdminUrl: `${store.url}/admin/products/${product.id}`,
      brand: store.name,
    })

    await design.save();
    return {
      success: true,
      data: design,
    }

  };
}

export default new AddPublishedProductToDesign();
