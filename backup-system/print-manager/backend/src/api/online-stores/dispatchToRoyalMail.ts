import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkAd<PERSON>, check<PERSON><PERSON>en, checkAuthenOptional, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");
import axios from 'axios';

const path = require('path'); 
const fs = require('fs'); 

// API DOC: 

class DispatchToRoyalMail implements TypeAPIHandler {
  url = "/api/online-stores/dispatch-to-royal-mail";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      orderReference: Joi.string(),
      recipient: Joi.object({
        address: Joi.object({
          fullName: Joi.string(),
          addressLine1: Joi.string(),
          addressLine2: Joi.string().allow(''),
          city: Joi.string(),
          county: Joi.string(),
          postcode: Joi.string(),
          countryCode: Joi.string(),
        }),
        phoneNumber: Joi.string().allow(''),
        emailAddress: Joi.string(),
      }),
      // sender: Joi.object({
      //   tradingName: Joi.string(),
      //   phoneNumber: Joi.string(),
      //   emailAddress: Joi.string(),
      // }),
      billing: Joi.object({
        address: Joi.object({
          fullName: Joi.string(),
          addressLine1: Joi.string(),
          addressLine2: Joi.string().allow(''),
          city: Joi.string(),
          county: Joi.string(),
          postcode: Joi.string(),
          countryCode: Joi.string(),
        }),
        phoneNumber: Joi.string().allow(''),
        emailAddress: Joi.string(),
      }),
      packages: Joi.array().items(Joi.object({
        packageFormatIdentifier: Joi.string(),
        contents: Joi.array().items(Joi.object({
          name: Joi.string(),
          SKU: Joi.string(),
          quantity: Joi.number(),
          unitWeightInGrams: Joi.number(),
          unitValue: Joi.number(),
        })),
        weightInGrams: Joi.number(),
      })),
      orderDate: Joi.string(),
      subtotal: Joi.number(),
      shippingCostCharged: Joi.number(),
      total: Joi.number(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
    checkAdmin,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { body } = request;

    const apiCallRes = await axios.request({
      url: 'https://api.parcel.royalmail.com/api/v1/orders',
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer b1eed28a-9beb-4e93-b8c9-5a0083c43bfd',
      },
      data: JSON.stringify({
        items: [body],
      }),
    })    

    return {
      success: true,
      data: apiCallRes.data,
    }

  };
}

export default new DispatchToRoyalMail();