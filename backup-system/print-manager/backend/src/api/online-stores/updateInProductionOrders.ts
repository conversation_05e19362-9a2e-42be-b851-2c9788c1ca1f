import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkA<PERSON>en, combineMiddlewares, validateRequest } from "api/api-middlewares";
import Joi = require("joi");
import { DB } from "db";
import { handleUpdateShopify, handleUpdateEtsy } from "./updateShopifyOrderStatus";
import axios from "axios";
import { updateOrderStatus, updatePipelineShareData } from "api/bg/services";
const moment = require("moment-timezone");

export const handleUpdateInProductionOrders = async (orders) => {
  const updatedOrders = []

  for (let i = 0; i < orders.length; i++) {
    try {
      let order = { ...orders[i] };
      if (order.Pipelines.length === 0) continue;
      const last = order.Pipelines[order.Pipelines.length - 1];
      const royalMailOrderIdentifier = last.SharedData.royalMailOrderIdentifier;
      if (royalMailOrderIdentifier && (!last.SharedData?.trackingNumber || !last.SharedData?.shippedOn)) {
        let royalMailData;
        let updateShareData: any = {};
        const nowStr = moment().toISOString();
        try {
          const apiCallRes = await axios.request({
            url: 'https://api.parcel.royalmail.com/api/v1/orders/' + royalMailOrderIdentifier,
            method: 'get',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer b1eed28a-9beb-4e93-b8c9-5a0083c43bfd',
            },
          })
          royalMailData = apiCallRes.data?.[0];
        } catch (error) {
          console.log("updateInProductionOrders/royalMailData_err", error?.message);
        }
        console.log("updateInProductionOrders/royalMailData", royalMailData);
        if (!!royalMailData?.trackingNumber) {
          const expectedStageStatus = royalMailData.shippedOn ? "Dispatched" : "Manifested";
          updateShareData.trackingNumber = royalMailData.trackingNumber;
          if (royalMailData.shippedOn) {
            updateShareData.shippedOn = royalMailData.shippedOn;
          }
          if (
            order.Status !== "Fulfilled" ||
            order.Stage !== "Fulfillment" ||
            order.StageStatus !== expectedStageStatus
          ) {
            order = {
              ...order,
              Status: "Fulfilled",
              Stage: "Fulfillment",
              StageStatus: expectedStageStatus,
            }
            console.log("updateInProductionOrders/updateOrderStatus", expectedStageStatus);
            await updateOrderStatus({
              id: order.Id,
              Status: "Fulfilled",
              Stage: "Fulfillment",
              StageStatus: expectedStageStatus,
            })
            const invoice = await DB.Invoice.findOne({
              where: {
                orderId: String(order['Order ID']),
              }
            });
            if (invoice?.id && !invoice.fulfilledAt) {
              invoice.fulfilledAt = nowStr;
              await invoice.save();
            }
          }

          if (order["Store ID"] && order.OrderType === "Shopify") {
            try {
              const err = await handleUpdateShopify([{
                lineItems: last.SharedData?.canBeProcessedItems,
                orderId: order['Order Source ID'] || order['Order ID'],
                storeId: order['Store ID'],
                trackingNumber: royalMailData.trackingNumber,
              }])
              console.log("updateInProductionOrders/handleUpdateShopify", order['Order ID'], royalMailData.trackingNumber, err);
              if (!err.length) {
                updateShareData.fulfilledAt = nowStr;
              }
            } catch (error) {
              console.log("updateInProductionOrders/handleUpdateShopify_err", error?.message || error);
            }
          } else if (order["Store ID"] && order.OrderType === "Etsy") {
            try {
              const err = await handleUpdateEtsy([{
                lineItems: last.SharedData?.canBeProcessedItems,
                orderId: order['Order Source ID'] || order['Order ID'],
                storeId: order['Store ID'],
                trackingNumber: royalMailData.trackingNumber,
              }])
              console.log("updateInProductionOrders/handleUpdateShopify", order['Order ID'], royalMailData.trackingNumber, err);
              if (!err.length) {
                updateShareData.fulfilledAt = nowStr;
              }
            } catch (err) {
              console.log("updateInProductionOrders/handleUpdateEtsy_err", err?.message || err);
            }
          }
          if (updateShareData && Object.keys(updateShareData).length) {
            order.Pipelines[order.Pipelines.length - 1] = {
              ...last,
              SharedData: {
                ...(last.SharedData || {}),
                ...updateShareData,
              }
            }
            console.log("updateInProductionOrders/updatePipelineShareData", updateShareData, last.Id)
            await updatePipelineShareData({
              data: updateShareData,
              pipelineId: last.Id,
            })
            updatedOrders.push(order);
          }
        }
      }
    } catch (error) {
      console.log("updateInProductionOrders/error", error?.message || error);
    }
  }
  return updatedOrders;
}

class UpdateInProductionOrder implements TypeAPIHandler {
  url = "/api/online-stores/update-in-production-orders";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      orders: Joi.array().items(Joi.any()),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { orders } = request.body;

    if (!orders?.length) return { success: false };
    const updatedOrders = await handleUpdateInProductionOrders(orders);

    return {
      success: true,
      data: updatedOrders.filter(Boolean),
    }
  };
}

export default new UpdateInProductionOrder();
