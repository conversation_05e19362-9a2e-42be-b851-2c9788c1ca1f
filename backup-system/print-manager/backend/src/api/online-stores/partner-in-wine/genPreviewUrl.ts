
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "helpers";
const puppeteer = require('puppeteer');
import { AlexanderLettering, BigCaslon, PinkChampagne } from './partnerInWineFont';
const fs = require('fs');
import { MATCH_SHOPIFY_PRODUCTS } from './INFO';
var htmlEncode = require('js-htmlencode');

export const genPreviewUrl = async (shopifyProductId, { text, orientation, font, color, productColor }) => {

  const MATCH_SHOPIFY_PRODUCTS_NOW = MATCH_SHOPIFY_PRODUCTS();
  const productColors = Object.keys((MATCH_SHOPIFY_PRODUCTS_NOW)[shopifyProductId].productColors).map(v => MATCH_SHOPIFY_PRODUCTS_NOW[shopifyProductId].productColors[v]);
  const defaultPreviewImage = MATCH_SHOPIFY_PRODUCTS_NOW[shopifyProductId].previewImage;
  const findProductColor = productColors.find(v => v.color === productColor);
  const previewImage = findProductColor?.preview || defaultPreviewImage;

  const data = MATCH_SHOPIFY_PRODUCTS_NOW[shopifyProductId];
  const isLettering = font === 'Alexander Lettering' || font === 'Pink Champagne';
  const style = isLettering ? data.preview?.lettering : data.preview?.initial;
  console.log('style', style);
  console.log('encode text', htmlEncode(text));

  const adjustFontSize = (fontSize) => {
    if (text.length > 5) return fontSize;
    return fontSize * 1.35;
    
    let numberOfCapitalize = 0;
    const reg = new RegExp("[A-Z]");
    for (let i=0; i<text.length; i++) {
      const character = text[i];
      const isValid = reg.test(character)
      if (isValid) {
        numberOfCapitalize += 1;
      }
    }
    if (numberOfCapitalize / text.length <= 0.7) return fontSize;
    return fontSize * 0.7;
  }

  const hasLineBreak = text.includes('/');

  const fileName = 'tempSVGFile-' + new Date().getTime();
  const tempPNGFile = fileName + '.png';
  const convertToPNG = async () => {
    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox'],
    });
    const page = await browser.newPage();
    await page.setDefaultNavigationTimeout(0);
    const adjustedFontSize = adjustFontSize(style?.fontSize);
    const sizeForMultipleRowsRatio = data.sizeForMultipleRowsRatio;
    const html = `
      <html>
        <head>
          <style>
            html, body {
              background-color: transparent;
              margin: 0;
              padding: 0;
            }
            ${
              font === 'Alexander Lettering' ? AlexanderLettering :
              (font === 'Pink Champagne' ? PinkChampagne : BigCaslon)
            }
            #preview-customized {
              width: 1125px;
              height: 1125px;
              position: relative;
            }
            #preview-customized-inner {
              position: absolute;
              top: 0;
              left: 0;
              bottom: 0;
              right: 0;
            }
            .customized-content-area {
              position: absolute;
              top: 0;
              left: 0;
              bottom: 0;
              right: 0;
              display: flex;
              justify-content: center;
              align-items: ${shopifyProductId === 6834889883745 ? 'center' : orientation === 'horizontal' ? 'flex-end' : 'center'};
            }
            .customized-content-area.c6816438288481 {
              width: 17.866%;
              height: 47.377%;
              top: 43.6%;
              left: 41.11%;
            }
            .customized-content-area.c6834889883745 {
              width: 31.66%;
              height: 44.99%;
              top: 33.72%;
              left: 33.34%;
            }
            .customized-content-area.c6838215999585 {
              width: 22.57%;
              height: 46.27%;
              top: 37.97%;
              left: 38.65%;
            }
            .customized-content-area pre {
              display: block;
              letter-spacing: 0.2px;
              white-space: nowrap;
              padding-bottom: 15px;
              color: ${color};
              font-family: ${font};
              ${!hasLineBreak ? `font-size: ${adjustedFontSize}px;` : `font-size: ${adjustedFontSize * sizeForMultipleRowsRatio[0]}px;`}
              ${!hasLineBreak ? '' : `line-height: ${adjustedFontSize * sizeForMultipleRowsRatio[1]}px;`}
              transform: ${orientation === 'vertical' && shopifyProductId !== 6834889883745 ? 'rotate(270deg)' : 'none'};
              text-align: center;
            }

          </style>
        </head>
        <body>
          <div id="preview-customized">
            <div id="preview-customized-inner">
              <img src="${previewImage}" style="width: 100%; height: 100%;" />
              <div class="customized-content-area c${shopifyProductId}">
                <pre>${text.replace(/\//, '<br />')}</pre>
              </div>
            </div>
          </div>
        </body>
      </html>
    `;
    await page.setContent(html);
    await page.setViewport({ width: 1125, height: 1125 });
    // await page.waitForNavigation({waitUntil: 'networkidle2'});
    await page.screenshot({ path: tempPNGFile, omitBackground: true });
    await browser.close();
    
  }
  const toBeSavedFileNameInS3 = `preview-for-product-${shopifyProductId}-${new Date().getTime()}.png`;
  await convertToPNG();
  const s3Url = await AWSHelper.upload({
    key: `print-prepare/${toBeSavedFileNameInS3}`,
    filePath: tempPNGFile,
  });
  return s3Url;
};