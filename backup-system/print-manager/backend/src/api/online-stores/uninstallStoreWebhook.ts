import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON><PERSON>, checkAuthenOptional, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, FileHelper, Shopify } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");
import axios from 'axios';
import { checkReseller } from "api/api-middlewares/authen";
import { Op } from "sequelize";

const path = require('path'); 
const fs = require('fs'); 

class PublishProduct implements TypeAPIHandler {
  url = "/api/online-stores/:store_id/uninstall";
  method = "POST";

  preHandler = combineMiddlewares([
    
  ]);

  handler = async (request: TRequestUser, reply) => {
    const storeId = request.params.store_id;
    console.log('storeId', storeId);
    console.log('BODY API /api/online-stores/:store_id/uninstall', request.body);

    const store = await DB.OnlineStore.findByPk(storeId);
    if (!store) return {};
    console.log('store.url', store.url);
    if (store.url === 'https://' + request.body.domain) {
      console.log('inactive store: ' + storeId );
      store.inactive = true;
      await store.save();
    }
    return {
      success: true,
    }

  };
}

export default new PublishProduct();