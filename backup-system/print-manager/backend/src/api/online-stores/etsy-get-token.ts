import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, checkAuthen, validateRe<PERSON> } from "api/api-middlewares";
import { DB } from "db";
import { <PERSON>ar<PERSON><PERSON><PERSON> } from "helpers";
import Joi = require("joi");
import <PERSON>tsyHelper from "helpers/EtsyHelper";

class EtsyAppConnectCallback implements TypeAPIHandler {

  url = "/api/online-stores/etsy-get-token";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      store_name: Joi.string(),
      code: Joi.string(),
      code_verifier: Joi.string(),
      redirect_uri: Joi.string(),
    })
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { code, code_verifier, redirect_uri, store_name } = request.body;
    const user = request.user;

    let tokenRes;
    let errMessage;
    try {
      tokenRes = await (new EtsyHelper('', '')).getStoreTokenFromCode({
        code,
        code_verifier,
        redirect_uri,
      });
    } catch (error) {
      errMessage = error.response?.data?.error_description || error.message || JSON.stringify(error);
    }
    if (errMessage) {
      return {
        success: false,
        error: errMessage,
      }
    }

    const accessToken = tokenRes?.access_token;
    const refreshToken = tokenRes?.refresh_token;
    if (!accessToken || !refreshToken) {
      return {
        success: false,
        error: 'Cannot get account info',
      }
    }
    const etsy = new EtsyHelper(accessToken, refreshToken);
    let shopInfo;
    try {
      shopInfo = await etsy.getShop();
    } catch (error) {
    }

    if (!shopInfo?.url) {
      return {
        success: false,
        error: 'Cannot get shop info',
      }
    }

    const existed = await DB.OnlineStore.findOne({
      where: {
        type: 'etsy',
        url: shopInfo.url,
      }
    })
    if (!existed) {
      const data = await DB.OnlineStore.create({
        id: VarHelper.genId(),
        name: store_name || shopInfo.shop_name,
        url: shopInfo.url,
        type: 'etsy',
        data: {
          etsyAccessToken: accessToken,
          etsyRefreshToken: refreshToken,
        },
        resellerId: user.resellerId || user.id,
      });
      return {
        success: true,
        data: {
          id: data.id,
        }
      }
    }
    return {
      success: true,
      data: {
        id: existed.id,
      }
    }
  };
}

export default new EtsyAppConnectCallback();
