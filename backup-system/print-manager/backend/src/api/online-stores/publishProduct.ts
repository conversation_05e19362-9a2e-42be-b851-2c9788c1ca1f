import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON><PERSON>, checkAuthenOptional, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, FileHelper, Shopify } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");
import axios from 'axios';
import { checkReseller } from "api/api-middlewares/authen";
import { Op } from "sequelize";
import { OnlineStoreModel } from "db/Schema.OnlineStore";
import { ProductModel } from "db/Schema.Product";
import { DesignModel } from "db/Schema.Design";
import Etsy from "helpers/EtsyHelper";

const etsyHandler = async ({ store, product, design, body }: {
  store: OnlineStoreModel,
  product: ProductModel,
  design: DesignModel,
  body: any,
}) => {
  const etsy = new Etsy(store.data?.etsyAccessToken, store.data?.etsyRefreshToken);
  const listingId = design.products.find(v => v.storeId === store.id)?.productId;
  if (!listingId) {
    const { shop_id: shopId } = await etsy.getMe() || {};
    const data = await etsy.createDraftListing({
      shopId,
      name: body.name,
      description: design.description,
      price: body?.variants?.[0]?.price || body?.price,
      sku: `d-${design.id}`,
    });
    if (data?.listing_id && design.image) {
      if (design.galleries?.length) {
        for (let i = 0; i < design.galleries.length; i++) {
          await etsy.uploadListingImage({
            url: design.galleries[i],
            id: data.listing_id,
            shopId,
            rank: 2 + i,
          })
        }
        // can not use Promise.all here because etsy api limit
        // await Promise.all(design.galleries.map(img =>
        //   etsy.uploadListingImage({
        //     url: img,
        //     id: data.listing_id,
        //     shopId,
        //   })
        // ))
      }
      await etsy.uploadListingImage({
        url: design.image,
        id: data.listing_id,
        shopId,
        rank: 1,
      })
    }
    const shouldPublish = false;
    let publishError;
    if (shouldPublish) {
      try {
        etsy.updateListing({
          id: data.listing_id,
          shopId,
          data: {
            state: "active"
          }
        })
      } catch(err) {
        console.log(err);
        publishError = err;
      }
      
    }
    return {
      ...data,
      productId: data.listing_id,
      publishError,
    };
  }
}

class PublishProduct implements TypeAPIHandler {
  url = "/api/online-stores/publish-product";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      productId: Joi.string(),
      name: Joi.string(),
      type: Joi.string(),
      designId: Joi.string(),
      updateExisting: Joi.boolean(),
      storeIds: Joi.array().items(Joi.string()),
      price: Joi.number(),
      cost: Joi.number(),
      variants: Joi.array().items(Joi.object({
        style: Joi.string(),
        variantDesignId: Joi.string(),
        image: Joi.string(),
        galleries: Joi.array().items(Joi.string()),
        price: Joi.number(),
      })),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
    checkReseller
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { body, user } = request;
    console.log('body', body);
    // if (user.role !== 'reseller') throw new Error(ERROR.PERMISSION_DENIED);

    const design = await DB.Design.findByPk(body.designId);
    if (!design) throw new Error('Design not existed');

    if (!design.productId) throw new Error('Design has not been attached to a product library');
    const product = await DB.Product.findByPk(design.productId);
    if (!product) throw new Error('Product library not existed');

    const productDescription = design.description;

    const stores = await DB.OnlineStore.findAll({
      where: {
        resellerId: user.resellerId || user.id,
        inactive: {
          [Op.not]: true
        }
      }
    });
    if (stores.length === 0) {
      throw new Error('Store not found. Please connect your store');
    }

    // console.log('stores', stores);

    const envTag = !process.env.BACKEND_CMS_URL ? '' : (
      process.env.BACKEND_CMS_URL.includes('dev.bg-production') ? '-dev' : ''
    );

    let productResults = [];
    let inDraft = [];

    for (let i = 0; i < stores.length; i++) {
      const store = stores[i];
      if (!body.storeIds.includes(store.id)) continue;
      if (store.type === 'etsy') {
        if (!store.data?.etsyAccessToken || !store.data?.etsyRefreshToken) {
          continue;
        }
        const newListing = await etsyHandler({
          store, product, design, body
        });
        productResults.push({
          url: newListing?.url,
          productId: newListing?.listing_id,
          storeId: store.id,
          productAdminUrl: `https://www.etsy.com/your/shops/me/listing-editor/edit/${newListing?.listing_id}`,
          brand: store.name,
          matchDesignId: {
            [String(newListing?.listing_id)]: body.designId,
          },
          matchImageId: {},
        });
        if (newListing?.publishError) {
          inDraft.push({
            storeId: store.id,
            storeName: store.name,
            error: newListing?.publishError,
          })
        }
        continue;
      }
      if (store.type !== 'shopify') continue;
      const url = store.url;
      const token = store.data?.shopifyAccessToken;

      const existingProducts = !body.updateExisting ? undefined : design.products.find(v => v.storeId === store.id);
      if (!!existingProducts) {
        // UPDATE PREVIOUSLY PUBLISHED PRODUCT
        continue;
      } else {
        // PUBLISH NEW PRODUCT
        const singlePrice = body?.variants?.[0]?.price || body?.price;
        const payload = {
          product: {
            title: body.name,
            body_html: productDescription,
            product_type: body.type === 'finished' ? 'Bottled Goose' : 'Bottled Goose Customizable',
            published: true,
            status: 'active',
            published_scope: 'global',
            tags: [
              `d${envTag}-${body.designId}`
            ],
            variants: [
              {
                option1: !body.variants || body.variants.length === 0 ? undefined : body.variants[0].style,
                price: String((Number(singlePrice) || 0).toFixed(2)), sku: `d${envTag}-${body.designId}`,
                fulfillment_service: 'bg-fulfillment',
              }
            ]
          }
        }
        console.log(JSON.stringify(payload));
        let apiCallData;
        let errorMessage;
        try {
          await Shopify.checkFulfillmentService(url, token);
          apiCallData = await Shopify.apiCall(`${url}/admin/api/2023-04/products.json`, 'post', token, payload);
        } catch (error) {
          console.error('publish Shopify product error', error.response.data.errors);
          if (error.response.data.errors) {
            errorMessage = typeof error.response.data.errors === 'string' ? error.response.data.errors : JSON.stringify(error.response.data.errors);
          }
        }
        if (!apiCallData) throw new Error(errorMessage || 'Failed to publish product');
        let product = apiCallData.product;
        // console.log('product', product);
        console.log('product variant id', product.variants?.[0]?.id);
        console.log('product', product.id);
        const newIndex = productResults.length;
        if (!!product?.id) {
          productResults[newIndex] = {
            url: `${store.url}/products/${product.handle}`,
            productId: product?.id,
            storeId: store.id,
            productAdminUrl: `${store.url}/admin/products/${product.id}`,
            brand: store.name,
            matchDesignId: {
              [String(product.variants?.[0]?.id)]: body.designId,
            },
            matchImageId: {},
          };
          try {
            const { image } = await Shopify.uploadImageToShopifyStore(design.image, { storeUrl: url, token, productId: product.id });
            // update image id for variant 0
            if (product.variants?.[0]?.id) {
              await Shopify.apiCall(
                `${store.url}/admin/api/2023-04/products/${product.id}/variants/${product.variants?.[0]?.id}.json`,
                'put',
                store.data?.shopifyAccessToken,
                {
                  variant: {
                    sku: `d${envTag}-${design.id}-${design.id}`,
                    image_id: image.id,
                    inventory_management: null,
                  }
                }
              );
            }
            productResults[newIndex].matchImageId[image.id] = design.image;
            productResults[newIndex].matchDesignId[String(product.variants?.[0]?.id)] = body.designId;
            if (design.galleries?.length) {
              await Promise.all(design.galleries.map(async img => {
                const { image } = await Shopify.uploadImageToShopifyStore(img, { storeUrl: url, token, productId: product.id });
                productResults[newIndex].matchImageId[image.id] = img;
              }))
            }
          } catch (err) {
            console.log('err', err);
          }
        }

        if (body.variants?.length) {
          // publish new variants
          for (let j = 1; j < body.variants.length; j++) {
            const variant = body.variants[j];
            const { image } = await Shopify.uploadImageToShopifyStore(variant.image, { storeUrl: url, token, productId: product.id });
            const variantPayload = {
              variant: {
                option1: variant.style,
                price: String((Number(variant.price) || 0).toFixed(2)),
                sku: `d${envTag}-${body.designId}-${variant.variantDesignId}`,
                image_id: image?.id || null,
                inventory_management: null,
              }
            }
            const variantApiCallData = await Shopify.apiCall(`${url}/admin/api/2023-04/products/${product.id}/variants.json`, 'post', token, variantPayload);
            console.log('variantApiCallData', variantApiCallData);
            productResults[newIndex].matchImageId[image.id] = variant.image;
            productResults[newIndex].matchDesignId[String(variantApiCallData.variant.id)] = String(variant.variantDesignId);

            if (variant.galleries?.length) {
              await Promise.all(variant.galleries.map(async img => {
                await Shopify.uploadImageToShopifyStore(img, { storeUrl: url, token, productId: product.id, variantId: variantApiCallData.variant.id });
              }))
            }
          }
        }

        // refetch product from id to get latest data
        const productApiCallData = await Shopify.apiCall(`${url}/admin/api/2023-04/products/${product.id}.json`, 'get', token);
        product = productApiCallData.product;
        if (product.variants) {
          for (let j = 0; j < product.variants.length; j++) {
            const inventoryId = product?.variants?.[j]?.inventory_item_id;
            const fullfillmentApiCall = await Shopify.apiCall(`${url}/admin/api/2023-04/fulfillment_services.json?scope=all`, 'get', token);
            const findBGFulfillment = fullfillmentApiCall.fulfillment_services.find(v => v.name === "BG Fulfillment");
            console.log('findBGFulfillment', findBGFulfillment);
            if (inventoryId) {
              await Shopify.apiCall(`${url}/admin/api/2023-04/inventory_items/${inventoryId}.json`, 'put', token, {
                "inventory_item": {
                  "id": inventoryId,
                  "cost": String((Number(body?.cost) || 0).toFixed(2)),
                  "tracked": false,
                }
              });
            }
          }
        }
      }
    }

    const designProducts = [...design.products];
    productResults.forEach(p => {
      const findExisting = designProducts.find(d => d.storeId === p.storeId && d.productId === p.productId);
      if (!findExisting) {
        designProducts.push(p);
      }
    })
    design.products = designProducts;
    await design.save();
    // @ts-ignore
    design.inDraft = inDraft;
    return {
      success: true,
      data: design,
    }

  };
}

export default new PublishProduct();
