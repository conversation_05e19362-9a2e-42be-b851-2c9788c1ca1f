import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON><PERSON>, checkAuthenOptional, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, FileHelper, Shopify } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");
import axios from 'axios';
import { checkReseller } from "api/api-middlewares/authen";
import { Op } from "sequelize";

const path = require('path'); 
const fs = require('fs'); 

// const uploadImageToShopifyStore = async (imageUrl, { storeUrl, token, productId }) => {
//   const randomName = `p-${productId}-${path.basename(imageUrl)}`;
//   await FileHelper.downloadFile(imageUrl, randomName);
//   const base64 = FileHelper.fileToBase64(randomName);
//   fs.unlink(randomName, () => {});
//   const res = await axios.request({
//     url: `${storeUrl}/admin/api/2022-07/products/${productId}/images.json`,
//     method: 'post',
//     headers: {
//       'Content-Type': 'application/json',
//       'X-Shopify-Access-Token': token,
//     },
//     data: JSON.stringify({
//       image: {
//         attachment: base64,
//         filename: randomName,
//       }
//     })
//   });
//   console.log('uploadImageToShopifyStore', res.data);
// }

class PublishProduct implements TypeAPIHandler {
  url = "/api/online-stores/publish-product";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      productId: Joi.string(),
      name: Joi.string(),
      type: Joi.string(),
      designId: Joi.string(),
      updateExisting: Joi.boolean(),
      storeIds: Joi.array().items(Joi.string()),
      price: Joi.number(),
      cost: Joi.number(),
      variants: Joi.array().items(Joi.object({
        style: Joi.string(),
        variantDesignId: Joi.string(),
        image: Joi.string(),
        galleries: Joi.array().items(Joi.string()),
        price: Joi.number(),
      })),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
    checkReseller
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { body, user } = request;
    console.log('body', body);
    // if (user.role !== 'reseller') throw new Error(ERROR.PERMISSION_DENIED);

    const design = await DB.Design.findByPk(body.designId);
    if (!design) throw new Error('Design not existed');

    if (!design.productId) throw new Error('Design has not been attached to a product library');
    const product = await DB.Product.findByPk(design.productId);
    if (!product) throw new Error('Product library not existed');

    const productDescription = design.description;

    const stores = await DB.OnlineStore.findAll({
      where: {
        resellerId: user.resellerId || user.id,
        inactive: {
          [Op.not]: true
        }
      }
    });
    if (stores.length === 0) {
      throw new Error('Store not found. Please connect your store');
    }

    // console.log('stores', stores);
    
    const envTag = !process.env.BACKEND_CMS_URL ? '' : (
      process.env.BACKEND_CMS_URL.includes('dev.bg-production') ? '-dev' : ''
    );

    let productResults = [];

    for (let i = 0; i < stores.length; i++) {
      const store = stores[i];
      if (!body.storeIds.includes(store.id)) continue;
      if (store.type !== 'shopify') continue;
      const url = store.url;
      const token = store.data?.shopifyAccessToken;

      const existingProducts = !body.updateExisting ? undefined : design.products.find(v => v.storeId === store.id);

      if (!!existingProducts) {
        // UPDATE PREVIOUSLY PUBLISHED PRODUCT
        const productId = existingProducts.productId;
        try {
          const axiosRes: any = await axios.request({
            url: `${url}/admin/api/2023-04/products/${productId}.json`,
            method: 'put',
            headers: {
              'Content-Type': 'application/json',
              'X-Shopify-Access-Token': token,
            },
            data: JSON.stringify({
              product: {
                title: body.name,
                product_type: body.type === 'finished' ? 'Bottled Goose' : 'Bottled Goose Customizable',
                published: true,
                published_scope: 'global',
                status: 'active',
                tags: [
                  `d${envTag}-${body.designId}`
                ],
              }
            })
          });
          const { product } = axiosRes.data;
          
        } catch(err) {
          continue;
        }
      } else {
        // PUBLISH NEW PRODUCT
        const singlePrice = body?.variants?.[0]?.price || body?.price;
        const payload = {
          product: {
            title: body.name,
            body_html: productDescription,
            product_type: body.type === 'finished' ? 'Bottled Goose' : 'Bottled Goose Customizable',
            published: true,
            status: 'active',
            published_scope: 'global',
            tags: [
              `d${envTag}-${body.designId}`
            ],
            variants: [
              {
                option1: !body.variants || body.variants.length === 0 ? undefined : body.variants[0].style,
                price: String((Number(singlePrice) || 0).toFixed(2)), sku: `d${envTag}-${body.designId}`,
                fulfillment_service: 'bg-fulfillment',
              }
            ]
          }
        }
        console.log(JSON.stringify(payload));
        // const axiosRes: any = await axios.request({
        //   url: `${url}/admin/api/2023-04/products.json`,
        //   method: 'post',
        //   headers: {
        //     'Content-Type': 'application/json',
        //     'X-Shopify-Access-Token': token,
        //   },
        //   data: JSON.stringify()
        // });
        const apiCallData = await Shopify.apiCall(`${url}/admin/api/2023-04/products.json`, 'post', token, payload);
        let product = apiCallData.product;
        // console.log('product', product);
        console.log('product variant id', product.variants?.[0]?.id);
        console.log('product', product.id);
        const newIndex = design.products.length;
        if (!!product?.id) {
          productResults[newIndex] = {
            url: `${store.url}/products/${product.handle}`,
            productId: product?.id,
            storeId: store.id,
            productAdminUrl: `${store.url}/admin/products/${product.id}`,
            brand: store.name,
            matchDesignId: {
              [body.designId]: String(product.variants?.[0]?.id),
            },
            matchImageId: {},
          };
          try {
            const { image } = await Shopify.uploadImageToShopifyStore(design.image, { storeUrl: url, token, productId: product.id });
            productResults[newIndex].matchImageId[image.id] = design.image;
            productResults[newIndex]
            if (design.galleries?.length) {
              await Promise.all(design.galleries.map(async img => {
                const { image } = await Shopify.uploadImageToShopifyStore(img, { storeUrl: url, token, productId: product.id });
                productResults[newIndex].matchImageId[image.id] = img;
              }))
            }
            
          } catch(err) {
          }
        }

        // publish new variants
        for (let j = 1; j < body.variants.length; j++) {
          const variant = body.variants[j];
          const { image } = await Shopify.uploadImageToShopifyStore(variant.image, { storeUrl: url, token, productId: product.id });
          const variantPayload = {
            variant: {
              option1: variant.style,
              price: String((Number(variant.price) || 0).toFixed(2)),
              sku: `d${envTag}-${body.designId}-${variant.variantDesignId}`,
              image_id: image?.id || null,
            }
          }
          const variantApiCallData = await Shopify.apiCall(`${url}/admin/api/2023-04/products/${product.id}/variants.json`, 'post', token, variantPayload);
          console.log('variantApiCallData', variantApiCallData);
          productResults[newIndex].matchImageId[image.id] = variant.image;
          productResults[newIndex].matchDesignId[variant.variantDesignId] = String(variantApiCallData.variant.id);
        }

        // refetch product from id to get latest data
        const productApiCallData = await Shopify.apiCall(`${url}/admin/api/2023-04/products/${product.id}.json`, 'get', token);
        product = productApiCallData.product;

        for (let j = 0; j < product.variants.length; j++) {
          const inventoryId = product?.variants?.[i]?.inventory_item_id;
          console.log('inventoryId', inventoryId);
          const fullfillmentApiCall = await Shopify.apiCall(`${url}/admin/api/2023-04/fulfillment_services.json?scope=all`, 'get', token);
          const findBGFulfillment = fullfillmentApiCall.fulfillment_services.find(v => v.name === "BG Fulfillment");
          console.log('findBGFulfillment', findBGFulfillment);
          if (inventoryId) {
            await Shopify.apiCall(`${url}/admin/api/2023-04/inventory_items/${inventoryId}.json`, 'put', token, {
              "inventory_item":{
                "id": inventoryId,
                "cost": String((Number(body?.cost) || 0).toFixed(2)),
              }
            });
          }
        }
      }
    }

    const designProducts = design.products.slice();
    productResults.forEach(p => {
      const findExisting = designProducts.find(d => d.storeId === p.storeId && d.productId === p.productId);
      if (!findExisting) {
        designProducts.push(p);
      }
    })
    design.products = designProducts;

    await design.save();
    return {
      success: true,
      data: design,
    }

  };
}

export default new PublishProduct();
