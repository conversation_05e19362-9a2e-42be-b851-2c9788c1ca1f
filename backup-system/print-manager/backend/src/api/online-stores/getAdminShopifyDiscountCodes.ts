import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON>en, combineMiddlewares } from "api/api-middlewares";
import axios from 'axios';

class GetAdminShopifyDiscountCodes implements TypeAPIHandler {
  url = "/api/online-stores/admin-shopify-discount-code";
  method = "GET";

  preHandler = combineMiddlewares([
    check<PERSON><PERSON><PERSON>,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const token = process.env.SHOPIFY_ACCESS_TOKEN;
    const shopifyDomain = "https://www.bottledgoose.co.uk";
  
    const priceRuleRes : any = await axios.request({
      url: `${shopifyDomain}/admin/api/2023-10/price_rules.json`,
      method: 'get',
      headers: {
        'X-Shopify-Access-Token': token,
      }
    });
    const priceRule = priceRuleRes.data?.price_rules?.[0]?.id;
    if (!priceRule) {
      return {
        success: true,
      }
    }

    const apiCall : any = await axios.request({
      url: `${shopifyDomain}/admin/api/2023-10/price_rules/${priceRule}/discount_codes.json`,
      method: 'get',
      headers: {
        'X-Shopify-Access-Token': token,
      }
    });

    return {
      success: true,
      data: apiCall.data?.discount_codes?.[0],
    }

  };
}

export default new GetAdminShopifyDiscountCodes();
