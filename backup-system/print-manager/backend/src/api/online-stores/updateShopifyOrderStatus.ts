import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON>en, combineMiddlewares, validateRequest } from "api/api-middlewares";
import Joi = require("joi");
import { DB } from "db";
import { Shopify } from "helpers";
import Strip<PERSON> from 'stripe';
import Mail<PERSON>elper from "helpers/MailHelper";
import EtsyHelper from "helpers/EtsyHelper";
import { getDispatchNotiEmailHtml } from "helpers/email-templates";
import { Op } from "sequelize";
import { UserModel } from "db/Schema.User";
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, { apiVersion: null });

export const handleUpdateShopify = async (orders) => {
  let error = [];
  for (let i = 0; i < orders.length; i++) {
    const order = orders[i];
    if (!order.storeId || !order.trackingNumber || !order.orderId || !order.lineItems?.length) continue;
    try {
      const store = await DB.OnlineStore.findOne({
        where: {
          id: order.storeId
        }
      });
      console.log("updateShopifyOrderStatus_storeId", order.storeId);
      if (!store) throw new Error(`Store with id ${order.storeId} not found`);
      console.log("updateShopifyOrderStatus_store", store);
      console.log("updateShopifyOrderStatus_params", {
        storeUrl: store.url,
        token: store.data?.shopifyAccessToken,
        orderId: order.orderId,
        lineIds: order.lineItems.map(i => i.id),
        trackingNumber: order.trackingNumber,
      });
      await Shopify.markOrderAsFulfilled({
        storeUrl: store.url,
        token: store.data?.shopifyAccessToken,
        orderId: order.orderId,
        lineIds: order.lineItems.map(i => i.id),
        trackingNumber: order.trackingNumber,
      });
      // send notice email
      const invoice = await DB.Invoice.findOne({
        where: {
          orderId: order.orderId,
        }
      })
      let stripeInvoice: Stripe.Invoice
      if (invoice.data?.stripeInvoiceID) {
        try {
          stripeInvoice = await stripe.invoices.retrieve(invoice.data?.stripeInvoiceID);
        } catch (error) {
          console.log("send dispatch notice email err", error);
        }
      }
      if (stripeInvoice && invoice.customerInfo?.email) {
        const resellerUser = await DB.User.findOne({
          where: {
            id: store.resellerId,
          }
        });
        if (resellerUser) {
          const setting = await DB.GeneralData.findOne({
            where: {
              type: 'reseller-settings-emails',
              field1: resellerUser.id,
              name: 'Dispatch Notification',
            }
          });
          const shouldSendEmail = setting?.field2 !== 'false';
          if (shouldSendEmail) {
            await MailHelper.sendSMTPEmail({
              to: resellerUser.email,
              subject: "Your customer's order is on its way",
              html: getDispatchNotiEmailHtml({
                customerName: invoice.customerInfo?.first_name,
                message: `Great news! Your customer's order is on its way.<br />The order <strong>${order.orderId}</strong> has successfully shipped. You can click on the button below to track your shipment.`,
                link: `https://www.royalmail.com/track-your-item#/tracking-results/${order.trackingNumber}`,
                items: (invoice.lineItems || []).map(i => ({
                  name: i.name,
                  quantity: i.quantity,
                })),
                resellerName: store.name || resellerUser.accountName || [resellerUser.firstName, resellerUser.lastName].filter(Boolean).join(' '),
              }),
            });
          }
        }
      }
      // end - send notice email
    } catch (err) {
      let errMessage = (err?.response?.data?.errors || []).join(", ");
      if (!errMessage) errMessage = err?.message;
      if (!errMessage) errMessage = JSON.stringify(err);
      error.push(errMessage);
    }
  }
  return error;
}

export const handleUpdateEtsy = async (orders) => {
  let error = [];
  for (let i = 0; i < orders.length; i++) {
    const order = orders[i];
    if (!order.storeId || !order.trackingNumber || !order.orderId || !order.lineItems?.length) continue;
    try {
      const store = await DB.OnlineStore.findOne({
        where: {
          id: order.storeId
        }
      });
      console.log("updateEtsyOrderStatus_storeId", order.storeId);
      if (!store) throw new Error(`Store with id ${order.storeId} not found`);
      console.log("updateEtsyOrderStatus_store", store);
      console.log("updateEtsyOrderStatus_params", {
        storeUrl: store.url,
        orderId: order.orderId,
        lineIds: order.lineItems.map(i => i.id),
        trackingNumber: order.trackingNumber,
      });
      const Etsy = new EtsyHelper(store.data?.etsyAccessToken, store.data?.etsyRefreshToken);
      await Etsy.createReceiptShipment({
        receiptId: order.orderId,
        trackingNumber: order.trackingNumber,
      });
      await Etsy.updateReceipt({
        receiptId: order.orderId,
        was_shipped: true,
      });
      // send notice email
      const invoice = await DB.Invoice.findOne({
        where: {
          orderId: order.orderId,
        }
      })
      let stripeInvoice: Stripe.Invoice
      if (invoice.data?.stripeInvoiceID) {
        try {
          stripeInvoice = await stripe.invoices.retrieve(invoice.data?.stripeInvoiceID);
        } catch (error) {
          console.log("send dispatch notice email err", error);
        }
      }
      if (stripeInvoice && invoice.customerInfo?.email) {
        const resellerUser = await DB.User.findOne({
          where: {
            id: store.resellerId,
          }
        });
        if (resellerUser) {
          const setting = await DB.GeneralData.findOne({
            where: {
              type: 'reseller-settings-emails',
              field1: resellerUser.id,
              name: 'Dispatch Notification',
            }
          });
          const shouldSendEmail = setting?.field2 !== 'false';
          if (shouldSendEmail) {
            await MailHelper.sendSMTPEmail({
              to: resellerUser.email,
              subject: "Your customer's order is on its way",
              html: getDispatchNotiEmailHtml({
                customerName: invoice.customerInfo?.first_name,
                message: `Great news! Your customer's order is on its way.<br />The order <strong>${order.orderId}</strong> has successfully shipped. You can click on the button below to track your shipment.`,
                link: `https://www.royalmail.com/track-your-item#/tracking-results/${order.trackingNumber}`,
                items: (invoice.lineItems || []).map(i => ({
                  name: i.name,
                  quantity: i.quantity,
                })),
                resellerName: store.name || resellerUser.accountName || [resellerUser.firstName, resellerUser.lastName].filter(Boolean).join(' '),
              }),
            });
          }
        }
      }
      // end - send notice email
    } catch (err) {
      let errMessage = (err?.response?.data?.errors || []).join(", ");
      if (!errMessage) errMessage = err?.message;
      if (!errMessage) errMessage = JSON.stringify(err);
      error.push(errMessage);
    }
  }
  return error;
}

class UpdateShopifyOrderStatus implements TypeAPIHandler {
  url = "/api/online-stores/update-shopify-order";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      orders: Joi.array().items(Joi.object({
        orderId: Joi.number(),
        lineItems: Joi.array().items(Joi.any()),
        storeId: Joi.string(),
        trackingNumber: Joi.string(),
      })),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { orders } = request.body;

    if (!orders?.length) return { success: false };
    const error = await handleUpdateShopify(orders);

    return {
      success: true,
      error: error,
    }
  };
}

export default new UpdateShopifyOrderStatus();
