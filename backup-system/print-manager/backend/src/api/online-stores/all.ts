import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, checkAuthen, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON><PERSON> } from "helpers";
import Joi = require("joi");
import { ERROR } from "const";
import { Op } from "sequelize";
import { checkReseller } from "api/api-middlewares/authen";

class ListAllOnlineStore implements TypeAPIHandler {

  url = "/api/online-stores/all";
  method = "GET";
  apiSchema = {
    query: Joi.object({
      resellerId: Joi.string().allow(''),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const user = request.user;
    if (user.role === 'guess') throw new Error(ERROR.PERMISSION_DENIED);
    let { resellerId } = request.query;
    console.log('resellerId', resellerId);
    if ((user.role === 'reseller' || user.role === 'user') && !resellerId) throw new Error(ERROR.PERMISSION_DENIED);
    if (user.role === 'reseller' && resellerId !== user.id) throw new Error(ERROR.PERMISSION_DENIED);
    if (user.role === 'user' && resellerId !== user.resellerId) throw new Error(ERROR.PERMISSION_DENIED);

    const inactiveFilter = {
      inactive: {
        [Op.not]: true
      }
    }

    const result = await DB.OnlineStore.findAndCountAll({
      where: !!resellerId ? {
        resellerId,
        ...inactiveFilter,
      } : {
        ...inactiveFilter,
      },
      order: [
        ['updatedAt', 'DESC'],
      ],
    });

    return {
      success: true,
      data: result.rows,
    }
  };
}

export default new ListAllOnlineStore();