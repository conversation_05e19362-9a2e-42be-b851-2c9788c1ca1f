import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkA<PERSON><PERSON>, check<PERSON><PERSON><PERSON>, checkAuthenOptional, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");
import { Op } from "sequelize";

const path = require('path'); 
const fs = require('fs'); 

class FindPublishedProduct implements TypeAPIHandler {
  url = "/api/online-stores/find-published-products/:published_product_id";
  method = "GET";
  apiSchema = {
    params: Joi.object({
      published_product_id: Joi.string().required(),
    })
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    // checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { published_product_id } = request.params;

    const design = await DB.Design.findOne({
      where: {
        products: {
          [Op.like]: `%:${published_product_id},%`,
        }
      }
    });

    if (!design) return { success: false, message: 'Design not found' };
    
    return {
      success: true,
      data: design,
    }

  };
}

export default new FindPublishedProduct();