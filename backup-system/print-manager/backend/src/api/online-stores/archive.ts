import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, checkAuthen, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import Joi = require("joi");
import { ERROR } from "const";
import RolesHelper from "../../helpers/RolesHelper";
import { Shopify } from "helpers";

class ArchiveStore implements TypeAPIHandler {

  url = "/api/online-stores/archive";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      id: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { id } = request.body;
    const user = request.user;
    if (RolesHelper.isNoRoles(user)) throw new Error(ERROR.PERMISSION_DENIED);

    const store = await DB.OnlineStore.findByPk(id);
    if (!store) throw new Error(ERROR.NOT_EXISTED);
    if (store.resellerId !== user.id) throw new Error(ERROR.PERMISSION_DENIED);

    // remove webhook
    const url = store.url;
    const token = store.data?.shopifyAccessToken;
    await Shopify.deleteOrderWebhook(url, token);

    store.inactive = true;
    await store.save();
    return {
      success: true,
    }
  };
}

export default new ArchiveStore();
