import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, checkAuthen, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON>per, Shopify } from "helpers";
import Joi = require("joi");
import { ERROR } from "const";
import { checkReseller } from "api/api-middlewares/authen";

class ShopifyAppConnectCallback implements TypeAPIHandler {

  url = "/api/online-stores/shopify-app-connect-callback";
  method = "GET";
  apiSchema = {
    
  }

  preHandler = combineMiddlewares([
    // validateRequest(this.apiSchema),
  ]);

  handler = async (request: TRequestUser, reply) => {
    console.log(request.query);
    const isValid = Shopify.verifyHmac(request.query);
    if (!isValid) {
      reply.status(400).send({
        success: false,
        error: String("Invalid request"),
      });
      return;
    }
    const apiCall = await Shopify.getAccessToken(request.query.shop, request.query.code);
    const token = apiCall.access_token;
    console.log('ACCESS TOKEN: ', token);
    const myshopifyUrl = `https://${request.query.shop}`;
    const store = await DB.OnlineStore.findOne({
      where: {
        url: myshopifyUrl,
        inactive: false,
      }
    });
    if (!store) {
      console.log(`no store found for slug: ${request.query.shop}. Creating new one... url ${myshopifyUrl}`);
      const shopInfo = await Shopify.getShopinfo(myshopifyUrl, token);
      let newStore = await DB.OnlineStore.findOne({
        where: {
          url: myshopifyUrl,
        }
      });
      if (!newStore?.id) {
        newStore = await DB.OnlineStore.create({
          id: VarHelper.genId(),
          url: myshopifyUrl,
          data: {
            shopifyAccessToken: token,
          },
          name: shopInfo.name,
          resellerId: 'await-claim',
          type: 'shopify',
        });
      } else {
        newStore.inactive = false;
        newStore.data = {
          ...newStore.data,
          shopifyAccessToken: token,
        }
        newStore.name = shopInfo.name;
        newStore.resellerId = 'await-claim';
        await newStore.save();
      }
      const replyHTML = `
      <htmt>
        <head>
          <meta http-equiv="refresh" content="0; url=${process.env.BACKEND_CMS_URL || 'https://bg-production.bottledgoose.co.uk'}/claim-my-stores/${newStore.id}?resellerId=${newStore.resellerId}">
        </head>
        <body>You have successfully connected the store. You will be redirected shortly...</body>
      </html>
    `;
      console.log(replyHTML)
      reply.type('text/html').status(200).send(replyHTML);
      // reply.status(200).send(`OK`);
      return;
    } else {
      const data = Object.assign({}, store.data);
      data.shopifyAccessToken = token;
      store.data = data;
      await store.save();
      reply.type('text/html').status(200).send(`
        <htmt>
          <head>
            <meta http-equiv="refresh" content="2; url=${process.env.BACKEND_CMS_URL || 'https://bg-production.bottledgoose.co.uk'}/claim-my-stores/${store.id}?resellerId=${store.resellerId}">
          </head>
          <body>You have successfully connected the store. You will be redirected shortly...</body>
        </html>
      `);
    }
    return;
  };
}

export default new ShopifyAppConnectCallback();
