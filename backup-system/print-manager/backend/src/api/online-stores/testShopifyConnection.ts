import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON><PERSON>, checkAuthenOptional, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON><PERSON> } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");
import axios from 'axios';
import { checkReseller } from "api/api-middlewares/authen";
import { Op } from "sequelize";

class TestShopifyConnection implements TypeAPIHandler {

  url = "/api/online-stores/test-shopify-connection";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      url: Joi.string(),
      token: Joi.string(),
      isNewStore: Joi.boolean().optional(),
      currentStoreId: Joi.string().optional(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkA<PERSON>en,
    checkReseller
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { body, user } = request;
    const { url, token, isNewStore, currentStoreId } = body;
    // if (user.role !== 'reseller') throw new Error(ERROR.PERMISSION_DENIED);
    const normalizedUrl = url.replace(/\/+$/, ''); // Remove trailing slashes
    const existedStore = await DB.OnlineStore.findOne({
      where: {
        url: normalizedUrl,
        inactive: {
          [Op.not]: true
        }
      }
    });
    if (existedStore?.id) {
      if (isNewStore || (currentStoreId && String(existedStore.id) !== String(currentStoreId))) {
        throw new Error('Store with the same url already exists');
      }
    }

    const axiosRes: any = await axios.request({
      url: `${url}/admin/api/2023-04/products.json`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': token,
      },
      data: JSON.stringify({
        product: {
          title: 'Hello World from Bottled Goose Print Manager',
          product_type: 'Bottled Goose',
          published: false,
          status: 'draft',
        }
      })
    });
    const product = axiosRes.data.product;

    return {
      success: true,
      data: product,
    }
  };
}

export default new TestShopifyConnection();
