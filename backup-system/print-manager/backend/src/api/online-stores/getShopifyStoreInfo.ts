import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkAd<PERSON>, check<PERSON><PERSON><PERSON>, checkAuthenOptional, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>elper } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");
import axios from 'axios';
import { Op } from "sequelize";

const path = require('path'); 
const fs = require('fs'); 

class GetShopifyStoreInfo implements TypeAPIHandler {
  url = "/api/online-stores/shopify-store-info/:cms_store_id";
  method = "GET";
  apiSchema = {
    params: Joi.object({
      cms_store_id: Joi.string().required(),
    })
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { cms_store_id } = request.params;

    const store = await DB.OnlineStore.findByPk(cms_store_id);
    if (!store) throw new Error(ERROR.NOT_EXISTED);
    if (store.resellerId !== request.user.id && store.resellerId !== request.user.resellerId) throw new Error(ERROR.PERMISSION_DENIED);
    const token = store.data.shopifyAccessToken;
    const apiCall : any = await axios.request({
      url: `${store.url}/admin/api/2023-07/shop.json`,
      method: 'get',
      headers: {
        'X-Shopify-Access-Token': token,
      }
    });
    if (!apiCall.data.shop?.id) throw new Error(ERROR.NOT_EXISTED);
    return {
      success: true,
      data: apiCall.data.shop
    }

  };
}

export default new GetShopifyStoreInfo();