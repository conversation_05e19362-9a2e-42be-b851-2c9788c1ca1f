import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, checkAuthen, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON><PERSON> } from "helpers";
import Joi = require("joi");
import { ERROR } from "const";
import { checkReseller } from "api/api-middlewares/authen";

class DetailOnlineStore implements TypeAPIHandler {

  url = "/api/online-stores/:id";
  method = "GET";
  apiSchema = {
    params: Joi.object({
      id: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
    checkReseller
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { id } = request.params;
    const user = request.user;
    // if (user.role === 'user' || user.role === 'guess') throw new Error(ERROR.PERMISSION_DENIED);
    
    const store = await DB.OnlineStore.findByPk(id);
    if (!store) throw new Error(ERROR.NOT_EXISTED);
    // if (user.role === 'reseller'
    //   && store.resellerId !== user.id
    // ) {
    //   throw new Error(ERROR.PERMISSION_DENIED);
    // }

    return {
      success: true,
      data: store,
    }
  };
}

export default new DetailOnlineStore();