import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, checkAuthen, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON><PERSON> } from "helpers";
import Joi = require("joi");
import { ERROR } from "const";
import { Op } from "sequelize";
import { checkReseller } from "api/api-middlewares/authen";

class ListOnlineStore implements TypeAPIHandler {

  url = "/api/online-stores";
  method = "GET";
  apiSchema = {
    query: Joi.object({
      page: Joi.number(),
      resellerId: Joi.string().allow(''),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const user = request.user;
    if (user.role === 'guess') throw new Error(ERROR.PERMISSION_DENIED);
    let { page, resellerId } = request.query;
    console.log('resellerId', resellerId);
    console.log('user.id', user.id);
    if ((user.role === 'reseller' || user.role === 'user') && !resellerId) throw new Error(ERROR.PERMISSION_DENIED);
    if (user.role === 'reseller' && resellerId !== user.id) throw new Error(ERROR.PERMISSION_DENIED);
    if (user.role === 'user' && resellerId !== user.resellerId) throw new Error(ERROR.PERMISSION_DENIED);
    console.log('here');
    if (user.role !== 'admin' && (!resellerId || resellerId === 'undefined')) throw new Error(ERROR.PERMISSION_DENIED);

    if (!page) page = 1;

    const inactiveFilter = {
      inactive: {
        [Op.not]: true
      }
    }

    const PAGE_SIZE = 1000;
    const result = await DB.OnlineStore.findAndCountAll({
      where: (!!resellerId && resellerId !== 'undefined') ? {
        resellerId,
        ...inactiveFilter,
      } : {
        ...inactiveFilter,
      },
      order: [
        ['updatedAt', 'DESC'],
      ],
      offset: (page - 1) * PAGE_SIZE,
      limit: PAGE_SIZE,
    });

    return {
      success: true,
      data: {
        list: result.rows,
        total: result.count,
        hasNext: page * PAGE_SIZE < result.count,
      },
    }
  };
}

export default new ListOnlineStore();