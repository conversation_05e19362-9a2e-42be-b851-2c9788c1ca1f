import { TypeAP<PERSON>Hand<PERSON> } from 'type';
import { validateRequest, combineMiddlewares } from '../api-middlewares'
import { DB, InMemory as RedisCache } from 'db';
import Joi = require("joi");
import Etsy from 'helpers/EtsyHelper';
import Bg from 'api/bg/@utils/Bg';
import { listPipeline } from 'api/bg/services';

const moment = require('moment');

var { Liquid } = require('liquidjs');
var engine = new Liquid();

export const LIQUID_TEMPLATE = `
<html>
<head>
<meta charset="UTF-8">
</head>
<body>
<div class="page"></div>
<div class="page"></div>
<div class="page"></div>
<div class="page-absolute">
<div class="wrapper">
  <div class="header">
    <div>
      <div class="shop-title">
        {% if shop.logo != blank %}
          <img src="{{ shop.logo }}" class="shop-logo" />
        {% else %}
          <p class="to-uppercase">
            {{ shop.name }}
          </p>
        {% endif %}
      </div>
      <div>
        <p style="margin-top: 10px;">{{ shop.email }}</p>
        <p>{{ shop.phone }}</p>
        <p>{{ shop.address }}</p>
      </div>
    </div>
    <div class="order-title">
      <p class="text-align-right">
        Order #{{ orderId }}
      </p>
      {% if order.po_number != blank %}
        <p class="text-align-right">
          PO number #{{ order.po_number }}
        </p>
      {% endif %}
      <p class="text-align-right">
        {{ format_date }}
      </p>
    </div>
  </div>
  <div class="customer-addresses">
    <div class="shipping-address">
      <p class="subtitle-bold to-uppercase">
        {% if delivery_method.instructions != blank %}
          Delivery to
        {% else %}
          Ship to
        {% endif %}
      </p>
      <p class="address-detail">
        {% if shipping_address != blank %}
          {{ shipping_address.name }}
          {% if shipping_address.company != blank %}
            <br>
            {{ shipping_address.company }}
          {% endif %}
          <br>
          {{ shipping_address.address1 }}
          {% if shipping_address.address2 != blank %}
            <br>
            {{ shipping_address.address2 }}
          {% endif %}
          {% if shipping_address.city != blank %}
            <br>
            {{ shipping_address.city }}
          {% endif %}
          {% if shipping_address.province != blank %}
            <br>
            {{ shipping_address.province }}
          {% endif %}
          {% if shipping_address.zip != blank %}
            <br>
            {{ shipping_address.zip }}
          {% endif %}
          <br>
          {{ shipping_address.country }}
          {% if shipping_address.phone != blank %}
            <br>
            {{ shipping_address.phone }}
          {% endif %}
        {% else %}
          No shipping address
        {% endif %}
      </p>
    </div>
    <div class="billing-address">
      <p class="subtitle-bold to-uppercase">
        Bill to
      </p>
      <p class="address-detail">
        {% if billing_address != blank %}
          {{ billing_address.name }}
          {% if billing_address.company != blank %}
            <br>
            {{ billing_address.company }}
          {% endif %}
          <br>
          {{ billing_address.address1 }}
          {%  if billing_address.address2 != blank %}
            <br>
            {{ billing_address.address2 }}
          {% endif %}
          {% if billing_address.city != blank %}
            <br>
            {{ billing_address.city }}
          {% endif %}
          {% if billing_address.province != blank %}
            <br>
            {{ billing_address.province }}
          {% endif %}
          {% if billing_address.zip != blank %}
            <br>
            {{ billing_address.zip }}
          {% endif %}
          <br>
          {{ billing_address.country }}
        {% else %}
          No billing address
        {% endif %}
      </p>
    </div>
  </div>
  <hr>
  <div class="order-container">
    <div class="order-container-header">
      <div class="order-container-header-left-content">
        <p class="subtitle-bold to-uppercase">
          Items
        </p>
      </div>
      <div class="order-container-header-right-content">
        <p class="subtitle-bold to-uppercase">
          Quantity
        </p>
      </div>
    </div>

    {% comment %}
    To adjust the size of line item images, change desired_image_size.
    The other variables make sure your images print at high quality.
    {% endcomment %}
    {% assign desired_image_size = 58 %}
    {% assign resolution_adjusted_size = desired_image_size | times: 300 | divided_by: 72 | ceil %}
    {% capture effective_image_dimensions %}
      {{ resolution_adjusted_size }}x{{ resolution_adjusted_size }}
    {% endcapture %}

    {% for line_item in line_items_in_shipment %}
      {% if line_item.previewImages %}
        <div class="flex-line-item" style="align-items: flex-start;">
          <div style="display: flex; flex-direction: column; align-items: flex-start; flex: 1">
            <div class="flex-line-item-description">
              <p>
              {% if line_item.name != blank %}
                <span class="line-item-description-line">
                  {{ line_item.name }}
                </span>
              {% else %}
                <span class="line-item-description-line">
                  {{ line_item.title }}
                </span>
              {% endif %}
              </p>
            </div>
            <div style="display: flex; flex-direction: row;">
              {% for previewImage in line_item.previewImages %}
                <img src="{{ previewImage }}" style="width: 130px; height: 130px; object-fit: contain;" />
              {% endfor %}
            </div>
          </div>
          <div class="flex-line-item-quantity">
            <p class="text-align-right">
              {{ line_item.quantity }}
            </p>
          </div>
        </div>
      {% else %}
        <div class="flex-line-item">
          <div class="flex-line-item-img">
            {% if line_item.image != blank %}
              <img src="{{ line_item.image }}" style="width: 110px; height: 110px; object-fit: contain;" />
            {% endif %}
          </div>
          <div class="flex-line-item-description">
            <p>
            {% if line_item.name != blank %}
              <span class="line-item-description-line">
                {{ line_item.name }}
              </span>
            {% else %}
              <span class="line-item-description-line">
                {{ line_item.title }}
               </span>
            {% endif %}
            </p>
          </div>
          <div class="flex-line-item-quantity">
            <p class="text-align-right">
              {{ line_item.quantity }}
            </p>
          </div>
        </div>
      {% endif %}
    {% endfor %}
  </div>
  <hr>
  {% if order.note != blank %}
    <div class="notes">
      <p class="subtitle-bold to-uppercase">
        Notes
      </p>
      <p class="notes-details">
        {{ order.note }}
      </p>
    </div>
  {% endif %}
  {% if delivery_method.instructions != blank %}
    <div class="notes">
      <p class="subtitle-bold to-uppercase">
        Delivery instructions
      </p>
      <p class="notes-details">
        {{ delivery_method.instructions }}
      </p>
    </div>
  {% endif %}
  <div class="footer">
    <p>
      Thank you for shopping with us!
    </p>
    <p>
      <strong>
        {{ shop.name }}
      </strong>
      {% if shop_address.summary != blank %}
        <br>
        {{ shop_address.summary }}
      {% endif %}
      {% if shop_address.email != blank %}
        <br>
        {{ shop_address.email }}
      {% endif %}
      {% if shop_address.domain != blank %}
        <br>
        {{ shop_address.domain }}
      {% endif %}
    </p>
  </div>
</div>
</div>
<button onclick="window.print();">Print</button>
<style type="text/css">
  body {
    font-size: 15px;
    position: relative;
    margin: 0;
    padding: 0;
  }
  html: {
    margin: 0;
    padding: 0;
  }
  @page {
    size: A4;
    margin: 0;
  }
  .page {
    width: 210mm;
    height: 297mm;
    page-break-before:always;
  }
  .page-absolute {
    position: absolute;
    top: 0px;
    left: 0px;
    right: 0px;
  }
  * {
    box-sizing: border-box;
  }

  .wrapper {
    width: 831px;
    margin: auto;
    padding: 4em;
    font-family: "Noto Sans", sans-serif;
    font-weight: 250;
  }

  .header {
    width: 100%;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    flex-direction: row;
    align-items: top;
  }

  .header p {
    margin: 0;
  }

  .shop-title {
    -webkit-box-flex: 6;
    -webkit-flex: 6;
    flex: 6;
    font-size: 1.9em;
  }

  .order-title {
    -webkit-box-flex: 4;
    -webkit-flex: 4;
    flex: 4;
  }

  .customer-addresses {
    width: 100%;
    display: inline-block;
    margin: 2em 0;
  }

  .address-detail {
    margin: 0.7em 0 0;
    line-height: 1.5;
  }

  .subtitle-bold {
    font-weight: bold;
    margin: 0;
    font-size: 0.85em;
  }

  .to-uppercase {
    text-transform: uppercase;
  }

  .text-align-right {
    text-align: right;
  }

  .shipping-address {
    float: left;
    min-width: 18em;
    max-width: 50%;
  }

  .billing-address {
    padding-left: 20em;
    min-width: 18em;
  }

  .order-container {
    padding: 0 0.7em;
  }

  .order-container-header {
    display: inline-block;
    width: 100%;
    margin-top: 1.4em;
  }

  .order-container-header-left-content {
    float: left;
  }

  .order-container-header-right-content {
    float: right;
  }

  .flex-line-item {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    flex-direction: row;
    align-items: center;
    margin: 1.4em 0;
    page-break-inside: avoid;
  }

  .flex-line-item-img {
    margin-right: 1.4em;
    min-width: {{ desired_image_size }}px;
  }

  .flex-line-item-description {
    -webkit-box-flex: 7;
    -webkit-flex: 7;
    flex: 7;
  }

  .line-item-description-line {
    display: block;
  }

  .flex-line-item-description p {
    margin: 0;
    line-height: 1.5;
  }

  .flex-line-item-quantity {
    -webkit-box-flex: 3;
    -webkit-flex: 3;
    flex: 3;
  }

  .subdued-separator {
    height: 0.07em;
    border: none;
    color: lightgray;
    background-color: lightgray;
    margin: 0;
  }

  .missing-line-items-text {
    margin: 1.4em 0;
    padding: 0 0.7em;
  }

  .notes {
    margin-top: 2em;
  }

  .notes p {
    margin-bottom: 0;
  }

  .notes .notes-details {
    margin-top: 0.7em;
  }

  .footer {
    margin-top: 2em;
    text-align: center;
    line-height: 1.5;
  }

  .footer p {
    margin: 0;
    margin-bottom: 1.4em;
  }

  hr {
    height: 0.14em;
    border: none;
    color: black;
    background-color: black;
    margin: 0;
  }

  .aspect-ratio {
    position: relative;
    display: block;
    background: #fafbfc;
    padding: 0;
  }

  .aspect-ratio::before {
    z-index: 1;
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    border: 1px solid rgba(195,207,216,0.3);
  }

  .aspect-ratio--square {
    width: 100%;
    padding-bottom: 100%;
  }

  .aspect-ratio__content {
    position: absolute;
    max-width: 100%;
    max-height: 100%;
    display: block;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    margin: auto;
  }
  button {
    display: inline-block;
    outline: none;
    background-color: transparent;
    border-radius: 10px;
    width: 150px;
    height: 40px;
    border: 1px solid black;
    text-align: center;
    position: absolute;
    right: 20px;
    top: 20px;
  }
  .bottom-footer {
    position: absolute;
    top: calc( 70% + 20px );
    left: 0px;
    right: 0px;
    display: none;
  }
  .shop-logo {
    width: 120px;
    height: 90px;
    object-fit: contain;
  }
  @media print {
    button { display: none; }
    .bottom-footer { display: block; }
    html, body { height: auto }
  }
</style>
<div class="bottom-footer" >
<div style="width: 831px; margin: auto; padding: 4em;">
  <p class="address-detail">
    {% if shipping_address != blank %}
      {{ shipping_address.name }}
      {% if shipping_address.company != blank %}
        <br>
        {{ shipping_address.company }}
      {% endif %}
      <br>
      {{ shipping_address.address1 }}
      {% if shipping_address.address2 != blank %}
        <br>
        {{ shipping_address.address2 }}
      {% endif %}
      {% if shipping_address.city != blank %}
        <br>
        {{ shipping_address.city }}
      {% endif %}
      {% if shipping_address.province != blank %}
        <br>
        {{ shipping_address.province }}
      {% endif %}
      {% if shipping_address.zip != blank %}
        <br>
        {{ shipping_address.zip }}
      {% endif %}
      <br>
      {{ shipping_address.country }}
      {% if shipping_address.phone != blank %}
        <br>
        {{ shipping_address.phone }}
      {% endif %}
    {% else %}
      No shipping address
    {% endif %}
    {% if orderId != blank %}
      <br>
      #{{ orderId }}
    {% endif %}
  </p>
</div>
</div>
<script>
  function elementsOverlap(el1, el2) {
    const domRect1 = el1.getBoundingClientRect();
    const domRect2 = el2.getBoundingClientRect();

    return !(
      domRect1.top > domRect2.bottom ||
      domRect1.right < domRect2.left ||
      domRect1.bottom < domRect2.top ||
      domRect1.left > domRect2.right
    );
  }
  const pages = document.querySelectorAll('.page');
  const content = document.querySelector('.wrapper');
  for (let i=pages.length -1; i>=0; i--) {
    if (!elementsOverlap(pages[i], content)) {
      pages[i].remove();
    }
  }
  if (window.matchMedia("print").matches) {
    //   console.log('run here');
    //   const numberOfPage = Math.ceil((window.innerWidth / window.innerHeight) / (210/297));
    //   console.log('number of page', numberOfPage);
    //   const totalHeight = numberOfPage * window.innerWidth * 297 / 210;
    //   document.body.style.height = totalHeight + 'px';
    //   document.body.style.boxSizing = 'border-box';
    
  }
</script>
</body>
</html>
`

export const LIQUID_TEMPLATE_ETSY = `
<html>

<head>
  <meta charset="UTF-8">
</head>

<body>
  <div class="page"></div>
  <div class="page"></div>
  <div class="page"></div>
  <div class="page-absolute">
    <div class="wrapper">
      <div class="header">
        {% if shop.logo != blank %}
          <div class="shop-title">
            <img src="{{ shop.logo }}" class="shop-logo" />
          </div>
        {% endif %}
        <div>
          <p style="font-weight: 600; font-size: 1.3em;">{{ shop.name }}</p>
          <p style="margin-top: 0.3em;">{{ shop.website }}</p>
        </div>
      </div>
      <div class="main-content">
        <div class="customer-addresses">
          <p class="subtitle-bold">
            Delivery to
          </p>
          <p class="address-detail">
            {% if shipping_address != blank %}
            {{ shipping_address.name }}
            {% if shipping_address.company != blank %}
            <br>
            {{ shipping_address.company }}
            {% endif %}
            <br>
            {{ shipping_address.address1 }}
            {% if shipping_address.address2 != blank %}
            <br>
            {{ shipping_address.address2 }}
            {% endif %}
            {% if shipping_address.city != blank %}
            <br>
            {{ shipping_address.city }}
            {% endif %}
            {% if shipping_address.province != blank %}
            <br>
            {{ shipping_address.province }}
            {% endif %}
            {% if shipping_address.zip != blank %}
            <br>
            {{ shipping_address.zip }}
            {% endif %}
            <br>
            {{ shipping_address.country }}
            {% if shipping_address.phone != blank %}
            <br>
            {{ shipping_address.phone }}
            {% endif %}
            {% else %}
            No shipping address
            {% endif %}
          </p>
          <!-- <p class="subtitle-bold">
            Scheduled to dispatch by
          </p>
          <p class="address-detail">
            xxxxxx
          </p> -->
          <div class="line">
            <svg height="1" width="100%">
              <line x1="0" y1="0" x2="100%" y2="0" style="stroke: black; stroke-width: 1;" />
            </svg>
          </div>
          <p class="subtitle-bold">
            From
          </p>
          <p class="address-detail">
            {% if billing_address != blank %}
            {{ billing_address.name }}
            {% if billing_address.company != blank %}
            <br>
            {{ billing_address.company }}
            {% endif %}
            <br>
            {{ billing_address.address1 }}
            {% if billing_address.address2 != blank %}
            <br>
            {{ billing_address.address2 }}
            {% endif %}
            {% if billing_address.city != blank %}
            <br>
            {{ billing_address.city }}
            {% endif %}
            {% if billing_address.province != blank %}
            <br>
            {{ billing_address.province }}
            {% endif %}
            {% if billing_address.zip != blank %}
            <br>
            {{ billing_address.zip }}
            {% endif %}
            <br>
            {{ billing_address.country }}
            {% else %}
            No billing address
            {% endif %}
          </p>
        
          <p class="subtitle-bold">
            Order ID
          </p>
          <p class="address-detail">
            #{{ orderId }}
          </p>

          <p class="subtitle-bold">
            Order Number
          </p>
          <p class="address-detail">
            #{{ order.order_number }}
          </p>
        
          <p class="subtitle-bold">
            Order date
          </p>
          <p class="address-detail">
            {{ format_date }}
          </p>
        
          <p class="subtitle-bold">
            Buyer
          </p>
          <p class="address-detail">
            {{ order.customer.first_name }} {{ order.customer.last_name }}
          </p>
        
          <p class="subtitle-bold">
            Payment method
          </p>
          <p class="address-detail">
            Paid via Etsy Payments
          </p>
        </div>
        <div class="order-container">
          <div class="order-container-header">
            {% if line_items_in_shipment.length > 1 %}
              <p class="subtitle-bold">
                {{ line_items_in_shipment.length }} items
              </p>
            {% else %}
              <p class="subtitle-bold">
                1 item
              </p>
            {% endif %}
            <div class="line">
              <svg height="1" width="100%">
                <line x1="0" y1="0" x2="100%" y2="0" style="stroke: black; stroke-width: 1;" />
              </svg>
            </div>
          </div>
          {% for line_item in line_items_in_shipment %}
          <div class="flex-line-item">
            {% if line_item.image != blank %}
              <div class="flex-line-item-img">
                <img src="{{ line_item.image }}" style="width: 64px; height: 64px; object-fit: contain;" />
              </div>
            {% endif %}
            <div class="flex-line-item-description">
              <p class="subtitle-bold" style="margin-top: 0; font-size: 0.9em">
                {% if line_item.name != blank %}
                  {{ line_item.name }}
                {% else %}
                  {{ line_item.title }}
                {% endif %}
              </p>
            </div>
            <div class="flex-line-item-quantity">
              <p class="text-align-right" style="margin-top: 0;">
                {{ line_item.quantity }} x £{{ line_item.price }}
              </p>
            </div>
          </div>
          {% endfor %}
          <div class="line">
            <svg height="1" width="100%">
              <line x1="0" y1="0" x2="100%" y2="0" style="stroke: black; stroke-width: 1;" />
            </svg>
          </div>
          <div class="summary-container">
            <div style="flex: 1">
            </div>
            <div style="display:flex; flex: 1; flex-direction: row">
              <div style="width: 100px;">
                <p style="margin: 0; margin-top: 0.7em; text-align: right">Item total</p>
                <p style="margin: 0; margin-top: 0.4em; text-align: right">Delivery total</p>
                <p style="font-weight: 500; margin: 0; margin-top: 0.4em; text-align: right">Order total</p>
              </div>
              <div style="flex: 1; align-items: flex-end; display:flex; flex-direction: column">
                <p style="margin: 0; margin-top: 0.7em;">£{{ total_price }}</p>
                <p style="margin: 0; margin-top: 0.4em;">£{{ total_shipping_price }}</p>
                <p style="font-weight: 500; margin: 0; margin-top: 0.4em;">£{{ grand_total }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="footer-absolute">
        <?xml version="1.0" encoding="UTF-8"?>
        <svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 540.57 53.67">
          <defs>
            <style>
              .cls-1 {
                font-family: GraphikApp-Medium, 'GraphikApp Medium';
                font-weight: 500;
              }

              .cls-1,
              .cls-2 {
                font-size: 8px;
              }

              .cls-3 {
                fill: none;
                stroke: #000;
                stroke-linecap: square;
                stroke-miterlimit: 10;
                stroke-width: .57px;
              }

              .cls-2 {
                font-family: GraphikApp-Regular, 'GraphikApp Regular';
              }

              .cls-4 {
                fill: #231f20;
              }

              .cls-4,
              .cls-5 {
                fill-rule: evenodd;
              }
            </style>
          </defs>
          <g id="Layer_1-2" data-name="Layer 1">
            <line class="cls-3" x1=".28" y1=".28" x2="540.28" y2=".28" />
            <path class="cls-5"
              d="m12.87,25.44c-.33,1.88-1.22,2.56-2.33,2.68v.54h1.38v4.76c0,1.33.88,1.98,2.06,1.98.91,0,1.86-.39,2.19-1.19l-.33-.42c-.16.26-.65.65-1.27.65-.68,0-1.05-.46-1.05-1.64v-4.2l2.29.17.12-1.05-2.42.09v-2.37h-.65m4.88,7.32l-.59.02c.08.74.09,1.49,0,2.17,0,0,1.18.43,2.37.43,1.61,0,2.9-.77,2.9-2.28,0-2.64-3.91-2.22-3.91-3.98,0-.73.67-1.01,1.35-1.01.57,0,1.04.2,1.13.5l.39,1.16.57-.03c.03-.62.08-1.33.17-1.91-.5-.22-1.53-.34-2.19-.34-1.52,0-2.74.67-2.74,2.15,0,2.6,3.81,2.03,3.81,3.98,0,.65-.42,1.15-1.35,1.15s-1.32-.45-1.49-.9l-.43-1.12m6.34,5.01c-.47,0-.6-.22-.67-.53l-.23-1.21-.65.03c-.09.71-.23,1.52-.42,2.15.43.29.96.43,1.67.43,1.05,0,2.63-.29,4.06-3.66l2.48-5.87c.2-.46.26-.53.87-.76l.33-.12v-.54l-1.47.08-1.57-.08v.54l.4.12c.4.12.59.26.59.59,0,.17-.05.25-.14.46-.17.43-1.27,3.15-1.81,4.32-.68-1.71-1.61-4.25-1.72-4.62-.03-.09-.05-.17-.05-.25,0-.25.17-.45.54-.53l.51-.11v-.54l-2.12.08-1.69-.08v.54l.28.09c.4.14.45.19.71.78,1.07,2.42,2.26,5.5,2.65,6.51-.84,1.69-1.83,2.15-2.56,2.15M3.67,25.01c0-.15.02-.25.28-.25h3.55c.62,0,.96.53,1.21,1.52l.2.79h.6c.11-2.25.2-3.22.2-3.22,0,0-1.52.17-2.42.17H2.75l-2.43-.08v.65l.82.15c.57.11.71.23.76.76,0,0,.05,1.55.05,4.11s-.05,4.09-.05,4.09c0,.46-.19.64-.76.74l-.82.16v.65l2.43-.08h4.06c.91,0,3.04.08,3.04.08.05-.56.36-3.08.4-3.36h-.57l-.6,1.38c-.48,1.08-1.18,1.16-1.95,1.16h-2.31c-.78,0-1.15-.31-1.15-.98v-3.53s1.72,0,2.28.05c.43.03.7.16.84.76l.19.81h.67l-.05-2.03.09-2.05h-.67l-.22.9c-.14.59-.23.7-.84.76-.79.08-2.29.06-2.29.06v-4.17" />
            <path class="cls-4"
              d="m104.44,25.4h.28c0-1.23-.53-2.13-1.05-2.71-.52-.58-1.04-.86-1.06-.87l-.13.24h.27v-3.48c0-.07-.03-.14-.08-.19-.05-.05-.12-.08-.19-.08h-3.21c-.07,0-.14.03-.19.08-.05.05-.08.12-.08.19v3.48h.27l-.13-.24s-.53.29-1.06.87c-.52.58-1.05,1.49-1.05,2.71,0,.58.08,1.1.24,1.56.24.69.66,1.24,1.26,1.61.6.37,1.34.56,2.23.59v-.27s-.26,0-.26,0v.02c0,.07.03.14.08.19s.12.08.19.08c.07,0,.11,0,.11,0v-.27s0,.27,0,.27c0,0,.04,0,.11,0,.07,0,.14-.03.19-.08s.08-.12.08-.19v-.02h-.27v.27c.6-.02,1.13-.11,1.59-.28.7-.25,1.24-.69,1.61-1.28.36-.59.54-1.34.54-2.21h-.55c0,.53-.07.99-.2,1.38-.2.59-.54,1.02-1.03,1.32-.49.31-1.14.48-1.96.51-.15,0-.27.13-.27.27v.02h.27v-.27s-.03,0-.1,0h0c-.07,0-.1,0-.1,0v.27h.27v-.02c0-.15-.12-.27-.27-.27-.55-.02-1.02-.1-1.41-.25-.6-.22-1.03-.57-1.32-1.05-.29-.49-.45-1.12-.45-1.92,0-1.07.45-1.83.91-2.35.23-.26.46-.45.63-.57.09-.06.16-.11.2-.14l.05-.03h.01s0,0,0,0l-.02-.04.02.04-.02-.04.02.04c.09-.05.15-.14.15-.24v-3.2h2.66v3.2c0,.1.06.2.15.24l.02-.04-.02.04.02-.04-.02.04s.48.26.93.77c.45.51.89,1.27.89,2.32h.27" />
            <path class="cls-4"
              d="m96.14,34.28l2.64,2.94,1.82,8.49c.03.15.18.24.33.21.15-.03.24-.18.21-.33l-1.83-8.56s-.03-.09-.06-.13l-2.69-3c-.1-.11-.27-.12-.39-.02-.11.1-.12.27-.02.39" />
            <path class="cls-4"
              d="m109.64,31.99s-.13-.13-.23-.27c-.36-.5-.92-1.51-1.38-2.61-.23-.55-.44-1.12-.58-1.67s-.23-1.06-.23-1.48c0-.16.01-.31.04-.44.02-.09-.01-.19-.08-.25l-.03-.03c-.14-.14-.79-.77-1.39-1.51-.3-.37-.59-.77-.8-1.13-.21-.36-.33-.7-.33-.91,0-.1.02-.16.05-.22.04-.05.09-.1.2-.15l-.1-.26-.04.27h.01c.18.03,1.89.33,3.8,1.54,1.91,1.21,4.03,3.32,5.08,7.01,1.1,3.88,1.86,7.03,2.35,9.22.24,1.09.42,1.94.53,2.52.06.29.1.51.12.66.03.15.04.22.04.22.03.15.17.25.32.22.15-.03.25-.17.22-.32,0,0-.85-4.9-3.05-12.67-1.12-3.95-3.45-6.2-5.5-7.44-2.05-1.24-3.81-1.49-3.83-1.5-.05,0-.09,0-.14.02-.19.07-.35.19-.46.35-.11.16-.15.34-.15.52,0,.21.05.41.13.62.14.37.37.75.64,1.13.8,1.13,1.94,2.22,1.95,2.23l.19-.2-.27-.05c-.03.17-.05.35-.05.55,0,.56.13,1.2.32,1.86.29.99.74,2.02,1.18,2.86.22.42.43.79.62,1.08.1.15.18.27.27.38.08.11.16.19.24.26.12.1.29.08.39-.03.1-.12.08-.29-.04-.39" />
            <path class="cls-4"
              d="m91.64,17.74l.27.05c.03-.16.1-.26.18-.33.08-.07.2-.11.34-.11.18,0,.4.07.59.22.2.16.48.45.76.82.43.54.88,1.24,1.24,1.89.18.33.33.64.44.92.11.28.18.52.2.68.01.1.02.18.02.25,0,.14-.02.25-.04.32-.04.11-.08.14-.12.17-.04.03-.1.04-.17.04-.04,0-.08,0-.11-.01h-.04s0-.01,0-.01h0s-.02.05-.02.05l.02-.05h0s-.02.05-.02.05l.02-.05-.11.25.18-.21-.03-.03c-.17-.15-.94-.83-1.69-1.67-.38-.42-.75-.88-1.05-1.33-.29-.44-.5-.88-.56-1.21-.03-.15-.04-.29-.04-.41,0-.09,0-.17.02-.24l-.27-.05-.27-.05c-.02.1-.03.22-.03.34,0,.15.02.32.05.5.09.49.36,1.01.71,1.52.52.76,1.22,1.52,1.79,2.09.57.57,1.01.95,1.02.95l.07.04s.1.04.16.05c.06.01.14.02.22.02.09,0,.19-.01.29-.05.16-.05.32-.16.43-.34.11-.18.16-.41.16-.69,0-.1,0-.2-.02-.32-.04-.32-.18-.7-.37-1.13-.29-.64-.72-1.37-1.17-2.02-.22-.33-.45-.63-.67-.89-.22-.26-.43-.48-.63-.64-.28-.22-.61-.33-.93-.33-.24,0-.48.07-.68.22-.2.15-.34.38-.38.67l.27.05" />
            <path class="cls-4"
              d="m92.56,19.92s-.05,0-.08,0c-.29,0-.55.13-.76.3-.21.18-.37.4-.46.65-.07.18-.1.38-.1.56,0,.32.08.64.21.92.2.44.5.8.78,1.13.43.51.86,1.08,1.35,1.59.49.51,1.03.98,1.69,1.28.07.03.15.07.23.1.08.03.18.05.29.05.06,0,.13,0,.2-.02.12-.03.23-.09.31-.17.12-.12.19-.25.23-.38.04-.13.06-.25.06-.35v-.02c0-.12-.01-.25-.03-.39-.02-.13-.05-.27-.11-.39-.52-1.18-1.28-2.17-2.15-3.09-.1-.11-.28-.12-.39-.01-.11.1-.12.28-.01.39.84.9,1.56,1.83,2.05,2.93.03.06.05.16.07.26.02.1.03.21.03.3h0c0,.08-.02.19-.06.27-.02.04-.04.07-.06.09-.02.02-.04.03-.06.03-.02,0-.05,0-.07,0-.03,0-.07,0-.11-.02-.05-.02-.1-.04-.17-.07-.57-.27-1.06-.68-1.52-1.17-.46-.48-.89-1.04-1.32-1.56-.28-.34-.54-.66-.71-1.01-.1-.22-.16-.46-.16-.69,0-.13.02-.26.06-.38.05-.15.17-.31.3-.42.13-.11.28-.18.41-.18h.03c.15.01.28-.1.3-.25.01-.15-.1-.28-.25-.3" />
            <path class="cls-4"
              d="m94.51,25.94c.47.57.95,1.02,1.29,1.6.08.13.15.36.17.53v.06c0,.05,0,.11-.02.15-.02.04-.04.07-.06.08-.04.03-.1.05-.19.05-.18,0-.44-.09-.63-.19-.89-.46-1.86-1.28-2.6-2.16-.18-.22-.32-.38-.41-.51-.1-.14-.16-.25-.21-.38v-.02s-.02,0-.02,0h0s0,0,0,0h0s-.28-.57-.27-1.11c0-.2.03-.39.11-.57.08-.18.21-.34.42-.48.13-.08.16-.26.07-.38-.08-.13-.25-.16-.38-.08-.29.2-.5.45-.62.71-.12.26-.16.54-.16.8,0,.73.33,1.34.34,1.36l.24-.13-.26.1c.07.18.16.34.27.5.11.16.26.33.44.55.79.93,1.79,1.79,2.77,2.29.24.12.55.25.89.25.17,0,.35-.04.5-.14.12-.08.2-.2.25-.31.05-.12.07-.24.07-.37,0-.04,0-.09,0-.13-.03-.24-.12-.51-.25-.73-.39-.66-.89-1.14-1.34-1.68-.1-.12-.27-.13-.39-.04-.12.1-.13.27-.04.39" />
            <path class="cls-4"
              d="m107.81,30.08v2.71c0,.29-.11.54-.3.73-.19.19-.44.3-.73.3h-12.19c-.29,0-.54-.11-.73-.3-.19-.19-.3-.44-.3-.73v-3.4c0-.15-.12-.28-.27-.28s-.27.12-.27.28v3.4c0,.87.71,1.58,1.58,1.58h12.19c.87,0,1.58-.71,1.58-1.58v-2.71c0-.15-.12-.27-.27-.27s-.27.12-.27.27" />
            <path class="cls-4"
              d="m93.61,17.47l-.04-3.42h-.27s.27,0,.27,0c0-.28.11-.54.3-.73.19-.19.44-.3.73-.3h12.19c.29,0,.54.11.73.3.19.19.3.44.3.73v8.15c0,.15.12.27.27.27s.28-.12.28-.27v-8.15c0-.87-.71-1.58-1.58-1.58h-12.19c-.87,0-1.58.71-1.58,1.58h0s.04,3.43.04,3.43c0,.15.13.27.28.27.15,0,.27-.13.27-.28" />
            <path class="cls-4"
              d="m94.81,18.96v-4.33c0-.1.04-.18.1-.25.07-.07.15-.1.25-.1h11.05c.1,0,.18.04.25.1s.1.15.1.25v6.95c0,.15.12.27.27.27s.27-.12.27-.27v-6.95c0-.5-.4-.9-.9-.9h-11.05c-.5,0-.9.4-.9.9v4.33c0,.15.12.28.27.28s.27-.12.27-.28" />
            <path class="cls-4"
              d="m94.26,30.14v2.08c0,.5.4.9.9.9h11.05c.5,0,.9-.4.9-.9v-6.78c0-.15-.12-.27-.27-.27s-.28.12-.28.27v6.78c0,.1-.04.18-.1.25-.07.07-.15.1-.25.1h-11.05c-.1,0-.18-.04-.25-.1-.07-.07-.1-.15-.1-.25v-2.08c0-.15-.12-.27-.27-.27s-.28.12-.28.27" />
            <path class="cls-4"
              d="m100.67,47l-.27.05c.16.84.9,1.42,1.72,1.42.11,0,.22-.01.33-.03.84-.16,1.42-.9,1.42-1.72,0-.11,0-.22-.03-.33-.16-.84-.89-1.43-1.72-1.43-.11,0-.22.01-.33.03-.84.16-1.42.89-1.42,1.72,0,.11,0,.22.03.33l.27-.05.27-.05c-.01-.08-.02-.15-.02-.23,0-.57.4-1.07.98-1.18.08-.02.15-.02.23-.02.57,0,1.07.4,1.18.98.01.08.02.15.02.23,0,.57-.4,1.07-.98,1.18-.08.01-.15.02-.23.02-.57,0-1.07-.4-1.18-.98l-.27.05" />
            <path class="cls-4"
              d="m103.57,46.44l-.27.05c.16.84.9,1.42,1.72,1.42.11,0,.22,0,.33-.03.84-.16,1.43-.9,1.43-1.72,0-.11-.01-.22-.03-.33-.16-.84-.89-1.43-1.72-1.43-.11,0-.22.01-.33.03-.84.16-1.42.89-1.42,1.72,0,.11.01.22.03.33l.27-.05.27-.05c-.01-.08-.02-.15-.02-.23,0-.57.4-1.07.98-1.18.08-.02.15-.02.23-.02.57,0,1.07.4,1.18.98.02.08.02.15.02.23,0,.57-.4,1.07-.98,1.18-.08.01-.15.02-.23.02-.57,0-1.07-.4-1.18-.98l-.27.05" />
            <path class="cls-4"
              d="m106.48,45.89l-.27.05c.16.84.9,1.42,1.72,1.42.11,0,.22-.01.33-.03.84-.16,1.42-.9,1.42-1.72,0-.11-.01-.22-.03-.33-.16-.84-.89-1.42-1.72-1.42-.11,0-.22.01-.33.03-.84.16-1.42.89-1.42,1.72,0,.11.01.22.03.33l.27-.05.27-.05c-.02-.08-.02-.15-.02-.23,0-.57.4-1.07.98-1.18.08-.02.15-.02.23-.02.57,0,1.07.4,1.18.98.01.08.02.15.02.23,0,.57-.4,1.07-.98,1.18-.08.02-.15.02-.23.02-.57,0-1.07-.4-1.18-.98l-.27.05" />
            <path class="cls-4"
              d="m109.39,45.33l-.27.05c.16.84.9,1.42,1.72,1.42.11,0,.22-.01.33-.03.84-.16,1.42-.9,1.42-1.72,0-.11,0-.22-.03-.33-.16-.84-.9-1.43-1.72-1.43-.11,0-.22.01-.33.03-.84.16-1.42.89-1.42,1.72,0,.11.01.22.03.33l.27-.05.27-.05c-.02-.08-.02-.15-.02-.23,0-.57.4-1.07.98-1.18.08-.02.15-.02.23-.02.57,0,1.07.4,1.18.98.01.08.02.15.02.23,0,.57-.4,1.07-.98,1.18-.08.01-.15.02-.23.02-.57,0-1.07-.4-1.18-.98l-.27.05" />
            <path class="cls-4"
              d="m112.29,44.77l-.27.05c.16.84.9,1.42,1.72,1.42.11,0,.22-.01.33-.03.84-.16,1.43-.9,1.43-1.72,0-.11-.01-.22-.03-.33-.16-.84-.9-1.43-1.72-1.43-.11,0-.22.01-.33.03-.84.16-1.42.89-1.42,1.72,0,.11,0,.22.03.33l.27-.05.27-.05c-.01-.08-.02-.15-.02-.23,0-.57.4-1.07.98-1.18.08-.02.15-.02.23-.02.57,0,1.07.4,1.18.98.02.08.02.15.02.23,0,.57-.4,1.07-.98,1.18-.08.01-.15.02-.23.02-.57,0-1.07-.4-1.18-.98l-.27.05" />
            <path class="cls-4"
              d="m115.2,44.22l-.27.05c.16.84.9,1.42,1.72,1.42.11,0,.22-.01.33-.03.84-.16,1.42-.9,1.42-1.72,0-.11-.01-.22-.03-.33-.16-.84-.89-1.42-1.72-1.42-.11,0-.22.01-.33.03-.84.16-1.42.89-1.42,1.72,0,.11.01.22.03.33l.27-.05.27-.05c-.02-.08-.02-.15-.02-.23,0-.57.4-1.07.98-1.18.08-.02.15-.02.23-.02.57,0,1.07.4,1.18.98.02.08.02.15.02.23,0,.57-.4,1.07-.98,1.18-.08.01-.15.02-.23.02-.57,0-1.07-.4-1.18-.98l-.27.05" />
            <path class="cls-4"
              d="m91.58,25.91l.24.13-.19-.19s-.02.02-.05.06l.24.13-.19-.19.18.18-.15-.21-.03.03.18.18-.15-.21.09.12-.08-.12h0s.09.12.09.12l-.08-.12-.04.02c-.07.05-.19.14-.29.3-.1.16-.18.39-.18.68v.04c0,.33.14.65.33.97.29.47.73.94,1.21,1.35.48.41,1,.77,1.46,1.01.41.22.75.3,1.03.3.28,0,.49-.09.63-.19.14-.1.2-.2.21-.22l-.24-.14.21.18s.16-.2.16-.5c0-.32-.15-.73-.62-1.21-.11-.11-.28-.11-.39,0-.11.11-.11.28,0,.39.41.42.47.7.47.83,0,.07-.01.11-.02.13v.02s.1.08.1.08l-.1-.09h0s.11.09.11.09l-.1-.09-.03.05.1.05-.09-.06h0s.1.06.1.06l-.09-.06s-.03.04-.08.07c-.05.03-.14.07-.28.07-.17,0-.42-.05-.77-.24-.55-.29-1.24-.79-1.79-1.34-.27-.27-.51-.56-.67-.82-.16-.26-.25-.51-.25-.7v-.03c0-.24.07-.37.13-.44.03-.04.06-.06.08-.08l.03-.02h.02s.02-.03.02-.03l.02-.02s.03-.03.05-.06c.07-.13.02-.3-.11-.37-.13-.07-.3-.02-.37.11" />
            <text class="cls-1" transform="translate(136.35 20.88)">
              <tspan x="0" y="0">Love what you bought?</tspan>
            </text>
            <text class="cls-2" transform="translate(136.35 31.84)">
              <tspan x="0" y="0">Add a photo and review with the Etsy app.</tspan>
            </text>
            <text class="cls-2" transform="translate(136.35 41.75)">
              <tspan x="0" y="0">Visit etsy.com/mobile to download.</tspan>
            </text>
            <path class="cls-5"
              d="m341.1,16.48c-3.87,0-7,3.13-7,7,0,1.15.28,2.22.77,3.18l6.52-4.88.71.71-10,10,3,1,2.51-3.94c1.06.62,2.27.94,3.49.94,3.87,0,7-3.13,7-7v-7h-7" />
            <text class="cls-1" transform="translate(374.45 20.88)">
              <tspan x="0" y="0">Carbon-Offset Delivery</tspan>
            </text>
            <text class="cls-2" transform="translate(374.45 31.84)">
              <tspan x="0" y="0">Etsy offsets 100% of the carbon emissions</tspan>
            </text>
            <text class="cls-2" transform="translate(374.45 41.75)">
              <tspan x="0" y="0">from your delivery. Learn how at</tspan>
            </text>
            <text class="cls-2" transform="translate(374.45 51.67)">
              <tspan x="0" y="0">etsy.com/impact.</tspan>
            </text>
          </g>
        </svg>
      </div>
    </div>
  </div>
  <button onclick="window.print();">Print</button>
  <style type="text/css">
    body {
      font-size: 15px;
      position: relative;
      margin: 0;
      padding: 0;
    }

    html {
      margin: 0;
      padding: 0;
    }

    @page {
      size: A4;
      margin: 0;
    }

    .page {
      width: 210mm;
      height: 297mm;
      page-break-before: always;
    }

    .page-absolute {
      position: absolute;
      top: 0px;
      left: 0px;
      right: 0px;
    }

    * {
      box-sizing: border-box;
    }

    .wrapper {
      width: 831px;
      height: 1120px;
      margin: auto;
      padding: 3em;
      font-family: "Noto Sans", sans-serif;
      font-weight: 250;
    }

    .header {
      width: 100%;
      display: flex;
      flex-direction: row;
      align-items: center;
    }

    .main-content {
      display: flex;
      flex-direction: row;
      margin-top: 2em;
    }
  
    .header p {
      margin: 0;
    }

    .shop-title {
      margin-right: 12px;
    }

    .customer-addresses {
      flex: 1;
      display: inline-block;
      margin-right: 12px;
    }

    .address-detail {
      line-height: 1.2em;
      margin: 0;
    }

    .subtitle-bold {
      font-weight: bold;
      margin: 0;
      margin-top: 1em;
      font-size: 0.9em;
    }

    .text-align-right {
      text-align: right;
    }

    .shipping-address {
      float: left;
      min-width: 18em;
      max-width: 50%;
    }

    .billing-address {
      padding-left: 20em;
      min-width: 18em;
    }

    .order-container {
      flex: 3;
    }

    .order-container-header {
      width: 100%;
    }

    .flex-line-item {
      display: flex;
      flex-direction: row;
      align-items: flex-start;
      margin: 1em 0;
      page-break-inside: avoid;
    }

    .flex-line-item-img {
    }

    .flex-line-item-description {
      margin-left: 16px;
      flex: 1;
    }

    .line-item-description-line {
      display: block;
    }

    .flex-line-item-quantity {
    }

    hr {
      height: 0.14em;
      border: none;
      color: black;
      background-color: black;
      margin: 0;
    }

    .aspect-ratio {
      position: relative;
      display: block;
      background: #fafbfc;
      padding: 0;
    }

    .aspect-ratio::before {
      z-index: 1;
      content: "";
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      border: 1px solid rgba(195, 207, 216, 0.3);
    }

    .aspect-ratio--square {
      width: 100%;
      padding-bottom: 100%;
    }

    .aspect-ratio__content {
      position: absolute;
      max-width: 100%;
      max-height: 100%;
      display: block;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      margin: auto;
    }

    button {
      display: inline-block;
      outline: none;
      background-color: transparent;
      border-radius: 10px;
      width: 150px;
      height: 40px;
      border: 1px solid black;
      text-align: center;
      position: absolute;
      right: 20px;
      top: 20px;
    }

    .bottom-footer {
      position: absolute;
      top: calc(70% + 20px);
      left: 0px;
      right: 0px;
    }

    .shop-logo {
      width: 90px;
      height: 90px;
      object-fit: contain;
    }
    
    .line {
      width: 100%;
      margin-top: 1em;
    }

    .summary-container {
      display: flex;
      flex-direction: row;
    }

    .footer-absolute {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      width: 831px;
      margin: auto;
      padding-left: 3em;
      padding-right: 3em;
    }

    @media print {
      button {
        display: none;
      }

      html,
      body {
        height: auto
      }
    }
  </style>
  <script>
    function elementsOverlap(el1, el2) {
      const domRect1 = el1.getBoundingClientRect();
      const domRect2 = el2.getBoundingClientRect();

      return !(
        domRect1.top > domRect2.bottom ||
        domRect1.right < domRect2.left ||
        domRect1.bottom < domRect2.top ||
        domRect1.left > domRect2.right
      );
    }
    const pages = document.querySelectorAll('.page');
    const content = document.querySelector('.wrapper');
    for (let i = pages.length - 1; i >= 0; i--) {
      if (!elementsOverlap(pages[i], content)) {
        pages[i].remove();
      }
    }
    if (window.matchMedia("print").matches) {
    }
  </script>
</body>
</html>
`

export const LIQUID_TEMPLATE_ETSY_GIFT = `
<html>

<head>
  <meta charset="UTF-8">
  <script src="https://cdnjs.cloudflare.com/ajax/libs/qrious/4.0.2/qrious.min.js"></script>
</head>

<body>
  <div class="page"></div>
  <div class="page"></div>
  <div class="page"></div>
  <div class="page-absolute">
    <div class="wrapper">
      <div style="flex: 1;">

      </div>
      <div style="flex: 1.18; position: relative;">
        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="Layer_1"
          viewBox="0 0 566.12 412.59">
          <defs>
            <style>
              .cls-1 {
                fill: none;
              }

              .cls-2 {
                clip-path: url(#clippath-1);
              }

              .cls-3 {
                clip-path: url(#clippath-3);
              }

              .cls-4 {
                clip-path: url(#clippath-4);
              }

              .cls-5 {
                clip-path: url(#clippath-2);
              }

              .cls-6 {
                clip-path: url(#clippath-7);
              }

              .cls-7 {
                clip-path: url(#clippath-6);
              }

              .cls-8 {
                clip-path: url(#clippath-5);
              }

              .cls-9 {
                fill: #9e9e9e;
              }

              .cls-10 {
                clip-path: url(#clippath);
              }
            </style>
            <clipPath id="clippath">
              <path class="cls-1"
                d="M9.51,61.62h41.91c.6,0,1.1.5,1.1,1.1v41.91c0,.61-.5,1.1-1.1,1.1H9.51c-.61,0-1.1-.49-1.1-1.1v-41.91c0-.6.49-1.1,1.1-1.1" />
            </clipPath>
            <clipPath id="clippath-1">
              <rect class="cls-1" x="8.41" y="61.62" width="44.11" height="44.11" />
            </clipPath>
            <clipPath id="clippath-2">
              <path class="cls-1"
                d="M283.86,175.35v-3.58h.27v3.58h-.27M283.86,168.74v-3.58h.27v3.58h-.27M283.86,162.12v-3.58h.27v3.58h-.27M283.86,155.5v-3.58h.27v3.58h-.27M283.86,148.88v-3.58h.27v3.58h-.27M283.86,142.26v-3.58h.27v3.58h-.27M283.86,135.65v-3.58h.27v3.58h-.27M283.86,129.03v-3.58h.27v3.58h-.27M283.86,122.41v-3.58h.27v3.58h-.27M283.86,115.79v-3.58h.27v3.58h-.27M283.86,109.17v-3.58h.27v3.58h-.27M205.16,50.72v-.27h3.58v.27h-3.58M211.78,50.72v-.27h3.58v.27h-3.58M218.4,50.72v-.27h3.58v.27h-3.58M225.01,50.72v-.27h3.58v.27h-3.58M231.63,50.72v-.27h3.58v.27h-3.58M238.25,50.72v-.27h3.58v.27h-3.58M244.87,50.72v-.27h3.58v.27h-3.58M251.49,50.72v-.27h3.58v.27h-3.58M258.1,50.72v-.27h3.58v.27h-3.58M264.72,50.72v-.27h3.58v.27h-3.58M271.34,50.72v-.27h3.58v.27h-3.58M277.96,50.72v-.27h3.58v.27h-3.58M284.58,50.72v-.27h3.58v.27h-3.58M291.19,50.72v-.27h3.58v.27h-3.58M297.81,50.72v-.27h3.58v.27h-3.58M304.43,50.72v-.27h3.58v.27h-3.58M311.05,50.72v-.27h3.58v.27h-3.58M317.67,50.72v-.27h3.58v.27h-3.58M324.29,50.72v-.27h3.58v.27h-3.58M330.9,50.72v-.27h3.58v.27h-3.58M337.52,50.72v-.27h3.58v.27h-3.58M344.14,50.72v-.27h3.58v.27h-3.58M350.76,50.72v-.27h3.58v.27h-3.58M357.38,50.72v-.27h3.58v.27h-3.58M363.99,50.72v-.27h3.58v.27h-3.58M370.61,50.72v-.27h3.58v.27h-3.58M377.23,50.72v-.27h3.58v.27h-3.58M383.85,50.72v-.27h3.58v.27h-3.58M390.47,50.72v-.27h3.58v.27h-3.58M397.08,50.72v-.27h3.58v.27h-3.58M403.7,50.72v-.27h3.58v.27h-3.58M410.32,50.72v-.27h3.58v.27h-3.58M416.94,50.72v-.27h3.58v.27h-3.58M423.56,50.72v-.27h3.58v.27h-3.58M430.17,50.72v-.27h3.58v.27h-3.58M436.79,50.72v-.27h3.58v.27h-3.58M443.41,50.72v-.27h3.58v.27h-3.58M450.03,50.72v-.27h3.58v.27h-3.58M456.65,50.72v-.27h3.58v.27h-3.58M463.26,50.72v-.27h3.58v.27h-3.58M469.88,50.72v-.27h3.58v.27h-3.58M476.5,50.72v-.27h3.58v.27h-3.58M483.12,50.72v-.27h3.58v.27h-3.58M489.74,50.72v-.27h3.58v.27h-3.58M496.36,50.72v-.27h3.58v.27h-3.58M502.97,50.72v-.27h3.58v.27h-3.58M509.59,50.72v-.27h3.58v.27h-3.58M516.21,50.72v-.27h3.58v.27h-3.58M522.83,50.72v-.27h3.58v.27h-3.58M529.45,50.72v-.27h3.58v.27h-3.58M536.06,50.72v-.27h3.58v.27h-3.58M542.68,50.72v-.27h3.58v.27h-3.58M549.3,50.72v-.27h3.58v.27h-3.58M555.92,50.72v-.27h3.58v.27h-3.58M559.71.41H201.28v50.04h.85v.27h-.85v128.9h82.58v-1.23h.27v1.23h275.58V.41" />
            </clipPath>
            <clipPath id="clippath-3">
              <path class="cls-1"
                d="M202.12,50.45h-.85v.27h.85v-.27M208.74,50.45h-3.58v.27h3.58v-.27M215.36,50.45h-3.58v.27h3.58v-.27M221.98,50.45h-3.58v.27h3.58v-.27M228.6,50.45h-3.58v.27h3.58v-.27M235.21,50.45h-3.58v.27h3.58v-.27M241.83,50.45h-3.58v.27h3.58v-.27M248.45,50.45h-3.58v.27h3.58v-.27M255.07,50.45h-3.58v.27h3.58v-.27M261.69,50.45h-3.58v.27h3.58v-.27M268.3,50.45h-3.58v.27h3.58v-.27M274.92,50.45h-3.58v.27h3.58v-.27M281.54,50.45h-3.58v.27h3.58v-.27M288.16,50.45h-3.58v.27h3.58v-.27M294.78,50.45h-3.58v.27h3.58v-.27M301.39,50.45h-3.58v.27h3.58v-.27M308.01,50.45h-3.58v.27h3.58v-.27M314.63,50.45h-3.58v.27h3.58v-.27M321.25,50.45h-3.58v.27h3.58v-.27M327.87,50.45h-3.58v.27h3.58v-.27M334.48,50.45h-3.58v.27h3.58v-.27M341.1,50.45h-3.58v.27h3.58v-.27M347.72,50.45h-3.58v.27h3.58v-.27M354.34,50.45h-3.58v.27h3.58v-.27M360.96,50.45h-3.58v.27h3.58v-.27M367.57,50.45h-3.58v.27h3.58v-.27M374.19,50.45h-3.58v.27h3.58v-.27M380.81,50.45h-3.58v.27h3.58v-.27M387.43,50.45h-3.58v.27h3.58v-.27M394.05,50.45h-3.58v.27h3.58v-.27M400.67,50.45h-3.58v.27h3.58v-.27M407.28,50.45h-3.58v.27h3.58v-.27M413.9,50.45h-3.58v.27h3.58v-.27M420.52,50.45h-3.58v.27h3.58v-.27M427.14,50.45h-3.58v.27h3.58v-.27M433.76,50.45h-3.58v.27h3.58v-.27M440.37,50.45h-3.58v.27h3.58v-.27M446.99,50.45h-3.58v.27h3.58v-.27M453.61,50.45h-3.58v.27h3.58v-.27M460.23,50.45h-3.58v.27h3.58v-.27M466.85,50.45h-3.58v.27h3.58v-.27M473.46,50.45h-3.58v.27h3.58v-.27M480.08,50.45h-3.58v.27h3.58v-.27M486.7,50.45h-3.58v.27h3.58v-.27M493.32,50.45h-3.58v.27h3.58v-.27M499.94,50.45h-3.58v.27h3.58v-.27M506.55,50.45h-3.58v.27h3.58v-.27M513.17,50.45h-3.58v.27h3.58v-.27M519.79,50.45h-3.58v.27h3.58v-.27M526.41,50.45h-3.58v.27h3.58v-.27M533.03,50.45h-3.58v.27h3.58v-.27M539.64,50.45h-3.58v.27h3.58v-.27M546.26,50.45h-3.58v.27h3.58v-.27M552.88,50.45h-3.58v.27h3.58v-.27M559.5,50.45h-3.58v.27h3.58v-.27" />
            </clipPath>
            <clipPath id="clippath-4">
              <path class="cls-1"
                d="M284.13,178.39h-.27v1.23h.27v-1.23M284.13,171.77h-.27v3.58h.27v-3.58M284.13,165.15h-.27v3.58h.27v-3.58M284.13,158.54h-.27v3.58h.27v-3.58M284.13,151.92h-.27v3.58h.27v-3.58M284.13,145.3h-.27v3.58h.27v-3.58M284.13,138.68h-.27v3.58h.27v-3.58M284.13,132.06h-.27v3.58h.27v-3.58M284.13,125.45h-.27v3.58h.27v-3.58M284.13,118.83h-.27v3.58h.27v-3.58M284.13,112.21h-.27v3.58h.27v-3.58M284.13,105.59h-.27v3.58h.27v-3.58" />
            </clipPath>
            <clipPath id="clippath-5">
              <rect class="cls-1" x="419.03" y="69.89" width="27.58" height="13.78" />
            </clipPath>
            <clipPath id="clippath-6">
              <rect class="cls-1" x="419.03" y="69.89" width="27.58" height="13.78" />
            </clipPath>
            <clipPath id="clippath-7">
              <polyline class="cls-1" points="457.62 370.98 416.26 370.98 416.26 412.33 457.62 412.33 457.62 370.98" />
            </clipPath>
          </defs>
          <path class="cls-9"
            d="M562.54,50.45h3.58v.27h-3.58v-.27M555.92,50.45h3.58v.27h-3.58v-.27M549.3,50.45h3.58v.27h-3.58v-.27M542.68,50.45h3.58v.27h-3.58v-.27M536.06,50.45h3.58v.27h-3.58v-.27M529.45,50.45h3.58v.27h-3.58v-.27M522.83,50.45h3.58v.27h-3.58v-.27M516.21,50.45h3.58v.27h-3.58v-.27M509.59,50.45h3.58v.27h-3.58v-.27M502.97,50.45h3.58v.27h-3.58v-.27M496.36,50.45h3.58v.27h-3.58v-.27M489.74,50.45h3.58v.27h-3.58v-.27M483.12,50.45h3.58v.27h-3.58v-.27M476.5,50.45h3.58v.27h-3.58v-.27M469.88,50.45h3.58v.27h-3.58v-.27M463.26,50.45h3.58v.27h-3.58v-.27M456.65,50.45h3.58v.27h-3.58v-.27M450.03,50.45h3.58v.27h-3.58v-.27M443.41,50.45h3.58v.27h-3.58v-.27M436.79,50.45h3.58v.27h-3.58v-.27M430.17,50.45h3.58v.27h-3.58v-.27M423.56,50.45h3.58v.27h-3.58v-.27M416.94,50.45h3.58v.27h-3.58v-.27M410.32,50.45h3.58v.27h-3.58v-.27M403.7,50.45h3.58v.27h-3.58v-.27M397.08,50.45h3.58v.27h-3.58v-.27M390.47,50.45h3.58v.27h-3.58v-.27M383.85,50.45h3.58v.27h-3.58v-.27M377.23,50.45h3.58v.27h-3.58v-.27M370.61,50.45h3.58v.27h-3.58v-.27M363.99,50.45h3.58v.27h-3.58v-.27M357.38,50.45h3.58v.27h-3.58v-.27M350.76,50.45h3.58v.27h-3.58v-.27M344.14,50.45h3.58v.27h-3.58v-.27M337.52,50.45h3.58v.27h-3.58v-.27M330.9,50.45h3.58v.27h-3.58v-.27M324.29,50.45h3.58v.27h-3.58v-.27M317.67,50.45h3.58v.27h-3.58v-.27M311.05,50.45h3.58v.27h-3.58v-.27M304.43,50.45h3.58v.27h-3.58v-.27M297.81,50.45h3.58v.27h-3.58v-.27M291.19,50.45h3.58v.27h-3.58v-.27M284.58,50.45h3.58v.27h-3.58v-.27M277.96,50.45h3.58v.27h-3.58v-.27M271.34,50.45h3.58v.27h-3.58v-.27M264.72,50.45h3.58v.27h-3.58v-.27M258.1,50.45h3.58v.27h-3.58v-.27M251.49,50.45h3.58v.27h-3.58v-.27M244.87,50.45h3.58v.27h-3.58v-.27M238.25,50.45h3.58v.27h-3.58v-.27M231.63,50.45h3.58v.27h-3.58v-.27M225.01,50.45h3.58v.27h-3.58v-.27M218.4,50.45h3.58v.27h-3.58v-.27M211.78,50.45h3.58v.27h-3.58v-.27M205.16,50.45h3.58v.27h-3.58v-.27M198.54,50.45h3.58v.27h-3.58v-.27M191.92,50.45h3.58v.27h-3.58v-.27M185.31,50.45h3.58v.27h-3.58v-.27M178.69,50.45h3.58v.27h-3.58v-.27M172.07,50.45h3.58v.27h-3.58v-.27M165.45,50.45h3.58v.27h-3.58v-.27M158.83,50.45h3.58v.27h-3.58v-.27M152.22,50.45h3.58v.27h-3.58v-.27M145.6,50.45h3.58v.27h-3.58v-.27M138.98,50.45h3.58v.27h-3.58v-.27M132.36,50.45h3.58v.27h-3.58v-.27M125.74,50.45h3.58v.27h-3.58v-.27M119.13,50.45h3.58v.27h-3.58v-.27M112.51,50.45h3.58v.27h-3.58v-.27M105.89,50.45h3.58v.27h-3.58v-.27M99.27,50.45h3.58v.27h-3.58v-.27M92.65,50.45h3.58v.27h-3.58v-.27M86.03,50.45h3.58v.27h-3.58v-.27M79.42,50.45h3.58v.27h-3.58v-.27M72.8,50.45h3.58v.27h-3.58v-.27M66.18,50.45h3.58v.27h-3.58v-.27M59.56,50.45h3.58v.27h-3.58v-.27M52.94,50.45h3.58v.27h-3.58v-.27M46.33,50.45h3.58v.27h-3.58v-.27M39.71,50.45h3.58v.27h-3.58v-.27M33.09,50.45h3.58v.27h-3.58v-.27M26.47,50.45h3.58v.27h-3.58v-.27M19.85,50.45h3.58v.27h-3.58v-.27M13.24,50.45h3.58v.27h-3.58v-.27M6.62,50.45h3.58v.27h-3.58v-.27M0,50.45h3.58v.27H0v-.27" />
          <path class="cls-9"
            d="M284.13,376.93v3.58h-.27v-3.58h.27M284.13,370.31v3.58h-.27v-3.58h.27M284.13,363.7v3.58h-.27v-3.58h.27M284.13,357.08v3.58h-.27v-3.58h.27M284.13,350.46v3.58h-.27v-3.58h.27M284.13,343.84v3.58h-.27v-3.58h.27M284.13,337.22v3.58h-.27v-3.58h.27M284.13,330.61v3.58h-.27v-3.58h.27M284.13,323.99v3.58h-.27v-3.58h.27M284.13,317.37v3.58h-.27v-3.58h.27M284.13,310.75v3.58h-.27v-3.58h.27M284.13,304.13v3.58h-.27v-3.58h.27M284.13,297.52v3.58h-.27v-3.58h.27M284.13,290.9v3.58h-.27v-3.58h.27M284.13,284.28v3.58h-.27v-3.58h.27M284.13,277.66v3.58h-.27v-3.58h.27M284.13,271.04v3.58h-.27v-3.58h.27M284.13,264.43v3.58h-.27v-3.58h.27M284.13,257.81v3.58h-.27v-3.58h.27M284.13,251.19v3.58h-.27v-3.58h.27M284.13,244.57v3.58h-.27v-3.58h.27M284.13,237.95v3.58h-.27v-3.58h.27M284.13,231.34v3.58h-.27v-3.58h.27M284.13,224.72v3.58h-.27v-3.58h.27M284.13,218.1v3.58h-.27v-3.58h.27M284.13,211.48v3.58h-.27v-3.58h.27M284.13,204.86v3.58h-.27v-3.58h.27M284.13,198.25v3.58h-.27v-3.58h.27M284.13,191.63v3.58h-.27v-3.58h.27M284.13,185.01v3.58h-.27v-3.58h.27M284.13,178.39v3.58h-.27v-3.58h.27M284.13,171.77v3.58h-.27v-3.58h.27M284.13,165.15v3.58h-.27v-3.58h.27M284.13,158.54v3.58h-.27v-3.58h.27M284.13,151.92v3.58h-.27v-3.58h.27M284.13,145.3v3.58h-.27v-3.58h.27M284.13,138.68v3.58h-.27v-3.58h.27M284.13,132.06v3.58h-.27v-3.58h.27M284.13,125.45v3.58h-.27v-3.58h.27M284.13,118.83v3.58h-.27v-3.58h.27M284.13,112.21v3.58h-.27v-3.58h.27M284.13,105.59v3.58h-.27v-3.58h.27" />
          <g class="cls-5">
            <image width="822" height="884" transform="translate(200.88) scale(.2)"
              xlink:href="data:image/png;base64,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" />
            <image width="821" height="884" transform="translate(364.07) scale(.2)"
              xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAzUAAAOICAYAAADvuN9HAAAACXBIWXMAADerAAA3qwGYrfpiAAAgAElEQVR4nOzdeZBm60Hf999z3l5n3+6+aRfSlWQJhAAjQCBxibHYbDAx2ElICtsBXK6EhGx/pCqkkkpSqVRIypVUUQa7XFjBgJAwBiFLaAHtO0gXXeleLXe/c2fv6f09T/54u2d6erpn6+mZefp+PlVTM/2+Z845rbnqOt/3PM9zSq21BgAAoEGllNLd7JMAAADYClEDAAA0TdQAAABNEzUAAEDTRA0AANA0UQMAADRN1AAAAE0TNQAAQNNEDQAA0DRRAwAANE3UAAAATRM1AABA00QNAADQNFEDAAA0TdQAAABNEzUAAEDTRA0AANA0UQMAADRN1AAAAE0TNQAAQNNEDQAA0DRRAwAANE3UAAAATRM1AABA00QNAADQNFEDAAA0TdQAAABNEzUAAEDTRA0AANA0UQMAADRN1AAAAE0TNQAAQNNEDQAA0DRRAwAANE3UAAAATRM1AABA00QNAADQNFEDAAA0TdQAAABNEzUAAEDTRA0AANA0UQMAADRN1AAAAE0TNQAAQNNEDQAA0DRRAwAANE3UAAAATRM1AABA00QNAADQNFEDAAA0TdQAAABNEzUAAEDTRA0AANA0UQMAADRN1AAAAE0TNQAAQNNEDQAA0DRRAwAANE3UAAAATRM1AABA00QNAADQNFEDAAA0TdQAAABNEzUAAEDTRA0AANA0UQMAADRN1AAAAE0TNQAAQNNEDQAA0DRRAwAANE3UAAAATRM1AABA00QNAADQNFEDAAA0TdQAAABNEzUAAEDTRA0AANA0UQMAADRN1AAAAE0TNQAAQNNEDQAA0DRRAwAANE3UAAAATRM1AABA00QNAADQNFEDAAA0TdQAAABNEzUAAEDTRA0AANA0UQMAADRN1AAAAE0TNQAAQNNEDQAA0DRRAwAANE3UAAAATRM1AABA00QNAADQNFEDAAA0TdQAAABNEzUAAEDTRA0AANA0UQMAADRN1AAAAE0TNQAAQNNEDQAA0DRRAwAANE3UAAAATRM1AABA00QNAADQNFEDAAA0TdQAAABNEzUAAEDTRA0AANA0UQMAADRN1AAAAE0TNQAAQNNEDQAA0DRRAwAANE3UAAAATRM1AABA00QNAADQNFEDAAA0TdQAAABNEzUAAEDTRA0AANA0UQMAADRN1AAAAE0TNQAAQNNEDQAA0DRRAwAANE3UAAAATRM1AABA00QNAADQNFEDAAA0TdQAAABNEzUAAEDTRA0AANA0UQMAADRN1AAAAE0TNQAAQNNEDQAA0DRRAwAANE3UAAAATRM1AABA00QNAADQNFEDAAA0TdQAAABNEzUAAEDTRA0AANA0UQMAADRN1AAAAE0TNQAAQNNEDQAA0DRRAwAANE3UAAAATRM1AABA00QNAADQNFEDAAA0TdQAAABNEzUAAEDTRA0AANA0UQMAADRN1AAAAE0TNQAAQNNEDQAA0DRRAwAANE3UAAAATRM1AABA00QNAADQNFEDAAA0TdQAAABNEzUAAEDTRA0AANA0UQMAADRN1AAAAE0TNQAAQNNEDQAA0DRRAwAANE3UAAAATRM1AABA00QNAADQNFEDAAA0TdQAAABNEzUAAEDTRA0AANA0UQMAADRN1AAAAE0TNQAAQNNEDQAA0DRRAwAANE3UAAAATRM1AABA00QNAADQNFEDAAA0TdQAAABNEzUAAEDTRA0AANA0UQMAADRN1AAAAE0TNQAAQNNEDQAA0DRRAwAANE3UAAAATRM1AABA00QNAADQNFEDAAA0TdQAAABNEzUAAEDTRA0AANA0UQMAADRN1AAAAE0TNQAAQNNEDQAA0DRRAwAANE3UAAAATRM1AABA00QNAADQNFEDAAA0TdQAAABNEzUAAEDTRA0AANA0UQMAADRN1AAAAE0TNQAAQNNEDQAA0DRRAwAANE3UAAAATRM1AABA00QNAADQNFEDAAA0TdQAAABNEzUAAEDTRA0AANA0UQMAADRN1AAAAE0TNQAAQNNEDQAA0DRRAwAANE3UAAAATRM1AABA00QNAADQNFEDAAA0TdQAAABNEzUAAEDTRA0AANA0UQMAADRN1AAAAE0TNQAAQNNEDQAA0DRRAwAANE3UAAAATRM1AABA00QNAADQNFEDAAA0TdQAAABNEzUAAEDTRA0AANA0UQMAADRN1AAAAE0TNQAAQNNEDQAA0DRRAwAANE3UAAAATRM1AABA00QNAADQNFEDAAA0TdQAAABNEzUAAEDTRA0AANA0UQMAADRN1AAAAE0TNQAAQNNEDQAA0DRRAwAANE3UAAAATRM1AABA00QNAADQNFEDAAA0TdQAAABNEzUAAEDTRA0AANA0UQMAADRN1AAAAE0TNQAAQNNEDQAA0DRRAwAANE3UAAAATRM1AABA00QNAADQNFEDAAA0TdQAAABNEzUAAEDTRA0AANA0UQMAADRN1AAAAE0TNQAAQNNEDQAA0DRRAwAANE3UAAAATRM1AABA00QNAADQNFEDAAA0TdQAAABNEzUAAEDTRA0AANA0UQMAADRN1AAAAE0TNQAAQNNEDQAA0DRRAwAANE3UAAAATRM1AABA00QNAADQNFEDAAA0TdQAAABNEzUAAEDTRA0AANA0UQMAADRN1AAAAE0TNQAAQNNEDQAA0DRRAwAANE3UAAAATRM1AABA00QNAADQNFEDAAA0TdQAAABNEzUAAEDTRA0AANA0UQMAADRN1AAAAE0TNQAAQNNEDQAA0DRRAwAANE3UAAAATRM1AABA00QNAADQNFEDAAA0TdQAAABNG7vZJwAAwAvYyQ+mX3gy3f43J1P3JBnc7DOiQaXWWm/2SQAA8AI0+2iGT/xqMjyVjN2bMv1gusNvTSbuSgZ7bvbZ0YhSSnGnBgCAm6I/+d6knxl9sfxE6pknMpz9WDL9ugz2fUey+8FksC8pZkxwae7UAABwgw3TP/1bqafendTlTbcqky9N2fumlD3fnkzfH0PT2EgpRfYCAHCD1WHq8vO5cM2qiy9L68Kj6Z//Vxl+/b9PTn3ihp0e7RE1AADcOLVPPf7e5Mwn192l6Tf/O1P3J7teue2nRrvMqQEA4MYpNZn9UlLmrnD7sZRdr03GD23vedE0d2oAALhh6ulPpp/57JpXNrocHZx/ffzedAe+8wacGS0TNQAA3Bj9fOqpjydZWPviuo0GSeq518vUq0ZLPMMlGH4GAMCNMfvl1NmPZvP5M12S4fkvB7el7P6WpEzcgJOjZe7UAABwAwzTn/nYusUB1l+KroudyVemHPje7T4xdgBRAwDwQrHwjfRP/tP0i49nNMTrBpr9SurM55O+rLzQ5ZIrniUpU3du+2mxMxh+BgDwQrB0PPXo76Se+bOUxW9mmD0pux5Id/AtycR923vs/myGR/8gWX42KashU3LJsBm7Ld3eN27vebFjiBoAgJ2un0//3DtSZz6VJKnzXx79vvD59DNfSjnyw8mub0kZv217jr94LDn76TVBk1wwd2YDZdfrk+mXbc/5sOOIGgCAHa4ee3fqmQ8mdXHdG8upi3+V+tSTyfSrMjj4Pcm+b0/K5PU7eD+f/th7krJ0iY1WZ0SsrHg2/tJ0d/ydpAyu33mwo4kaAIAdrJ79Yvrjf3Bx0FxgNpn7dIbzn085+5Z0h34ombo3yfjWT2A4mzrzwVxu/sza9+u+b0/Gjmz92LxgiBoAgJ2o1mT+q+mf/N+SfuYyG68MBavD1FPvzfDUR1IOvC3dHX8r6fZs5SRSn/2NpM5fYptBLhiKNnZ7BnvfsIVj8kIkagAAdqL+bIbP/GYyPHWJjdYuhNuv+f1M6sl3ZnjmAylTr0s58raUXa+5+nOY/XL6059KymbzZ9YtxFsGo7k0k/df/bF4QRM1AAA7TF0+m/r0v0gWHrnMln0u+YSP4YnU2Y+kfuOz6Y78zZSDP5B0e5Pu8nNuar+Q/tnfTsrauzRrVzvrktJd8NyaMvUt6Y78WNJNXXb/sJao2ZFqRsskAgAvOHWYnP5w6uxH1j3ocjMbzXVZEx91KclS6vF3pZ79UspgKuXQ25Ndr0zK5nNuyvyjydzDay5J1t+V6UZD5FaP1Q+SA29JJu++gnOGC4manaBfSBafSZaOpS48nvRLKbf/5M0+KwDgZqjzydnPJXUhKWNXGDbrrd7BWTN5v59N5j6fmi6Z++pozs2RH026XRf/9eGZ9M+9Y81dmi6julkzDG1Yk66eO0a391tTdptLw7URNa2py6kLTyRLz6fOPpa6+M2U5eOpS88m/dmkLqWMvzjlth+5vssxAgBt6Hal3P7vpzx1JnXxG0mdSBmU1OHZq9zRJVYr60+mHvvXGS4+l8HtfzeZuPD5NvXMZ1Jn196l2WAEyZqgSZlIOfQjyfjhqzzHzdTRam9lYs2xazKcGQ2dKxPX6TjcKkRNY4aP/1oy/6WU/kzqytKMNcnaT1Pq8Ink7MPJntffrNMEAG6akky+KN39/3Uy91hqP5f088nz/yZ16Wu5/NLKm1m91uhXLj66ZObDGc49lu7A96ccemsy2JM6+0j65347KWvvENUN9rfmPCZflTp19xYHz/dJXUp/5rPJzJdTlx5Pmbg7ZfzwaFW3/mzK8lzKbT9xUYTRPlHTktqn2/em1IUvngua89b8YOiX0888nE7UAMAL12BvsuevnQuFsuulqc/+dvqZzySZvYYd9kkdX1nJbDVu+mT5G+mP/VZKfzbd3m9N/+y/SoZPXfx3N1OnMjj8N5Kxlbs0w5mkn0ldfDYpUykTB9PPP5mSLmXsYOrYvpTlU8nyTNKfST88m7J8PJl9JLU/m7rwldGQuzKROvu51FJW5u4Mk0M/lTLh+Tc70SWWuwAAALj1uVPTktKl7Htj+lMfSJaPb/D+YLTiSYbJ7OeS4U8kA0siAgBJJu5LueeXMlh6PvXp30y/8PBoPu7VKMOklKReuIhA6mLqiT9JXfhaMvvFNR+br/7hEndq9rw+dfxIcvJDqfNPpix+Pf3iM6NzK2Mr1zZLqZMvTvrFpIynLM+kLp8YzZvJzIaD27I6quXcm13KngdjhdidSdS0pp9JmbgndfbT694oSd+de7hVHS6MfhiIGgBgVTeZTN6Tcs9/mjL/aOrz70wWvzaKhQ3DY7Dy++qqZf1K0JSV99ZM9q+n0p/91LpxQCUbz6c5d0Lp+j79k/93sjwasnbxEPsVs58798dL7XFTg/2Wi97BRE1j6vJM6plPZP0yi0m94Gm9pS5d4xKO13pic0mZvnHHAwCu3fihdOOHkun70z/37tSZTyT9zMrdjTXXFyWj58d0WbljkjXvrz48c91SzRdYEz0b6tPPf2Ybr1lWA6ykm3xxzkcaO405NY0p44eTibtWPiXJ6LbsOWvWki9L697bHnX+68mJ96Z/6l+ufMoDADRj7I50d/x0usM/ljJ2bzJ2+8ojIVauM+owKYvnVzu7QL8SI5e6b3IFK61dcdB0a85hkPOB0m3w/gUHSLKcOnY4ZezgFR6L1rhT05rBrpTB/tTVZRL7saSsrOix1vD06BOXXK/13tdZejb90X+bzH4s/fKJ0VKJ+741Ze+3bc/xAIDtMdiXcuih5MD3pVt4MsOjf5jMfiQXjgpZDZf1I0WywddbtfrBbTdatWx194OVuy51uHI+6+fGbHQe5+8qlckHNvg77BSipjkrt3rPfbm8yQckNf2JP0t35/25rv8HHp5JnfmL5Ph7U+e/kAvuDp35tKgBgBaViZTBRLLrWzK4967UYw+kP/mepD+xEhHXO1zWWo2Y1aWXV4az1Qvfvnj4Wzb5eqNDTCeWct7RRE1zSsrUS1PPvH/0ZV3OZrda69m/GK2SNrbFuzX92WTxmfQnPpbMfSF14fEkc+s2Gozm+iz9aDJ+59aOBwDcPIP9KUd+JINdL0s98b70cw8nwxPZvrBZXWhgk9evSZf0JelWQqhMJBOuT3YyUdOiXffnwtu/G/yfvg5HD68azlxj1NTUpaOpi0+nPvOOZOmx80sjXqRbOYUTycznk4N+aABA07rJZM/rU6ZfkcHpP8vwud9L+qPZ3js2W7XuQ95uTSWVqRQrn+1ooqZBZeolydgdyfLTl9iqv2A1tKsyPJV64sOpp/4kdflU0p/eZMM1a8+v/LE//p50B942emYOANC2wa7kwNsy2PXy9Mc+lMx8IHV48maf1TqrH/RuHlzd+H2x8tnOJmpaVMaSiXvXRM1Gk/aSLJ9KHc5f3Yyafj790T9MPf3e5LI/tC4+Zl16Kpl/NJl+xdUcFQC4FS0/n3rqY6lLzyf9mdS6dLPPaAOXu3s0SJ16kSUCdjhR06Kl08nCU9k0ZlZfH78tWT5xZfusy+lPfjA59YHR04Cv9gnD5/azkP7UJ9OJGgBoUz+XzD2WfvGZ1OPvTRa/fLPP6BqsLj6wNxnsTdn9kgsXWmLHETUtGtszWiHkUnNqkmT4XMrl/on7+dTj708988HU+Ueu8YTWDENLUk99OPXI21PG9l/j/gCAG6ouJv1c6pnPp558X+r8l5O6sMGGaz9Q3ezD1WtRMlopYHWCf03KeMrEfanDsynddOrgQMrKKJLaL44WNOhnk7G9ydiR1AxTlk+klOlk10tSdj2YTL8oqX1Sxq/TeXKrEjUt6ibSTd2ffuaJlRcGufA5NSs/YPrl9DNfTLfvjRfvow6Tha+lf/KfpS4+fBUHH+T82vCrTwleOV7ZNZpLM3FvUueSiBoAuGXV5dThTMrcY+nP/mXq/DeShcdXFgTYSDd6+Hfpc12e394Pkqn7kuUzSZlK+mdTxu4bfXA79eJksD/Z87oMJu8bPZNv9fKjDJJu3+g8ls+mZjGl27vmNKe2fm40R9S06KIlD9ePEl07Ye7ip/TWM59Of/KjKbMfS71gmNnaH1DrP3np1h1nTUSVsZSxB9Id/qHUqZekTL3YLV4AuEXV4UzK/NfTz3whdfaRZPGppD+WzYe0J+euK8plRolcjS4pdZDseVO6/d+dOkjKxP1Jv5iMHVgZlbL6a5MdjO03V4YkomabrXySUYfXdzWw0qUODq47zvrjrhibTjJMlk4ly8cyPPnh5PT7k/7sBs/s3Ghp6KlkMEj6+Zy/M7NGtzuDIz+T7P22ZPxIilXPAOCW1h/7w5RT70kdnll53t1mLhxefv0NUyZuSyYOJ3tenbKaJy4luAaiZrv0c+mf/PVk/EDSz6bsfk3STaRkkH75ZMr44ZTJe5PBntFa8Fepjq25zbrpD5tRVPXPviP19J8n/Zmkzl/mB9iqMhp/WrqVoFm3PPTYfekOvCXlwHcl43dd9fkDADfY0rH0x/84Of47G3ywueoyz8G7Juvn3qx+3aVfeDRlsPd80MA1EjXbZemZZO4zqTMrE9pO/rukLI/GefZLo8lsEw+MVuWYvCdlz2tT9rwmWXhudMt14rbRfuri6Cm46wymX5FhmdxkEt+qLvX4H49WMSlrH5y5Oh9m478z+kEzGN1hynIu+EHU7U6366+l3PMLSbf7Sv/XAABuorr0bPL0b6ae/cQNPnI3ehRFXXs9seb35aOp80+MPkA1F4YtEDXbZf6Z1OWZ88NAy8rdkX7+3CZ18RujPyz8ZeqZ9yXpkm46KXtTpl+WdPtSFp9MnbgjmX5JSjedsvuVydjh1LHp0VjTzT9qSdIn9dSFQ1HL2Og4dV2sXGTd3Zzx+9Pt+86U6Vcme9+Qzce3AgC3nNOfTD/76TUvbDa0bIt3Z8rUKE5qTfpTo/1tOkJkNF+3JKMPcSNquHaiZpvU2a+cD5kNrbsVu/p/+OFiklOpZ0Yrm9UkmUtyKqndZNLdlrLvTalLJ0YrkGy2v01PbOPFA85bs48ylozdm+7A9ya7X5cy/dLL7x8AuOXU5bMZXVVMJoPdSZlOhk+vXBdsoIyNwmT98POLdEkdT8YPppTdye5XpI7fncw9ksz8eS59bdInGUvq4mjZ5sG+a/jOYETUbJfl5zd5Y7Byt2RFXcoVfyrSLyT9E6nHnxrNdbngB9Fm++hW7uisW375UspY0h1Md9vfTdn7rclgesMhcABAG8qB70hZ+Hr62UdSBtMriwRcarWzmksNVS+DA8ne70vG96QrE8muVyZlMpm8MymD9F//ZOoVzc3pk31vTBk7fK3fGiQRNdujn09d+Nombw7XBMb6ZY/XP9Bq7dyXtdv3m3+yspF6uU9ZVg2S8XvSTd6bcuTHk+mXX/kxAIBb03AmmftG+uHJpJ9JXTq9yZCw1Q9Chxv3TJlIJl85Gh5/8M0p+749569N1g5Lr2vm/G40kmTt8/X6USB1Pjxla0TNdlh8LnV5dvTnMtggKjb75GL9nZd1PwguO4dmI1cYP2N3JmMH0t3xcymTd49WZQMA2jfYlTJ9f7qlN6ZO3JM6++Vk+alseB2y4XXG7qTbk7L71SkH35Yy/cDogdubza9deCp1uLpA0UbXIWuvi7pkePrqvh/YgKjZDnUhyUrU1KuukDXW/SC44jsuV65MvDhl73em1jr61GX8zuv7TB0A4Cbrkon7Uw7dlsw+nDr7V7miDz27qXTTr04Ovj1l+r5ksHc0xOxy6kLSXypU1nxou/LsPcsPsVWiZhvUhadGz4NJcsm5LueGl200FG27HnS1YuzelH3fnXL4b6QMpkdrj5Tx7T0mAHBzlEFG1xp1NDpj+elcdK1RxpNyMN3Uvanjt6c78JbRXJmrNZhacx10ufOaTJk4ePnt4DJEzXboVpZNvmCC/nqXfmDm5g+q2qLB/nRHfiZl/5uSbv/oHAGAHa8uPp169LeTxSey9pqijN+V7H5juumXJntfnwz2b/HOyWQufYm55npmeX50Bwi2SNRsg7L7dRnc8fPpZ7+SuvRcytQDSbc3WX4+dfHJJOPJ4tNJPZvUlWFqF82nWe963bmZSPY8mAwOXKf9AQAtKBN3pO59U3LiWFKnUna/Mdn1ynQHvjvpdl2/A40dSCYfSOa+cJkNu2RsOmXxyWu7IwRriJrtMNibHHoo3cEfHD1MqpvKaKjZMBmeHU3CP/ul1NlHUodnUheeSxa/ltIPk8Egdenp0ZjVUi54WOd10R9PnvoXqYd+IGXfGzNagQQA2PEGu9IdeGv6wYF0Y/uSPa9fuUa5zpafT1k+egVrG/XJcD59v3zRIHy4WqXWLc1kZ8uWRkPUap8MT6SmpAwXU4czydLRZPlk+qUTycxHR5Pual15tk3NKJSu8Q7O4LaUIz+Z7tBD1/F7AQBe8JaOZ/jkryVzn7/8tmU8ZepV6e79x8nYke0/N3akUkpxp+amGx+1SUnS3TUawzp+4SKJgyTpfyr19KeTupA699Vk8dmknxs9IXi4wWS/yxkeTT32e6mDXSl7Xm8JZwDg+ugGKeN3pl5R1AxSMpUMh8YPsSX+82lFtzvlwPcmqSn7vydZeDz9sfclg6PJ7NPXts/lZ9M/8/+mHHgo3e0/PXqoFgDAViw8m8x+bs0LYxmNMNng0RS1JoNuZQnoO27M+bEjGcLYnDIa/zr98nSHvj+ZfThXPwRtzT97P5t64t9k+MxvJUvHrueJAgAvRN1kai05d71RkpRNLjnrcvrhYuqSB3CyNaKmVcMzGT7/+0nmVl64mn/KdSut1cXk5LsyfOL/SF165vqdIwDwwjN5V8pg//mvV+cOb2iYbrArZfreG3Jq7FyiplWzjySzn135YvVBnlv855z/Yvonfz115i+2eHIAwAtWKUldzvkPUeslnovXpZ/7YjLzuUuED1yeqGlRXUx/6qNJXVj3xrX8MBis/Fr5T2Hu0+mf+N9Tj//brZ0jAPCCVIdnk7LyIPIko1Eh6x8onnPvlcGeZHAwuYJFoGEzoqZB9dh7Umc+te7VDSbfpcvl/4mHGX2CsiZs6qn0z/92ht/4n1LPfO5SfxkA4AKl25Psenk2f7D4haNLaj9Mpu9duRaBayNqGlPnvpb++DuTemrNqxvdoemSbk/K3rckg3ty4UM21/+z90kdXvj68FQy+8n0T/5a+if/abL8fHyCAgBcVhlLt+ull1hVdd31xPIzyamPj+b4wjWypHNLFp9Pff6dyfDE5bctJemm0x1+e3L47anP/Vb6s19IymI2jqB+49friWTm4xnOfzPljp9Ot/vVSZnc4jcCAOxo0y9PsifJ8dGHrGUstUym1EEymEjNVDLYmzJxKGXijmT89tGCAptNvYHLKLVWH7+3YOlY+mf+eerZj6zcVUlGd1bWj1E9//Xg9v8kOfhQ0k0kqanPvyv90X9+hQdcv++kDA4lu9+U7u6fT1IuMekPAHhBG55ITn08tZtKMpYMplLKWDLYnXS7krE9STedc0PRzi357NqCq1dKKaKmEfXkh9If/dfJ8hMZBUU3+kRjk8UByr4fTnfPz697dZgc+6MMj787WT667r2NAmnVmtfLRMrEy1P2fVvKkR/NhcPaAACyco2SlUYRKmyvUspmT0LillL71LmvJItPj74ug5XhqJvMpRm7K+XQ2zZ4b5AceijdkZ/ZYJzr+h84m6ykVhdTF76Y/rnfyvAb/0vq/JNX850AAC8EZXVEh6DhxjCnphmTSbcy7Kwub7xJmUwmX5Lu8I+mTL94k20mUg6+Jd3YvvRP/7NkuBolG62edoklosvyaCGBZxbS3fa3Una/Ln5wAQBwMxh+1oKFJzN87FeSzK68MMiGETL9hgzu/kfJxO2X32cdpi58NfWJ30jtn02GJ6/gRC6eZzN6+faU/X893W0/nqx9gjAAAGwzw88AAIDmiZpbXF0+nv7o7+b8XZpkw7s0ZSrdvjde2V2aJCmDlKlXpjzwy+lu/7mUqW9Zefrvqo0e3LnJcLT+udQTv5/+6DvXPTEYAAC2nzk1t7C68Fz6p34tmf/a5Tce7E/Gb0vqUlLGr/gYZfy25MBtKbtfkf74B1NP/n7Sz+eS82nOWQ3hcV0AABZOSURBVI2ePmXsjnQHH1qzJCMAANwYrkBvVf18MvPJZPHxXHiXZiNdSh1LWXw219yp43emO/zWZOpbk+y70pMc/Rrck3LXLyaTd1/bsQEAYAtEza1q6en0x343qWcvv20ZT/Z9V7L/O7f2QMyxIxnc+w/T3fOLydhdo6WjL7J+WNog3Z1/P2XPa679uAAAsAWGn92KhjPpj78vGZ7KFQ0DK2Mpe16XjB3Z+rEH+1L2vSmD8dsyPPquZObPR8s3n9NnFDWD0U2aO3422fcdWz8uAABcI3dqbkVzj6ae/lDOB80g5/+pVoJiVT9Ipt6QsusV1/ccpl+cwb2/kO7uX0qZfFku/E+lTzJMOfBDycG3Xt/jAgDAVRI1t5q6lP7MF5L+zNoXcy5wyuDCyfi7H8zgjh9Lusnrfy7dRMqB70v3kv815c5/mNSp8+9Nvz7dXX8vGVzp/BsAANgehp/daupSsvDIuhf7C99fUaZfm+6+/yIZ7N3mkyrpDj6Uuvu1yTP/MnXpyXR3/QdJN73NxwUAgMsTNbeSOkw98f7UuYevYOMuZfcbk8GebT+tVWXiruTeX0gZLiTjh27YcQEA4FJEzS2kzj48eoDlRg/XXG/i5SkHvzfJFlY7uxbd7tEvAAC4RZhTc6uofepz70jq8SvbfveDydiB7T0nAABogKi5RdS5L6XOffnKNh4cyODw27b3hAAAoBGi5lbQz6Ue/7N1z4PZRJlM2fv9yfhd239eAADQAFFzC6gzn0+d+ewVbNmlTDyQ7s6/t+3nBAAArbBQwC2g7H1DBvfvS3/yg6lnv5AsP7PJljU5+EMXPqcGAABe4ETNraBMJrtenW765cnSsxke/0By5uPJ8hMXbjd2Z7o9D96UUwQAgFtVqbXWm30SrNcn80+knvlU6pmPpi58NekmM7jjHyUH3nKzTw4AAG4ZpZQiam5pfbL0bOrJD6fOP5vuzp/10EsAAFhD1LSiLibDuWRs/80+EwAAuKWIGgAAoGmlFMtoAQAAbRM1AABA00QNAADQNFEDAAA0TdQAAABNEzUAAEDTRA0AANA0UQMAADRN1AAAAE0TNQAAQNNEDQAA0DRRAwAANE3UAAAATRM1AABA00QNAADQNFEDAAA0TdQAAABNEzUAAEDTRA0AANA0UQMAADRN1AAAAE0TNQAAQNNEDQAA0DRRAwAANE3UAAAATRM1AABA00QNAADQNFEDAAA0TdQAAABNEzUAAEDTRA0AANA0UQMAADRN1AAAAE0TNQAAQNNEDQAA0DRRAwAANE3UAAAATRM1AABA00QNAADQNFEDAAA0TdQAAABNEzUAAEDTRA0AANA0UQMAADRN1AAAAE0TNQAAQNNEDQAA0DRRAwAANE3UAAAATRM1AABA00QNAADQNFEDAAA0TdQAAABNEzUAAEDTRA0AANA0UQMAADRN1AAAAE0TNQAAQNNEDQAA0DRRAwDA9qvLN/sM2MHGbvYJAACwQ/Xzyfzj6U98IFl6It19v5wM9t3ss2IHEjUAAFw/i8+lzj+W/uxfJTOfSJafHr3e7U89/v6Uw/9e0k3d3HNkxxE1AABsWT/7xeTEB5O5z6UuH0/qcN0Gp9If/51040dSDrz55pwkO5Y5NQAAbE2tqcf+JPX0+1KXjl4cNKv6s+mP/u6NPTd2uNFcLVEDAMDWlJKy+zVJuYJLy+FT6Y/98fafEy8IdflUElEDAAA0TtQAALBl3YE3p7vtP0q6/ZttsfL7MDn1/qQu3KAzYyerpz+dRNQAAHA9dNMph/9mcvhvX2qjpA5TF76SevqTN+zU2KHqQsqZjyURNQAAXEeDPa9KBgc3eKdf+TVSn//dZGU+BFyLevbR9POPJBE1AABcT1MvS9n/tiSDS2+39Gz6M5++/scfnko99sepx99z/ffNLaRPZv8y6c8m8ZwaAACus+7Q92d4+mPJ8uObbZFa51KOvSPZ82AyfsfWDzo8nf7oO5PZv0yd/3oydke6Pa9Jmbhn6/vm1tPPpZ5877kv3akBAOD6Gr8rZc93JGXykpvVpaPpj291eec+9ewXM3z0v0099d7Uha8mZTkZPpk8844t7ptbWR2/79yfRQ0AANddd/gtSZna5N01c2tm/zKZf+zaDjL/ePqnfyP9E//jKGJWhiKdO8rsJ1Kf/4Nr2ze3tm53utt/MmXyFUmSUmutN/mUAADYaepi+if/n9SZj1x2+eYy+WDKA7+SMth3Zfvuz6Yef3/6Y//fRSFz0b7Hbkt50a+mXI8hbtx65r6WsuslV/LYVwAAuEplIt3tP5EyuHPNixtdenapCw+nnPnsFe22nvxQ+q/9d1cUNElSl4+mHn1XUpev7Lxpy/SLkxh+BgDAdpm4L+XA9+T8JWdJ+tVV0bqkjCXpk9Kln33k0vsazmT4zf8z/bO/nrr4jTVBs3EorX29nvqj5PRHt/KdcIuz+hkAANvnwHcnx9+d9DNJhkm3GjV9sjoJovbJ3MOpi0+lTNx9wV+vc19NPfH+1LOfSpaPrtv5ZkGzRhlL2ftQsvu1W/9euGWJGgAArk1dXrnbsrkyfkfK3u9KPfXejd7NKEL61MVvJsf/JOXO/3D0+vBE6ok/Sz31vtGdmQ31l3itSwZ3p7vr51L2vCEpl3luDk0TNQAAXLG6+FTq3JeT4XKy+HRKrcn+b0sZP5wMDibd+hXPSsrB70s985GkP5Pzt2eSZDgajtaN/lyWjiVzX02d+Wz64+++ojkzG+r2phx4KN2+70qmX3pt+6ApVj8DAODKLB3P8LH/Jumfz0V3ScZuTyYeyGD6FamDqdF8mukXJd1EUgYZfv1/SOa/uMFOu/P7KhNJX5OytG6bQZLhutfG1pzD+XMpEw+k7P/+lIM/mAx2Xdv3SVNKKZe5XwgAAEnSzyYL3xz9uYxfvEzz8nPJ8nMZzn5yZZtBysRLk24yGbs95dBDqc98fYO7L2viqC6ORqRdoMuFd3e6pHSjeTgXhNW+5MBb093+48lgbzbYETuYqAEA4Ar06U+8P6nHk8GdSRaS5WPZeF5LkjpMXVizotnZLyT93BUeq8soSoYb7L9cGDTd7mTyZRnc/neSXa++8m+HHUXUAABwBcaTfW9IWXw8dfHrm2yzZijZ+j/361cuu5RNQinJuWFodSJl1ytTDv5gyp5vM9TsBU7UAABwectHU1JSl569xEb9xn/uy3V9OmKZfEXK3b+QMnnPZVdf44XBfwUAAFxafzb9c+9Mnf38xXNpkpwvli7J8gZvD9dtd6k7MZcwOJTu9r+fsuf1ydiBa9sHO5KoAQDg0moyGkJ2JpcMklKT2l1im2uMmXPn0aU/9p4MhrOpu1+dMvWire2PHcOSzgAAXN7wVOrsI8lwLv3pz6QsPpG6/M2krl9++Qao4ymDqZRdr00O/sBoTg0vWKWUImoAALh6w9lk8cn0pz+TnPpQav9cUtcOPbvUHZtrscn+BodTjvx0uj1vSCaOXMfj0QpRAwDA1vVzqcOZZPFY6sxnUue/mizPJUuPjTqkjCWZ3eJBuqSUpK5/COfovXLg7ekOvz2ZuG2Lx6E1ogYAgOuu1uWU9KnLx1NKSX/8T1NPfSBZfjZbXixg9e+Xbt2doSSTr0t38AdS9r9x9PwaXhBEDQAA26/2ST+bzHwqwzOfS1l4NHXxiS3udLC686wPpDL50pT9b0058OZksCejB3myU4kaAABurOXnU5/+jfQzH0vSJ/1gzZLPqwbJYG8yPJ3L3tEpY0nW3rVZs/3Y7en2vTmZfCBl94PJ+KEInJ1H1AAAcOPUxdSj70p//HfXPO9mgwUAutuT6ZckwzPJ/JdX3r9U3KwOSdtgzk0ZJHWYMv7SlDt+JmXvt16Xb4Vbh6gBAOCG6Z//vdTj706GpzbfqIwlU29Id+RHk7Fdqcf/Xerpjyf1dDZ8sOdFNlt1rUvG70938KGUg9+blKlR8NA8UQMAwA1RF76Z/tH/Kinzl9myS9n95nT3/2crf7FPXfhm6smPpiw8ln7uS0m9kpXUNombMp4y9ap0d/+DZOKeq/wuuBWVUsrYzT4JAAB2uLqYnP7oJYJmNUC6pJtOd+SHzr9VupSpF6Xc+UBSFzOY/UqGz/1eMv/Zyxx0bdCsWXFt/J6UI39b0Oww7tQAALCNaurZL6T/5q8m2egZM2sM7ky57afSHfyBS2/XzybH35vhiT9Mlo+ue3NNIK0LmzL5snT3/lIycd/VfhPcwkoppbv8ZgAAcG3qwpOppz6aywZNunSHf+zyQZMk3a7kyI9l8OL/OWXP96ybG7MaMuXC1ydfle7+XxE0O5SoAQBge9Q+9dgfpZ7+08tvu/stKYfeenX7Hzuc7u5/kO7If5wy/dp1bw7Pr4Q2dl8G9/3jZOzw1e2fZogaAAC2x9LR5OynRnNq0iXZaDp3l0y/IYN7fi4p41d/jMGelCM/nO6eX0x2/fWk233h+2O3pbvvP0/G77iGb4BWWCgAAIDrr5/L8Kn/K1l+fvR17ZKuJutnc/clg0MPJYM9Wzve+B0ZPPBfJotPpJ75bPqT708Wnsng7n+STL1oa/vmlidqAAC47urCN5KFR3N+VbNc/GDMJJm4K9n1sut34Il7Uw7fm8HBtyULTyTTL79+++aWJWoAALj+Zr6U9KtLOPdJ3eiBmIfSHX570u29/sfvpgXNC4g5NQAAXF/Lp1KXjl08v2WdbteLR3dquskbdGLsVJ5TAwDAdVaT4enkzOcyPPG+ZP7RJLOjJZZXh6CViXR3/ZOUfW9KPA+eLSilFFEDAMA2qcnwZHL8TzM89eFk6eujl7upZPK1Gdz1s8nkAzf1DGmfqAEAYPvVPlk+mv7UR1JPvD+psxnc+8vJrlff7DNjByilFHNqAACAprlTAwDADVKTpePpz3wm3d4Hk/G7b/YJsQMYfgYAwE2wevlZbupZsDOUUiw1AQDAjSZmuL7MqQEAAJomagAAgKaJGgAAoGmiBgAAaJqoAQAAmiZqAACApokaAACgaaIGAABomqgBAACaJmoAAICmiRoAAKBpogYAAGiaqAEAAJomagAAgKaJGgAAoGmiBgAAaJqoAQAAmiZqAACApokaAACgaaIGAABomqgBAACaJmoAAICmiRoAAKBpogYAAGiaqAEAAJomagAAgKaJGgAAoGmiBgAAaJqoAQAAmiZqAACApokaAACgaaIGAABomqgBAACaJmoAAICmiRoAAKBpogYAAGiaqAEAAJomagAAgKaJGgAAoGmiBgAAaJqoAQAAmiZqAACApokaAACgaaIGAABomqgBAACaJmoAAICmiRoAAKBpogYAAGiaqAEAAJomagAAgKaJGgAAoGmiBgAAaJqoAQAAmiZqAACApokaAACgaaIGAABomqgBAACaJmoAAICmiRoAAKBpogYAAGiaqAEAAJomagAAgKaJGgAAoGmiBgAAaJqoAQAAmiZqAACApokaAACgaaIGAABomqgBAACaJmoAAICmiRoAAKBpogYAAGiaqAEAAJomagAAgKaJGgAAoGmiBgAAaJqoAQAAmiZqAACApokaAACgaaIGAABomqgBAACaJmoAAICmiRoAAKBpogYAAGiaqAEAAJomagAAgKaJGgAAoGmiBgAAaJqoAQAAmiZqAACApokaAACgaaIGAABomqgBAACaJmoAAICmiRoAAKBpogYAAGiaqAEAAJomagAAgKaJGgAAoGmiBgAAaJqoAQAAmiZqAACApokaAACgaaIGAABomqgBAACaJmoAAICmiRoAAKBpogYAAGiaqAEAAJomagAAgKaJGgAAoGmiBgAAaJqoAQAAmiZqAACApokaAACgaaIGAABomqgBAACaJmoAAICmiRoAAKBpogYAAGiaqAEA+P/brwMSAAAAhkH9Wz/GGWgLgTSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAACBNagAAgDSpAQAA0qQGAABIkxoAAAAAAICXAXAL+UsK0mwhAAAAAElFTkSuQmCC" />
            <image width="166" height="884" transform="translate(527.07) scale(.2)"
              xlink:href="data:image/png;base64,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" />
          </g>
          <g class="cls-3">
            <image width="1802" height="3" transform="translate(201.08 50.3) scale(.2)"
              xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAABwoAAAAECAYAAACeA7tOAAAACXBIWXMAADerAAA3qwGYrfpiAAAASElEQVR4nO3YQQkAIBAAQc+EgphSBBtqCvFxMwn2vTHXPgUAAAAAILnRW/ilAGRSfwcAAAAAAAAAAAAAAAAAAAAAAAAAAAAvXHYJBwvuZr9/AAAAAElFTkSuQmCC" />
          </g>
          <g class="cls-4">
            <image width="3" height="366" transform="translate(283.67 105.29) scale(.2)"
              xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAF3CAYAAABkGSHhAAAACXBIWXMAADerAAA3qwGYrfpiAAAAF0lEQVQ4jWNgGAWjYBSMglEwCkYBFQAAB1MAAbKPdlYAAAAASUVORK5CYII=" />
          </g>
          <g class="cls-8">
            <g class="cls-7">
              <image width="75" height="36" transform="translate(419.03 69.89) scale(.37 .38)"
                xlink:href="data:image/png;base64,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" />
            </g>
          </g>
          <path
            d="M503.67,92.09c.31-.28.47-.66.47-1.12,0-.27-.04-.49-.13-.67-.09-.17-.21-.31-.37-.42-.16-.11-.35-.19-.57-.25-.22-.06-.46-.12-.72-.17-.23-.04-.43-.08-.58-.13-.15-.05-.27-.1-.36-.16-.09-.06-.15-.13-.19-.21-.04-.08-.05-.18-.05-.3,0-.22.09-.4.26-.53.18-.13.41-.2.7-.2.32,0,.57.06.73.19s.28.33.34.61h.78c-.03-.27-.1-.51-.21-.7-.11-.19-.25-.34-.41-.46-.17-.12-.35-.2-.56-.25-.21-.05-.42-.07-.65-.07-.2,0-.4.03-.61.09s-.39.15-.55.27-.3.27-.4.45c-.1.18-.16.38-.16.62,0,.***********.***********.*********.***********.**********.**********.***********.*********.31.3.57s-.09.47-.26.61c-.18.13-.46.2-.85.2-.41,0-.7-.09-.88-.26-.18-.18-.28-.41-.32-.72h-.79c.***********.56,**********.83.44,1.43.44s1.09-.14,1.4-.42ZM499.08,92.46c.1-.02.2-.05.28-.08v-.68c-.09.03-.18.06-.28.08-.1.02-.21.03-.34.03-.42,0-.63-.25-.63-.74v-3.05h1.19v-.68h-1.19v-1.16h-.81v1.16h-.73v.68h.73v3.13c0,.4.11.73.34.97.22.25.57.37,1.04.37.17,0,.3,0,.41-.03ZM493.82,92.42h.81v-4.41h1.25v-.68h-1.25v-.91c0-.26.06-.47.17-.63.11-.16.3-.24.57-.24.11,0,.22,0,.32.03.1.02.19.05.26.08v-.69c-.1-.03-.2-.06-.28-.07-.08-.02-.19-.02-.32-.02-.51,0-.89.14-1.14.43-.25.29-.38.69-.38,1.21v.83h-.73v.68h.73v4.41ZM491.83,86.12c.1-.1.16-.23.16-.38s-.05-.28-.16-.38c-.1-.1-.23-.16-.38-.16s-.28.05-.38.16c-.1.1-.16.23-.16.38s.05.28.16.38c.1.1.23.16.38.16s.28-.05.38-.16ZM491.06,92.42h.81v-5.09h-.81v5.09ZM486.24,91.45c-.19-.08-.36-.2-.5-.35-.14-.15-.25-.34-.34-.55-.08-.22-.13-.46-.13-.74v-.08c0-.26.04-.51.11-.73.07-.22.18-.41.32-.57.14-.16.3-.28.51-.37.2-.09.43-.14.68-.14.5,0,.89.16,1.18.47.29.31.43.75.43,1.32v.07c0,.28-.04.54-.13.76-.08.22-.2.41-.35.56-.15.15-.33.27-.53.35-.2.08-.42.12-.66.12-.21,0-.42-.04-.61-.12ZM487.84,94.14c.29-.09.55-.23.76-.41.21-.18.38-.41.51-.69.12-.28.18-.6.18-.97v-4.75h-.81v.82c-.15-.25-.36-.46-.62-.64-.27-.18-.61-.27-1.04-.27-.35,0-.67.07-.96.2-.29.13-.54.31-.75.54-.21.23-.37.5-.49.81-.12.31-.18.63-.18.98v.07c0,.35.06.67.18.97.12.3.28.55.48.76.2.21.45.38.73.5.29.12.59.18.92.18.18,0,.36-.03.53-.08.18-.05.34-.13.49-.21.15-.09.29-.19.41-.31.12-.12.22-.24.29-.36v.74c0,.54-.15.94-.44,1.19-.3.25-.69.37-1.19.37-.86,0-1.35-.3-1.47-.89h-.83c.03.21.1.42.2.61.1.19.25.36.43.5.18.14.41.25.69.34.28.08.6.13.98.13.36,0,.68-.05.98-.14ZM479.66,92.42h.82v-7.52h-.82v7.52ZM476.5,92.24c.28-.18.47-.39.59-.63v.81h.81v-5.09h-.81v3.1c0,.23-.04.43-.12.61-.08.18-.18.32-.31.44-.13.12-.28.21-.45.26-.17.06-.34.09-.53.09-.4,0-.7-.1-.89-.31-.19-.21-.29-.54-.29-.99v-3.19h-.81v3.22c0,.35.05.65.14.89.09.25.21.45.37.61.16.16.34.27.56.35.22.07.45.11.71.11.41,0,.75-.09,1.03-.27ZM470.44,92.42h.81v-4.41h1.25v-.68h-1.25v-.91c0-.26.06-.47.17-.63.11-.16.3-.24.57-.24.11,0,.22,0,.32.03.1.02.19.05.26.08v-.69c-.1-.03-.2-.06-.28-.07-.08-.02-.19-.02-.32-.02-.51,0-.89.14-1.14.43-.25.29-.38.69-.38,1.21v.83h-.73v.68h.73v4.41ZM465.47,91.45c-.19-.08-.36-.2-.5-.35-.14-.15-.25-.34-.34-.55-.08-.22-.13-.46-.13-.74v-.08c0-.26.04-.51.11-.73.07-.22.18-.41.32-.57.14-.16.3-.28.51-.37.2-.09.43-.14.68-.14.5,0,.89.16,1.18.47.29.31.43.75.43,1.32v.07c0,.28-.04.54-.13.76-.08.22-.2.41-.35.56-.15.15-.33.27-.53.35-.2.08-.42.12-.66.12-.21,0-.42-.04-.61-.12ZM467.06,94.14c.29-.09.55-.23.76-.41.21-.18.38-.41.51-.69.12-.28.18-.6.18-.97v-4.75h-.81v.82c-.15-.25-.36-.46-.62-.64-.27-.18-.61-.27-1.04-.27-.35,0-.67.07-.96.2-.29.13-.54.31-.75.54-.21.23-.37.5-.49.81-.12.31-.18.63-.18.98v.07c0,.35.06.67.18.97.12.3.28.55.48.76s.45.38.73.5c.29.12.59.18.92.18.18,0,.36-.03.53-.08.18-.05.34-.13.49-.21.15-.09.29-.19.41-.31.12-.12.22-.24.29-.36v.74c0,.54-.15.94-.44,1.19-.3.25-.69.37-1.19.37-.86,0-1.35-.3-1.47-.89h-.83c.03.21.1.42.2.61.1.19.25.36.43.5.18.14.41.25.69.34.28.08.6.13.98.13.36,0,.68-.05.98-.14ZM458.12,92.42h.81v-3.1c0-.23.04-.43.12-.61.08-.18.18-.32.31-.44.13-.12.28-.21.45-.26.17-.06.35-.09.54-.09.41,0,.71.1.9.31.19.21.29.54.29.99v3.19h.81v-3.12c0-.37-.04-.68-.13-.94-.09-.26-.21-.47-.37-.64-.16-.17-.35-.29-.57-.36-.22-.07-.46-.11-.72-.11-.42,0-.77.09-1.05.27-.28.18-.47.39-.59.63v-.81h-.81v5.09ZM456.31,86.12c.1-.1.16-.23.16-.38s-.05-.28-.16-.38c-.1-.1-.23-.16-.38-.16s-.28.05-.38.16c-.1.1-.16.23-.16.38s.05.28.16.38c.1.1.23.16.38.16s.28-.05.38-.16ZM455.54,92.42h.81v-5.09h-.81v5.09ZM449.59,92.42h.81v-3.1c0-.23.04-.43.12-.61s.18-.32.31-.44c.13-.12.28-.21.45-.26.17-.06.35-.09.54-.09.41,0,.71.1.9.31.19.21.29.54.29.99v3.19h.81v-3.12c0-.37-.04-.68-.13-.94-.09-.26-.21-.47-.37-.64-.16-.17-.35-.29-.57-.36-.22-.07-.46-.11-.72-.11-.42,0-.77.09-1.05.27-.28.18-.47.39-.59.63v-.81h-.81v5.09ZM444.82,91.64c-.14-.15-.21-.36-.21-.63,0-.19.04-.35.13-.47.09-.13.21-.23.37-.31.16-.08.35-.13.56-.17.22-.04.45-.05.71-.05h.7v.63c0,.19-.04.37-.12.52-.08.15-.18.28-.32.38-.13.1-.29.18-.48.24-.18.06-.38.08-.6.08-.36,0-.61-.08-.75-.23ZM446.43,92.32c.25-.12.46-.3.65-.54v.64h.81v-3.36c0-.36-.05-.65-.16-.89-.1-.23-.24-.42-.41-.56-.17-.14-.37-.24-.59-.29-.23-.06-.46-.08-.7-.08s-.48.03-.71.08c-.23.06-.44.15-.62.27-.18.13-.34.29-.46.49-.12.2-.19.45-.22.74h.81c.08-.6.47-.9,1.17-.9.39,0,.67.09.84.27.17.18.25.47.25.87v.37h-.73c-.32,0-.64.03-.95.08-.31.06-.58.14-.82.27-.24.12-.43.29-.57.49-.14.2-.21.45-.21.74,0,.26.04.48.13.67.09.19.21.34.36.47.15.12.33.21.54.27.21.06.43.09.66.09.38,0,.69-.06.93-.18ZM439.06,89.39c.07-.45.24-.81.5-1.07.26-.26.6-.39,1.02-.39s.76.11,1.02.34c.26.23.4.6.44,1.12h-2.98ZM442.11,92.12c.39-.26.63-.65.72-1.16h-.81c-.1.58-.55.87-1.35.87-.53,0-.92-.15-1.2-.45-.27-.3-.42-.74-.44-1.32h3.83v-.26c0-.46-.06-.85-.19-1.17-.13-.32-.3-.59-.51-.79-.21-.2-.46-.35-.73-.45-.28-.09-.56-.14-.85-.14-.35,0-.67.06-.96.19-.29.13-.54.3-.75.53-.21.23-.37.51-.49.83-.12.32-.18.67-.18,1.06v.08c0,.39.06.74.18,1.06.12.32.29.59.51.81.22.22.48.4.78.52.3.12.63.18.99.18.58,0,1.06-.13,1.45-.39ZM429.61,92.42h.81v-3.13c0-.23.04-.43.12-.6.08-.17.18-.32.31-.43.13-.11.27-.2.43-.25.16-.06.32-.08.49-.08.35,0,.62.1.8.3.18.2.28.52.28.97v3.22h.81v-3.13c0-.23.04-.43.12-.6.08-.17.18-.32.31-.43.13-.11.27-.2.43-.25.16-.06.32-.08.49-.08.35,0,.62.1.8.3.18.2.28.52.28.97v3.22h.81v-3.15c0-.37-.05-.68-.14-.94-.09-.26-.22-.47-.37-.63-.16-.16-.34-.28-.54-.35-.21-.07-.42-.11-.65-.11-.16,0-.32.02-.49.06-.17.04-.33.1-.49.18-.16.08-.3.18-.44.3-.14.12-.25.26-.33.42-.13-.34-.33-.59-.59-.73-.27-.15-.56-.22-.89-.22-.37,0-.69.08-.95.25-.26.17-.45.37-.57.61v-.78h-.81v5.09ZM423.26,92.42h.81v-2.79c0-.31.04-.57.12-.77.08-.2.19-.37.34-.49.15-.12.32-.21.53-.26.21-.05.44-.08.69-.1v-.76c-.23,0-.43.04-.6.09-.18.05-.33.12-.46.21-.13.09-.24.19-.35.31-.1.12-.19.25-.27.39v-.91h-.81v5.09ZM420.38,92.32c.3-.13.57-.3.79-.53.22-.23.4-.5.53-.82.13-.32.19-.67.19-1.06v-.08c0-.38-.06-.73-.19-1.05-.13-.32-.3-.59-.52-.82-.22-.23-.48-.4-.79-.53-.31-.13-.64-.19-1-.19s-.7.06-1,.19c-.3.13-.57.3-.79.53-.22.23-.39.5-.52.82-.13.32-.19.67-.19,1.05v.08c0,.37.06.71.18,1.03.12.32.3.59.52.82.22.23.48.41.79.54.3.13.64.19,1,.19s.7-.06,1-.19ZM418.17,91.31c-.3-.35-.44-.81-.44-1.39v-.08c0-.29.04-.55.12-.79.08-.24.19-.44.33-.6.14-.17.32-.3.53-.39.21-.09.44-.14.69-.14s.48.05.69.14.38.22.53.39c.15.17.26.37.34.61.08.24.12.5.12.79v.07c0,.28-.04.54-.12.78-.08.24-.19.44-.33.6-.14.17-.32.3-.53.39-.21.09-.44.14-.69.14-.52,0-.93-.17-1.22-.52ZM413.96,92.42h.81v-4.41h1.24v-.68h-1.24v-.91c0-.26.06-.47.17-.63.11-.16.3-.24.57-.24.11,0,.22,0,.32.03.1.02.19.05.26.08v-.69c-.1-.03-.2-.06-.28-.07-.08-.02-.19-.02-.32-.02-.51,0-.89.14-1.14.43-.25.29-.38.69-.38,1.21v.83h-.73v.68h.73v4.41ZM408.33,92.32c.3-.13.57-.3.79-.53.22-.23.4-.5.53-.82.13-.32.19-.67.19-1.06v-.08c0-.38-.06-.73-.19-1.05-.13-.32-.3-.59-.52-.82-.22-.23-.48-.4-.79-.53-.31-.13-.64-.19-1-.19s-.7.06-1,.19c-.3.13-.57.3-.79.53-.22.23-.39.5-.52.82-.13.32-.19.67-.19,1.05v.08c0,.37.06.71.18,1.03.12.32.3.59.52.82.22.23.48.41.79.54.3.13.64.19,1,.19s.7-.06,1-.19ZM406.12,91.31c-.3-.35-.44-.81-.44-1.39v-.08c0-.29.04-.55.12-.79.08-.24.19-.44.33-.6.14-.17.32-.3.53-.39.21-.09.44-.14.69-.14s.48.05.69.14.38.22.53.39c.15.17.26.37.34.61.08.24.12.5.12.79v.07c0,.28-.04.54-.12.78-.08.24-.19.44-.33.6-.14.17-.32.3-.53.39-.21.09-.44.14-.69.14-.52,0-.93-.17-1.22-.52ZM403.6,92.46c.1-.02.2-.05.28-.08v-.68c-.09.03-.18.06-.28.08-.1.02-.21.03-.34.03-.42,0-.63-.25-.63-.74v-3.05h1.19v-.68h-1.19v-1.16h-.81v1.16h-.73v.68h.73v3.13c0,.4.11.73.34.97.22.25.57.37,1.04.37.17,0,.3,0,.41-.03ZM398.05,90.13h2.08v-.76h-2.08v.76ZM395.4,92.32c.3-.13.57-.3.79-.53.22-.23.4-.5.53-.82.13-.32.19-.67.19-1.06v-.08c0-.38-.06-.73-.19-1.05-.13-.32-.3-.59-.52-.82-.22-.23-.48-.4-.79-.53-.31-.13-.64-.19-1-.19s-.7.06-1,.19c-.3.13-.57.3-.79.53-.22.23-.39.5-.52.82-.13.32-.19.67-.19,1.05v.08c0,.37.06.71.18,1.03.12.32.29.59.52.82.22.23.48.41.79.54.3.13.64.19,1,.19s.7-.06,1-.19ZM393.19,91.31c-.3-.35-.44-.81-.44-1.39v-.08c0-.29.04-.55.12-.79.08-.24.19-.44.33-.6.14-.17.32-.3.53-.39.21-.09.44-.14.69-.14s.48.05.69.14.38.22.53.39c.15.17.26.37.34.61.08.24.12.5.12.79v.07c0,.28-.04.54-.12.78-.08.24-.19.44-.33.6-.14.17-.32.3-.53.39-.21.09-.44.14-.69.14-.52,0-.93-.17-1.22-.52ZM387.49,91.45c-.19-.08-.36-.2-.5-.35-.14-.15-.25-.34-.34-.55-.08-.22-.13-.46-.13-.74v-.08c0-.26.04-.51.11-.73.07-.22.18-.41.32-.57.14-.16.3-.28.51-.37.2-.09.43-.14.68-.14.5,0,.89.16,1.18.47.29.31.43.75.43,1.32v.07c0,.28-.04.54-.13.76-.08.22-.2.41-.35.56-.15.15-.33.27-.53.35-.2.08-.42.12-.66.12-.21,0-.42-.04-.61-.12ZM389.08,94.14c.3-.09.55-.23.76-.41.21-.18.38-.41.51-.69.12-.28.18-.6.18-.97v-4.75h-.81v.82c-.15-.25-.36-.46-.62-.64-.27-.18-.61-.27-1.04-.27-.35,0-.67.07-.96.2-.29.13-.54.31-.75.54-.21.23-.37.5-.49.81-.12.31-.18.63-.18.98v.07c0,.35.06.67.18.97.12.3.28.55.48.76s.45.38.73.5c.29.12.59.18.92.18.18,0,.36-.03.53-.08.18-.05.34-.13.49-.21.15-.09.29-.19.41-.31.12-.12.22-.24.29-.36v.74c0,.54-.15.94-.44,1.19-.3.25-.69.37-1.19.37-.86,0-1.35-.3-1.47-.89h-.83c.03.21.1.42.2.61.1.19.25.36.43.5.18.14.41.25.69.34.28.08.6.13.98.13.36,0,.68-.05.98-.14ZM379.72,92.42h.81v-2.79c0-.31.04-.57.12-.77.08-.2.19-.37.34-.49.15-.12.32-.21.53-.26.21-.05.44-.08.69-.1v-.76c-.23,0-.43.04-.6.09-.18.05-.33.12-.46.21-.13.09-.24.19-.35.31-.1.12-.19.25-.27.39v-.91h-.81v5.09ZM376.58,92.24c.28-.18.47-.39.59-.63v.81h.81v-5.09h-.81v3.1c0,.23-.04.43-.12.61-.08.18-.18.32-.31.44-.13.12-.28.21-.45.26-.17.06-.34.09-.53.09-.4,0-.7-.1-.89-.31-.19-.21-.29-.54-.29-.99v-3.19h-.81v3.22c0,.35.05.65.14.89.09.25.21.45.37.61.16.16.34.27.56.35.22.07.45.11.71.11.41,0,.75-.09,1.03-.27ZM370.95,92.32c.3-.13.57-.3.79-.53.22-.23.4-.5.53-.82.13-.32.19-.67.19-1.06v-.08c0-.38-.06-.73-.19-1.05-.13-.32-.3-.59-.52-.82-.22-.23-.48-.4-.79-.53-.31-.13-.64-.19-1-.19s-.7.06-1,.19c-.3.13-.57.3-.79.53-.22.23-.39.5-.52.82-.13.32-.19.67-.19,1.05v.08c0,.37.06.71.18,1.03.12.32.29.59.52.82.22.23.48.41.79.54.3.13.64.19,1,.19s.7-.06,1-.19ZM368.74,91.31c-.3-.35-.44-.81-.44-1.39v-.08c0-.29.04-.55.12-.79.08-.24.19-.44.33-.6.14-.17.32-.3.53-.39.21-.09.44-.14.69-.14s.48.05.69.14c.2.09.38.22.53.39.15.17.26.37.34.61.08.24.12.5.12.79v.07c0,.28-.04.54-.12.78-.08.24-.19.44-.33.6-.14.17-.32.3-.53.39-.21.09-.44.14-.69.14-.52,0-.93-.17-1.22-.52ZM363.63,92.42h.85v-3.02l2.2-3.93h-.85l-1.75,3.23-1.82-3.23h-.95l2.32,3.92v3.03Z" />
          <path
            d="M490.19,285.54h.47l1.67-6.01h-.86l-1.27,6.01ZM490.39,287.58c.11-.11.17-.25.17-.41,0-.12-.04-.24-.13-.33-.09-.1-.21-.15-.36-.15s-.28.05-.39.16c-.11.1-.17.24-.17.4,0,.12.04.24.13.34.09.1.21.15.36.15.15,0,.28-.05.39-.16ZM482.9,288.58h-.48c-.08.08-.16.19-.25.33-.09.14-.13.29-.13.44,0,.2.07.35.22.46.16.1.35.15.6.15.3,0,.6-.05.89-.16s.58-.26.86-.45c.28-.19.57-.43.86-.71.29-.28.58-.59.89-.93.24-.27.49-.58.75-.92.26-.34.51-.7.75-1.06.25-.37.48-.73.69-1.1.22-.37.41-.72.58-1.06.17-.33.3-.64.4-.92.1-.28.15-.51.15-.7.02-.26-.12-.39-.4-.39-.2,0-.41.03-.62.09-.21.06-.39.13-.54.21l-.07.34.97-.02c-.06.3-.19.67-.38,1.09-.19.42-.42.86-.68,1.32-.26.46-.55.91-.86,1.37-.31.45-.62.87-.92,1.25h-.03c.05-.41.08-.86.1-1.33.02-.47.03-.95.03-1.41s0-.91-.02-1.32c-.02-.42-.04-.77-.06-1.07-.02-.19-.05-.33-.11-.4-.06-.07-.16-.11-.31-.11-.18,0-.38.03-.6.08-.22.05-.4.11-.55.17l-.09.38.89-.02c.03.28.06.62.08,1.02.02.4.04.85.05,1.35,0,.5,0,1.04-.01,1.63-.02.59-.05,1.21-.09,1.86-.4.41-.82.75-1.24,1.01-.42.26-.83.39-1.21.39-.12,0-.21-.02-.27-.05l.18-.81ZM482.13,287.6c.26-.09.48-.21.67-.37.19-.16.33-.34.43-.55.1-.21.15-.44.15-.69,0-.19-.03-.35-.09-.5-.06-.14-.14-.28-.24-.41-.1-.13-.22-.25-.37-.37-.14-.12-.3-.25-.47-.38-.17-.13-.32-.25-.44-.35-.12-.1-.23-.2-.31-.29-.08-.09-.14-.18-.18-.28-.04-.09-.06-.2-.06-.31,0-.32.12-.58.37-.77.25-.19.56-.29.93-.29.22,0,.39.02.53.07.14.05.25.12.34.2l-.15.83h.5c.09-.11.16-.23.23-.37.07-.14.1-.28.1-.42,0-.26-.14-.46-.41-.6-.28-.14-.63-.21-1.07-.21-.29,0-.56.04-.81.13-.25.09-.47.2-.65.36-.18.15-.33.33-.43.53-.11.2-.16.42-.16.67,0,.34.1.63.29.86.19.23.46.48.81.75.15.12.28.22.41.31.12.09.23.19.33.28.09.1.17.2.22.31.05.11.08.23.08.37,0,.34-.13.61-.38.82-.25.21-.61.31-1.08.31-.29,0-.51-.04-.67-.11-.16-.08-.28-.17-.37-.27l.16-.81h-.53c-.09.09-.18.19-.25.32-.07.12-.11.26-.11.41,0,.14.05.27.14.39.09.12.22.22.39.31.16.09.36.15.58.2s.46.07.72.07c.31,0,.6-.04.85-.13ZM477.15,282.18h.85l-1.27,4.21c-.03.09-.05.18-.06.27l-.02.23c0,.28.09.49.26.62.18.13.39.2.66.2.33,0,.6-.04.83-.13.23-.09.41-.18.56-.27l.09-.39c-.17.07-.36.13-.57.18-.21.05-.44.08-.69.08-.23,0-.36-.07-.39-.2-.03-.14-.02-.31.04-.51l1.31-4.28h1.41l.14-.53h-1.4l.46-1.49h-.75l-.43,1.47-.95.2-.08.35ZM469.24,287.62h6.02l.6-2.17h-.42l-.79,1.68h-3.44l.86-3.42h1.86l-.04,1.11h.39l.58-2.71h-.4l-.47,1.11h-1.82l.82-3.2h2.96l.13,1.41h.42l.19-1.9h-5.43l-.08.36.98.16-1.76,7.03-1.07.16-.08.37ZM465.96,287.62c.26-.06.47-.12.64-.18l.09-.32-1.04.02,1.28-3.99c.09-.3.13-.56.13-.76,0-.28-.07-.49-.21-.64-.14-.14-.33-.22-.57-.22s-.5.06-.76.18c-.26.12-.5.26-.74.44-.24.18-.46.38-.68.6-.21.22-.4.43-.57.64h-.05c.09-.26.14-.47.16-.61.02-.14.04-.27.04-.37,0-.28-.07-.5-.2-.65-.14-.15-.33-.23-.59-.23-.24,0-.49.06-.74.17-.25.11-.5.27-.75.46-.25.19-.49.42-.74.67-.24.25-.47.52-.69.81h-.04l.63-2.04-.08-.08-1.68.27-.09.37h.99s-1.66,5.44-1.66,5.44h.75l.93-3.08c.19-.26.4-.54.65-.82.25-.28.49-.54.75-.77.25-.23.5-.42.75-.57.25-.15.46-.22.66-.22.26,0,.39.14.39.41,0,.15-.04.36-.13.64l-1.35,4.41h.74l1.04-3.3c.18-.25.39-.5.63-.76.24-.26.48-.49.73-.69.25-.21.48-.38.72-.51.23-.13.43-.2.6-.2.13,0,.23.03.3.08.07.05.11.16.11.3,0,.16-.05.39-.14.67l-1.25,3.96c-.06.2-.05.34.02.43.08.08.19.12.34.12.2,0,.43-.03.69-.09ZM455.52,287.41c.41-.21.77-.51,1.1-.88.32-.37.58-.82.78-1.32.2-.51.3-1.06.3-1.66s-.16-1.08-.48-1.45c-.32-.37-.79-.55-1.41-.55-.41,0-.82.11-1.23.32s-.78.5-1.1.88-.59.82-.8,1.32c-.21.51-.31,1.06-.31,1.66s.17,1.07.5,1.45c.33.37.81.56,1.41.56.42,0,.83-.11,1.24-.32ZM453.43,286.88c-.21-.24-.31-.58-.31-1.02,0-.5.07-.99.22-1.45.14-.46.34-.87.58-1.22.24-.35.52-.63.84-.84.32-.21.65-.31.99-.31.4,0,.7.12.9.35.2.24.3.57.3,1.01,0,.5-.07.99-.22,1.45-.14.46-.34.88-.58,1.23-.24.36-.52.64-.83.85-.32.21-.64.31-.99.31-.4,0-.7-.12-.9-.36ZM447.55,287.62h.75l.84-2.75c.19-.33.41-.65.65-.97.24-.32.48-.61.73-.87.25-.26.49-.47.72-.63.24-.16.45-.24.65-.24l-.15.85h.51c.1-.13.19-.29.26-.48.07-.19.11-.35.11-.49,0-.33-.2-.49-.6-.49-.21,0-.43.06-.65.19-.23.13-.45.3-.68.51-.23.21-.45.46-.67.74-.22.28-.42.57-.61.86h-.04l.69-2.24-.08-.08-1.68.27-.09.37h.99s-1.66,5.44-1.66,5.44ZM444.84,282.18h.79l-1.25,5.56c-.08.34-.17.62-.26.84-.1.22-.2.4-.32.52-.12.13-.24.22-.37.27-.13.05-.26.08-.4.08-.1,0-.19-.01-.26-.03l.11-.69h-.4c-.09.06-.16.15-.23.26-.07.11-.1.23-.1.37,0,.19.09.33.26.43.18.1.38.15.62.15.54,0,.97-.21,1.31-.62.33-.41.6-1.02.78-1.81l1.26-5.33h1.53l.13-.53h-1.53c.19-.77.44-1.34.75-1.7.3-.36.67-.54,1.11-.54.22,0,.39.05.5.14l-.13.71h.5c.06-.07.13-.17.19-.3.07-.13.1-.26.1-.37,0-.23-.1-.39-.29-.5-.19-.11-.46-.16-.81-.16-.26,0-.52.05-.78.14-.26.09-.51.25-.75.46-.24.21-.46.49-.67.83-.2.35-.37.78-.49,1.29l-.84.19-.08.35ZM439.22,282.18h.85l-1.27,4.21c-.03.09-.05.18-.06.27l-.02.23c0,.28.09.49.26.62.18.13.39.2.66.2.33,0,.6-.04.83-.13.23-.09.41-.18.56-.27l.09-.39c-.17.07-.36.13-.57.18-.21.05-.44.08-.69.08-.23,0-.36-.07-.39-.2-.03-.14-.02-.31.04-.51l1.31-4.28h1.41l.14-.53h-1.4l.46-1.49h-.75l-.43,1.47-.95.2-.08.35ZM435.84,282.18h.79l-1.25,5.56c-.08.34-.17.62-.26.84-.1.22-.2.4-.32.52-.12.13-.24.22-.37.27-.13.05-.26.08-.4.08-.1,0-.19-.01-.26-.03l.11-.69h-.4c-.09.06-.16.15-.23.26-.07.11-.1.23-.1.37,0,.19.09.33.26.43.18.1.38.15.62.15.54,0,.97-.21,1.31-.62.33-.41.6-1.02.78-1.81l1.26-5.33h1.53l.13-.53h-1.53c.19-.77.44-1.34.75-1.7.3-.36.67-.54,1.11-.54.22,0,.39.05.5.14l-.13.71h.5c.06-.07.13-.17.19-.3.07-.13.1-.26.1-.37,0-.23-.1-.39-.29-.5-.19-.11-.46-.16-.81-.16-.26,0-.52.05-.78.14-.26.09-.51.25-.75.46-.24.21-.46.49-.67.83-.2.35-.37.78-.49,1.29l-.84.19-.08.35ZM435.19,280.11c.1-.1.15-.22.15-.38,0-.12-.04-.22-.12-.3-.08-.09-.19-.13-.33-.13s-.26.05-.36.15c-.1.1-.15.22-.15.38,0,.12.04.22.12.3.08.09.19.13.33.13s.26-.05.36-.15ZM433.15,287.68c.12-.02.25-.04.38-.07.13-.03.25-.06.36-.09.11-.04.21-.06.3-.09l.09-.32-1.07.02,1.65-5.52-.08-.08-1.77.27-.09.37h1.02s-1.46,4.98-1.46,4.98c-.06.2-.06.34.02.43.07.08.18.12.33.12.1,0,.21,0,.34-.03ZM428.73,284.79c-.15-.17-.23-.41-.23-.72s.04-.58.13-.83c.09-.25.21-.47.36-.66.15-.19.33-.33.54-.43.21-.1.43-.15.67-.15.31,0,.54.08.69.25.15.17.23.41.23.72s-.04.58-.13.83c-.09.25-.21.47-.36.66-.15.19-.33.33-.54.43-.21.1-.43.15-.67.15-.31,0-.54-.08-.69-.25ZM430.08,287.69c.15.08.26.15.35.23l.18.23.05.24c0,.13-.04.26-.12.39-.08.13-.19.24-.35.34-.16.1-.35.17-.59.23-.24.06-.51.09-.82.09-.67,0-1.18-.08-1.53-.25-.35-.17-.53-.41-.53-.72,0-.15.04-.29.13-.43.09-.14.21-.27.37-.39s.34-.23.55-.34c.21-.1.44-.2.68-.28.14.06.29.13.46.19.17.07.36.14.59.21.24.09.44.18.58.26ZM430.7,289.45c.44-.32.67-.71.67-1.18,0-.18-.03-.34-.09-.47-.06-.14-.16-.26-.3-.38-.14-.12-.31-.23-.52-.33-.21-.1-.47-.21-.77-.32-.25-.08-.45-.16-.61-.23-.16-.07-.28-.13-.36-.19l-.17-.18-.04-.21c0-.12.02-.24.07-.34.05-.1.1-.19.15-.26.19.07.41.11.65.11.31,0,.61-.06.91-.18.3-.12.56-.29.79-.51.23-.22.41-.48.55-.79.14-.31.21-.64.21-1.01,0-.41-.12-.74-.36-.98l1.11.13.16-.61-1.74.15c-.11-.04-.23-.07-.36-.1-.13-.03-.26-.04-.4-.04-.31,0-.61.06-.91.18-.3.12-.56.29-.79.51-.23.22-.42.48-.56.79-.14.31-.22.64-.22,1.01,0,.27.06.51.17.72.11.21.27.37.47.48-.13.13-.25.29-.34.47-.1.18-.15.39-.15.61,0,.11.01.2.04.27l.1.21c-.3.09-.57.2-.83.32-.26.12-.48.25-.68.4-.19.14-.35.3-.46.47-.11.17-.16.36-.16.56s.07.39.2.56c.13.17.32.31.56.43.24.12.53.21.87.28.34.07.72.1,1.14.1.88,0,1.54-.16,1.98-.48ZM420.21,287.62h.75l.84-2.75c.19-.33.41-.65.65-.97.24-.32.48-.61.73-.87.25-.26.49-.47.72-.63.24-.16.45-.24.65-.24l-.15.85h.51c.1-.13.19-.29.26-.48.07-.19.11-.35.11-.49,0-.33-.2-.49-.6-.49-.21,0-.43.06-.65.19-.23.13-.45.3-.68.51-.23.21-.45.46-.67.74-.22.28-.42.57-.61.86h-.04l.69-2.24-.08-.08-1.68.27-.09.37h.99s-1.66,5.44-1.66,5.44ZM415.74,287.57c.24-.11.49-.25.74-.43.25-.18.5-.39.75-.63.25-.24.49-.5.72-.78h.05l-.46,1.44c-.06.2-.06.34.02.43.07.08.18.12.33.12.1,0,.21,0,.33-.03.12-.02.24-.04.37-.07.12-.03.24-.06.36-.09.11-.04.21-.06.3-.09l.08-.32-1.04.02,1.63-5.49h-.75l-.98,3.23c-.2.26-.43.52-.68.78-.25.26-.51.5-.77.72-.26.22-.51.4-.76.53-.25.14-.45.2-.62.2-.27,0-.41-.15-.41-.44,0-.16.04-.37.12-.61l1.39-4.45-.08-.08-1.7.27-.09.37h.98s-1.26,3.91-1.26,3.91c-.04.14-.07.27-.09.39-.02.12-.04.23-.04.34,0,.3.08.52.23.68.16.16.37.23.65.23.22,0,.45-.05.69-.16ZM411.38,287.41c.41-.21.77-.51,1.1-.88.32-.37.58-.82.78-1.32.2-.51.3-1.06.3-1.66s-.16-1.08-.48-1.45c-.32-.37-.79-.55-1.41-.55-.41,0-.82.11-1.23.32-.41.21-.78.5-1.1.88s-.59.82-.8,1.32c-.21.51-.31,1.06-.31,1.66s.17,1.07.5,1.45c.33.37.81.56,1.41.56.42,0,.83-.11,1.24-.32ZM409.29,286.88c-.21-.24-.31-.58-.31-1.02,0-.5.07-.99.22-1.45.14-.46.34-.87.58-1.22.24-.35.52-.63.84-.84.32-.21.65-.31.99-.31.4,0,.7.12.9.35.2.24.3.57.3,1.01,0,.5-.07.99-.22,1.45-.14.46-.34.88-.58,1.23-.24.36-.52.64-.83.85-.32.21-.64.31-.99.31-.4,0-.7-.12-.9-.36ZM401.61,288.58h-.48c-.08.08-.16.19-.25.33-.09.14-.13.29-.13.44,0,.2.07.35.22.46.16.1.35.15.6.15.3,0,.6-.05.89-.16.29-.11.58-.26.86-.45.28-.19.57-.43.86-.71.29-.28.58-.59.89-.93.24-.27.49-.58.75-.92.26-.34.51-.7.75-1.06.25-.37.48-.73.69-1.1.22-.37.41-.72.58-1.06.17-.33.3-.64.4-.92.1-.28.15-.51.15-.7.02-.26-.12-.39-.4-.39-.2,0-.41.03-.62.09-.21.06-.39.13-.54.21l-.07.34.97-.02c-.06.3-.19.67-.38,1.09-.19.42-.42.86-.68,1.32-.26.46-.55.91-.86,1.37-.31.45-.62.87-.92,1.25h-.04c.05-.41.08-.86.1-1.33.02-.47.03-.95.03-1.41s0-.91-.02-1.32c-.02-.42-.04-.77-.06-1.07-.02-.19-.05-.33-.11-.4-.06-.07-.16-.11-.31-.11-.18,0-.38.03-.6.08-.22.05-.4.11-.55.17l-.09.38.89-.02c.03.28.06.62.08,1.02.02.4.04.85.05,1.35,0,.5,0,1.04-.01,1.63-.02.59-.05,1.21-.09,1.86-.4.41-.82.75-1.24,1.01-.42.26-.83.39-1.21.39-.12,0-.21-.02-.27-.05l.18-.81ZM394.07,288.58h-.48c-.08.08-.16.19-.25.33-.09.14-.13.29-.13.44,0,.2.07.35.22.46.16.1.35.15.6.15.3,0,.6-.05.89-.16.29-.11.58-.26.86-.45.28-.19.57-.43.86-.71.29-.28.58-.59.89-.93.24-.27.49-.58.75-.92.26-.34.51-.7.75-1.06.25-.37.48-.73.69-1.1.22-.37.41-.72.58-1.06.17-.33.3-.64.4-.92.1-.28.15-.51.15-.7.02-.26-.12-.39-.4-.39-.2,0-.41.03-.62.09-.21.06-.39.13-.54.21l-.07.34.97-.02c-.06.3-.19.67-.38,1.09-.19.42-.42.86-.68,1.32-.26.46-.55.91-.86,1.37-.31.45-.62.87-.92,1.25h-.04c.05-.41.08-.86.1-1.33.02-.47.03-.95.03-1.41s0-.91-.02-1.32c-.02-.42-.04-.77-.06-1.07-.02-.19-.05-.33-.11-.4-.06-.07-.16-.11-.31-.11-.18,0-.38.03-.6.08-.22.05-.4.11-.55.17l-.09.38.89-.02c.03.28.06.62.08,1.02.02.4.04.85.05,1.35,0,.5,0,1.04-.01,1.63-.02.59-.05,1.21-.09,1.86-.4.41-.82.75-1.24,1.01-.42.26-.83.39-1.21.39-.12,0-.21-.02-.27-.05l.18-.81ZM392.8,287.41c.41-.21.77-.51,1.1-.88.32-.37.58-.82.78-1.32.2-.51.3-1.06.3-1.66s-.16-1.08-.48-1.45c-.32-.37-.79-.55-1.41-.55-.41,0-.82.11-1.23.32-.41.21-.78.5-1.1.88s-.59.82-.8,1.32c-.21.51-.31,1.06-.31,1.66s.17,1.07.5,1.45c.33.37.81.56,1.41.56.42,0,.83-.11,1.24-.32ZM390.71,286.88c-.21-.24-.31-.58-.31-1.02,0-.5.07-.99.22-1.45.14-.46.34-.87.58-1.22.24-.35.52-.63.84-.84.32-.21.65-.31.99-.31.4,0,.7.12.9.35.2.24.3.57.3,1.01,0,.5-.07.99-.22,1.45-.14.46-.34.88-.58,1.23-.24.36-.52.64-.83.85-.32.21-.64.31-.99.31-.4,0-.7-.12-.9-.36ZM389.79,280.11c.1-.1.15-.22.15-.38,0-.12-.04-.22-.12-.3-.08-.09-.19-.13-.33-.13s-.26.05-.36.15c-.1.1-.15.22-.15.38,0,.12.04.22.12.3.08.09.19.13.33.13s.26-.05.36-.15ZM385.21,288.68h-.4c-.07.06-.14.14-.22.25-.07.1-.11.22-.11.35,0,.12.03.22.09.3.06.09.14.15.23.2.1.05.21.09.33.12.12.03.25.04.37.04.51,0,.93-.17,1.25-.5.32-.33.58-.82.76-1.45l1.91-6.37-.08-.08-1.77.27-.09.37h1.02s-1.75,6-1.75,6c-.14.47-.32.79-.53.98-.21.19-.45.29-.71.29-.17,0-.32-.03-.43-.1l.12-.67ZM384.85,287.68c.12-.02.24-.04.36-.07.12-.03.24-.06.34-.09.11-.04.21-.06.29-.09l.09-.32-1.03.02,1.27-3.97c.09-.26.14-.51.14-.74,0-.29-.07-.51-.22-.66-.14-.15-.34-.23-.59-.23s-.51.06-.77.18c-.26.12-.53.27-.8.47-.27.2-.53.43-.79.7-.26.27-.5.55-.72.85h-.05l.65-2.12-.08-.08-1.68.27-.09.37h.99s-1.66,5.44-1.66,5.44h.75l.92-3.01c.19-.26.42-.54.68-.83.26-.29.52-.56.8-.79.28-.24.54-.43.81-.59.26-.16.49-.23.69-.23.12,0,.21.03.27.09.07.06.1.17.1.32s-.04.36-.13.64l-1.25,3.96c-.06.2-.06.34.02.43.07.08.18.12.33.12.1,0,.21,0,.33-.03ZM373.28,287.62h6.02l.6-2.17h-.42l-.79,1.68h-3.44l.86-3.42h1.86l-.04,1.11h.39l.58-2.71h-.4l-.47,1.11h-1.82l.82-3.2h2.96l.13,1.41h.42l.19-1.9h-5.43l-.08.36.98.16-1.76,7.03-1.07.16-.08.37Z" />
          <path
            d="M561.77,354.43c.25-.23.37-.52.37-.9,0-.22-.04-.4-.11-.53-.07-.14-.17-.25-.3-.33-.13-.09-.28-.15-.46-.2-.18-.05-.37-.09-.58-.14-.19-.03-.34-.07-.46-.1-.12-.04-.22-.08-.29-.13l-.15-.17c-.03-.06-.04-.14-.04-.24,0-.18.07-.32.21-.42.14-.11.33-.16.56-.16.26,0,.46.05.59.15.13.1.22.26.27.49h.62c-.03-.22-.08-.4-.17-.56-.09-.15-.2-.27-.33-.37-.13-.09-.28-.16-.45-.2-.17-.04-.34-.06-.52-.06-.16,0-.32.02-.49.07-.16.05-.31.12-.44.22-.13.1-.24.22-.32.36-.08.14-.12.31-.12.49,0,.18.02.33.07.46.05.13.13.24.24.33s.25.17.44.24c.18.06.4.12.67.17.32.06.55.13.71.22.16.09.24.25.24.46s-.07.38-.21.48c-.14.11-.37.16-.68.16-.33,0-.56-.07-.7-.21-.14-.14-.23-.33-.25-.58h-.63c.02.42.17.75.45.98.28.23.66.35,1.15.35s.87-.11,1.12-.34ZM557.94,349.65c.08-.08.12-.18.12-.3s-.04-.22-.12-.3c-.08-.08-.18-.12-.3-.12s-.22.04-.3.12-.12.18-.12.3.04.22.12.3c.08.08.18.12.3.12s.22-.04.3-.12ZM557.32,354.69h.65v-4.07h-.65v4.07ZM552.56,354.69h.65v-2.48c0-.18.03-.34.09-.48s.15-.26.25-.35c.1-.09.22-.16.36-.21s.28-.07.43-.07c.33,0,.57.08.72.25.16.17.23.43.23.79v2.56h.65v-2.5c0-.3-.04-.55-.11-.75-.07-.21-.17-.38-.3-.51-.13-.13-.28-.23-.46-.29-.18-.06-.37-.09-.58-.09-.34,0-.62.07-.84.21-.22.14-.38.31-.47.5v-2.59h-.65v6.02ZM551.27,354.72l.23-.06v-.54l-.23.06c-.08.02-.17.02-.27.02-.34,0-.51-.2-.51-.59v-2.44h.95v-.54h-.95v-.93h-.65v.93h-.58v.54h.58v2.51c0,.32.09.58.27.78.18.2.46.3.83.3.13,0,.24,0,.33-.02ZM546.35,354.72l.23-.06v-.54l-.23.06c-.08.02-.17.02-.27.02-.34,0-.51-.2-.51-.59v-2.44h.95v-.54h-.95v-.93h-.65v.93h-.58v.54h.58v2.51c0,.32.09.58.27.78.18.2.46.3.83.3.13,0,.24,0,.33-.02ZM542.27,354.55c.22-.14.38-.31.47-.5v.65h.65v-4.07h-.65v2.48c0,.18-.03.34-.09.48-.06.14-.15.26-.25.35-.1.09-.22.16-.36.21-.14.05-.28.07-.42.07-.32,0-.56-.08-.71-.25-.15-.17-.23-.43-.23-.79v-2.56h-.65v2.58c0,.28.04.52.11.72.07.2.17.36.3.49.12.13.27.22.45.28.17.06.36.09.56.09.33,0,.6-.07.82-.21ZM537.77,354.61c.24-.1.46-.24.63-.42.18-.18.32-.4.42-.66.1-.26.15-.54.15-.84v-.06c0-.31-.05-.59-.15-.84-.1-.25-.24-.47-.42-.65-.18-.18-.39-.32-.63-.42-.25-.1-.51-.15-.8-.15s-.56.05-.8.15c-.24.1-.45.24-.63.43-.18.18-.32.4-.42.66-.1.25-.15.53-.15.84v.06c0,.3.05.57.15.82.1.25.24.47.41.66.18.18.39.33.63.43.24.1.51.16.8.16s.56-.05.8-.15ZM536,353.8c-.24-.28-.35-.65-.35-1.11v-.06c0-.23.03-.44.09-.63.06-.19.15-.35.26-.48.11-.13.25-.24.42-.31.17-.07.35-.11.55-.11s.39.04.55.11c.16.07.3.18.42.31.12.13.21.3.27.49.06.19.09.4.09.63v.05c0,.23-.03.43-.09.62-.06.19-.15.35-.26.48-.11.13-.25.24-.42.31-.17.07-.35.11-.55.11-.42,0-.74-.14-.98-.41ZM531.72,354.13c-.17-.06-.31-.16-.42-.28-.12-.13-.21-.29-.27-.48-.06-.19-.1-.42-.1-.67v-.06c0-.26.03-.48.1-.68.07-.19.16-.35.28-.48.12-.13.26-.22.42-.28.16-.06.34-.09.53-.09.17,0,.33.03.49.1.15.06.29.16.4.29.11.13.2.29.27.48.07.19.1.42.1.67v.06c0,.5-.11.87-.32,1.13-.21.26-.52.39-.93.39-.2,0-.38-.03-.54-.09ZM533.09,354.61c.23-.1.42-.25.58-.43.16-.19.29-.41.37-.67.09-.26.13-.54.13-.85v-.06c0-.32-.05-.6-.14-.86-.09-.25-.22-.47-.39-.65-.16-.18-.36-.31-.59-.4-.23-.09-.47-.14-.74-.14-.15,0-.3.02-.44.06-.14.04-.27.1-.39.17-.12.07-.23.15-.32.24-.1.09-.17.19-.23.28v-2.63h-.65v6.02h.65v-.67c.12.2.3.38.53.52.23.15.52.22.85.22.28,0,.53-.05.76-.15ZM526.49,354.07c-.11-.12-.17-.29-.17-.5,0-.15.04-.28.11-.38.07-.1.17-.18.3-.25.13-.06.28-.11.45-.14.17-.03.36-.04.56-.04h.56v.51c0,.16-.03.29-.09.42-.06.12-.15.22-.25.31-.11.08-.23.15-.38.19-.15.04-.31.07-.48.07-.29,0-.49-.06-.6-.18ZM527.78,354.62c.2-.1.37-.24.52-.44v.51h.65v-2.68c0-.29-.04-.52-.12-.71-.08-.19-.19-.34-.33-.45-.13-.11-.29-.19-.47-.23-.18-.04-.37-.07-.56-.07s-.39.02-.57.07c-.18.04-.35.12-.5.22-.15.1-.27.23-.37.39-.1.16-.15.36-.18.59h.65c.06-.48.37-.72.93-.72.31,0,.53.07.67.21.13.14.2.37.2.7v.29h-.58c-.26,0-.51.02-.76.07-.25.04-.46.12-.65.21-.19.1-.34.23-.46.39-.11.16-.17.36-.17.59,0,.21.04.39.11.54.07.15.17.28.29.37.12.1.27.17.43.22.17.05.34.07.53.07.3,0,.55-.05.75-.15ZM521.88,354.69h.65v-6.02h-.65v6.02ZM519.8,354.69h.65v-6.02h-.65v6.02ZM515.97,354.07c-.11-.12-.17-.29-.17-.5,0-.15.04-.28.11-.38.07-.1.17-.18.3-.25.13-.06.28-.11.45-.14.17-.03.36-.04.56-.04h.56v.51c0,.16-.03.29-.09.42-.06.12-.15.22-.25.31-.11.08-.23.15-.38.19-.15.04-.31.07-.48.07-.29,0-.49-.06-.6-.18ZM517.25,354.62c.2-.1.37-.24.52-.44v.51h.65v-2.68c0-.29-.04-.52-.12-.71-.08-.19-.19-.34-.33-.45-.13-.11-.29-.19-.47-.23-.18-.04-.37-.07-.56-.07s-.39.02-.57.07c-.18.04-.35.12-.5.22-.15.1-.27.23-.37.39-.1.16-.15.36-.18.59h.65c.06-.48.37-.72.93-.72.31,0,.53.07.67.21.13.14.2.37.2.7v.29h-.58c-.26,0-.51.02-.76.07-.25.04-.46.12-.65.21-.19.1-.34.23-.46.39-.11.16-.17.36-.17.59,0,.21.03.39.11.54.07.15.17.28.29.37.12.1.27.17.43.22.17.05.34.07.53.07.3,0,.55-.05.75-.15ZM508.68,354.69h.65v-2.48c0-.18.03-.34.09-.48.06-.14.15-.26.25-.35.1-.09.22-.16.36-.21.14-.05.28-.07.43-.07.33,0,.57.08.72.25.16.17.23.43.23.79v2.56h.65v-2.5c0-.3-.04-.55-.11-.75-.07-.21-.17-.38-.3-.51-.13-.13-.28-.23-.46-.29-.18-.06-.37-.09-.58-.09-.34,0-.62.07-.84.21-.22.14-.38.31-.47.5v-.65h-.65v4.07ZM505.66,354.69h.65v-2.23c0-.25.03-.46.09-.62.06-.16.15-.29.27-.39.12-.1.26-.17.42-.21.17-.04.35-.07.55-.08v-.61c-.18,0-.34.03-.48.07-.14.04-.26.1-.37.17-.1.07-.2.15-.28.25-.08.1-.15.2-.21.32v-.73h-.65v4.07ZM501.84,354.07c-.11-.12-.17-.29-.17-.5,0-.15.04-.28.11-.38.07-.1.17-.18.3-.25.13-.06.28-.11.45-.14.17-.03.36-.04.56-.04h.56v.51c0,.16-.03.29-.09.42-.06.12-.15.22-.25.31-.11.08-.23.15-.38.19-.15.04-.31.07-.48.07-.29,0-.49-.06-.6-.18ZM503.13,354.62c.2-.1.37-.24.52-.44v.51h.65v-2.68c0-.29-.04-.52-.12-.71-.08-.19-.19-.34-.33-.45-.13-.11-.29-.19-.47-.23-.18-.04-.37-.07-.56-.07s-.39.02-.57.07c-.18.04-.35.12-.5.22-.15.1-.27.23-.37.39-.1.16-.15.36-.18.59h.65c.06-.48.37-.72.93-.72.31,0,.53.07.67.21.13.14.2.37.2.7v.29h-.58c-.26,0-.51.02-.76.07-.25.04-.46.12-.65.21-.19.1-.34.23-.46.39-.11.16-.17.36-.17.59,0,.21.03.39.11.54.07.15.17.28.29.37.12.1.27.17.43.22.17.05.34.07.53.07.3,0,.55-.05.75-.15ZM497.24,352.27c.06-.36.19-.65.4-.85.21-.21.48-.31.81-.31s.61.09.81.27c.2.18.32.48.35.9h-2.38ZM499.67,354.45c.31-.21.5-.52.58-.92h-.65c-.08.46-.44.69-1.08.69-.42,0-.74-.12-.96-.36-.22-.24-.33-.59-.35-1.05h3.07v-.21c0-.37-.05-.68-.15-.94-.1-.26-.24-.47-.41-.63-.17-.16-.37-.28-.59-.36-.22-.08-.45-.11-.68-.11-.28,0-.54.05-.77.15-.23.1-.43.24-.6.43-.17.18-.3.4-.39.66s-.14.54-.14.84v.06c0,.31.05.59.14.85.1.25.23.47.41.65.18.18.38.32.62.42.24.1.5.15.79.15.46,0,.85-.1,1.16-.31ZM494.78,354.69h.65v-6.02h-.65v6.02ZM488.85,354.13c-.15-.06-.28-.15-.39-.28-.11-.12-.2-.28-.26-.47-.06-.19-.1-.41-.1-.67v-.06c0-.5.11-.88.34-1.15.23-.27.54-.4.95-.4s.71.12.95.37c.23.25.35.63.35,1.15v.06c0,.26-.04.48-.11.68-.07.19-.16.35-.28.48-.12.13-.26.22-.43.28-.17.06-.34.09-.53.09-.17,0-.33-.03-.48-.09ZM489.69,354.7c.14-.04.27-.1.4-.17.12-.07.23-.15.33-.25.1-.09.18-.19.23-.29v.7h.65v-6.02h-.65v2.6c-.12-.2-.29-.37-.5-.51-.21-.14-.49-.21-.83-.21-.28,0-.54.05-.77.16-.23.1-.43.25-.6.44-.17.19-.3.41-.39.67-.09.26-.14.54-.14.86v.06c0,.32.05.6.14.85.09.25.22.46.38.64.16.17.36.31.58.4s.47.14.74.14c.15,0,.29-.02.43-.07ZM482.98,354.69h.65v-2.48c0-.18.03-.34.09-.48s.15-.26.25-.35c.1-.09.22-.16.36-.21.14-.05.28-.07.43-.07.33,0,.57.08.72.25.16.17.23.43.23.79v2.56h.65v-2.5c0-.3-.04-.55-.11-.75-.07-.21-.17-.38-.3-.51-.13-.13-.28-.23-.46-.29-.18-.06-.37-.09-.58-.09-.34,0-.62.07-.84.21-.22.14-.38.31-.47.5v-.65h-.65v4.07ZM479.17,354.07c-.11-.12-.17-.29-.17-.5,0-.15.04-.28.11-.38.07-.1.17-.18.3-.25.13-.06.28-.11.45-.14.17-.03.36-.04.56-.04h.56v.51c0,.16-.03.29-.09.42-.06.12-.15.22-.25.31-.11.08-.23.15-.38.19-.15.04-.31.07-.48.07-.29,0-.49-.06-.6-.18ZM480.45,354.62c.2-.1.37-.24.52-.44v.51h.65v-2.68c0-.29-.04-.52-.12-.71-.08-.19-.19-.34-.33-.45-.13-.11-.29-.19-.47-.23-.18-.04-.37-.07-.56-.07s-.39.02-.57.07c-.18.04-.35.12-.5.22-.15.1-.27.23-.37.39-.1.16-.15.36-.18.59h.65c.06-.48.37-.72.93-.72.31,0,.53.07.67.21.13.14.2.37.2.7v.29h-.58c-.26,0-.51.02-.76.07-.25.04-.46.12-.65.21-.19.1-.34.23-.46.39-.11.16-.17.36-.17.59,0,.21.04.39.11.54.07.15.17.28.29.37.12.1.27.17.43.22.17.05.34.07.53.07.3,0,.55-.05.75-.15ZM472.78,356.11h.65l2.25-5.49h-.66l-1.13,2.92-1.24-2.92h-.69l1.6,3.65-.77,1.84ZM470.97,354.43c.25-.23.37-.52.37-.9,0-.22-.04-.4-.11-.53-.07-.14-.17-.25-.3-.33-.13-.09-.28-.15-.46-.2-.18-.05-.37-.09-.58-.14-.19-.03-.34-.07-.46-.1-.12-.04-.22-.08-.29-.13l-.15-.17c-.03-.06-.04-.14-.04-.24,0-.18.07-.32.21-.42.14-.11.33-.16.56-.16.26,0,.46.05.59.15.13.1.22.26.27.49h.62c-.03-.22-.08-.4-.17-.56-.09-.15-.2-.27-.33-.37-.13-.09-.28-.16-.45-.2-.17-.04-.34-.06-.52-.06-.16,0-.32.02-.49.07-.16.05-.31.12-.44.22-.13.1-.24.22-.32.36-.08.14-.12.31-.12.49,0,.18.02.33.07.46.05.13.13.24.24.33s.25.17.44.24c.18.06.4.12.67.17.32.06.55.13.71.22.16.09.24.25.24.46s-.07.38-.21.48c-.14.11-.37.16-.68.16-.33,0-.56-.07-.7-.21-.14-.14-.23-.33-.25-.58h-.63c.02.42.17.75.45.98.28.23.66.35,1.15.35s.87-.11,1.12-.34ZM467.29,354.72l.23-.06v-.54l-.23.06c-.08.02-.17.02-.27.02-.34,0-.51-.2-.51-.59v-2.44h.95v-.54h-.95v-.93h-.65v.93h-.58v.54h.58v2.51c0,.32.09.58.27.78.18.2.46.3.83.3.13,0,.24,0,.33-.02ZM461.28,354.69h3.38v-.54h-2.71v-2.03h2.07v-.54h-2.07v-1.9h2.57v-.54h-3.24v5.56ZM454.42,354.69h.65v-2.48c0-.18.03-.34.09-.48s.15-.26.25-.35c.1-.09.22-.16.36-.21.14-.05.28-.07.43-.07.33,0,.57.08.72.25.16.17.23.43.23.79v2.56h.65v-2.5c0-.3-.04-.55-.11-.75-.07-.21-.17-.38-.3-.51-.13-.13-.28-.23-.46-.29-.18-.06-.37-.09-.58-.09-.34,0-.62.07-.84.21-.22.14-.38.31-.47.5v-.65h-.65v4.07ZM452.12,354.61c.24-.1.46-.24.63-.42.18-.18.32-.4.42-.66.1-.26.15-.54.15-.84v-.06c0-.31-.05-.59-.15-.84-.1-.25-.24-.47-.42-.65-.18-.18-.39-.32-.63-.42-.25-.1-.51-.15-.8-.15s-.56.05-.8.15c-.24.1-.45.24-.63.43-.18.18-.32.4-.42.66-.1.25-.15.53-.15.84v.06c0,.3.05.57.15.82.1.25.24.47.41.66.18.18.39.33.63.43.24.1.51.16.8.16s.56-.05.8-.15ZM450.35,353.8c-.24-.28-.35-.65-.35-1.11v-.06c0-.23.03-.44.09-.63.06-.19.15-.35.26-.48.11-.13.25-.24.42-.31.17-.07.35-.11.55-.11s.39.04.55.11c.16.07.3.18.42.31.12.13.21.3.27.49.06.19.09.4.09.63v.05c0,.23-.03.43-.09.62-.06.19-.15.35-.26.48-.11.13-.25.24-.42.31-.17.07-.35.11-.55.11-.42,0-.74-.14-.98-.41ZM446.12,354.43c.25-.23.37-.52.37-.9,0-.22-.04-.4-.11-.53-.07-.14-.17-.25-.3-.33-.13-.09-.28-.15-.46-.2-.18-.05-.37-.09-.58-.14-.19-.03-.34-.07-.46-.1-.12-.04-.22-.08-.29-.13l-.15-.17c-.03-.06-.04-.14-.04-.24,0-.18.07-.32.21-.42.14-.11.33-.16.56-.16.26,0,.46.05.59.15.13.1.22.26.27.49h.62c-.03-.22-.08-.4-.17-.56-.09-.15-.2-.27-.33-.37-.13-.09-.28-.16-.45-.2-.17-.04-.34-.06-.52-.06-.16,0-.32.02-.49.07-.16.05-.31.12-.44.22s-.24.22-.32.36c-.08.14-.12.31-.12.49,0,.18.02.33.07.46.05.13.13.24.24.33s.25.17.44.24c.18.06.4.12.67.17.32.06.55.13.71.22.16.09.24.25.24.46s-.07.38-.21.48c-.14.11-.37.16-.68.16-.33,0-.56-.07-.7-.21-.14-.14-.23-.33-.25-.58h-.63c.02.42.17.75.45.98.28.23.66.35,1.15.35s.87-.11,1.12-.34ZM439.89,354.13c-.15-.06-.28-.15-.39-.28-.11-.12-.2-.28-.26-.47-.06-.19-.1-.41-.1-.67v-.06c0-.5.11-.88.34-1.15.23-.27.54-.4.95-.4s.71.12.95.37c.23.25.35.63.35,1.15v.06c0,.26-.04.48-.11.68-.07.19-.16.35-.28.48-.12.13-.26.22-.43.28-.17.06-.34.09-.53.09-.17,0-.33-.03-.48-.09ZM440.73,354.7c.14-.04.27-.1.4-.17.12-.07.23-.15.33-.25.1-.09.18-.19.23-.29v.7h.65v-6.02h-.65v2.6c-.12-.2-.29-.37-.5-.51-.21-.14-.49-.21-.83-.21-.28,0-.54.05-.77.16-.23.1-.43.25-.6.44-.17.19-.3.41-.39.67-.09.26-.14.54-.14.86v.06c0,.32.05.6.14.85.09.25.22.46.38.64.16.17.36.31.58.4.23.09.47.14.74.14.15,0,.29-.02.43-.07ZM434.02,354.69h.65v-2.48c0-.18.03-.34.09-.48.06-.14.15-.26.25-.35.1-.09.22-.16.36-.21.14-.05.28-.07.43-.07.33,0,.57.08.72.25.16.17.23.43.23.79v2.56h.65v-2.5c0-.3-.04-.55-.11-.75-.07-.21-.17-.38-.3-.51-.13-.13-.28-.23-.46-.29-.18-.06-.37-.09-.58-.09-.34,0-.62.07-.84.21-.22.14-.38.31-.47.5v-.65h-.65v4.07ZM432.57,349.65c.08-.08.12-.18.12-.3s-.04-.22-.12-.3c-.08-.08-.18-.12-.3-.12s-.22.04-.3.12c-.08.08-.12.18-.12.3s.04.22.12.3c.08.08.18.12.3.12s.22-.04.3-.12ZM431.96,354.69h.65v-4.07h-.65v4.07ZM429.3,354.69h.65v-3.52h1v-.54h-1v-.73c0-.21.05-.37.14-.5.09-.13.24-.19.46-.19l.25.02.21.06v-.55l-.23-.06c-.07-.01-.15-.02-.26-.02-.4,0-.71.11-.91.34-.2.23-.3.55-.3.96v.66h-.58v.54h.58v3.52ZM425.03,354.69h.65v-6.02h-.65v6.02ZM421.2,354.07c-.11-.12-.17-.29-.17-.5,0-.15.04-.28.11-.38.07-.1.17-.18.3-.25.13-.06.28-.11.45-.14.17-.03.36-.04.56-.04h.56v.51c0,.16-.03.29-.09.42-.06.12-.15.22-.25.31-.11.08-.23.15-.38.19-.15.04-.31.07-.48.07-.29,0-.49-.06-.6-.18ZM422.48,354.62c.2-.1.37-.24.52-.44v.51h.65v-2.68c0-.29-.04-.52-.12-.71-.08-.19-.19-.34-.33-.45-.13-.11-.29-.19-.47-.23-.18-.04-.37-.07-.56-.07s-.39.02-.57.07c-.18.04-.35.12-.5.22-.15.1-.27.23-.37.39-.1.16-.15.36-.18.59h.65c.06-.48.37-.72.93-.72.31,0,.53.07.67.21.13.14.2.37.2.7v.29h-.58c-.26,0-.51.02-.76.07-.25.04-.46.12-.65.21-.19.1-.34.23-.46.39-.11.16-.17.36-.17.59,0,.21.03.39.11.54.07.15.17.28.29.37.12.1.27.17.43.22.17.05.34.07.53.07.3,0,.55-.05.75-.15ZM419.27,349.65c.08-.08.12-.18.12-.3s-.04-.22-.12-.3c-.08-.08-.18-.12-.3-.12s-.22.04-.3.12c-.08.08-.12.18-.12.3s.04.22.12.3c.08.08.18.12.3.12s.22-.04.3-.12ZM418.65,354.69h.65v-4.07h-.65v4.07ZM416.47,354.65c.21-.08.39-.18.54-.32.16-.13.28-.3.38-.49.1-.19.15-.39.18-.61h-.6c-.02.16-.06.31-.13.43s-.16.23-.26.31c-.11.08-.23.14-.36.19-.13.04-.27.06-.42.06-.39,0-.71-.13-.96-.38-.25-.26-.37-.63-.37-1.13v-.06c0-.24.03-.45.1-.64.07-.19.16-.35.28-.48.12-.13.25-.23.41-.31.16-.07.33-.11.52-.11.28,0,.53.07.74.2.21.13.34.36.39.68h.64c-.03-.26-.1-.48-.21-.66-.11-.18-.24-.33-.4-.44-.16-.11-.34-.19-.54-.25-.2-.05-.41-.08-.62-.08-.28,0-.53.05-.77.15-.24.1-.45.24-.63.42-.18.18-.32.4-.42.66-.1.26-.16.54-.16.85v.06c0,.32.05.6.15.86.1.25.24.47.42.65.18.18.39.32.63.41.24.1.5.14.79.14.24,0,.47-.04.68-.12ZM409.98,352.27c.06-.36.19-.65.4-.85.21-.21.48-.31.81-.31s.61.09.81.27c.2.18.32.48.35.9h-2.38ZM412.42,354.45c.31-.21.5-.52.58-.92h-.65c-.08.46-.44.69-1.08.69-.42,0-.74-.12-.96-.36-.22-.24-.33-.59-.35-1.05h3.07v-.21c0-.37-.05-.68-.15-.94-.1-.26-.24-.47-.41-.63-.17-.16-.37-.28-.59-.36-.22-.08-.45-.11-.68-.11-.28,0-.54.05-.77.15-.23.1-.43.24-.6.43-.17.18-.3.4-.39.66s-.14.54-.14.84v.06c0,.31.05.59.14.85.1.25.23.47.41.65.18.18.38.32.62.42.24.1.5.15.79.15.46,0,.85-.1,1.16-.31ZM406.04,354.13c-.17-.06-.31-.16-.42-.28-.12-.13-.21-.29-.27-.48-.06-.19-.1-.42-.1-.67v-.06c0-.26.03-.48.1-.68.07-.19.16-.35.28-.48.12-.13.26-.22.42-.28.16-.06.34-.09.53-.09.17,0,.33.03.49.1.15.06.29.16.4.29.11.13.2.29.27.48.07.19.1.42.1.67v.06c0,.5-.11.87-.32,1.13-.21.26-.52.39-.93.39-.2,0-.38-.03-.54-.09ZM404.62,356.11h.65v-2.09c.12.2.29.38.53.52.23.15.51.22.85.22.28,0,.53-.05.76-.15.23-.1.42-.25.58-.43.16-.19.29-.41.37-.67.09-.26.13-.54.13-.85v-.06c0-.32-.05-.6-.14-.86-.09-.25-.22-.47-.39-.65-.16-.18-.36-.31-.59-.4-.23-.09-.47-.14-.74-.14-.15,0-.3.02-.44.06-.14.04-.27.1-.39.17-.12.07-.23.15-.32.24-.1.09-.17.19-.23.28v-.68h-.65v5.49ZM403.18,354.43c.25-.23.37-.52.37-.9,0-.22-.04-.4-.11-.53-.07-.14-.17-.25-.3-.33-.13-.09-.28-.15-.46-.2-.18-.05-.37-.09-.58-.14-.19-.03-.34-.07-.46-.1-.12-.04-.22-.08-.29-.13l-.15-.17c-.03-.06-.04-.14-.04-.24,0-.18.07-.32.21-.42s.33-.16.56-.16c.26,0,.46.05.59.15.13.1.22.26.27.49h.62c-.03-.22-.08-.4-.17-.56-.09-.15-.2-.27-.33-.37-.13-.09-.28-.16-.45-.2-.17-.04-.34-.06-.52-.06-.16,0-.32.02-.49.07-.16.05-.31.12-.44.22s-.24.22-.32.36c-.08.14-.12.31-.12.49,0,.18.02.33.07.46.05.13.13.24.24.33s.25.17.44.24c.18.06.4.12.67.17.32.06.55.13.71.22.16.09.24.25.24.46s-.07.38-.21.48c-.14.11-.37.16-.68.16-.33,0-.56-.07-.7-.21-.14-.14-.23-.33-.25-.58h-.63c.02.42.17.75.45.98.28.23.66.35,1.15.35s.87-.11,1.12-.34ZM394.61,352.27c.06-.36.19-.65.4-.85.21-.21.48-.31.81-.31s.61.09.81.27c.2.18.32.48.35.9h-2.38ZM397.04,354.45c.31-.21.5-.52.58-.92h-.65c-.08.46-.44.69-1.08.69-.42,0-.74-.12-.96-.36s-.33-.59-.35-1.05h3.07v-.21c0-.37-.05-.68-.15-.94-.1-.26-.24-.47-.41-.63-.17-.16-.37-.28-.59-.36-.22-.08-.45-.11-.68-.11-.28,0-.54.05-.77.15-.23.1-.43.24-.6.43-.17.18-.3.4-.39.66-.09.26-.14.54-.14.84v.06c0,.31.05.59.14.85.1.25.23.47.41.65.18.18.38.32.62.42.24.1.5.15.79.15.46,0,.85-.1,1.16-.31ZM391.2,354.69h.65v-2.23c0-.25.03-.46.09-.62.06-.16.15-.29.27-.39.12-.1.26-.17.42-.21.17-.04.35-.07.55-.08v-.61c-.18,0-.34.03-.48.07-.14.04-.26.1-.37.17-.1.07-.2.15-.28.25-.08.1-.15.2-.21.32v-.73h-.65v4.07ZM388.9,354.61c.24-.1.46-.24.63-.42.18-.18.32-.4.42-.66.1-.26.15-.54.15-.84v-.06c0-.31-.05-.59-.15-.84-.1-.25-.24-.47-.42-.65-.18-.18-.39-.32-.63-.42-.25-.1-.51-.15-.8-.15s-.56.05-.8.15c-.24.1-.45.24-.63.43-.18.18-.32.4-.42.66-.1.25-.15.53-.15.84v.06c0,.3.05.57.15.82.1.25.24.47.41.66.18.18.39.33.63.43.24.1.51.16.8.16s.56-.05.8-.15ZM387.13,353.8c-.24-.28-.35-.65-.35-1.11v-.06c0-.23.03-.44.09-.63.06-.19.15-.35.26-.48.11-.13.25-.24.42-.31.17-.07.35-.11.55-.11s.39.04.55.11c.16.07.3.18.42.31.12.13.21.3.27.49.06.19.09.4.09.63v.05c0,.23-.03.43-.09.62-.06.19-.15.35-.26.48-.11.13-.25.24-.42.31-.17.07-.35.11-.55.11-.42,0-.74-.14-.98-.41ZM379.23,354.69h.65v-2.5c0-.18.03-.34.09-.48.06-.14.14-.25.25-.34s.22-.16.34-.2c.13-.04.26-.07.39-.07.28,0,.49.08.64.24.15.16.22.42.22.78v2.58h.65v-2.5c0-.18.03-.34.09-.48.06-.14.14-.25.25-.34s.22-.16.34-.2c.13-.04.26-.07.39-.07.28,0,.49.08.64.24.15.16.22.42.22.78v2.58h.65v-2.52c0-.3-.04-.55-.11-.75-.08-.2-.18-.37-.3-.5-.12-.13-.27-.22-.44-.28-.17-.06-.34-.09-.52-.09-.12,0-.25.02-.39.05-.13.03-.26.08-.39.14s-.24.14-.35.24c-.11.1-.2.21-.26.34-.1-.27-.26-.47-.47-.59-.21-.12-.45-.18-.72-.18-.3,0-.55.07-.76.2-.21.13-.36.3-.46.49v-.62h-.65v4.07ZM373.61,354.13c-.17-.06-.31-.16-.42-.28-.12-.13-.21-.29-.27-.48-.06-.19-.1-.42-.1-.67v-.06c0-.26.03-.48.1-.68.07-.19.16-.35.28-.48.12-.13.26-.22.42-.28.16-.06.34-.09.53-.09.17,0,.33.03.49.1.15.06.29.16.4.29.11.13.2.29.27.48.07.19.1.42.1.67v.06c0,.5-.11.87-.32,1.13-.21.26-.52.39-.93.39-.2,0-.38-.03-.54-.09ZM372.2,356.11h.65v-2.09c.12.2.29.38.53.52.23.15.51.22.85.22.28,0,.53-.05.76-.15.23-.1.42-.25.58-.43.16-.19.29-.41.37-.67.09-.26.13-.54.13-.85v-.06c0-.32-.05-.6-.14-.86-.09-.25-.22-.47-.39-.65-.16-.18-.36-.31-.59-.4-.23-.09-.47-.14-.74-.14-.15,0-.3.02-.44.06-.14.04-.27.1-.39.17-.12.07-.23.15-.32.24-.1.09-.17.19-.23.28v-.68h-.65v5.49ZM369.89,354.61c.24-.1.46-.24.63-.42.18-.18.32-.4.42-.66.1-.26.15-.54.15-.84v-.06c0-.31-.05-.59-.15-.84-.1-.25-.24-.47-.42-.65-.18-.18-.39-.32-.63-.42-.25-.1-.51-.15-.8-.15s-.56.05-.8.15c-.24.1-.45.24-.63.43-.18.18-.32.4-.42.66-.1.25-.15.53-.15.84v.06c0,.3.05.57.15.82.1.25.24.47.41.66.18.18.39.33.63.43.24.1.51.16.8.16s.56-.05.8-.15ZM368.12,353.8c-.24-.28-.35-.65-.35-1.11v-.06c0-.23.03-.44.09-.63.06-.19.15-.35.26-.48.11-.13.25-.24.42-.31.17-.07.35-.11.55-.11s.39.04.55.11c.16.07.3.18.42.31.12.13.21.3.27.49.06.19.09.4.09.63v.05c0,.23-.03.43-.09.62-.06.19-.15.35-.26.48-.11.13-.25.24-.42.31-.17.07-.35.11-.55.11-.42,0-.74-.14-.98-.41ZM362.66,354.69h.65v-2.48c0-.18.03-.34.09-.48.06-.14.15-.26.25-.35.1-.09.22-.16.36-.21.14-.05.28-.07.43-.07.33,0,.57.08.72.25.16.17.23.43.23.79v2.56h.65v-2.5c0-.3-.04-.55-.11-.75-.07-.21-.17-.38-.3-.51-.13-.13-.28-.23-.46-.29-.18-.06-.37-.09-.58-.09-.34,0-.62.07-.84.21-.22.14-.38.31-.47.5v-2.59h-.65v6.02ZM361.21,354.43c.25-.23.37-.52.37-.9,0-.22-.04-.4-.11-.53-.07-.14-.17-.25-.3-.33-.13-.09-.28-.15-.46-.2-.18-.05-.37-.09-.58-.14-.19-.03-.34-.07-.46-.1-.12-.04-.22-.08-.29-.13l-.15-.17c-.03-.06-.04-.14-.04-.24,0-.18.07-.32.21-.42s.33-.16.56-.16c.26,0,.46.05.59.15.13.1.22.26.27.49h.62c-.03-.22-.08-.4-.17-.56-.09-.15-.2-.27-.33-.37-.13-.09-.28-.16-.45-.2-.17-.04-.34-.06-.52-.06-.16,0-.32.02-.49.07-.16.05-.31.12-.44.22s-.24.22-.32.36c-.08.14-.12.31-.12.49,0,.18.02.33.07.46.05.13.13.24.24.33s.25.17.44.24c.18.06.4.12.67.17.32.06.55.13.71.22.16.09.24.25.24.46s-.07.38-.21.48c-.14.11-.37.16-.68.16-.33,0-.56-.07-.7-.21-.14-.14-.23-.33-.25-.58h-.63c.02.42.17.75.45.98.28.23.66.35,1.15.35s.87-.11,1.12-.34ZM354.46,354.61c.24-.1.46-.24.63-.42.18-.18.32-.4.42-.66.1-.26.15-.54.15-.84v-.06c0-.31-.05-.59-.15-.84-.1-.25-.24-.47-.42-.65-.18-.18-.39-.32-.63-.42-.25-.1-.51-.15-.8-.15s-.56.05-.8.15c-.24.1-.45.24-.63.43-.18.18-.32.4-.42.66-.1.25-.15.53-.15.84v.06c0,.3.05.57.15.82.1.25.24.47.41.66.18.18.39.33.63.43.24.1.51.16.8.16s.56-.05.8-.15ZM352.69,353.8c-.24-.28-.35-.65-.35-1.11v-.06c0-.23.03-.44.09-.63.06-.19.15-.35.26-.48.11-.13.25-.24.42-.31.17-.07.35-.11.55-.11s.39.04.55.11c.16.07.3.18.42.31.12.13.21.3.27.49.06.19.09.4.09.63v.05c0,.23-.03.43-.09.62-.06.19-.15.35-.26.48-.11.13-.25.24-.42.31-.17.07-.35.11-.55.11-.42,0-.74-.14-.98-.41ZM350.68,354.72l.23-.06v-.54l-.23.06c-.08.02-.17.02-.27.02-.34,0-.51-.2-.51-.59v-2.44h.95v-.54h-.95v-.93h-.65v.93h-.58v.54h.58v2.51c0,.32.09.58.27.78.18.2.46.3.83.3.13,0,.24,0,.33-.02ZM341.6,354.69h.71l.95-3.05.85,3.05h.72l1.27-4.07h-.66l-.96,3.28-.93-3.28h-.56l-.99,3.28-.95-3.28h-.7l1.25,4.07ZM338.48,354.61c.24-.1.46-.24.63-.42.18-.18.32-.4.42-.66.1-.26.15-.54.15-.84v-.06c0-.31-.05-.59-.15-.84-.1-.25-.24-.47-.42-.65-.18-.18-.39-.32-.63-.42-.25-.1-.51-.15-.8-.15s-.56.05-.8.15c-.24.1-.45.24-.63.43-.18.18-.32.4-.42.66-.1.25-.15.53-.15.84v.06c0,.3.05.57.15.82.1.25.24.47.41.66.18.18.39.33.63.43.24.1.51.16.8.16s.56-.05.8-.15ZM336.71,353.8c-.24-.28-.35-.65-.35-1.11v-.06c0-.23.03-.44.09-.63.06-.19.15-.35.26-.48.11-.13.25-.24.42-.31.17-.07.35-.11.55-.11s.39.04.55.11c.16.07.3.18.42.31.12.13.21.3.27.49.06.19.09.4.09.63v.05c0,.23-.03.43-.09.62-.06.19-.15.35-.26.48-.11.13-.25.24-.42.31-.17.07-.35.11-.55.11-.42,0-.74-.14-.98-.41ZM333.92,354.69h.65v-6.02h-.65v6.02ZM329.78,352.27c.06-.36.19-.65.4-.85.21-.21.48-.31.81-.31s.61.09.81.27c.2.18.32.48.35.9h-2.38ZM332.22,354.45c.31-.21.5-.52.58-.92h-.65c-.08.46-.44.69-1.08.69-.42,0-.74-.12-.96-.36-.22-.24-.33-.59-.35-1.05h3.07v-.21c0-.37-.05-.68-.15-.94-.1-.26-.24-.47-.41-.63-.17-.16-.37-.28-.59-.36-.22-.08-.45-.11-.68-.11-.28,0-.54.05-.77.15-.23.1-.43.24-.6.43-.17.18-.3.4-.39.66s-.14.54-.14.84v.06c0,.31.05.59.14.85.1.25.23.47.41.65.18.18.38.32.62.42.24.1.5.15.79.15.46,0,.85-.1,1.16-.31ZM325.84,354.13c-.17-.06-.31-.16-.42-.28-.12-.13-.21-.29-.27-.48-.06-.19-.1-.42-.1-.67v-.06c0-.26.03-.48.1-.68.07-.19.16-.35.28-.48.12-.13.26-.22.42-.28.16-.06.34-.09.53-.09.17,0,.33.03.49.1.15.06.29.16.4.29.11.13.2.29.27.48.07.19.1.42.1.67v.06c0,.5-.11.87-.32,1.13-.21.26-.52.39-.93.39-.2,0-.38-.03-.54-.09ZM327.21,354.61c.23-.1.42-.25.58-.43.16-.19.29-.41.37-.67.09-.26.13-.54.13-.85v-.06c0-.32-.05-.6-.14-.86-.09-.25-.22-.47-.39-.65-.16-.18-.36-.31-.59-.4-.23-.09-.47-.14-.74-.14-.15,0-.3.02-.44.06-.14.04-.27.1-.39.17-.12.07-.23.15-.32.24-.1.09-.17.19-.23.28v-2.63h-.65v6.02h.65v-.67c.12.2.3.38.53.52.23.15.52.22.85.22.28,0,.53-.05.76-.15ZM317.61,354.69h.65v-2.48c0-.18.03-.34.09-.48.06-.14.15-.26.25-.35.1-.09.22-.16.36-.21.14-.05.28-.07.43-.07.33,0,.57.08.72.25.16.17.23.43.23.79v2.56h.65v-2.5c0-.3-.04-.55-.11-.75-.07-.21-.17-.38-.3-.51-.13-.13-.28-.23-.46-.29-.18-.06-.37-.09-.58-.09-.34,0-.62.07-.84.21-.22.14-.38.31-.47.5v-.65h-.65v4.07ZM313.8,354.07c-.11-.12-.17-.29-.17-.5,0-.15.04-.28.11-.38.07-.1.17-.18.3-.25.13-.06.28-.11.45-.14.17-.03.36-.04.56-.04h.56v.51c0,.16-.03.29-.09.42-.06.12-.15.22-.25.31-.11.08-.23.15-.38.19-.15.04-.31.07-.48.07-.29,0-.49-.06-.6-.18ZM315.08,354.62c.2-.1.37-.24.52-.44v.51h.65v-2.68c0-.29-.04-.52-.12-.71-.08-.19-.19-.34-.33-.45-.13-.11-.29-.19-.47-.23-.18-.04-.37-.07-.56-.07s-.39.02-.57.07c-.18.04-.35.12-.5.22-.15.1-.27.23-.37.39-.1.16-.15.36-.18.59h.65c.06-.48.37-.72.93-.72.31,0,.53.07.67.21.13.14.2.37.2.7v.29h-.58c-.26,0-.51.02-.76.07-.25.04-.46.12-.65.21-.19.1-.34.23-.46.39-.11.16-.17.36-.17.59,0,.21.03.39.11.54.07.15.17.28.29.37.12.1.27.17.43.22.17.05.34.07.53.07.3,0,.55-.05.75-.15ZM311.15,354.65c.21-.08.39-.18.54-.32.16-.13.28-.3.38-.49.1-.19.15-.39.18-.61h-.6c-.02.16-.06.31-.13.43-.07.12-.16.23-.26.31-.11.08-.23.14-.36.19-.13.04-.27.06-.42.06-.39,0-.71-.13-.96-.38-.25-.26-.37-.63-.37-1.13v-.06c0-.24.03-.45.1-.64.07-.19.16-.35.28-.48.12-.13.25-.23.41-.31.16-.07.33-.11.52-.11.28,0,.53.07.74.2.21.13.34.36.39.68h.64c-.03-.26-.1-.48-.21-.66-.11-.18-.24-.33-.4-.44-.16-.11-.34-.19-.54-.25-.2-.05-.41-.08-.62-.08-.28,0-.53.05-.77.15-.24.1-.45.24-.63.42-.18.18-.32.4-.42.66-.1.26-.16.54-.16.85v.06c0,.32.05.6.15.86.1.25.24.47.42.65.18.18.39.32.63.41s.5.14.79.14c.24,0,.47-.04.68-.12ZM306.42,354.64c.24-.08.44-.2.61-.35.17-.15.3-.32.4-.52.1-.2.14-.42.14-.65,0-.26-.04-.49-.12-.67-.08-.18-.2-.33-.36-.46-.16-.12-.36-.23-.59-.3-.23-.08-.51-.14-.82-.19-.29-.04-.51-.09-.69-.15-.17-.06-.31-.13-.4-.21-.1-.08-.16-.17-.19-.28-.03-.11-.05-.23-.05-.36,0-.26.1-.48.29-.65.19-.17.48-.25.87-.25s.69.08.89.23c.21.15.34.41.41.76h.61c-.06-.5-.25-.88-.58-1.14-.33-.26-.77-.39-1.33-.39-.26,0-.51.04-.72.12-.22.08-.41.18-.56.32-.16.13-.28.29-.37.48-.09.18-.13.38-.13.6,0,.25.04.46.12.63.08.17.2.32.37.44.16.12.36.21.59.28.23.07.49.13.78.18.26.04.48.09.66.15.17.06.31.13.41.21.1.08.17.18.21.3.04.12.06.25.06.41s-.03.3-.1.43c-.06.13-.15.24-.27.33-.11.09-.25.16-.4.21-.16.05-.32.07-.5.07-.28,0-.51-.03-.69-.1-.18-.06-.32-.15-.43-.26-.11-.11-.19-.24-.24-.39-.05-.15-.09-.3-.11-.47h-.65c.03.24.08.46.17.68.09.21.21.4.38.56.17.16.38.29.63.39s.57.14.95.14c.27,0,.53-.04.77-.12Z" />
          <path
            d="M468.03,364.24c.1-.1.15-.21.15-.35s-.05-.26-.15-.35c-.1-.1-.22-.14-.35-.14s-.25.05-.35.14c-.1.1-.15.21-.15.35s.05.26.15.35c.1.1.22.14.35.14s.25-.05.35-.14ZM464.22,364.34h.65v-2.23c0-.25.03-.46.09-.62.06-.16.15-.29.27-.39.12-.1.26-.17.42-.21.17-.04.35-.07.55-.08v-.61c-.18,0-.34.03-.48.07-.14.04-.26.1-.37.17-.1.07-.2.15-.28.25-.08.1-.15.2-.21.32v-.73h-.65v4.07ZM460.1,361.91c.06-.36.19-.65.4-.85.21-.21.48-.31.81-.31s.61.09.81.27c.2.18.32.48.35.9h-2.38ZM462.53,364.1c.31-.21.5-.52.58-.92h-.65c-.08.46-.44.69-1.08.69-.42,0-.74-.12-.96-.36-.22-.24-.33-.59-.35-1.05h3.07v-.21c0-.37-.05-.68-.15-.94-.1-.26-.24-.47-.41-.63-.17-.16-.37-.28-.59-.36-.22-.08-.45-.11-.68-.11-.28,0-.54.05-.77.15-.23.1-.43.24-.6.43-.17.18-.3.4-.39.66-.09.26-.14.54-.14.84v.06c0,.31.05.59.14.85.1.25.23.47.41.65.18.18.38.32.62.42.24.1.5.15.79.15.46,0,.85-.1,1.16-.31ZM457.64,364.34h.65v-6.02h-.65v6.02ZM455.55,364.34h.65v-6.02h-.65v6.02ZM451.41,361.91c.06-.36.19-.65.4-.85.21-.21.48-.31.81-.31s.61.09.81.27c.2.18.32.48.35.9h-2.38ZM453.85,364.1c.31-.21.5-.52.58-.92h-.65c-.08.46-.44.69-1.08.69-.42,0-.74-.12-.96-.36-.22-.24-.33-.59-.35-1.05h3.07v-.21c0-.37-.05-.68-.15-.94-.1-.26-.24-.47-.41-.63-.17-.16-.37-.28-.59-.36-.22-.08-.45-.11-.68-.11-.28,0-.54.05-.77.15-.23.1-.43.24-.6.43-.17.18-.3.4-.39.66-.09.26-.14.54-.14.84v.06c0,.31.05.59.14.85.1.25.23.47.41.65.18.18.38.32.62.42.24.1.5.15.79.15.46,0,.85-.1,1.16-.31ZM449.58,364.07c.25-.23.37-.52.37-.9,0-.22-.04-.4-.11-.53-.07-.14-.17-.25-.3-.33-.13-.09-.28-.15-.46-.2-.18-.05-.37-.09-.58-.14-.19-.03-.34-.07-.46-.1-.12-.04-.22-.08-.29-.13l-.15-.17c-.03-.06-.04-.14-.04-.24,0-.18.07-.32.21-.42.14-.11.33-.16.56-.16.26,0,.46.05.59.15.13.1.22.26.27.49h.62c-.03-.22-.08-.4-.17-.56-.09-.15-.2-.27-.33-.37-.13-.09-.28-.16-.45-.2-.17-.04-.34-.06-.52-.06-.16,0-.32.02-.49.07-.16.05-.31.12-.44.22-.13.1-.24.22-.32.36-.08.14-.12.31-.12.49,0,.18.02.33.07.46.05.13.13.24.24.33.11.09.25.17.44.24.18.06.4.12.67.17.32.06.55.13.71.22.16.09.24.25.24.46s-.07.38-.21.48c-.14.11-.37.16-.68.16-.33,0-.56-.07-.7-.21-.14-.14-.23-.33-.25-.58h-.63c.02.42.17.75.45.98.28.23.66.35,1.15.35s.87-.11,1.12-.34ZM443.84,364.37l.23-.06v-.54l-.23.06c-.08.02-.17.02-.27.02-.34,0-.51-.2-.51-.59v-2.44h.95v-.54h-.95v-.93h-.65v.93h-.58v.54h.58v2.51c0,.32.09.58.27.78.18.2.46.3.83.3.13,0,.24,0,.33-.02ZM437.53,364.34h.65v-2.48c0-.18.03-.34.09-.48.06-.14.15-.26.25-.35.1-.09.22-.16.36-.21.14-.05.28-.07.43-.07.33,0,.57.08.72.25.16.17.23.43.23.79v2.56h.65v-2.5c0-.3-.04-.55-.11-.75-.07-.21-.17-.38-.3-.51-.13-.13-.28-.23-.46-.29-.18-.06-.37-.09-.58-.09-.34,0-.62.07-.84.21-.22.14-.38.31-.47.5v-.65h-.65v4.07ZM433.41,361.91c.06-.36.19-.65.4-.85.21-.21.48-.31.81-.31s.61.09.81.27c.2.18.32.48.35.9h-2.38ZM435.84,364.1c.31-.21.5-.52.58-.92h-.65c-.08.46-.44.69-1.08.69-.42,0-.74-.12-.96-.36-.22-.24-.33-.59-.35-1.05h3.07v-.21c0-.37-.05-.68-.15-.94-.1-.26-.24-.47-.41-.63-.17-.16-.37-.28-.59-.36-.22-.08-.45-.11-.68-.11-.28,0-.54.05-.77.15-.23.1-.43.24-.6.43-.17.18-.3.4-.39.66-.09.26-.14.54-.14.84v.06c0,.31.05.59.14.85.1.25.23.47.41.65.18.18.38.32.62.42.24.1.5.15.79.15.46,0,.85-.1,1.16-.31ZM429.16,363.77c-.15-.06-.28-.15-.39-.28-.11-.12-.2-.28-.26-.47-.06-.19-.1-.41-.1-.67v-.06c0-.5.11-.88.34-1.15.23-.27.54-.4.95-.4s.71.12.95.37c.23.25.35.63.35,1.15v.06c0,.26-.04.48-.11.68s-.16.35-.28.48c-.12.13-.26.22-.43.28-.17.06-.34.09-.53.09-.17,0-.33-.03-.48-.09ZM430.01,364.35c.14-.04.27-.1.4-.17.12-.07.23-.15.33-.25.1-.09.18-.19.23-.29v.7h.65v-6.02h-.65v2.6c-.12-.2-.29-.37-.5-.51-.21-.14-.49-.21-.83-.21-.28,0-.54.05-.77.16-.23.1-.43.25-.6.44-.17.19-.3.41-.39.67-.09.26-.14.54-.14.86v.06c0,.32.05.6.14.85.09.25.22.46.38.64.16.17.36.31.58.4s.47.14.74.14c.15,0,.29-.02.43-.07ZM423.3,364.34h.65v-2.48c0-.18.03-.34.09-.48.06-.14.15-.26.25-.35.1-.09.22-.16.36-.21.14-.05.28-.07.43-.07.33,0,.57.08.72.25.16.17.23.43.23.79v2.56h.65v-2.5c0-.3-.04-.55-.11-.75-.07-.21-.17-.38-.3-.51-.13-.13-.28-.23-.46-.29-.18-.06-.37-.09-.58-.09-.34,0-.62.07-.84.21-.22.14-.38.31-.47.5v-.65h-.65v4.07ZM419.17,361.91c.06-.36.19-.65.4-.85.21-.21.48-.31.81-.31s.61.09.81.27c.2.18.32.48.35.9h-2.38ZM421.61,364.1c.31-.21.5-.52.58-.92h-.65c-.08.46-.44.69-1.08.69-.42,0-.74-.12-.96-.36-.22-.24-.33-.59-.35-1.05h3.07v-.21c0-.37-.05-.68-.15-.94-.1-.26-.24-.47-.41-.63-.17-.16-.37-.28-.59-.36-.22-.08-.45-.11-.68-.11-.28,0-.54.05-.77.15-.23.1-.43.24-.6.43-.17.18-.3.4-.39.66-.09.26-.14.54-.14.84v.06c0,.31.05.59.14.85.1.25.23.47.41.65.18.18.38.32.62.42.24.1.5.15.79.15.46,0,.85-.1,1.16-.31ZM415.23,363.77c-.17-.06-.31-.16-.42-.28-.12-.13-.21-.29-.27-.48-.06-.19-.1-.42-.1-.67v-.06c0-.26.03-.48.1-.68.07-.19.16-.35.28-.48.12-.13.26-.22.42-.28.16-.06.34-.09.53-.09.17,0,.33.03.49.1.15.06.29.16.4.29.11.13.2.29.27.48.07.19.1.42.1.67v.06c0,.5-.11.87-.32,1.13-.21.26-.52.39-.93.39-.2,0-.38-.03-.54-.09ZM413.81,365.76h.65v-2.09c.12.2.29.38.53.52.23.15.51.22.85.22.28,0,.53-.05.76-.15.23-.1.42-.25.58-.43.16-.19.29-.41.37-.67.09-.26.13-.54.13-.85v-.06c0-.32-.05-.6-.14-.86-.09-.25-.22-.47-.39-.65-.16-.18-.36-.31-.59-.4-.23-.09-.47-.14-.74-.14-.15,0-.3.02-.44.06-.14.04-.27.1-.39.17-.12.07-.23.15-.32.24-.1.09-.17.19-.23.28v-.68h-.65v5.49ZM409.69,361.91c.06-.36.19-.65.4-.85.21-.21.48-.31.81-.31s.61.09.81.27c.2.18.32.48.35.9h-2.38ZM412.12,364.1c.31-.21.5-.52.58-.92h-.65c-.08.46-.44.69-1.08.69-.42,0-.74-.12-.96-.36-.22-.24-.33-.59-.35-1.05h3.07v-.21c0-.37-.05-.68-.15-.94-.1-.26-.24-.47-.41-.63-.17-.16-.37-.28-.59-.36-.22-.08-.45-.11-.68-.11-.28,0-.54.05-.77.15-.23.1-.43.24-.6.43-.17.18-.3.4-.39.66-.09.26-.14.54-.14.84v.06c0,.31.05.59.14.85.1.25.23.47.41.65.18.18.38.32.62.42.24.1.5.15.79.15.46,0,.85-.1,1.16-.31ZM405.45,363.77c-.15-.06-.28-.15-.39-.28-.11-.12-.2-.28-.26-.47-.06-.19-.1-.41-.1-.67v-.06c0-.5.11-.88.34-1.15.23-.27.54-.4.95-.4s.71.12.95.37c.23.25.35.63.35,1.15v.06c0,.26-.04.48-.11.68s-.16.35-.28.48c-.12.13-.26.22-.43.28-.17.06-.34.09-.53.09-.17,0-.33-.03-.48-.09ZM406.29,364.35c.14-.04.27-.1.4-.17.12-.07.23-.15.33-.25.1-.09.18-.19.23-.29v.7h.65v-6.02h-.65v2.6c-.12-.2-.29-.37-.5-.51-.21-.14-.49-.21-.83-.21-.28,0-.54.05-.77.16-.23.1-.43.25-.6.44-.17.19-.3.41-.39.67-.09.26-.14.54-.14.86v.06c0,.32.05.6.14.85.09.25.22.46.38.64.16.17.36.31.58.4.23.09.47.14.74.14.15,0,.29-.02.43-.07ZM399.58,364.34h.65v-2.48c0-.18.03-.34.09-.48.06-.14.15-.26.25-.35.1-.09.22-.16.36-.21.14-.05.28-.07.43-.07.33,0,.57.08.72.25.16.17.23.43.23.79v2.56h.65v-2.5c0-.3-.04-.55-.11-.75-.07-.21-.17-.38-.3-.51-.13-.13-.28-.23-.46-.29-.18-.06-.37-.09-.58-.09-.34,0-.62.07-.84.21-.22.14-.38.31-.47.5v-.65h-.65v4.07ZM398.13,359.3c.08-.08.12-.18.12-.3s-.04-.22-.12-.3c-.08-.08-.18-.12-.3-.12s-.22.04-.3.12c-.08.08-.12.18-.12.3s.04.22.12.3c.08.08.18.12.3.12s.22-.04.3-.12ZM397.52,364.34h.65v-4.07h-.65v4.07Z" />

        </svg>
        <div class="main-content">
          <div class="left-box">
            <div style="display: flex; flex-direction:row; margin-top: 1.2em; margin-left: 0.8em">
              <img src="{{ shop.logo }}"
                style="width: 70px; height: 70px; object-fit: contain; margin-right: 0.6em;" />
              <div>
                <p style="font-weight: 600; font-size: 1.2em;">{{ shop.name }}</p>
                <!-- <p style="margin-top: 0.15em;">London, England</p> --!>
                <p style="margin-top: 0.15em;">{{ shop.title }}</p>
                <p style="margin-top: 0.15em;">{{ shop.transaction_sold_count }} Sales</p>
              </div>
            </div>
            <div style="margin-left: 0.8em;">
              <p class="subtitle">
                Order #{{ orderId }}
              </p>
              <p>Dispatch to: {{ shipping_address.name }}</p>
              <p>Order Number: {{ order.order_number }}</p>
              {% for line_item in line_items_in_shipment %}
                <p class="subtitle">
                  {% if line_item.name != blank %}
                    {{ line_item.name }}
                  {% else %}
                    {{ line_item.title }}
                  {% endif %}
                </p>
                <p>Quantity: {{ line_item.quantity }}</p>
              {% endfor %}
            </div>
          </div>
          <div class="right-box">
            <div style="flex: 1; display: flex; align-items: center; justify-content: center;">
              <img src="{{ line_items_in_shipment[0].image }}" style="width: 11em; height: 11em; object-fit: contain; border-radius: 6px;">
            </div>
            <div style="display: flex; justify-content: center; width: 100%; padding-top: 0.8em">
              <canvas id="qrcode"></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <button onclick="window.print();">Print</button>
  <style type="text/css">
    body {
      font-size: 15px;
      position: relative;
      margin: 0;
      padding: 0;
    }

    html {
      margin: 0;
      padding: 0;
    }

    @page {
      size: A4;
      margin: 0;
    }

    p {
      margin: 0;
      font-size: 0.8em;
      line-height: 1.16em;
    }
  
    .page {
      width: 210mm;
      height: 297mm;
      page-break-before: always;
    }

    .page-absolute {
      position: absolute;
      top: 0px;
      left: 0px;
      right: 0px;
    }

    * {
      box-sizing: border-box;
    }

    .wrapper {
      width: 831px;
      height: 1120px;
      margin: auto;
      padding: 1em;
      font-family: "Noto Sans", sans-serif;
      font-weight: 250;
      display: flex;
      flex-direction: column;
    }

    .main-content {
      display: flex;
      flex-direction: row;
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      top: 5em;
    }

    .left-box {
      flex: 1;
    }

    .right-box {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-left: 3em;
    }

    .subtitle {
      font-weight: 600;
      margin-top: 1.2em;
    }
  
    @media print {
      button {
        display: none;
      }

      html,
      body {
        height: auto
      }
    }
  </style>
  <script>
      // Create a new QRious instance and generate a QR code
      var qr = new QRious({
          element: document.getElementById('qrcode'),
          value: '{{ shop.url }}',
          size: 70,
          background: 'white',
          foreground: 'black',
      });
  </script>
  <script>
    function elementsOverlap(el1, el2) {
      const domRect1 = el1.getBoundingClientRect();
      const domRect2 = el2.getBoundingClientRect();

      return !(
        domRect1.top > domRect2.bottom ||
        domRect1.right < domRect2.left ||
        domRect1.bottom < domRect2.top ||
        domRect1.left > domRect2.right
      );
    }
    const pages = document.querySelectorAll('.page');
    const content = document.querySelector('.wrapper');
    for (let i = pages.length - 1; i >= 0; i--) {
      if (!elementsOverlap(pages[i], content)) {
        pages[i].remove();
      }
    }
    if (window.matchMedia("print").matches) {
    }
  </script>
</body>

</html>
`

const projects = [
  { id: '440478626329', name: 'Bottled Goose', slug: 'bg', },
  { id: '665847158329', name: 'Partner in Wine', slug: 'piw' },
  { id: '578953627077', name: 'Great Harbouring Gifts', slug: 'ghg' },
];

class OrderPackingSlips implements TypeAPIHandler {
  url = '/api/online-stores/order-packing-slips/:orderId';
  method = 'GET';
  apiSchema = {
    params: Joi.object({
      orderId: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
  ]);

  handler = async (request, reply) => {
    const { orderId } = request.params;
    await Bg.initDB();

    const returnHTML = (html) => {
      reply.type('text/html').send(html);
    }

    let orderData;
    let slug;
    let clientId;
    for (let i = 0; i < projects.length; i++) {
      const { slug: _slug, id } = projects[i];

      const order = await Bg.Order.findByOrderID(orderId);
      if (!order) continue;
      orderData = order;
      slug = _slug;
      clientId = order['Client ID'] || id;
      break;
    }

    if (!orderData?.Id) {
      returnHTML('Order not found');
      return;
    }
    const resPipeline = await listPipeline({
      limit: 1,
      offset: 0,
      orderId
    });

    const pipeline = resPipeline?.rows?.[0];
    const canBeProcessedItem = pipeline?.SharedData?.canBeProcessedItems || [];

    const order = orderData['Raw Data'];

    const packingSlipData = await DB.PackingSlip.findOne({
      where: orderData['Store ID'] ? {
        storeId: orderData['Store ID'],
      } : {
        resellerId: clientId,
      }
    });

    const name = packingSlipData?.companyName || (
      slug === 'bg' ? 'Bottled Goose' :
        slug === 'piw' ? 'Partner in Wine' :
          slug === 'ghg' ? 'Great Harbour Gifts' :
            ''
    );
    const withOnlyPersonaliseTextProperty = (item) => {
      return {
        ...item,
        properties: item.properties.filter(v => v.name === 'Personalise Text'),
      }
    }


    // const line_items = await (async() => {

    //   const withOnlyPersonaliseTextProperty = (item) => {
    //     return {
    //       ...item,
    //       properties: item.properties.filter(v => v.name === 'Personalise Text'),
    //     }
    //   }

    //   if (name === 'Great Harbour Gifts') {
    //     const arr = [];
    //     const promiseArr = order.line_items.map(async(val) => {
    //       try {
    //         if (!val.sku) return;
    //         const product = await DB.Product.findOne({
    //           where: {
    //             data: {
    //               [Op.like]: `%${val.sku}%`,
    //             }
    //           },
    //         });
    //         if (!!product) {
    //           arr.push(
    //             withOnlyPersonaliseTextProperty(val)
    //           );
    //         }
    //       } catch(err) {}
    //     });
    //     await Promise.all(promiseArr);
    //     return arr;
    //   }
    //   const listPrintJobs = await DB.PrintJob.findAll({
    //     where: {
    //       data: {
    //         [Op.like]: `%${orderId}%`
    //       }
    //     }
    //   })
    //   return order.line_items.filter(l => {
    //     const findPrintJob = listPrintJobs.find(p => p.data && p.data.variantId === l.id);
    //     // console.log('findPrintJob', findPrintJob.id);
    //     return !!findPrintJob
    //   }).map(val => withOnlyPersonaliseTextProperty(val));
    // })();

    const filterSupported = (items) => {
      if (slug === 'piw') return items.map(v => withOnlyPersonaliseTextProperty(v)).filter(v => v.properties.length > 0);
      // if (slug ==='piw') return supported.map(v => withOnlyPersonaliseTextProperty(v));
      const supported = items.filter(v => orderData['Item Supported']?.includes(String(v.id)));
      const addImagePreview = supported.map(v => {
        const findCanBeProcessedItem = canBeProcessedItem.find(val => val.id === v.id);
        if (findCanBeProcessedItem) {
          return {
            ...v,
            image: findCanBeProcessedItem.previewUrl,
          }
        }
        return v;
      })
      return addImagePreview;
    }

    let line_items = filterSupported(order.line_items)

    for (let i = 0; i < line_items.length; i++) {
      const variant = line_items[i];
      const findPrintJob = variant.properties.find(val => val.name === 'Print Job');
      if (findPrintJob) {
        const printJobId = findPrintJob.value;
        const printJob = await DB.PrintJob.findByPk(printJobId);
        if (printJob?.data?.previewImages) {
          line_items[i].previewImages = printJob.data?.previewImages || [];
        }
        const design = await DB.Design.findByPk(printJob.designId);
        if (design) {
          line_items[i].designName = design.name;
          if (design.galleries && !line_items[i].previewImages?.length) {
            line_items[i].previewImages = design.galleries;
          }
        }
      }
      const designId = variant.properties.find(val =>
        val.name === 'BG Product Variant Number' ||
        val.name === 'BG Product Number'
      );
      if (designId) {
        const design = await DB.Design.findByPk(designId.value);
        if (design?.galleries && !line_items[i].previewImages?.length) {
          line_items[i].previewImages = design.galleries;
        }
      }
    }

    const displayData = {
      shop: {
        name,
        email: packingSlipData?.email,
        address: packingSlipData?.address,
        phone: packingSlipData?.phone,
        logo: packingSlipData?.companyLogo,
      },
      orderId,
      order: order,
      shipping_address: order.shipping_address,
      billing_address: order.billing_address,
      line_items_in_shipment: line_items,
      format_date: moment(order.created_at).format('MMMM DD, YYYY'),
      show_properties: name === 'Partner in Wine',
      currrency: order.currrency,
      subtotal_price: order.subtotal_price,
      total_discounts: order.total_discounts,
      total_tax: order.total_tax,
      total_price: order.total_price,
      total_shipping_price: order.total_shipping_price_set?.shop_money?.amount || 0,
      grand_total: "",
    }
    // const encodeObj = (obj) => {
    //   if (typeof obj === 'string') return encodeURIComponent(obj);
    //   else if (typeof obj === 'function' || typeof obj === 'number' || typeof obj === 'boolean' || !obj) return obj;
    //   else if (Array.isArray(obj)) {
    //     for(let i=0; i<obj.length; i++) {
    //       obj[i] = encodeObj(obj[i]);
    //     }
    //   } else if (typeof obj === 'object') {
    //     for (let key in obj) {
    //       obj[key] = encodeObj(obj[key]);
    //     }
    //   }
    //   return obj;
    // }
    if (orderData.OrderType === "Etsy") {
      const store = await DB.OnlineStore.findByPk(orderData['Store ID']);
      const etsy = new Etsy(store.data?.etsyAccessToken, store.data?.etsyRefreshToken);
      const shopInfo = await etsy.getShop()

      if (shopInfo) {
        displayData.shop = {
          ...displayData.shop,
          ...shopInfo,
        }
      }
      if (shopInfo.shop_name) {
        displayData.shop.name = shopInfo.shop_name
      }
      if (shopInfo.icon_url_fullxfull) {
        displayData.shop.logo = shopInfo.icon_url_fullxfull
      }
      if (shopInfo.url) {
        // @ts-ignore
        displayData.shop.website = shopInfo.url
      }
      const receiptData = await etsy.getReceiptById({ id: order.id });
      if (receiptData?.total_price?.amount) {
        displayData.total_price = (Number(receiptData?.total_price?.amount) / Number(receiptData?.total_price?.divisor)).toFixed(2);
      }
      if (receiptData?.total_shipping_cost?.amount) {
        displayData.total_shipping_price = (Number(receiptData?.total_shipping_cost?.amount) / Number(receiptData?.total_shipping_cost?.divisor)).toFixed(2);
      }
      if (receiptData?.grandtotal?.amount) {
        displayData.grand_total = (Number(receiptData?.grandtotal?.amount) / Number(receiptData?.grandtotal?.divisor)).toFixed(2);
      }
      if (receiptData?.payment_method) {
        // 'cc' (credit card), 'paypal', 'check', 'mo' (money order), 'bt' (bank transfer), 'other', 'ideal', 'sofort', 'apple_pay', 'google', 'android_pay', 'google_pay', 'klarna', 'k_pay_in_4' (klarna), 'k_pay_in_3' (klarna), or 'k_financing' (klarna).
      }
      let html
      if (receiptData?.is_gift) {
        html = await engine.parseAndRender(LIQUID_TEMPLATE_ETSY_GIFT, displayData);
      } else {
        html = await engine.parseAndRender(LIQUID_TEMPLATE_ETSY, displayData);
      }
      returnHTML(html);
      return;
    }
    const html = await engine.parseAndRender(LIQUID_TEMPLATE, displayData);
    // console.log('html', html);
    returnHTML(html);
  }
}

export default new OrderPackingSlips();
