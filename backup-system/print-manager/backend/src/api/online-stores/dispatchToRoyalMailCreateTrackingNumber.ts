import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkAdmin, checkA<PERSON><PERSON>, checkAuthenOptional, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");
import axios from 'axios';

const path = require('path'); 
const fs = require('fs'); 
const moment = require('moment');

// API DOC: 

class DispatchToRoyalMail implements TypeAPIHandler {
  url = "/api/online-stores/dispatch-to-royal-mail/create-tracking-number";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      orderIdentifier: Joi.number(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
    checkAdmin,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { body } = request;

    console.log(JSON.stringify({
      items: [
        {
          orderIdentifier: body.orderIdentifier,
          trackingNumber: `TP${VarHelper.genId()}GB`,
          status: "despatched",
          despatchDate: moment().format(),
          // shippingCarrier: '',
          // shippingService: '',
        }
      ]
    }))

    const apiCallRes = await axios.request({
      url: 'https://api.parcel.royalmail.com/api/v1/orders/status',
      method: 'put',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer b1eed28a-9beb-4e93-b8c9-5a0083c43bfd',
      },
      data: JSON.stringify({
        items: [
          {
            orderIdentifier: body.orderIdentifier,
            trackingNumber: `TP${VarHelper.genId()}GB`,
            status: "new",
            despatchDate: moment().format(),
            // shippingCarrier: '',
            // shippingService: '',
          }
        ]
      }),
    })    

    return {
      success: true,
      data: apiCallRes.data,
    }

  };
}

export default new DispatchToRoyalMail();