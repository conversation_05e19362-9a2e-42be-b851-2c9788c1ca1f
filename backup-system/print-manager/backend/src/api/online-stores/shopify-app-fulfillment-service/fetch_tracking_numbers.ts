import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, checkAuthen, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { VarH<PERSON>per, Shopify } from "helpers";
import Joi = require("joi");
import { ERROR } from "const";
import { checkReseller } from "api/api-middlewares/authen";

class ShopifyAppWebhook implements TypeAPIHandler {

  url = "/api/online-stores/shopify-app-fulfillment-service/fetch_tracking_numbers";
  method = "GET";
  apiSchema = {
    
  }

  preHandler = combineMiddlewares([
    // validateRequest(this.apiSchema),
  ]);

  handler = async (request: TRequestUser, reply) => {
    console.log('WEBHOOK POST: api/online-stores/shopify-app-fulfillment-service/fetch_tracking_numbers');
    console.log('QUERY', request.query);
    const isValid = Shopify.verifyHmac(request.query);
    if (!isValid) {
      reply.status(401).send(`Unauthorized`);
      return;
    }
    return {
      success: true,
    }
  };
}

export default new ShopifyAppWebhook();