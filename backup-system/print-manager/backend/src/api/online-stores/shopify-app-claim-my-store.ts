import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, checkAuthen, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON><PERSON>, Shopify } from "helpers";
import Joi = require("joi");
import { ERROR } from "const";
import { checkReseller } from "api/api-middlewares/authen";

class ShopifyAppClaimMyStore implements TypeAPIHandler {

  url = "/api/online-stores/shopify-app-claim-my-store";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      storeId: Joi.string().required(),
    })
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    console.log('WEBHOOK POST: api/online-stores/shopify-app-claim-my-store');
    const user = request.user;
    const resellerId = user.role === 'reseller' ? user.id : user.resellerId;
    if (!resellerId) throw new Error('Reseller ID not found');

    const store = await DB.OnlineStore.findByPk(request.body.storeId);
    if (store.resellerId !== 'await-claim') return {
      success: true,
      data: store,
    }
    store.resellerId = resellerId;
    await store.save();
    return {
      success: true,
      data: store,
    }
  };
}

export default new ShopifyAppClaimMyStore();