import { <PERSON>e<PERSON><PERSON><PERSON> } from "helpers";
import { TypeAPIHandler } from "type";

class HelloWorld implements TypeAPIHandler {
  url = "/b/api/test/stripe";
  method = "POST";

  handler = async (request, reply) => {
    const { func, params } = request.body;
    const results = !!func && !!params ? await StripeHelper[func](...params) : null;
    return {
      hello: "world5",
      ip: request.headers["x-real-ip"] || "127.0.0.1",
      params: request.query,
      results: results,
    };
  };
}

export default new HelloWorld();
