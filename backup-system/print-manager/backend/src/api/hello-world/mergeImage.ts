import { <PERSON>AP<PERSON>Handler } from "type";
import Joi = require("joi");
import { generatePngMultipleUrl, TEachImage } from '../pdf/generatePngMultipleUrl'
import { ERROR, MM_TO_PIXEL } from 'const';

class SamplePost implements TypeAPIHandler {
  url = "/b/api/test/merge-image";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      images: Joi.array().items(Joi.string()).required(),
      data: Joi.object({
        product: Joi.object({

          physicalWidth: Joi.number().required(),
          physicalHeight: Joi.number().required(),
          printAreas: Joi.array().items({
            width: Joi.number().required(),
            height: Joi.number().required(),
            top: Joi.number().required(),
            left: Joi.number().required(),
          }),


        }).required()
      }).required(),
    }),
  }

  handler = async (request, reply) => {
    const { data, images } = request.body;
    const printAreas = data.product.printAreas;

    const promiseArr = images.map(async (val, index) => {
      try {
        const imageUrl = val;
        console.log('imageUrl', imageUrl);
        return {
          url: imageUrl,
          width: printAreas[index].width * MM_TO_PIXEL,
          height: printAreas[index].height * MM_TO_PIXEL,
          top: printAreas[index].top * MM_TO_PIXEL,
          left: printAreas[index].left * MM_TO_PIXEL,
        }
      } catch(err) {}
    })
    let printImages : Array<TEachImage> = await Promise.all(promiseArr);
    const imageUrl = await generatePngMultipleUrl(
      data.product.physicalWidth * MM_TO_PIXEL,
      data.product.physicalHeight * MM_TO_PIXEL,
      printImages,
      `merge-images-${new Date().getTime()}.png`,
    );

    return {
      url: imageUrl,
    };
  };
}

export default new SamplePost();
