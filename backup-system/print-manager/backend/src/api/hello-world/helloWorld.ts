import { <PERSON>APIHand<PERSON> } from "type";

class HelloWorld implements TypeAPIHandler {
  url = "/b/api";
  method = "GET";

  handler = async (request, reply) => {
    Logger.saveLogToDB({
      url: this.url,
      contentType: request.headers["content-type"],
      body: request.body,
      otherData: {},
    });
    return {
      hello: "world5",
      ip: request.headers["x-real-ip"] || "127.0.0.1",
      params: request.query,
    };
  };
}

export default new HelloWorld();
