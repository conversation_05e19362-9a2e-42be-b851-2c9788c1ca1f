import { <PERSON>AP<PERSON><PERSON><PERSON><PERSON> } from 'type';
import { validateRequest, combineMiddlewares, receiveFileAndFields, IModifiedBody, TFileField } from '../api-middlewares'
import { ERROR } from 'const';
import { DB, InMemory as RedisCache } from 'db';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Helper, Polotno, AWSHelper } from 'helpers';
import Joi = require("joi");
const fs = require('fs');
const path = require('path');
const axios = require('axios')

// { [url]: numberOfUse }
const defaultAvailableServers = {
  'https://2d-render.personify.tech/': 0,
  'https://blender-1.personify.tech/': 0,
  'https://blender-2.personify.tech/': 0,
  // 'https://blender-3.personify.tech/': 0,
  'https://blender-4.personify.tech/': 0,
}
const availableServers = {};

class Blender2DRender implements TypeAPIHandler {
  url = '/api/images/blender-2d-render';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      artworkBase64: Joi.string().allow(''),
      artworkUrl: Joi.string().allow(''),
      blend: Joi.string(),
      support: Joi.string(),
      angle: Joi.string().allow(''),
      type: Joi.string().allow(''),
      debug: Joi.boolean(),
      additionRotationLayer: Joi.string(),
      mainArtworkRotationLayer: Joi.string(),
      resolution_x: Joi.number(),
      resolution_y: Joi.number(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
  ]);

  handler = async (request, reply) => {
    const {
      artworkBase64, artworkUrl, blend, support, angle, debug,
      additionRotationLayer, resolution_x, resolution_y,
      mainArtworkRotationLayer,
    } = request.body;
    console.log('POST /api/images/blender-2d-render', {
      artworkUrl, blend, support, angle, debug, additionRotationLayer,
      mainArtworkRotationLayer,
    })
    // let key = `bg/2d-render/artwork_${new Date().getTime()}/Artwork.png`;
    // const toS3Key = stringUrl => !stringUrl ? '' : stringUrl.replace('https://print-manager-media.s3.eu-west-1.amazonaws.com/', '');
    // const artworkPath = `Artwork_${new Date().getTime()}_${Math.floor(Math.random() * 1000)}.png`;
    // let base64Image = !artworkBase64 ? '' : artworkBase64.split(';base64,').pop();
    // if (!base64Image) {
    //   key = '';
    // } else {
    //   fs.writeFileSync(artworkPath, base64Image, { encoding: 'base64' });
    //   await AWSHelper.upload({ filePath: artworkPath, key });
    // }

    // const apiCallRes = await axios.request({
    //   url: 'https://msubg5vjtjs3n23niqk4vwfwre0tunct.lambda-url.eu-west-1.on.aws/',
    //   method: 'post',
    //   headers: { 'content-type': 'application/json' },
    //   data: JSON.stringify({
    //     "file_name": toS3Key(blend) || "bg/2d-render/Glass-10.blend",
    //     "frame": "2",
    //     "support_files": [toS3Key(support) || "bg/2d-render/small_empty_room_3_4k.exr", key].filter(Boolean),
    //     "angle": angle,
    //     "type": request.body.type || '',
    //   }),
    // });

    // fetch to get list
    const apiCall = await axios.request({
      url: 'https://nocodb.personify.tech/api/v1/db/data/v1/pmk6g5joaa863j9/Blender Servers/',
      method: 'get',
      headers: {
        'xc-token': 'efoDr21CkrSUuEW4hSmYtwION6ZiHPRShILhTOha'
      }
    });
    let list = apiCall.data?.list || [];
    const enabledServers = list.filter(server => server.Enabled);
    for (let key in availableServers) {
      if (!enabledServers.find(server => server.URL === key)) {
        delete availableServers[key];
      }
    }
    if (enabledServers.length === 0) {
      // use default 
      for (let key in defaultAvailableServers) {
        availableServers[key] = 0;
      }
    } else {
      enabledServers.forEach(server => {
        if (!availableServers[server.URL]) {
          availableServers[server.URL] = 0;
        }
      });
    }
    const theServerThatHaveLeastUse = Object.keys(availableServers).reduce((a, b) => availableServers[a] < availableServers[b] ? a : b);
    console.log('theServerThatHaveLeastUse', theServerThatHaveLeastUse);
    availableServers[theServerThatHaveLeastUse] += 1;
    let apiCallRes;
    let ukey;
    let error;
    const servers = Object.keys(availableServers);
    let startingIndex = servers.indexOf(theServerThatHaveLeastUse);
    for (let i = startingIndex; i < servers.length + startingIndex; i++) {
      const serverIndex = i % servers.length;
      try {
        apiCallRes = await axios.request({
          url: servers[serverIndex],
          method: 'post',
          headers: { 'content-type': 'application/json' },
          data: JSON.stringify({
            artworkBase64, blend, support, angle,
            type: request.body.type,
            artworkUrl,
            debug,
            additionRotationLayer,
            resolution_x,
            resolution_y,
            mainArtworkRotationLayer
          }),
        });
        ukey = apiCallRes.data?.ukey;
        if (ukey) {
          break;
        }
      } catch (e) {
        error = e;
        console.log(`Error occurred while calling server: ${servers[serverIndex]}`);
      }
    }
    if (!ukey && !request.body.type) {
      throw error || new Error('An error has occurred');
    }

    return {
      success: true,
      data: request.body.type === 'prepare-s3' ? {
        message: 'Done prepare render',
      } : {
        url: 'https://print-manager-media.s3.eu-west-1.amazonaws.com/' + ukey
      },
    }

  }

}

export default new Blender2DRender();
