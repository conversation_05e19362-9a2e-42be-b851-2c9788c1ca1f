import { <PERSON>AP<PERSON><PERSON><PERSON><PERSON> } from 'type';
import { validateRequest, combineMiddlewares, receiveFileAndFields, IModifiedBody, TFileField } from '../api-middlewares'
import { ERROR } from 'const';
import { DB, InMemory as RedisCache } from 'db';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'helpers';
import Joi = require("joi");


class FromPolotno implements TypeAPIHandler {
  url = '/api/images/from-polotno';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      json: Joi.any(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
  ]);

  handler = async (request, reply) => {

    const imageBase64 = await Polotno.jsonToImageBase64(request.body.json);

    return {
      success: true,
      data: imageBase64,
    }

  }
    
}

export default new FromPolotno();
