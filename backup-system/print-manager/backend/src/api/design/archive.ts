import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, checkAuthen, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import Joi = require("joi");
import { ERROR } from "const";
import RolesHelper from "../../helpers/RolesHelper";

class ArchiveProduct implements TypeAPIHandler {

  url = "/api/designs/archive";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      id: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { id } = request.body;
    const user = request.user;
    if (RolesHelper.isNoRoles(user)) throw new Error(ERROR.PERMISSION_DENIED);

    const design = await DB.Design.findByPk(id);
    if (!design) throw new Error(ERROR.NOT_EXISTED);
    if (RolesHelper.isUserOrReseller(user) && RolesHelper.getResellerId(user) !== design.createdByUserId) {
      throw new Error(ERROR.PERMISSION_DENIED);
    }
    design.inactive = true;
    await design.save();

    return {
      success: true,
    }
  };
}

export default new ArchiveProduct();
