import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON><PERSON>, checkAuthenOptional, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON>elper } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");
import RolesHelper from "../../helpers/RolesHelper";

class AddCustomArtwork implements TypeAPIHandler {

  url = "/api/designs/add-custom-artwork";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      id: Joi.string(),
      resellerId: Joi.string(),
      customArtwork: Joi.string(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { body, user } = request;
    const { id, resellerId, customArtwork } = body;
    if (RolesHelper.isNoR<PERSON>s(user)) throw new Error(ERROR.PERMISSION_DENIED);
    if (user.id !== resellerId && user.resellerId !== resellerId ) throw new Error(ERROR.PERMISSION_DENIED);
    
    const design = await DB.Design.findByPk(id);
    if (design.createdByUserId !== resellerId) throw new Error(ERROR.PERMISSION_DENIED);

    const data = { ...design.data };
    data.settings = {
      ...data.settings,
      customArtwork,
    };
    design.data = data;
    await design.save();

    return {
      success: true,
      data: design,
    }
  };
}

export default new AddCustomArtwork();
