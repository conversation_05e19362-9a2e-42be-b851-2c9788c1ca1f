import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'type';
import { validateRequest, combineMiddlewares, receiveFileAndFields, IModifiedBody, TFileField, checkAuthen } from '../api-middlewares'
import { ERROR } from 'const';
import { DB, InMemory as RedisCache } from 'db';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, VarHelper } from 'helpers';
import Joi = require("joi");
import { Op } from 'sequelize';
import Stripe from 'stripe';
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, { apiVersion: null });

class MarkDepositComplete implements TypeAPIHandler {
  url = '/api/payment/admin-add-balance-to-reseller-wallet';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      amount: Joi.number(),
      currency: Joi.string(),
      resellerId: Joi.string(),
      depositMessage: Joi.string(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request, reply) => {
    const { currency, amount, depositMessage, resellerId } = request.body;
    if (request.user.role !== 'admin') throw new Error(ERROR.PERMISSION_DENIED);
    const fullUser = await DB.User.findByPk(resellerId);
    if (!fullUser.resellerStripeId) throw new Error(ERROR.NOT_EXISTED);
    
    const transactions = await stripe.customers.createBalanceTransaction(fullUser.resellerStripeId, {
      amount: -Math.round(amount * 100),
      currency: currency.toLowerCase() || 'gbp',
      metadata: {
        type: "deposit",
        paymentMethod: `System credit`,
        depositMessage: depositMessage,
      }
    });

    return { success: true, data: transactions };
  }
}

export default new MarkDepositComplete();
