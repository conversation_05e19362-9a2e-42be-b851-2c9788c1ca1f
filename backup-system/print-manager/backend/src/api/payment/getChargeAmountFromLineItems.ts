import { TRequestUser, <PERSON>AP<PERSON><PERSON><PERSON><PERSON> } from 'type';
import { combineMiddlewares, checkAuthen } from '../api-middlewares'
import { DB } from 'db';
import Joi = require('joi');
import { <PERSON>e<PERSON><PERSON>per, VarHelper } from 'helpers';
import Stripe from 'stripe';
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, { apiVersion: null });

class GetChargeAmountFromLineItems implements TypeAPIHandler {
  url = '/api/payment/get-charge-amount';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      orders: Joi.array().items(Joi.any()),
    }),
  }

  preHandler = combineMiddlewares([
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const orders = request.body.orders;

    let total = 0;
    const orderWeights = {};
    let shouldIncludeVAT = false;
    const invoices = [];
    const stripeInvoiceIds = [];
    for (let i = 0; i < orders.length; i++) {
      const { line_items, shipping_address, orderId } = orders[i];

      let invoice = await DB.Invoice.findOne({
        where: {
          orderId,
        }
      });
      console.log('invoice', invoice?.id);
      if (invoice?.id) {
        invoices.push(invoice);
        total = total + (invoice.total || 0);
        orderWeights[orderId] = invoice?.data?.weight || 0;
        if (invoice?.data?.stripeInvoiceID) stripeInvoiceIds.push(invoice?.data?.stripeInvoiceID);
        continue;
      }

      let orderWeight = 0;
      const printJobInfo = line_items.map(v => {
        return {
          quantity: v.quantity,
          printJobId: v.printJobId,
          designId: v.designRenderId,
          productRenderId: v.productRenderId,
          isSampleRequest: v.isSampleRequest,
        }
      }).filter(v => !!v.printJobId || !!v.designId || !!v.productId);

      console.log('printJobInfo', printJobInfo);
    
      let orderAmount = 0;
      let productsPrice = {};
      for (let i = 0; i < printJobInfo.length; i++) {
        const { quantity, printJobId, designId, productRenderId, isSampleRequest } = printJobInfo[i];
        try {

          const product = await ( async () => {
            if (productRenderId) {
              return await DB.Product.findByPk(productRenderId);
            }
            if (printJobId) {
              const printJob = await DB.PrintJob.findByPk(printJobId);
              if (!!printJob) {
                const { productId } = printJob;
                return await DB.Product.findByPk(productId);
              }
            }
            if (designId) {
              const design = await DB.Design.findByPk(designId);
              if (!!design) {
                const { productId } = design;
                return await DB.Product.findByPk(productId);
              }
            }
          })();
          if (!product) continue;
          const a = (product.price || 0) * quantity;
          orderAmount += a;
          orderWeight += (product.data?.weight || 1) * quantity;
          productsPrice[String(printJobId)] = product.price;
          
        } catch (err) { }
      }
      if (!shouldIncludeVAT) shouldIncludeVAT = shipping_address.country === "United Kingdom";
      const amountWithTax = shouldIncludeVAT ? orderAmount * 1.2 : orderAmount;
      orderWeights[orderId] = orderWeight;

      invoice = await DB.Invoice.create({
        id: VarHelper.genId(),
        orderId,
        total: amountWithTax,
        taxes: shouldIncludeVAT ? 0 : orderAmount * 0.2,
        prices: productsPrice,
        customerInfo: shipping_address,
        shippingAddress: shipping_address,
        data: {
          weight: orderWeight,
          includeTax: shouldIncludeVAT,
          isSampleRequest: !!line_items[0]?.isSampleRequest,
        }
      });
      invoices.push(invoice);
      total = total + amountWithTax;
    }

    const stripeInvoices = await Promise.all(stripeInvoiceIds.map(async id => {
      try {
        const stripeInvoice = await stripe.invoices.retrieve(id);
        const updatedInvoice = await StripeHelper.updateInvoiceProductPrice(stripeInvoice);
        return updatedInvoice;
      } catch (error) {
        console.log("retrieve_stripe_invoice_err", error);
        return {
          id,
          error: error.message,
        };
      }
    }));

    return {
      success: true,
      data: {
        // dont use total, use invoices
        // amount: total,
        invoices,
        stripeInvoices,
        orderWeights,
        currency: "GBP",
      },
    }
  }
}

export default new GetChargeAmountFromLineItems();
