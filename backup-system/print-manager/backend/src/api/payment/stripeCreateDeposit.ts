import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'type';
import { validateRequest, combineMiddlewares, receiveFileAndFields, IModifiedBody, TFileField, checkA<PERSON>en } from '../api-middlewares'
import { ERROR } from 'const';
import { DB, InMemory as RedisCache } from 'db';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FileHelper } from 'helpers';
import Joi = require("joi");
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

class StripeCreatePayment implements TypeAPIHandler {
  url = '/api/payment/create-deposit';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      amount: Joi.number(),
      currency: Joi.string(),
      paymentMethodId: Joi.string(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request, reply) => {
    if (request.user.role === 'admin') throw new Error(ERROR.PERMISSION_DENIED);

    const { amount, currency, paymentMethodId } = request.body;

    const resellerId = request.user.resellerId || request.user.id;

    const fullUser = await DB.User.findByPk(resellerId);
    if (!fullUser.resellerStripeId) {
      const resellerName = fullUser.accountName || [fullUser.firstName, fullUser.lastName].filter(Boolean).join(' ');
      const customer = await stripe.customers.create({
        name: resellerName,
        email: fullUser.email,
      });
      fullUser.resellerStripeId = customer.id;
      await fullUser.save();
      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(amount * 100), // in cents
        currency: currency.toLowerCase() || 'gbp',
        payment_method_types: ['card'],
        metadata: {
          amount,
          currency,
          paymentType: 'deposit funds',
          resellerId: resellerId,
        },
        customer: fullUser.resellerStripeId,
        setup_future_usage: 'off_session',
      });
      return {
        success: true,
        data: paymentIntent,
      }
    }

    // pay using saved card

    if (!!paymentMethodId && paymentMethodId !== 'new') {
      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(amount * 100), // in cents
        currency: currency.toLowerCase() || 'gbp',
        customer: fullUser.resellerStripeId,
        payment_method: paymentMethodId,
        off_session: true,
        confirm: true,
      });
      return {
        success: true,
        data: paymentIntent,
      }
    }

    if (!paymentMethodId) {
      const paymentMethods = await stripe.paymentMethods.list({
        customer: fullUser.resellerStripeId,
        type: 'card',
      });
  
      if (paymentMethods.data.length > 0) {
        return {
          success: true,
          extraConfirmStepData: {
            paymentMethods: paymentMethods.data,
          },
          data: null,
        }
      }
    }
    
    // fallback to first time payment

    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount * 100), // in cents
      currency: currency.toLowerCase() || 'gbp',
      payment_method_types: ['card'],
      metadata: {
        amount,
        currency,
        paymentType: 'deposit funds',
        resellerId: resellerId,
      },
      customer: fullUser.resellerStripeId,
      setup_future_usage: 'off_session',
    });
    return {
      success: true,
      data: paymentIntent,
    }
  }
}

export default new StripeCreatePayment();
