import { TRequestUser, TypeAPIHandler } from 'type';
import { DB } from 'db';
import Joi = require('joi');
import { combineMiddlewares, checkAuthen } from '../api-middlewares'
import { DISCOUNT_RATE, EUROPEAN_COUNTRIES, TAX_ONLY_RATE, shippingPrice, shippingPriceEurope, shippingPriceTheRestOfTheWorld } from './utils';
import { VarHelper } from 'helpers';

class GetChargeAmountFromLineItems implements TypeAPIHandler {
  url = '/api/payment/get-charge-amount-without-invoice';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      address: Joi.any(),
      products: Joi.array().items(Joi.any()),
      discounted: Joi.bool(),
    }),
  }

  preHandler = combineMiddlewares([
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { products, address, discounted } = request.body;

    const fullUser = await DB.User.findByPk(request.user.id);
    const country = address?.country || fullUser?.country;

    const shouldIncludeVAT = country === "United Kingdom";

    let totalAmount = 0;
    let productAmount = 0;
    for (let i = 0; i < products.length; i++) {
      const { productId, designId, quantity } = products[i];
      try {
        const product = await (async () => {
          if (productId) {
            return await DB.Product.findByPk(productId);
          }
          if (designId) {
            const design = await DB.Design.findByPk(designId);
            if (!!design) {
              const { productId } = design;
              return await DB.Product.findByPk(productId);
            }
          }
        })();
        if (!product) continue;
        const discountedPrice = VarHelper.calculateDiscountedPrice({
          price: product.price || 0,
          quantity,
          discountType: product.data?.discountType,
        }, product.packPrices);
        const a = discountedPrice * (quantity || 1);
        productAmount += discounted ? a * DISCOUNT_RATE : a;
      } catch (err) { }
    }
    totalAmount += productAmount;
    const shippingFee = (() => {
      if (country === 'United Kingdom') {
        return shippingPrice.RM48 || 0;
      }
      // TODO: handle international shipping fee
      if (EUROPEAN_COUNTRIES.includes(country)) {
        return shippingPriceEurope.RM48 || 0;
      }
      return shippingPriceTheRestOfTheWorld.RM48 || 0;
    })();
    totalAmount += shippingFee
    if (shouldIncludeVAT) {
      totalAmount += (productAmount + shippingFee) * TAX_ONLY_RATE;
    }

    return {
      success: true,
      data: {
        amount: totalAmount,
        currency: "GBP",
      },
    }
  }
}

export default new GetChargeAmountFromLineItems();
