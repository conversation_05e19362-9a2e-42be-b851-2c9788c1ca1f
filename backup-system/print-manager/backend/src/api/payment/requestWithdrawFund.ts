import { TRequestUser, Type<PERSON><PERSON><PERSON>and<PERSON> } from 'type';
import { combineMiddlewares, checkAuthen } from '../api-middlewares'
import { DB } from 'db';
import Joi = require('joi');
import { ERROR } from 'const';
import { VarHelper } from 'helpers';
import MailHelper from 'helpers/MailHelper';
import { genEmailHtml } from 'helpers/email-templates';
import Stripe from 'stripe';
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, { apiVersion: null });

class RequestWithdrawFund implements TypeAPIHandler {
  url = '/api/payment/request-withdraw-fund';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      amount: Joi.number(),
      currency: Joi.string(),
    }),
  }

  preHandler = combineMiddlewares([
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { user, body, } = request;
    const { amount, currency } = body;
    if (user.role === 'admin') throw new Error(ERROR.PERMISSION_DENIED);
    const resellerId = user.resellerId || user.id;
    const fullUser = await DB.User.findByPk(resellerId);
    if (!fullUser) throw new Error(ERROR.NOT_EXISTED);

    const log = await DB.GeneralData.create({
      id: VarHelper.genId(),
      type: 'request-withdraw',
      userId: resellerId,
      field1: String(amount),
      field2: '',
      name: (fullUser.firstName || '') + ' ' + (fullUser.lastName || ''),
      data: {
        currency
      },
      publicPermission: { c: false, r: false, u: false, d: false },
    });

    await MailHelper.sendSMTPEmail({
      to: "<EMAIL>",
      cc: [],
      subject: "New withdraw request",
      html: genEmailHtml({
        title: "Withdraw request",
        message: `There is a withdrawal fund request from ${user.firstName} ${user.lastName} (${user.email}). Open this link to resolve:`,
        link: `https://${process.env.DEV ? 'bg-dev.' : 'bg-production.'}bottledgoose.co.uk/products/list-withdraw-request`,
      }),
    })

    const balances: any = await stripe.customers.retrieve(fullUser.resellerStripeId, {
      expand: ['cash_balance'],
    });

    const transactions = await stripe.customers.createBalanceTransaction(fullUser.resellerStripeId, {
      amount: balances.balance * -1,
      currency: balances.currency,
      metadata: {
        type: "refund",
      }
    });

    return {
      success: true,
      data: log,
      transactions,
    }
  }
}

export default new RequestWithdrawFund();
