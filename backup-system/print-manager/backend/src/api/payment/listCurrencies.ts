import { TRequestUser, <PERSON>AP<PERSON><PERSON><PERSON><PERSON> } from 'type';
import { combineMiddlewares, checkAuthen } from '../api-middlewares'
import { DB } from 'db';
const axios = require('axios');
const list_currencies = require('./list_currencies.json');

let cacheData;
let cacheTime;

class ListCurrencies implements TypeAPIHandler {
  url = '/api/payment/list-currencies';
  method = 'GET';

  preHandler = combineMiddlewares([
    // checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {

    const getCurrency = async () => {
      const res = await axios.request({
        url: 'https://api.apilayer.com/fixer/latest?base=GBP',
        headers: { 'apiKey': 'gRu8s8SHOLF3iSrp74saZZCS8C4bLHrT' },
        method: 'get'
      });
      const json = res.data;
      const newList : Array<{ rate: number; cc: string; symbol: string; name: string; }> = [];
      const { rates } = json;

      ['GBP', 'USD', 'EUR', 'AUD', 'CNY', 'JPY'].forEach(key => {
        const findCurrency = list_currencies.find(val => val.cc === key);
        if (!!findCurrency) newList.push({ ...findCurrency, rate: rates[key] });
      })

      return newList;
    }

    let shouldFetchNew = false;
    if (!cacheData) shouldFetchNew = true;
    else if (new Date().getTime() - cacheTime > 1000 * 60 * 60) shouldFetchNew = true;

    if (!shouldFetchNew) {
      return {
        success: true,
        data: cacheData,
      }
    }
    try {
      const newData = await getCurrency();
      cacheData = newData;
      cacheTime = new Date().getTime();
      return {
        success: true,
        data: newData,
      }
    } catch(err) {
      return {
        success: true,
        data: cacheData,
      }
    }
  }
}

export default new ListCurrencies();
