import { TRequestUser, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'type';
import { combineMiddlewares, checkAuthen } from '../api-middlewares'
import { DB } from 'db';
import Joi = require('joi');
import { ERROR } from 'const';
import Stripe from 'stripe';
import <PERSON><PERSON><PERSON>elper from 'helpers/XeroHelper';
import { AWSHelper, FileHelper } from 'helpers';
import { DISCOUNT_RATE, EUROPEAN_COUNTRIES, TAX_ONLY_RATE, shippingPrice, shippingPriceEurope, shippingPriceTheRestOfTheWorld } from './utils';
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, { apiVersion: null });
const moment = require('moment');

class ChargeFromWallet implements TypeAPIHandler {
  url = '/api/payment/charge-from-wallet';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      orders: Joi.array().items(Joi.object({
        orderId: Joi.string(),
        slug: Joi.string(),
        line_items: Joi.array().items(Joi.any()),
        shipping_address: Joi.any(),
      })),
      shippingService: Joi.string(),
    }),
  }

  preHandler = combineMiddlewares([
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { orders, shippingService } = request.body;

    const fullUser = await DB.User.findByPk(request.user.id);
    if (!fullUser.resellerStripeId) {
      return {
        success: true,
        data: {},
      }
    }

    // token for sample request
    let sampleToken = typeof fullUser?.otherData?.sampleToken === 'number' ? fullUser?.otherData?.sampleToken : 10;
    let preCalculateTotalSampleToken = typeof fullUser?.otherData?.sampleToken === 'number' ? fullUser?.otherData?.sampleToken : 10;
    const shippingFeeOrder = {};
    // let isShippingFeeWithTax = false;

    const balances: any = await stripe.customers.retrieve(fullUser.resellerStripeId, {
      expand: ['cash_balance'],
    });
    console.log('balances.balance', balances.balance);
    if (balances.balance >= 0) throw new Error(ERROR.NOT_ENOUGH_BALANCE)

    let total = 0;
    for (let i = 0; i < orders.length; i++) {
      const { orderId } = orders[i];
      const invoice = await DB.Invoice.findOne({
        where: {
          orderId,
        }
      });
      const isSampleRequest = !!invoice.data?.isSampleRequest;
      const thisShippingFee = (() => {
        const address = orders[i].shipping_address;
        if (address?.country === 'United Kingdom') {
          return shippingPrice[shippingService] || 0;
        }
        // TODO: handle international shipping fee
        if (EUROPEAN_COUNTRIES.includes(address?.country)) {
          return shippingPriceEurope[shippingService] || 0;
        }
        return shippingPriceTheRestOfTheWorld[shippingService] || 0;
      })();
      const shouldDiscount = !isSampleRequest ? false : (preCalculateTotalSampleToken > 0 ? true : false);
      if (shouldDiscount) preCalculateTotalSampleToken--;
      const discountRate = shouldDiscount ? DISCOUNT_RATE : 1;
      if (invoice?.total) {
        total += invoice?.total * discountRate + thisShippingFee * (invoice.taxes ? 1.2 : 1);
        console.log(`total += ${invoice?.total * discountRate} + ${thisShippingFee * (invoice.taxes ? 1.2 : 1)}`);
      }
      shippingFeeOrder[orderId] = thisShippingFee;
    }
    if (Number(total) * 100 > Math.abs(balances.balance)) {
      console.log('Total', total, balances.balance);
      throw new Error(ERROR.NOT_ENOUGH_BALANCE);
    }
    if (total === 0) {
      return {
        success: true,
        data: {},
      }
    }

    let productNames = [];
    let orderNumbers = [];
    let xeroLineItems = [];
    const amountPaid : any = {};
    for (let i = 0; i < orders.length; i++) {
      const { orderId, line_items } = orders[i];
      
      const invoice = await DB.Invoice.findOne({
        where: {
          orderId,
        }
      });
      const orderNumber = invoice.orderNumber;
      if (orderNumber) orderNumbers.push(orderNumber);
      const itemPrefix = `#${invoice.orderNumber} `;
      const isSampleRequest = !!invoice.data?.isSampleRequest;
      const shouldDiscount = !isSampleRequest ? false : (sampleToken > 0 ? true : false);
      if (shouldDiscount) sampleToken--;

      const discountRate = shouldDiscount ? DISCOUNT_RATE : 1;
      let tax = 0;
      amountPaid[orderId] = 0;
      if (line_items?.length) {
        for (let i = 0; i < line_items.length; i++) {
          const lineItem = line_items[i];
          if (line_items[i]?.name) {
            const printJobId = lineItem.properties?.find(i => i.name === 'Print Job')?.value;
            let linePrice = invoice?.prices?.[printJobId] || lineItem.price || 0;
            const quantity = +line_items[i]?.quantity || 1;
            if (invoice.taxes) {
              tax += linePrice * quantity * TAX_ONLY_RATE * discountRate;
            }
            const newPrice = await stripe.prices.create({
              currency: 'gbp',
              product_data: {
                name: itemPrefix + lineItem.name,
              },
              unit_amount: Math.round(Number(linePrice) * discountRate * 100),
            })
            amountPaid[orderId] += Math.round(Number(linePrice) * discountRate * 100);
            await stripe.invoiceItems.create({
              customer: fullUser.resellerStripeId,
              price: newPrice.id,
              quantity: lineItem.quantity,
            });
            productNames.push(line_items[i].name);
            xeroLineItems.push({
              description: lineItem.name,
              quantity: lineItem.quantity,
              unitAmount: linePrice * discountRate,
              taxType: invoice.taxes ? "OUTPUT2" : "NONE",
              lineAmount: linePrice * discountRate,
            })
          }
        }
      }

      const _shippingPrice = await stripe.prices.create({
        currency: 'gbp',
        product_data: {
          name: itemPrefix + `Shipping fee`,
        },
        unit_amount: Math.round(Number(shippingFeeOrder[orderId]) * 100),
      });
      amountPaid[orderId] += Math.round(Number(shippingFeeOrder[orderId]) * 100);
      if (invoice.taxes) {
        const shippingFeeTax = shippingFeeOrder[orderId] * TAX_ONLY_RATE;
        tax += shippingFeeTax;
      }
      await stripe.invoiceItems.create({
        customer: fullUser.resellerStripeId,
        price: _shippingPrice.id,
        quantity: 1,
      });
      if (tax > 0) {
        const _taxPrice = await stripe.prices.create({
          currency: 'gbp',
          product_data: {
            name: itemPrefix + 'VAT (20%)',
          },
          unit_amount: Math.round(Number(tax) * 100),
        });
        amountPaid[orderId] += Math.round(Number(tax) * 100);
        await stripe.invoiceItems.create({
          customer: fullUser.resellerStripeId,
          price: _taxPrice.id,
          quantity: 1,
        });
      }
      xeroLineItems.push({
        description: `Shipping fee #${orderNumber}`,
        quantity: 1,
        unitAmount: shippingFeeOrder[orderId],
        taxType: invoice.taxes ? "OUTPUT2" : "NONE",
        lineAmount: shippingFeeOrder[orderId],
      });
    }

    // CREATE XERO INVOICE
    console.log('create xero invoice')
    let xeroInvoiceID;
    let xeroInvoiceUrl;
    try {
      await XeroHelper.checkAuthen();
      const contact = await XeroHelper.createContactIfNotExist(fullUser.email, fullUser);
      console.log('contact', contact);
      console.log('xeroLineItems', JSON.stringify(xeroLineItems));
      const xeroInvoice = await XeroHelper.createInvoice(contact.ContactID, xeroLineItems);
      console.log('xeroInvoice', xeroInvoice);
      xeroInvoiceID = xeroInvoice?.InvoiceID;
      console.log('xeroInvoiceID', xeroInvoiceID);
      if (xeroInvoiceID) {
        await XeroHelper.markInvoiceAsPaid(xeroInvoice);
        xeroInvoiceUrl = await XeroHelper.getInvoicePublicURL(xeroInvoiceID);
        console.log('xeroInvoiceUrl', xeroInvoiceUrl);
      }
    } catch(err) {
      console.log('Xero Error', err);
    }

    const invoice = await stripe.invoices.create({
      customer: fullUser.resellerStripeId,
      metadata: {
        orderNumber: orderNumbers.join(','),
        productName: productNames.join('\n'),
        paymentMethod: "Wallet Credit",
        xeroInvoiceID,
        xeroInvoiceUrl,
      }
    });
    const stripeInvoiceID = invoice.id
    const readyInvoice = await stripe.invoices.finalizeInvoice(stripeInvoiceID);
    const tempFilePath = `/tmp/invoice-${stripeInvoiceID}.pdf`; 
    await FileHelper.downloadFile(readyInvoice.invoice_pdf, tempFilePath);
    const stripePdfUrl = await AWSHelper.upload({
      key: `bg/invoices/${stripeInvoiceID}.pdf`,
      filePath: tempFilePath,
    });

    for (let i = 0; i < orders.length; i++) {
      const { orderId } = orders[i];
      const invoice = await DB.Invoice.findOne({
        where: {
          orderId,
        }
      });
      

      invoice.data = {
        ...(invoice.data || {}),
        invoicePdf: `${process.env.BACKEND_CMS_URL}/api/order/gen-invoice/${orderId}`,
        stripeInvoiceUrl: stripePdfUrl,
        shippingService,
        shippingFee: shippingFeeOrder[orderId],
        xeroInvoiceID,
        xeroInvoiceUrl,
        stripeInvoiceID,
        amountPaid: amountPaid[orderId],
      }
      invoice.paidAt = moment().toISOString();
      await invoice.save();
    }

    // const paymentIntent = await stripe.paymentIntents.applyCustomerBalance(
    //   fullUser.resellerStripeId,
    //   amountWithTax,
    //   fullUser.currency.toLowerCase(),
    // );
    // const transactions = await stripe.customers.createBalanceTransaction(fullUser.resellerStripeId, {
    //   amount: -Math.round(total * 100),
    //   currency: "gbp", // (currency || fullUser.currency).toLowerCase(),
    //   metadata: {
    //     orderNumber: orderNumbers.join(','),
    //     productName: productNames.join(','),
    //     paymentMethod: "Wallet Credit",
    //     // invoicePDF: readyInvoice.invoice_pdf,
    //     // hostedInvoiceUrl: readyInvoice.hosted_invoice_url,
    //   }
    // });

    // update sample token to user
    const otherData = Object.assign({}, fullUser.otherData, { sampleToken: sampleToken });
    fullUser.otherData = otherData;
    await fullUser.save();

    return {
      success: true,
      data: readyInvoice,
    }
  }
}

export default new ChargeFromWallet();
