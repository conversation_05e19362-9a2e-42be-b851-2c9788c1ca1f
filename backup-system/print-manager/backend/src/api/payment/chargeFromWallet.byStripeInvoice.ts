import { TRequestUser, <PERSON><PERSON><PERSON><PERSON>and<PERSON> } from 'type';
import { combineMiddlewares, checkAuthen } from '../api-middlewares'
import { DB } from 'db';
import Joi = require('joi');
import { ERROR } from 'const';
import Stripe from 'stripe';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, FileHelper } from 'helpers';
import { DISCOUNT_RATE, EUROPEAN_COUNTRIES, SAMPLE_DISCOUNT, TAX_ONLY_RATE, shippingPrice, shippingPriceEurope, shippingPriceTheRestOfTheWorld } from './utils';
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, { apiVersion: null });
const moment = require('moment');

class ChargeFromWallet implements TypeAPIHandler {
  url = '/api/payment/charge-from-wallet-by-stripe-invoice';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      invoiceIds: Joi.array().items(Joi.string()),
      useSampleToken: Joi.bool().optional(),
    }),
  }

  preHandler = combineMiddlewares([
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { invoiceIds, useSampleToken } = request.body;

    const fullUser = await DB.User.findByPk(request.user.id);
    if (!fullUser.resellerStripeId) {
      return {
        success: true,
        data: {},
      }
    }
    let sampleToken = fullUser?.otherData?.sampleToken === undefined ? 10 : Number(fullUser?.otherData?.sampleToken);
    if (useSampleToken) {
      if (sampleToken <= 0) throw new Error("You don't have any sample tokens left, please contact admin for more details");
    }

    const shippingFeeOrder = {};
    const readyInvoices = [];

    const balances: any = await stripe.customers.retrieve(fullUser.resellerStripeId, {
      expand: ['cash_balance'],
    });
    if (balances.balance >= 0) throw new Error(ERROR.NOT_ENOUGH_BALANCE)

    let total = 0;
    for (let i = 0; i < invoiceIds.length; i++) {
      const stripeInvoice = await stripe.invoices.retrieve(invoiceIds[i]);
      const invoice = await DB.Invoice.findOne({
        where: stripeInvoice.metadata?.orderId ? {
          orderId: stripeInvoice.metadata?.orderId
        } : {
          orderNumber: stripeInvoice.metadata?.orderNumber,
        }
      })
      if (!invoice?.id) throw new Error("Can not find Invoice");
      total += stripeInvoice.total;
      if (useSampleToken && invoice.data?.isSampleRequest) {
        const productLine = stripeInvoice.lines?.data?.find(i => {
          return !i.description?.includes("Shipping fee") && !i.description?.includes("VAT ")
        });
        const taxLine = stripeInvoice.lines?.data?.find(i => i.description?.includes("VAT "))
        if (productLine) {
          total -= productLine.amount * SAMPLE_DISCOUNT * (1 + (taxLine ? TAX_ONLY_RATE : 0))
        }
      }
      // const thisShippingFee = (() => {
      //   const address = invoice.shippingAddress;
      //   if (address?.country === 'United Kingdom') {
      //     return shippingPrice[shippingService] || 0;
      //   }
      //   // TODO: handle international shipping fee
      //   if (EUROPEAN_COUNTRIES.includes(address?.country)) {
      //     return shippingPriceEurope[shippingService] || 0;
      //   }
      //   return shippingPriceTheRestOfTheWorld[shippingService] || 0;
      // })();
      // shippingFeeOrder[invoice.orderId] = thisShippingFee;
      // total += stripeInvoice.total + thisShippingFee * 100;
      // if (invoice.taxes) {
      //   total += Math.round((stripeInvoice.total + thisShippingFee * 100) * TAX_ONLY_RATE);
      // }
    }
    if (total > Math.abs(balances.balance)) {
      throw new Error(ERROR.NOT_ENOUGH_BALANCE + `:${total / 100}`);
    }
    if (total === 0) {
      return {
        success: true,
        data: {},
      }
    }

    // update stripe invoice - add shipping and tax
    for (let i = 0; i < invoiceIds.length; i++) {
      const stripeInvoice = await stripe.invoices.retrieve(invoiceIds[i]);
      const invoice = await DB.Invoice.findOne({
        where: stripeInvoice.metadata?.orderId ? {
          orderId: stripeInvoice.metadata?.orderId
        } : {
          orderNumber: stripeInvoice.metadata?.orderNumber,
        }
      })
      // update product & tax price for sample order
      if (useSampleToken && invoice.data?.isSampleRequest) {
        sampleToken--
        const productLine = stripeInvoice.lines?.data?.find(i => {
          return !i.description?.includes("Shipping fee") && !i.description?.includes("VAT ")
        })

        if (productLine) {
          const productPriceDiscounted = Math.round(Number(productLine.amount) * DISCOUNT_RATE);
          const newPrice = await stripe.prices.create({
            currency: 'gbp',
            product_data: {
              name: productLine.description,
            },
            unit_amount: productPriceDiscounted,
          })
          await stripe.invoiceItems.create({
            invoice: stripeInvoice.id,
            customer: fullUser.resellerStripeId,
            price: newPrice.id,
            quantity: 1,
          });
          await stripe.invoiceItems.del(productLine?.id);

          const taxLine = stripeInvoice.lines?.data?.find(i => i.description?.includes("VAT "))
          if (taxLine) {
            const _taxPrice = await stripe.prices.create({
              currency: 'gbp',
              product_data: {
                name: taxLine.description,
              },
              unit_amount: Math.round(taxLine.amount - productLine.amount * SAMPLE_DISCOUNT * TAX_ONLY_RATE),
            });
            await stripe.invoiceItems.create({
              invoice: stripeInvoice.id,
              customer: fullUser.resellerStripeId,
              price: _taxPrice.id,
              quantity: 1,
            });
            await stripe.invoiceItems.del(taxLine?.id);
          }
        }
      }

      // to-do: edit shipping fee & tax if use select different service
      // const itemPrefix = `#${invoice.orderNumber} `;
      // const _shippingPrice = await stripe.prices.create({
      //   currency: 'gbp',
      //   product_data: {
      //     name: itemPrefix + `Shipping fee`,
      //   },
      //   unit_amount: Math.round(Number(shippingFeeOrder[invoice.orderId]) * 100),
      // });
      // await stripe.invoiceItems.create({
      //   invoice: stripeInvoice.id,
      //   customer: fullUser.resellerStripeId,
      //   price: _shippingPrice.id,
      //   quantity: 1,
      // });
      // if (invoice.taxes) {
      //   const shippingFeeTax = shippingFeeOrder[invoice.orderId] * TAX_ONLY_RATE * 100;
      //   const _taxPrice = await stripe.prices.create({
      //     currency: 'gbp',
      //     product_data: {
      //       name: itemPrefix + 'VAT (20%)',
      //     },
      //     unit_amount: Math.round(Number(stripeInvoice.total) * TAX_ONLY_RATE + shippingFeeTax),
      //   });
      //   await stripe.invoiceItems.create({
      //     invoice: stripeInvoice.id,
      //     customer: fullUser.resellerStripeId,
      //     price: _taxPrice.id,
      //     quantity: 1,
      //   });
      // }

      // finalize & update db invoice
      const readyInvoice = await stripe.invoices.finalizeInvoice(stripeInvoice.id);
      let stripePdfUrl;
      if (readyInvoice.invoice_pdf) {
        try {
          const tempFilePath = `/tmp/invoice-${stripeInvoice.id}.pdf`;
          await FileHelper.downloadFile(readyInvoice.invoice_pdf, tempFilePath);
          stripePdfUrl = await AWSHelper.upload({
            key: `bg/invoices/${stripeInvoice.id}.pdf`,
            filePath: tempFilePath,
          });
        } catch (error) {
          console.log("[chargeFromWallet.byStripeInvoice] downloadInvoicePdfError", error);
        }
      }

      // update DB shipping fee & tax
      const shippingLine = readyInvoice.lines?.data?.find(i => i.description?.includes("Shipping fee"))
      if (shippingLine) {
        shippingFeeOrder[invoice.orderId] = shippingLine.amount / 100;
      }
      const taxLine = readyInvoice.lines?.data?.find(i => i.description?.includes("VAT "))
      if (taxLine) {
        invoice.taxes = taxLine.amount / 100;
      }
      invoice.total = readyInvoice.total / 100;
      console.log("[chargeFromWallet.byStripeInvoice] stripeInvoice.total", readyInvoice.total);

      // update sample token amount
      const otherData = Object.assign({}, fullUser.otherData, { sampleToken: sampleToken });
      fullUser.otherData = otherData;
      await fullUser.save();

      invoice.data = {
        ...(invoice.data || {}),
        invoicePdf: `${process.env.BACKEND_CMS_URL}/api/order/gen-invoice/${invoice.orderId}`,
        stripeInvoiceUrl: stripePdfUrl,
        shippingService: invoice.data?.shippingService || 'RM48',
        shippingFee: shippingFeeOrder[invoice.orderId],
        amountPaid: readyInvoice.total,
      }
      invoice.paidAt = moment().toISOString();
      await invoice.save();
      readyInvoices.push({
        ...readyInvoice,
        isSampleRequest: invoice.data?.isSampleRequest
      });
    }

    try {
      // update user totals
      let salesTotal = 0;
      let shippingTotal = 0;
      let vatTotal = 0;
      let sampleSalesTotal = 0;
      // let wholesaleTotal = 0;

      for (const readyInvoice of readyInvoices) {
        // Calculate totals from invoice line items
        for (const item of readyInvoice.lines.data) {
          if (item.description?.includes("Shipping fee")) {
            shippingTotal += item.amount;
          } else if (item.description?.includes("VAT")) {
            vatTotal += item.amount;
          } else {
            if (readyInvoice.isSampleRequest) {
              sampleSalesTotal += item.amount;
            } else {
              salesTotal += item.amount;
            }
          }
        }
      }

      fullUser.salesTotal = (fullUser.salesTotal || 0) + salesTotal;
      fullUser.shippingTotal = (fullUser.shippingTotal || 0) + shippingTotal;
      fullUser.vatTotal = (fullUser.vatTotal || 0) + vatTotal;
      fullUser.sampleSalesTotal = (fullUser.sampleSalesTotal || 0) + sampleSalesTotal;
      // fullUser.wholesaleTotal = (fullUser.wholesaleTotal || 0) + wholesaleTotal;
      await fullUser.save();
      // end update user totals
    } catch (error) {
      console.error('Error updating user totals:', error);
    }

    return {
      success: true,
      data: readyInvoices,
    }
  }
}

export default new ChargeFromWallet();
