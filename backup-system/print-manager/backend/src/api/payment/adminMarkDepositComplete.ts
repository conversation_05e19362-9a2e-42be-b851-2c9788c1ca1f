import { <PERSON>AP<PERSON><PERSON><PERSON><PERSON> } from 'type';
import { validateRequest, combineMiddlewares, receiveFileAndFields, IModifiedBody, TFileField, checkA<PERSON>en } from '../api-middlewares'
import { ERROR } from 'const';
import { DB, InMemory as RedisCache } from 'db';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, VarHelper } from 'helpers';
import Joi = require("joi");
import { Op } from 'sequelize';
import Stripe from 'stripe';
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, { apiVersion: null });

class MarkDepositComplete implements TypeAPIHandler {
  url = '/api/payment/admin-mark-deposit-complete';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      amount: Joi.number(),
      currency: Joi.string(),
      fulfillAmount: Joi.number(),
      payment_intent_id: Joi.string(),
      resellerId: Joi.string(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request, reply) => {
    const { currency, fulfillAmount, payment_intent_id, resellerId } = request.body;
    if (request.user.role !== 'admin') throw new Error(ERROR.PERMISSION_DENIED);
    const fullUser = await DB.User.findByPk(resellerId);
    if (!fullUser.resellerStripeId) throw new Error(ERROR.NOT_EXISTED);

    const paymentIntent = await stripe.paymentIntents.retrieve(payment_intent_id);
    if (!paymentIntent) throw new Error(ERROR.NOT_EXISTED);

    if (paymentIntent.status !== 'succeeded') {
      return {
        success: false,
        error: 'Payment error',
        data: paymentIntent,
      }
    }

    const paymentMethod = await stripe.paymentMethods.retrieve(paymentIntent.payment_method.toString());

    const charge = !paymentIntent.latest_charge ? null :
      typeof paymentIntent.latest_charge === 'string' ? await stripe.charges.retrieve(paymentIntent.latest_charge)
      : paymentIntent.latest_charge;
    const transactions = await stripe.customers.createBalanceTransaction(fullUser.resellerStripeId, {
      amount: -Math.round(fulfillAmount * 100),
      currency: currency.toLowerCase() || 'gbp',
      metadata: {
        type: "deposit",
        paymentMethod: `Visa ${paymentMethod.card.last4}`,
        paymentIntentId: payment_intent_id,
        receiptUrl: charge?.receipt_url,
      }
    });

    return { success: true, data: transactions };
  }
}

export default new MarkDepositComplete();
