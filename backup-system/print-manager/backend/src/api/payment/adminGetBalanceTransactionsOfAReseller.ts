import { TRequestUser, <PERSON>AP<PERSON><PERSON>and<PERSON> } from 'type';
import { combineMiddlewares, checkAuthen, checkAdmin } from '../api-middlewares'
import { DB } from 'db';
import Stripe from 'stripe';
import Joi = require('joi');
import { VarHelper } from 'helpers';
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, { apiVersion: null });

class GetBalanceTransactions implements TypeAPIHandler {
  url = '/api/payment/admin-get-balance-transactions/:resellerId';
  method = 'GET';
  apiSchema = {
    query: Joi.object({
      starting_after: Joi.string(),
    }),
    params: Joi.object({
      resellerId: Joi.string(),
    }),
  }

  preHandler = combineMiddlewares([
    checkAuthen,
    checkAdmin,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { starting_after } = request.query;
    const { resellerId } = request.params;
    console.log('resellerId', resellerId);
    const fullUser = await DB.User.findByPk(resellerId);
    if (!fullUser.resellerStripeId) {
      return {
        success: true,
        data: {},
      }
    }
    const balances = await stripe.customers.retrieve(fullUser.resellerStripeId, {
      expand: ['cash_balance'],
    });
    const transactions = await stripe.customers.listBalanceTransactions(fullUser.resellerStripeId, VarHelper.removeUndefinedField({ limit: 6, starting_after }));

    const trans = await Promise.all(transactions.data.map(async item => {
      if (!item.invoice) {
        try {
          const balanceTransaction = await stripe.balanceTransactions.retrieve(
            item.id
          );
          return {
            balanceTransaction,
            ...item,
          };
        } catch(err) {
          return item;
        }
        
      }
      const invoiceData = await stripe.invoices.retrieve(item.invoice.toString());
      return {
        ...item,
        invoiceData,
      }
    }))
    return {
      success: true,
      data: {
        trans,
        hasNext: transactions?.has_more,
        balances,
      },
    }
  }
}

export default new GetBalanceTransactions();
