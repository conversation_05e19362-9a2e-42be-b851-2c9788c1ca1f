import { TRequestUser, TypeAP<PERSON><PERSON><PERSON><PERSON> } from 'type';
import { combineMiddlewares, checkAuthen } from '../api-middlewares'
import { DB } from 'db';
import Stripe from 'stripe';
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, { apiVersion: null });

class GetBalance implements TypeAPIHandler {
  url = '/api/payment/get-balance';
  method = 'GET';

  preHandler = combineMiddlewares([
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const fullUser = await DB.User.findByPk(request.user.id);
    if (!fullUser.resellerStripeId) {
      return {
        success: true,
        data: {},
      }
    }
    const balances = await stripe.customers.retrieve(fullUser.resellerStripeId, {
      expand: ['cash_balance'],
    });
    return {
      success: true,
      data: balances,
    }
  }
}

export default new GetBalance();
