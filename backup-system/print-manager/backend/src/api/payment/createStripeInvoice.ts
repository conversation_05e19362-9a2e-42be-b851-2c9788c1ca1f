import { T<PERSON><PERSON><PERSON><PERSON>, TR<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'type';
import { combineMiddlewares, checkA<PERSON>en } from '../api-middlewares'
import { DB } from 'db';
import Joi = require('joi');
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Var<PERSON><PERSON>per } from 'helpers';
import Stripe from 'stripe';
import { DISCOUNT_RATE, EUROPEAN_COUNTRIES, TAX_ONLY_RATE, shippingPrice } from './utils';
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, { apiVersion: null });

class CreateStripeInvoice implements TypeAPIHandler {
  url = '/api/payment/create-stripe-invoice';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      order: Joi.any(),
    }),
  }

  preHandler = combineMiddlewares([
  ]);

  handler = async (request: TRequestUser, reply) => {
    const order: TCMSOrder = request.body.order;

    const orderId = String(order['Order ID']);
    let invoice = await DB.Invoice.findOne({
      where: {
        orderId,
      }
    });

    if (invoice?.id) {
      return {
        success: true,
        data: invoice,
      }
    }

    const fullUser = await DB.User.findByPk(order['Client ID']);
    if (!order['Client ID'] || !fullUser.resellerStripeId) {
      return {
        success: true,
        data: {},
      }
    }

    // token for sample request
    let sampleToken = typeof fullUser?.otherData?.sampleToken === 'number' ? fullUser?.otherData?.sampleToken : 10;

    let shouldIncludeVAT = false;
    let orderAmount = 0;
    let tax = 0;
    let orderWeight = 0;
    let productsPrice = {};
    let productNames = [];
    const { line_items, shipping_address } = order;
    const itemPrefix = `#${order['Order Number']}`;

    if (!shouldIncludeVAT) shouldIncludeVAT = shipping_address.country === "United Kingdom";
    // const amountWithTax = shouldIncludeVAT ? orderAmount * 1.2 : orderAmount;

    for (let j = 0; j < line_items.length; j++) {
      const lineItem = line_items[j];
      if (!lineItem.printJobId && !lineItem.designRenderId && !lineItem.productRenderId) continue;
      const product = await (async () => {
        if (lineItem.productRenderId) {
          return await DB.Product.findByPk(lineItem.productRenderId);
        }
        if (lineItem.printJobId) {
          const printJob = await DB.PrintJob.findByPk(lineItem.printJobId);
          if (!!printJob) {
            const { productId } = printJob;
            return await DB.Product.findByPk(productId);
          }
        }
        if (lineItem.designRenderId) {
          const design = await DB.Design.findByPk(lineItem.designRenderId);
          if (!!design) {
            const { productId } = design;
            return await DB.Product.findByPk(productId);
          }
        }
      })();
      if (!product) continue;

      const isSampleRequest = lineItem.isSampleRequest;
      const shouldDiscount = !isSampleRequest ? false : (sampleToken > 0 ? true : false);
      if (shouldDiscount) sampleToken--;

      orderWeight += (product.data?.weight || 1) * lineItem.quantity;
      orderAmount += (product.price || 0) * lineItem.quantity;
      productsPrice[String(lineItem.printJobId)] = product.price;
      productNames.push(lineItem.name);

      const discountRate = shouldDiscount ? DISCOUNT_RATE : 1;
      const newPrice = await stripe.prices.create({
        currency: 'gbp',
        product_data: {
          name: itemPrefix + lineItem.name,
        },
        unit_amount: Math.round(Number(product.price) * discountRate * 100),
      })
      await stripe.invoiceItems.create({
        customer: fullUser.resellerStripeId,
        price: newPrice.id,
        quantity: lineItem.quantity,
      });

      if (invoice.taxes) {
        tax += Number(product.price) * lineItem.quantity * TAX_ONLY_RATE * discountRate;
      }
    }

    // const _shippingPrice = await stripe.prices.create({
    //   currency: 'gbp',
    //   product_data: {
    //     name: itemPrefix + `Shipping fee`,
    //   },
    //   unit_amount: Math.round(Number(shippingFeeOrder[orderId]) * 100),
    // });

    // let taxes = shouldIncludeVAT ? 0 : orderAmount * 0.2;
    // if (taxes) {
    //   const shippingFeeTax = shippingFeeOrder[orderId] * TAX_ONLY_RATE;
    //   tax += shippingFeeTax;
    // }

    // await stripe.invoiceItems.create({
    //   customer: fullUser.resellerStripeId,
    //   price: _shippingPrice.id,
    //   quantity: 1,
    // });

    // if (tax > 0) {
    //   const _taxPrice = await stripe.prices.create({
    //     currency: 'gbp',
    //     product_data: {
    //       name: itemPrefix + 'VAT (20%)',
    //     },
    //     unit_amount: Math.round(Number(tax) * 100),
    //   });
    //   // amountPaid[orderId] += Math.round(Number(tax) * 100);
    //   await stripe.invoiceItems.create({
    //     customer: fullUser.resellerStripeId,
    //     price: _taxPrice.id,
    //     quantity: 1,
    //   });
    // }

    const stripeInvoice = await stripe.invoices.create({
      customer: fullUser.resellerStripeId,
      metadata: {
        orderNumber: String(order['Order Number']),
        productName: productNames.join('\n'),
        paymentMethod: "Wallet Credit",
      }
    });

    const stripeInvoiceID = stripeInvoice.id
    const tempFilePath = `/tmp/invoice-${stripeInvoiceID}.pdf`;
    await FileHelper.downloadFile(stripeInvoice.invoice_pdf, tempFilePath);
    const stripePdfUrl = await AWSHelper.upload({
      key: `bg/invoices/${stripeInvoiceID}.pdf`,
      filePath: tempFilePath,
    });

    const newInvoice = await DB.Invoice.create({
      id: VarHelper.genId(),
      orderId,
      orderNumber: String(order['Order Number']),
      total: shouldIncludeVAT ? orderAmount * 1.2 : orderAmount,
      taxes: shouldIncludeVAT ? 0 : orderAmount * 0.2,
      prices: productsPrice,
      customerInfo: shipping_address || order['Raw Data']?.customer,
      shippingAddress: shipping_address || order['Raw Data']?.shipping_address || order['Raw Data']?.customer,
      data: {
        weight: orderWeight,
        includeTax: shouldIncludeVAT,
        isSampleRequest: !!line_items[0]?.isSampleRequest,
        invoicePdf: `${process.env.BACKEND_CMS_URL}/api/order/gen-invoice/${orderId}`,
        stripeInvoiceUrl: stripePdfUrl,
        stripeInvoiceID,
      },
      resellerId: order['Client ID'],
      store: order['Client Name'],
      lineItems: order['Raw Data'].line_items,
    });

    return {
      success: true,
      data: newInvoice,
    }
  }
}

export default new CreateStripeInvoice();
