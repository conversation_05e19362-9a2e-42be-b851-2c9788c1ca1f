import { <PERSON>AP<PERSON>Hand<PERSON> } from 'type';
import { combineMiddlewares, checkAuthen } from '../api-middlewares'
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

class StripePublicKey implements TypeAPIHandler {
  url = '/api/payment/public-key';
  method = 'GET';

  preHandler = combineMiddlewares([
    checkAuthen,
  ]);

  handler = async (request, reply) => {
    return {
      success: true,
      data: process.env.STRIPE_PUBLISHABLE_KEY,
    }
  }
}

export default new StripePublicKey();
