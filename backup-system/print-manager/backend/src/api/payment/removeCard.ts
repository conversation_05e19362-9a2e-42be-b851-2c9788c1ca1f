import { TRequestUser, TypeAPIHandler } from 'type';
import { combineMiddlewares, checkAuthen } from '../api-middlewares'
import { DB } from 'db';
import { ERROR } from 'const';
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

class RemoveCard implements TypeAPIHandler {
  url = '/api/payment/remove-card';
  method = 'POST';

  preHandler = combineMiddlewares([
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const fullUser = await DB.User.findByPk(request.user.id);
    if (!fullUser.resellerStripeId) {
      throw new Error(ERROR.INVALID_PAYMENT_METHOD);
    }

    const paymentMethods = await stripe.paymentMethods.list({
      customer: fullUser.resellerStripeId,
      type: 'card',
    });

    if (!paymentMethods?.data?.length) throw new Error(ERROR.INVALID_PAYMENT_METHOD);

    await Promise.all(
      paymentMethods.data.map(method =>
        stripe.paymentMethods.detach(method.id)
      )
    )

    return {
      success: true,
    }
  }
}

export default new RemoveCard();
