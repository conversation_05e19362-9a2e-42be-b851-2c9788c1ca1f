import { TRequestUser, TypeAPIHandler } from 'type';
import { combineMiddlewares, checkAuthen } from '../api-middlewares'
import Stripe from 'stripe';
import Joi = require('joi');
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, { apiVersion: null });

class GetInvoice implements TypeAPIHandler {
  url = '/api/payment/get-invoice';
  method = 'GET';
  apiSchema = {
    query: Joi.object({
      orderNumber: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { orderNumber } = request.query;

    const invoce = await stripe.invoices.search({
      query: `metadata[\'orderNumber\']:\'${orderNumber}\'`,
    })

    return {
      success: true,
      data: invoce,
    }
  }
}

export default new GetInvoice();
