import { TRequestUser, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'type';
import { combineMiddlewares, checkAuthen } from '../api-middlewares'
import { DB } from 'db';
import Joi = require('joi');
import { ERROR } from 'const';
import Stripe from 'stripe';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, FileHelper } from 'helpers';
import { InvoiceModel } from 'db/Schema.Invoice';
import axios from 'axios';
import { listPipeline, updateOrderStatus, updatePipelineShareData } from 'api/bg/services';
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, { apiVersion: null });
const moment = require('moment');

class ChargeFromWallet implements TypeAPIHandler {
  url = '/api/payment/charge-from-wallet-by-draft-invoice';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      draftInvoiceIds: Joi.array().items(Joi.string()),
    }),
  }

  preHandler = combineMiddlewares([
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { draftInvoiceIds } = request.body;

    const fullUser = await DB.User.findByPk(request.user.id);

    if (!fullUser.otherData?.skipPayment) {
      if (!fullUser.resellerStripeId) {
        return {
          success: true,
          data: {},
        }
      }
      const balances: any = await stripe.customers.retrieve(fullUser.resellerStripeId, {
        expand: ['cash_balance'],
      });
      if (balances.balance >= 0) throw new Error(ERROR.NOT_ENOUGH_BALANCE)

      let total = 0;
      for (let i = 0; i < draftInvoiceIds.length; i++) {
        const draftInvoice = await DB.DraftInvoice.findByPk(draftInvoiceIds[i]);
        if (!draftInvoice?.id) throw new Error("Can not find Invoice");
        total += (draftInvoice.data?.invoice?.total || 0);
      }
      if (total > Math.abs(balances.balance)) {
        throw new Error(ERROR.NOT_ENOUGH_BALANCE + `:${total / 100}`);
      }
      if (total === 0) {
        return {
          success: true,
          data: {},
        }
      }
    }

    const readyInvoices = [];
    // update stripe invoice - add shipping and tax
    for (let i = 0; i < draftInvoiceIds.length; i++) {
      const draftInvoice = await DB.DraftInvoice.findByPk(draftInvoiceIds[i]);
      const client = await DB.User.findByPk(draftInvoice.resellerId);
      const clientName = client.accountName || [client.firstName, client.lastName].filter(Boolean).join(' ');
      const apiCall: any = await axios.request({
        url: `http://localhost:3000/api/bg/shopify-webhook?clientId=${draftInvoice.resellerId}&clientName=${clientName}&env=${process.env.DEV ? 'dev' : 'prod'}`,
        method: 'post',
        headers: {
          'Content-Type': 'application/json',
          'X-Auth-Token': process.env.MICRO_API_TOKEN,
        },
        data: JSON.stringify(draftInvoice.data?.order?.["Raw Data"])
      })

      const nocoOrder = apiCall.data.data;
      if (!nocoOrder) throw new Error("Can not find order");
      let dbInvoice: InvoiceModel
      let attemptCount = 0;
      await new Promise(resolve => setTimeout(resolve, 2000));
      while (!dbInvoice?.data?.stripeInvoiceID) {
        dbInvoice = await DB.Invoice.findOne({
          where: {
            orderId: nocoOrder?.["Order ID"],
          }
        });
        await new Promise(resolve => setTimeout(resolve, 500));
        attemptCount++;
        if (attemptCount > 30) throw new Error("Can not find stripe order");
      }
      console.log("dbInvoice", dbInvoice)

      if (!dbInvoice.data?.stripeInvoiceID) throw new Error("Can not find stripe order");
      // finalize & update db invoice
      const stripeInvoice = await stripe.invoices.retrieve(dbInvoice.data?.stripeInvoiceID);
      if (!stripeInvoice) throw new Error("Can not find stripe order");

      let stripePdfUrl;
      let readyInvoice;
      if (!fullUser.otherData?.skipPayment) {
        readyInvoice = await stripe.invoices.finalizeInvoice(stripeInvoice.id);
        if (readyInvoice?.invoice_pdf) {
          try {
            const tempFilePath = `/tmp/invoice-${stripeInvoice.id}.pdf`;
            await FileHelper.downloadFile(readyInvoice.invoice_pdf, tempFilePath);
            stripePdfUrl = await AWSHelper.upload({
              key: `bg/invoices/${stripeInvoice.id}.pdf`,
              filePath: tempFilePath,
            });
          } catch (error) {
            console.log("error_readyInvoice.invoice_pdf", error)
          }
        }

        // update DB shipping fee & tax
        const shippingLine = readyInvoice.lines?.data?.find(i => i.description?.includes("Shipping fee"))
        if (shippingLine) {
          dbInvoice.data = {
            ...(dbInvoice.data || {}),
            shippingFee: shippingLine.amount / 100,
          }
        }
        const taxLine = readyInvoice.lines?.data?.find(i => i.description?.includes("VAT "))
        if (taxLine) {
          dbInvoice.taxes = taxLine.amount / 100;
        }
        dbInvoice.total = readyInvoice.total / 100;
        console.log("[chargeFromWallet.byDraftInvoice] stripeInvoice.total", readyInvoice.total);
        // end update DB shipping fee & tax
      }

      dbInvoice.data = {
        ...(dbInvoice.data || {}),
        invoicePdf: `${process.env.BACKEND_CMS_URL}/api/order/gen-invoice/${dbInvoice.orderId}`,
        stripeInvoiceUrl: stripePdfUrl,
        amountPaid: readyInvoice?.total,
      }
      dbInvoice.paidAt = moment().toISOString();
      await dbInvoice.save();
      await draftInvoice.destroy();
      await updateOrderStatus({
        id: nocoOrder.Id,
        StageStatus: "Queued For Production"
      })
      const pipeline = await (async () => {
        const res = await listPipeline({
          limit: 1,
          offset: 0,
          orderId: nocoOrder["Order ID"]
        })
        return res.rows?.[0];
      })();
      if (!pipeline) throw new Error("Can not find pipeline");

      await updatePipelineShareData({
        data: {
          isPaid: true,
          isAdminApproved: false,
        },
        pipelineId: pipeline.Id
      })
      readyInvoices.push({
        ...readyInvoice,
        isSampleRequest: dbInvoice.data?.isSampleRequest,
        isWholesale: draftInvoice.type === "Wholesale",
      });
    }

    try {
      // update user totals
      let salesTotal = 0;
      let shippingTotal = 0;
      let vatTotal = 0;
      let sampleSalesTotal = 0;
      let wholesaleTotal = 0;

      for (const readyInvoice of readyInvoices) {
        // Calculate totals from invoice line items
        for (const item of readyInvoice.lines.data) {
          if (item.description?.includes("Shipping fee")) {
            shippingTotal += item.amount;
          } else if (item.description?.includes("VAT")) {
            vatTotal += item.amount;
          } else {
            if (readyInvoice.isWholesale) {
              wholesaleTotal += item.amount;
            } else if (readyInvoice.isSampleRequest) {
              sampleSalesTotal += item.amount;
            } else {
              salesTotal += item.amount;
            }
          }
        }
      }

      fullUser.salesTotal = (fullUser.salesTotal || 0) + salesTotal;
      fullUser.shippingTotal = (fullUser.shippingTotal || 0) + shippingTotal;
      fullUser.vatTotal = (fullUser.vatTotal || 0) + vatTotal;
      fullUser.sampleSalesTotal = (fullUser.sampleSalesTotal || 0) + sampleSalesTotal;
      fullUser.wholesaleTotal = (fullUser.wholesaleTotal || 0) + wholesaleTotal;
      await fullUser.save();
      // end update user totals
    } catch (error) {
      console.error('Error updating user totals:', error);
    }

    return {
      success: true,
      data: readyInvoices,
    }
  }
}

export default new ChargeFromWallet();
