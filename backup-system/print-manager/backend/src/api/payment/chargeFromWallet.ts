import { TRequestUser, TypeAP<PERSON><PERSON><PERSON><PERSON> } from 'type';
import { combineMiddlewares, checkAuthen } from '../api-middlewares'
import { DB } from 'db';
import Joi = require('joi');
import { ERROR } from 'const';
import Stripe from 'stripe';
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, { apiVersion: null });
const moment = require('moment');

const shippingPrice = {
  RM24: 5.2,
  RM48: 4.18,
  DPD: 10.99,
};

const EUROPEAN_COUNTRIES = ["Russia","Germany","France","Italy","Spain","Poland","Ukraine","Romania","Netherlands","Belgium","Sweden","Czech Republic (Czechia)","Greece","Portugal","Hungary","Belarus","Austria","Switzerland","Serbia","Bulgaria","Denmark","Slovakia","Finland","Norway","Ireland","Croatia","Moldova","Bosnia and Herzegovina","Albania","Lithuania","Slovenia","North Macedonia","Latvia","Estonia","Luxembourg","Montenegro","Malta","Iceland","Andorra","Liechtenstein","Monaco","San Marino","Holy See"];

const shippingPriceEurope = {
  RM24: 8.99,
  RM48: 15,
  DPD: 14.99,
};
const shippingPriceTheRestOfTheWorld = {
  RM24: 8.99,
  RM48: 22,
  DPD: 14.99,
};

class ChargeFromWallet implements TypeAPIHandler {
  url = '/api/payment/charge-from-wallet';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      orders: Joi.array().items(Joi.object({
        orderId: Joi.string(),
        slug: Joi.string(),
        line_items: Joi.array().items(Joi.any()),
        shipping_address: Joi.any(),
      })),
      shippingService: Joi.string(),
    }),
  }

  preHandler = combineMiddlewares([
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { orders, shippingService } = request.body;

    const fullUser = await DB.User.findByPk(request.user.id);

    // token for sample request
    let sampleToken = typeof fullUser?.otherData?.sampleToken === 'number' ? fullUser?.otherData?.sampleToken : 10;
    let preCalculateTotalSampleToken = typeof fullUser?.otherData?.sampleToken === 'number' ? fullUser?.otherData?.sampleToken : 10;

    if (!fullUser.resellerStripeId) {
      return {
        success: true,
        data: {},
      }
    }

    // { [orederId]: { withTax: boolean, shippingFee: number } }
    const shippingFeeOrder = {};

    const balances: any = await stripe.customers.retrieve(fullUser.resellerStripeId, {
      expand: ['cash_balance'],
    });
    if (balances.balance >= 0) throw new Error(ERROR.NOT_ENOUGH_BALANCE)

    let preCalculateTotal = 0;
    for (let i = 0; i < orders.length; i++) {
      const { orderId } = orders[i];
      const invoice = await DB.Invoice.findOne({
        where: {
          orderId,
        }
      });
      const isSampleRequest = !!invoice.data?.isSampleRequest;
      const thisShippingFee = (() => {
        const address = orders[i].shipping_address;
        if (address?.country === 'United Kingdom') {
          return shippingPrice[shippingService] || 0;
        }
        // TODO: handle international shipping fee
        if (EUROPEAN_COUNTRIES.includes(address?.country)) {
          return shippingPriceEurope[shippingService] || 0;
        }
        return shippingPriceTheRestOfTheWorld[shippingService] || 0;
      })();
      const withTax = invoice?.taxes > 0; 
      shippingFeeOrder[orderId] = {
        withTax,
        shippingFee: withTax ? thisShippingFee * 1.2 : thisShippingFee,
      }
      const shouldDiscount75Percent = !isSampleRequest ? false : (preCalculateTotalSampleToken > 0 ? true : false);
      if (shouldDiscount75Percent) preCalculateTotalSampleToken--;
      const discountRate = shouldDiscount75Percent ? 0.25 : 1;
      preCalculateTotal +=
        discountRate * (invoice?.total || 0)
        + shippingFeeOrder[orderId].shippingFee;
    }

    if (Number(preCalculateTotal) * 100 > Math.abs(balances.balance)) {
      throw new Error(ERROR.NOT_ENOUGH_BALANCE);
    }
    if (preCalculateTotal === 0) {
      return {
        success: true,
        data: {},
      }
    }

    let productNames = [];
    let orderNumbers = [];
    for (let i = 0; i < orders.length; i++) {
      const { orderId, orderNumber, line_items } = orders[i];
      if (orderNumber) orderNumbers.push(orderNumber);
      const orderPrefix = `#${orderNumber} - ${orderId} - `;
      const invoice = await DB.Invoice.findOne({
        where: {
          orderId,
        }
      });
      const isSampleRequest = !!invoice.data?.isSampleRequest;
      const shouldDiscount75Percent = !isSampleRequest ? false : (sampleToken > 0 ? true : false);
      if (shouldDiscount75Percent) sampleToken--;
      const discountRate = shouldDiscount75Percent ? 0.25 : 1;

      if (line_items?.length) {
        for (let i = 0; i < line_items.length; i++) {
          const lineItem = line_items[i];
          if (line_items[i]?.name) {
            const printJobId = lineItem.properties?.find(i => i.name === 'Print Job')?.value;
            let linePrice = invoice?.prices?.[printJobId] || lineItem.price || 0;
            if (invoice.taxes) linePrice = linePrice * 1.2;
            const newPrice = await stripe.prices.create({
              currency: 'gbp',
              product_data: {
                name: (shouldDiscount75Percent ? '(SAMPLE DISCOUNTED) ' : '') + orderPrefix + lineItem.name + (invoice.taxes ? ' (VAT included)' : ''),
              },
              unit_amount: Math.round(Number(linePrice) * discountRate * 100),
            })
            await stripe.invoiceItems.create({
              customer: fullUser.resellerStripeId,
              price: newPrice.id,
              quantity: lineItem.quantity,
            });
            productNames.push(line_items[i].name);
          }
        }
      }
      const _shippingPrice = await stripe.prices.create({
        currency: 'gbp',
        product_data: {
          name: (shouldDiscount75Percent ? '(SAMPLE DISCOUNTED) ' : '') +  `${orderPrefix}Shipping fee - ${shippingService}${invoice.taxes ? ' (VAT included)' : ''}`,
        },
        unit_amount: Math.round(Number(shippingFeeOrder[orderId].shippingFee) * 100),
      });
      await stripe.invoiceItems.create({
        customer: fullUser.resellerStripeId,
        price: _shippingPrice.id,
        quantity: 1,
      });
    }

    const invoice = await stripe.invoices.create({
      customer: fullUser.resellerStripeId,
      metadata: {
        orderNumber: orderNumbers.join(','),
        productName: productNames.join('\n'),
        paymentMethod: "Wallet Credit",
        orders: orders.map(v => v.orderId).join(', '),
      }
    });
    const readyInvoice = await stripe.invoices.finalizeInvoice(invoice.id);

    for (let i = 0; i < orders.length; i++) {
      const { orderId } = orders[i];
      const invoice = await DB.Invoice.findOne({
        where: {
          orderId,
        }
      });
      invoice.data = {
        ...(invoice.data || {}),
        invoicePdf: readyInvoice.invoice_pdf,
        stripeInvoiceId: invoice.id,
        shippingService,
        shippingFee: shippingFeeOrder[orderId].shippingFee,
        country: orders[i].shipping_address?.country,
      }
      invoice.paidAt = moment().toISOString();
      await invoice.save();
    }

    // const paymentIntent = await stripe.paymentIntents.applyCustomerBalance(
    //   fullUser.resellerStripeId,
    //   amountWithTax,
    //   fullUser.currency.toLowerCase(),
    // );
    // const transactions = await stripe.customers.createBalanceTransaction(fullUser.resellerStripeId, {
    //   amount: -Math.round(total * 100),
    //   currency: "gbp", // (currency || fullUser.currency).toLowerCase(),
    //   metadata: {
    //     orderNumber: orderNumbers.join(','),
    //     productName: productNames.join(','),
    //     paymentMethod: "Wallet Credit",
    //     // invoicePDF: readyInvoice.invoice_pdf,
    //     // hostedInvoiceUrl: readyInvoice.hosted_invoice_url,
    //   }
    // });

    // update sample token to user
    const otherData = Object.assign({}, fullUser.otherData, { sampleToken: sampleToken });
    fullUser.otherData = otherData;
    await fullUser.save();

    return {
      success: true,
      data: readyInvoice,
    }
  }
}

export default new ChargeFromWallet();
