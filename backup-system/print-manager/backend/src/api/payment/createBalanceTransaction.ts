import { TRequestUser, <PERSON>AP<PERSON><PERSON>and<PERSON> } from 'type';
import { combineMiddlewares, checkAuthen, validateRequest, checkAdmin } from '../api-middlewares'
import { DB } from 'db';
import Joi = require('joi');
import { ERROR } from 'const';
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

class GetBalance implements TypeAPIHandler {
  url = '/api/payment/create-balance-transaction';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      amount: Joi.number(),
      currency: Joi.string().allow(''),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
    checkAdmin,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const fullUser = await DB.User.findByPk(request.user.id);
    const { amount, currency } = request.body;

    if (!fullUser.resellerStripeId) {
      return {
        success: true,
        data: {},
      }
    }
    const paymentMethods = await stripe.paymentMethods.list({
      customer: fullUser.resellerStripeId,
      type: 'card',
    });
    
    if (!paymentMethods?.data?.length) throw new Error(ERROR.INVALID_PAYMENT_METHOD);

    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount * 100),
      currency: currency || 'gbp',
      customer: fullUser.resellerStripeId,
      payment_method: paymentMethods?.data?.[0]?.id,
      off_session: true,
      confirm: true,
    });

    console.log('paymentIntent', paymentIntent);

    const transactions = await stripe.customers.createBalanceTransaction(fullUser.resellerStripeId, {
      amount: paymentIntent?.amount_received,
      currency: currency || 'gbp',
    });

    return {
      success: true,
      data: paymentIntent,
    }
  }
}

export default new GetBalance();
