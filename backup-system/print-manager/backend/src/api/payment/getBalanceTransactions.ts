import { TRequestUser, <PERSON>AP<PERSON><PERSON><PERSON><PERSON> } from 'type';
import { combineMiddlewares, checkAuthen } from '../api-middlewares'
import { DB } from 'db';
import Stripe from 'stripe';
import Joi = require('joi');
import { Var<PERSON><PERSON>per } from 'helpers';
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, { apiVersion: null });

class GetBalanceTransactions implements TypeAPIHandler {
  url = '/api/payment/get-balance-transactions';
  method = 'GET';
  apiSchema = {
    query: Joi.object({
      starting_after: Joi.string(),
    }),
  }

  preHandler = combineMiddlewares([
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { starting_after } = request.query;
    const fullUser = await DB.User.findByPk(request.user.id);
    if (!fullUser.resellerStripeId) {
      return {
        success: true,
        data: {},
      }
    }
    const transactions = await stripe.customers.listBalanceTransactions(fullUser.resellerStripeId, VarHelper.removeUndefinedField({ limit: 6, starting_after }));

    const trans = await Promise.all(transactions.data.map(async item => {
      if (!item.invoice) {
        try {
          const balanceTransaction = await stripe.balanceTransactions.retrieve(
            item.id
          );
          return {
            balanceTransaction,
            ...item,
          };
        } catch(err) {
          return item;
        }
        
      }
      const invoiceData = await stripe.invoices.retrieve(item.invoice.toString());
      return {
        ...item,
        invoiceData,
      }
    }))
    return {
      success: true,
      data: trans,
      hasNext: transactions?.has_more,
    }
  }
}

export default new GetBalanceTransactions();
