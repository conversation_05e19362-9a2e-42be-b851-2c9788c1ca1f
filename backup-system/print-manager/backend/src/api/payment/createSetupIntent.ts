import { TRequestUser, TypeAPIHandler } from 'type';
import { combineMiddlewares, checkAuthen } from '../api-middlewares'
import { DB } from 'db';
import Joi = require('joi');

const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

class StripeCreateSetupIntent implements TypeAPIHandler {
  url = '/api/payment/create-setup-intent';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      createdIfNotExisted: Joi.boolean(),
    }),
  }

  preHandler = combineMiddlewares([
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { createdIfNotExisted } = request.body;
    const fullUser = await DB.User.findByPk(request.user.id);
    if (!fullUser.resellerStripeId) {
      if (!createdIfNotExisted) {
        return {
          success: true,
          data: null,
        }
      }
      const resellerName = fullUser.accountName || [fullUser.firstName, fullUser.lastName].filter(Boolean).join(' ');
      const customer = await stripe.customers.create({
        name: resellerName,
        email: fullUser.email,
      });
      fullUser.resellerStripeId = customer.id;
      await fullUser.save();
    }
    const intent = await stripe.setupIntents.create({
      customer: fullUser.resellerStripeId
    });

    return {
      success: true,
      data: intent,
    }
  }
}

export default new StripeCreateSetupIntent();
