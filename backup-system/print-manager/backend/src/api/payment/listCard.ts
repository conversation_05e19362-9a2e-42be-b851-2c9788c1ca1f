import { TRequestUser, TypeAPIHandler } from 'type';
import { combineMiddlewares, checkAuthen } from '../api-middlewares'
import { DB } from 'db';
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

class StripeSavedCards implements TypeAPIHandler {
  url = '/api/payment/cards';
  method = 'GET';

  preHandler = combineMiddlewares([
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const fullUser = await DB.User.findByPk(request.user.id);
    if (!fullUser.resellerStripeId) {
      return {
        success: true,
        data: [],
      }
    }
    const paymentMethods = await stripe.paymentMethods.list({
      customer: fullUser.resellerStripeId,
      type: 'card',
    });
    return {
      success: true,
      data: paymentMethods,
    }
  }
}

export default new StripeSavedCards();
