import { TRequestUser, TypeAP<PERSON><PERSON><PERSON><PERSON> } from 'type';
import { combineMiddlewares, checkAdmin, checkAuthen } from '../api-middlewares'
import { DB } from 'db';
import Joi = require('joi');
import { ERROR } from 'const';

class ApproveRefund implements TypeAPIHandler {
  url = '/api/payment/approve-refund';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      id: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    checkAuthen,
    checkAdmin,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { id } = request.body;

    const requestItem = await DB.GeneralData.findOne({
      where: { id },
    });
  
    if (!requestItem) throw new Error(ERROR.NOT_EXISTED);
  
    const fullUser = await DB.User.findByPk(requestItem.userId);
    if (!fullUser.resellerStripeId) {
      return {
        success: true,
        data: {},
      }
    }

    requestItem.field2 = "Approved";
    await requestItem.save();
  
    return {
      success: true,
    }
  }
}

export default new ApproveRefund();
