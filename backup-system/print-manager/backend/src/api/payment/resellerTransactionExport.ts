import { <PERSON>AP<PERSON><PERSON><PERSON><PERSON> } from 'type';
import { combineMiddlewares, checkAuthen } from '../api-middlewares'
import { DB } from 'db';
import { BOOLEAN, Op } from 'sequelize';
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const moment = require('moment');

class StripePublicKey implements TypeAPIHandler {
  url = '/api/payment/reseller-transaction-export';
  method = 'POST';

  preHandler = combineMiddlewares([
    checkAuthen,
  ]);

  handler = async (request, reply) => {
    const { exportExcel } = request.body;
    const resellers = await DB.User.findAll({
      where: {
        resellerStripeId: {
          [Op.ne]: '',
        }
      }
    });
    const list = resellers.map(u => ({
      email: u.email,
      name: [u.firstName, u.lastName].filter(Boolean).join(' '),
      walletBalance: 0,
      stripeId: u.resellerStripeId,
      transactions: [],
    }));

    for (const reseller of list) {
      const balances = await stripe.customers.retrieve(reseller.stripeId, {
        expand: ['cash_balance'],
      });
      reseller.walletBalance = balances.balance;
      const transactions = await  stripe.customers.listBalanceTransactions(reseller.stripeId, { limit: 100 });
      reseller.transactions = transactions.data.map(t => {
        const isRefund = t.metadata?.type === 'refund' && t.metadata?.refundMessage;
        const isDeposit = t.metadata?.type === 'Deposit';
        const transactionType = isRefund ? 'Refund' : isDeposit ? 'Deposit' : 'Pay';
        const orderNumber = (() => {
          if (isDeposit) return '';
          if (isRefund) return t.metadata.refundMessage.replace('Order ', '').replace('refund', '');
          return t?.invoiceData?.metadata?.orderNumber || '';
        })();
        return {
          amount: -t.amount / 100,
          created: t.created,
          description: t.description,
          type: transactionType,
          infoUrl: t?.invoiceData?.invoice_pdf || t.metadata.receiptUrl,
          orderNumber,
        };
      });
    }

    if (!exportExcel)
    return {
      success: true,
      data: list,
    }

    // convert transactions array into multi lines text
    const transactionsToText = (transactions) => {
      return transactions.map(t => {
        return [
          `[ ${t.type} ]`,
          t.orderNumber.split(',').map(i => `#${i.trim()}`).join(','),
          t.description,
          `[ £${t.amount / 100} ]`,
          moment(t.created * 1000).format('DD/MM/YYYY'),
          t.infoUrl || 'Unknown URL',
        ].join(' ');
      }).join('\n');
    }

    return {
      success: true,
      data: list.map(v => ({
        Email: v.email,
        Name: v.name,
        'Wallet Balance': 0,
        'Stripe ID': v.stripeId,
        Transactions: transactionsToText(v.transactions)}
      )),
    } 
  }
}

export default new StripePublicKey();
