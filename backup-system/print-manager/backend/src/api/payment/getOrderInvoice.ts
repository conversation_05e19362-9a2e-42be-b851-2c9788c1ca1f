import { TRequestUser, TypeAPIHandler } from 'type';
import { combineMiddlewares, checkAuthen, validateRequest } from '../api-middlewares'
import { DB } from 'db';
import Joi = require('joi');
import Stripe from 'stripe';
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, { apiVersion: null });

class GetOrderInvoice implements TypeAPIHandler {
  url = '/api/payment/get-order-invoice/:orderId';
  method = 'GET';
  apiSchema = {
    params: Joi.object({
      orderId: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { orderId } = request.params;

    const invoice = await DB.Invoice.findOne({
      where: {
        orderId,
      }
    })
    if (!invoice?.id) throw new Error("Invoice not found");

    const resellerId = invoice.resellerId;
    const reseller = !resellerId ? null : await DB.User.findByPk(resellerId);
    if (reseller) {
      // @ts-ignore
      invoice.setDataValue('resellerStripeId', reseller.resellerStripeId);
      // @ts-ignore
      invoice.setDataValue('resellerName', [reseller.firstName, reseller.lastName].filter(Boolean).join(' '));
    }

    let stripeInvoice
    if (invoice.data?.stripeInvoiceID) {
      try {
        stripeInvoice = await stripe.invoices.retrieve(invoice.data?.stripeInvoiceID);
      } catch (error) {
      }
    }
    return {
      success: true,
      data: {
        ...invoice.toJSON(),
        stripeInvoice,
      },
    }
  }
}

export default new GetOrderInvoice();
