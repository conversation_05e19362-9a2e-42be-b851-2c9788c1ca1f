import VarHelper from "helpers/VarHelper"

const BGEmailFooter = `
  <table align="center" width="100%" data-id="react-email-section"
    style="background-color:#223363;padding:12px;margin-top:12px" border="0" cellPadding="0" cellSpacing="0"
    role="presentation">
    <tbody>
      <tr>
        <td><img data-id="react-email-img" alt="Bottle Goose&#x27;s Logo"
            src="https://www.bottledgoose.co.uk/cdn/shop/files/footericon_52036403-1cbf-4d89-9999-5260c97d52a4.png?v=1673451873&amp;width=200"
            width="100" height="93" style="display:block;outline:none;border:none;text-decoration:none" />
          <p data-id="react-email-text"
            style="font-size:12px;line-height:14px;margin:16px 0;color:#f4f4f4;font-family:-apple-system, BlinkMacSystemFont, &#x27;Segoe UI&#x27;, &#x27;Inter&#x27;, &#x27;Oxygen&#x27;, &#x27;Ubuntu&#x27;, &#x27;Cantarell&#x27;, &#x27;Fira Sans&#x27;, &#x27;Droid Sans&#x27;, &#x27;Helvetica Neue&#x27;, sans-serif">
            Phone: 020 8941 3481</p>
          <p data-id="react-email-text"
            style="font-size:12px;line-height:14px;margin:16px 0;color:#f4f4f4;font-family:-apple-system, BlinkMacSystemFont, &#x27;Segoe UI&#x27;, &#x27;Inter&#x27;, &#x27;Oxygen&#x27;, &#x27;Ubuntu&#x27;, &#x27;Cantarell&#x27;, &#x27;Fira Sans&#x27;, &#x27;Droid Sans&#x27;, &#x27;Helvetica Neue&#x27;, sans-serif">
            Email: <EMAIL></p>
        </td>
      </tr>
    </tbody>
  </table>
`

export const genEmailHtml = ({ title, message, link, linkName = undefined }) => `
<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en">

<head data-id="__react-email-head">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
</head>

<body data-id="__react-email-body" style="background-color:#ffffff">
    <table align="center" width="100%" data-id="__react-email-container" role="presentation" cellSpacing="0"
        cellPadding="0" border="0" style="max-width:37.5em;padding-left:12px;padding-right:12px;margin:0 auto">
        <tbody>
            <tr style="width:100%">
                <td>
                    <h1 data-id="react-email-heading"
                        style="color:#333;font-family:-apple-system, BlinkMacSystemFont, &#x27;Segoe UI&#x27;, &#x27;Inter&#x27;, &#x27;Oxygen&#x27;, &#x27;Ubuntu&#x27;, &#x27;Cantarell&#x27;, &#x27;Fira Sans&#x27;, &#x27;Droid Sans&#x27;, &#x27;Helvetica Neue&#x27;, sans-serif;font-size:24px;font-weight:bold;margin:40px 0;padding:0">
                        ${title}</h1>
                    <p data-id="react-email-text"
                        style="font-size:14px;line-height:24px;margin:24px 0;color:#333;font-family:-apple-system, BlinkMacSystemFont, &#x27;Segoe UI&#x27;, &#x27;Inter&#x27;, &#x27;Oxygen&#x27;, &#x27;Ubuntu&#x27;, &#x27;Cantarell&#x27;, &#x27;Fira Sans&#x27;, &#x27;Droid Sans&#x27;, &#x27;Helvetica Neue&#x27;, sans-serif;margin-bottom:12px">
                        ${message}
                    </p>
                    <a href="${link}"
                        data-id="react-email-link" target="_blank"
                        style="color:#2754C5;text-decoration:underline;font-family:-apple-system, BlinkMacSystemFont, &#x27;Segoe UI&#x27;, &#x27;Inter&#x27;, &#x27;Oxygen&#x27;, &#x27;Ubuntu&#x27;, &#x27;Cantarell&#x27;, &#x27;Fira Sans&#x27;, &#x27;Droid Sans&#x27;, &#x27;Helvetica Neue&#x27;, sans-serif;font-size:14px;display:block;margin-bottom:36px">
                        ${linkName || link}
                    </a>
                    ${BGEmailFooter}
                </td>
            </tr>
        </tbody>
    </table>
</body>

</html>

`

export const getDispatchNotiEmailHtml = ({ customerName, message, link, linkName = undefined, items = [], resellerName }) => {
  const genInvoiceItem = ({ name, quantity }) => `
    <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation">
      <tbody>
        <tr>
          <td>
            <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation">
              <tbody style="width:100%">
                <tr style="width:100%">
                  <td data-id="__react-email-column" style="padding-left:22px">
                    <p style="font-size:12px;font-family:-apple-system, BlinkMacSystemFont, &#x27;Segoe UI&#x27;, &#x27;Inter&#x27;, &#x27;Oxygen&#x27;, &#x27;Ubuntu&#x27;, &#x27;Cantarell&#x27;, &#x27;Fira Sans&#x27;, &#x27;Droid Sans&#x27;, &#x27;Helvetica Neue&#x27;, sans-serif;line-height:1.4;margin:0;font-weight:600;padding:0">
                      ${name}
                    </p>
                  </td>
                  <td align="right" data-id="__react-email-column"
                    style="display:table-cell;padding:0px 20px 0px 0px;width:100px;vertical-align:top">
                    <p style="font-size:12px;font-family:-apple-system, BlinkMacSystemFont, &#x27;Segoe UI&#x27;, &#x27;Inter&#x27;, &#x27;Oxygen&#x27;, &#x27;Ubuntu&#x27;, &#x27;Cantarell&#x27;, &#x27;Fira Sans&#x27;, &#x27;Droid Sans&#x27;, &#x27;Helvetica Neue&#x27;, sans-serif;line-height:24px;margin:0;font-weight:600">${quantity}</p>
                  </td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
      </tbody>
    </table>
  `;

  const invoice = items.map(i => genInvoiceItem(i)).join("");
  
  return `
    <!DOCTYPE html
      PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
    <html lang="en">

    <head data-id="__react-email-head">
      <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    </head>

    <body data-id="__react-email-body" style="background-color:#ffffff">
      <table align="center" width="100%" data-id="__react-email-container" role="presentation" cellSpacing="0"
        cellPadding="0" border="0" style="max-width:37.5em;padding-left:12px;padding-right:12px;margin:0 auto">
        <tbody>
          <tr style="width:100%">
            <td>
              <p data-id="react-email-text"
                style="font-size:14px;line-height:24px;margin:24px 0;color:#333;font-family:-apple-system, BlinkMacSystemFont, &#x27;Segoe UI&#x27;, &#x27;Inter&#x27;, &#x27;Oxygen&#x27;, &#x27;Ubuntu&#x27;, &#x27;Cantarell&#x27;, &#x27;Fira Sans&#x27;, &#x27;Droid Sans&#x27;, &#x27;Helvetica Neue&#x27;, sans-serif;margin-bottom:12px">
                Hello ${customerName},
              </p>
              <p data-id="react-email-text"
                style="font-size:14px;line-height:24px;margin:24px 0;color:#333;font-family:-apple-system, BlinkMacSystemFont, &#x27;Segoe UI&#x27;, &#x27;Inter&#x27;, &#x27;Oxygen&#x27;, &#x27;Ubuntu&#x27;, &#x27;Cantarell&#x27;, &#x27;Fira Sans&#x27;, &#x27;Droid Sans&#x27;, &#x27;Helvetica Neue&#x27;, sans-serif;margin-bottom:12px">
                ${message}
              </p>
              <a href="${link}"
                data-id="react-email-link" target="_blank"
                style="color:#2754C5;text-decoration:underline;font-family:-apple-system, BlinkMacSystemFont, &#x27;Segoe UI&#x27;, &#x27;Inter&#x27;, &#x27;Oxygen&#x27;, &#x27;Ubuntu&#x27;, &#x27;Cantarell&#x27;, &#x27;Fira Sans&#x27;, &#x27;Droid Sans&#x27;, &#x27;Helvetica Neue&#x27;, sans-serif;font-size:14px;display:block;margin-bottom:36px">
                ${linkName || link}
              </a>
              <p data-id="react-email-text"
                style="font-size:14px;line-height:24px;margin:24px 0;color:#333;font-family:-apple-system, BlinkMacSystemFont, &#x27;Segoe UI&#x27;, &#x27;Inter&#x27;, &#x27;Oxygen&#x27;, &#x27;Ubuntu&#x27;, &#x27;Cantarell&#x27;, &#x27;Fira Sans&#x27;, &#x27;Droid Sans&#x27;, &#x27;Helvetica Neue&#x27;, sans-serif;margin-bottom:12px">
                Here are the details of your order:
              </p>
              <hr style="width:100%;border:none;border-top:1px solid #eaeaea" />
              ${invoice}
              <hr style="width:100%;border:none;border-top:1px solid #eaeaea;margin:30px 0 0 0" />

              <p data-id="react-email-text"
                style="font-size:14px;line-height:24px;margin:24px 0;color:#333;font-family:-apple-system, BlinkMacSystemFont, &#x27;Segoe UI&#x27;, &#x27;Inter&#x27;, &#x27;Oxygen&#x27;, &#x27;Ubuntu&#x27;, &#x27;Cantarell&#x27;, &#x27;Fira Sans&#x27;, &#x27;Droid Sans&#x27;, &#x27;Helvetica Neue&#x27;, sans-serif;margin-bottom:12px">
                Best Regards,
                <br />
                ${resellerName}
              </p>
            </td>
          </tr>
        </tbody>
      </table>
    </body>

    </html>

  `
}

export const getNewOrderNotiEmailHtml = ({ resellerName, customerName, link, linkName = undefined, items = [] }) => {
  const genInvoiceItem = ({ name, quantity }) => `
    <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation">
      <tbody>
        <tr>
          <td>
            <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation">
              <tbody style="width:100%">
                <tr style="width:100%">
                  <td data-id="__react-email-column" style="padding-left:22px">
                    <p style="font-size:12px;font-family:-apple-system, BlinkMacSystemFont, &#x27;Segoe UI&#x27;, &#x27;Inter&#x27;, &#x27;Oxygen&#x27;, &#x27;Ubuntu&#x27;, &#x27;Cantarell&#x27;, &#x27;Fira Sans&#x27;, &#x27;Droid Sans&#x27;, &#x27;Helvetica Neue&#x27;, sans-serif;line-height:1.4;margin:0;font-weight:600;padding:0">
                      ${name}
                    </p>
                  </td>
                  <td align="right" data-id="__react-email-column"
                    style="display:table-cell;padding:0px 20px 0px 0px;width:100px;vertical-align:top">
                    <p style="font-size:12px;font-family:-apple-system, BlinkMacSystemFont, &#x27;Segoe UI&#x27;, &#x27;Inter&#x27;, &#x27;Oxygen&#x27;, &#x27;Ubuntu&#x27;, &#x27;Cantarell&#x27;, &#x27;Fira Sans&#x27;, &#x27;Droid Sans&#x27;, &#x27;Helvetica Neue&#x27;, sans-serif;line-height:24px;margin:0;font-weight:600">${quantity}</p>
                  </td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
      </tbody>
    </table>
  `;

  const invoice = items.map(i => genInvoiceItem(i)).join("");
  
  return `
    <!DOCTYPE html
      PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
    <html lang="en">

    <head data-id="__react-email-head">
      <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    </head>

    <body data-id="__react-email-body" style="background-color:#ffffff">
      <table align="center" width="100%" data-id="__react-email-container" role="presentation" cellSpacing="0"
        cellPadding="0" border="0" style="max-width:37.5em;padding-left:12px;padding-right:12px;margin:0 auto">
        <tbody>
          <tr style="width:100%">
            <td>
              <p data-id="react-email-text"
                style="font-size:14px;line-height:24px;margin:24px 0;color:#333;font-family:-apple-system, BlinkMacSystemFont, &#x27;Segoe UI&#x27;, &#x27;Inter&#x27;, &#x27;Oxygen&#x27;, &#x27;Ubuntu&#x27;, &#x27;Cantarell&#x27;, &#x27;Fira Sans&#x27;, &#x27;Droid Sans&#x27;, &#x27;Helvetica Neue&#x27;, sans-serif;margin-bottom:12px">
                Hello ${resellerName},
              </p>
              <p data-id="react-email-text"
                style="font-size:14px;line-height:24px;margin:24px 0;color:#333;font-family:-apple-system, BlinkMacSystemFont, &#x27;Segoe UI&#x27;, &#x27;Inter&#x27;, &#x27;Oxygen&#x27;, &#x27;Ubuntu&#x27;, &#x27;Cantarell&#x27;, &#x27;Fira Sans&#x27;, &#x27;Droid Sans&#x27;, &#x27;Helvetica Neue&#x27;, sans-serif;margin-bottom:12px">
                Great news! You have received an order from ${customerName}
                <br/>
                Please click on the link below to accept this order.
              </p>
              <a href="${link}"
                data-id="react-email-link" target="_blank"
                style="color:#2754C5;text-decoration:underline;font-family:-apple-system, BlinkMacSystemFont, &#x27;Segoe UI&#x27;, &#x27;Inter&#x27;, &#x27;Oxygen&#x27;, &#x27;Ubuntu&#x27;, &#x27;Cantarell&#x27;, &#x27;Fira Sans&#x27;, &#x27;Droid Sans&#x27;, &#x27;Helvetica Neue&#x27;, sans-serif;font-size:14px;display:block;margin-bottom:36px">
                ${linkName || link}
              </a>
              <p data-id="react-email-text"
                style="font-size:14px;line-height:24px;margin:24px 0;color:#333;font-family:-apple-system, BlinkMacSystemFont, &#x27;Segoe UI&#x27;, &#x27;Inter&#x27;, &#x27;Oxygen&#x27;, &#x27;Ubuntu&#x27;, &#x27;Cantarell&#x27;, &#x27;Fira Sans&#x27;, &#x27;Droid Sans&#x27;, &#x27;Helvetica Neue&#x27;, sans-serif;margin-bottom:12px">
                Here are the details of your order:
              </p>
              <hr style="width:100%;border:none;border-top:1px solid #eaeaea" />
              ${invoice}
              <hr style="width:100%;border:none;border-top:1px solid #eaeaea;margin:30px 0 0 0" />

              <p data-id="react-email-text"
                style="font-size:14px;line-height:24px;margin:24px 0;color:#333;font-family:-apple-system, BlinkMacSystemFont, &#x27;Segoe UI&#x27;, &#x27;Inter&#x27;, &#x27;Oxygen&#x27;, &#x27;Ubuntu&#x27;, &#x27;Cantarell&#x27;, &#x27;Fira Sans&#x27;, &#x27;Droid Sans&#x27;, &#x27;Helvetica Neue&#x27;, sans-serif;margin-bottom:12px">
                Best Regards,
                <br />
                Bottled Goose
              </p>

              ${BGEmailFooter}
            </td>
          </tr>
        </tbody>
      </table>
    </body>

    </html>

  `
}

export const getOrderRejectedNotiEmailHtml = ({ resellerName, link }) => {
  return `
    <!DOCTYPE html
      PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
    <html lang="en">

    <head data-id="__react-email-head">
      <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    </head>

    <body data-id="__react-email-body" style="background-color:#ffffff">
      <table align="center" width="100%" data-id="__react-email-container" role="presentation" cellSpacing="0"
        cellPadding="0" border="0" style="max-width:37.5em;padding-left:12px;padding-right:12px;margin:0 auto">
        <tbody>
          <tr style="width:100%">
            <td>
              <p data-id="react-email-text"
                style="font-size:14px;line-height:24px;margin:24px 0;color:#333;font-family:-apple-system, BlinkMacSystemFont, &#x27;Segoe UI&#x27;, &#x27;Inter&#x27;, &#x27;Oxygen&#x27;, &#x27;Ubuntu&#x27;, &#x27;Cantarell&#x27;, &#x27;Fira Sans&#x27;, &#x27;Droid Sans&#x27;, &#x27;Helvetica Neue&#x27;, sans-serif;margin-bottom:12px">
                Hello ${resellerName},
              </p>
              <p data-id="react-email-text"
                style="font-size:14px;line-height:24px;margin:24px 0;color:#333;font-family:-apple-system, BlinkMacSystemFont, &#x27;Segoe UI&#x27;, &#x27;Inter&#x27;, &#x27;Oxygen&#x27;, &#x27;Ubuntu&#x27;, &#x27;Cantarell&#x27;, &#x27;Fira Sans&#x27;, &#x27;Droid Sans&#x27;, &#x27;Helvetica Neue&#x27;, sans-serif;margin-bottom:12px">
                Unfortunately, your order failed the bottled Goose review. Which means we are unable to process your order.
                <br />
                View your order <a href="${link}"
                  data-id="react-email-link" target="_blank"
                  style="color:#2754C5;text-decoration:underline;font-family:-apple-system, BlinkMacSystemFont, &#x27;Segoe UI&#x27;, &#x27;Inter&#x27;, &#x27;Oxygen&#x27;, &#x27;Ubuntu&#x27;, &#x27;Cantarell&#x27;, &#x27;Fira Sans&#x27;, &#x27;Droid Sans&#x27;, &#x27;Helvetica Neue&#x27;, sans-serif;font-size:14px;display:block;margin-bottom:36px">
                  here
                </a>
              </p>
              <p data-id="react-email-text"
                style="font-size:14px;line-height:24px;margin:24px 0;color:#333;font-family:-apple-system, BlinkMacSystemFont, &#x27;Segoe UI&#x27;, &#x27;Inter&#x27;, &#x27;Oxygen&#x27;, &#x27;Ubuntu&#x27;, &#x27;Cantarell&#x27;, &#x27;Fira Sans&#x27;, &#x27;Droid Sans&#x27;, &#x27;Helvetica Neue&#x27;, sans-serif;margin-bottom:12px">
                Your order has been cancelled, and payment has been refunded.
              </p>
              <p data-id="react-email-text"
                style="font-size:14px;line-height:24px;margin:24px 0;color:#333;font-family:-apple-system, BlinkMacSystemFont, &#x27;Segoe UI&#x27;, &#x27;Inter&#x27;, &#x27;Oxygen&#x27;, &#x27;Ubuntu&#x27;, &#x27;Cantarell&#x27;, &#x27;Fira Sans&#x27;, &#x27;Droid Sans&#x27;, &#x27;Helvetica Neue&#x27;, sans-serif;margin-bottom:12px">
                Best Regards,
                <br />
                Bottled Goose
              </p>

              ${BGEmailFooter}
            </td>
          </tr>
        </tbody>
      </table>
    </body>

    </html>

  `
}

export const getWithdrawFundNotiEmailHtml = ({ clientName }) => {
  return `
    <!DOCTYPE html
      PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
    <html lang="en">

    <head data-id="__react-email-head">
      <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    </head>

    <body data-id="__react-email-body" style="background-color:#ffffff">
      <table align="center" width="100%" data-id="__react-email-container" role="presentation" cellSpacing="0"
        cellPadding="0" border="0" style="max-width:37.5em;padding-left:12px;padding-right:12px;margin:0 auto">
        <tbody>
          <tr style="width:100%">
            <td>
              <p data-id="react-email-text"
                style="font-size:14px;line-height:24px;margin:24px 0;color:#333;font-family:-apple-system, BlinkMacSystemFont, &#x27;Segoe UI&#x27;, &#x27;Inter&#x27;, &#x27;Oxygen&#x27;, &#x27;Ubuntu&#x27;, &#x27;Cantarell&#x27;, &#x27;Fira Sans&#x27;, &#x27;Droid Sans&#x27;, &#x27;Helvetica Neue&#x27;, sans-serif;margin-bottom:12px">
                Hello ${clientName},
              </p>
              <p data-id="react-email-text"
                style="font-size:14px;line-height:24px;margin:24px 0;color:#333;font-family:-apple-system, BlinkMacSystemFont, &#x27;Segoe UI&#x27;, &#x27;Inter&#x27;, &#x27;Oxygen&#x27;, &#x27;Ubuntu&#x27;, &#x27;Cantarell&#x27;, &#x27;Fira Sans&#x27;, &#x27;Droid Sans&#x27;, &#x27;Helvetica Neue&#x27;, sans-serif;margin-bottom:12px">
                We have received your withdrawal request. This can take up to 10 working days to be approved and for the money to appear in your bank account.
              </p>
              <p data-id="react-email-text"
                style="font-size:14px;line-height:24px;margin:24px 0;color:#333;font-family:-apple-system, BlinkMacSystemFont, &#x27;Segoe UI&#x27;, &#x27;Inter&#x27;, &#x27;Oxygen&#x27;, &#x27;Ubuntu&#x27;, &#x27;Cantarell&#x27;, &#x27;Fira Sans&#x27;, &#x27;Droid Sans&#x27;, &#x27;Helvetica Neue&#x27;, sans-serif;margin-bottom:12px">
                Best Regards,
                <br />
                Bottled Goose
              </p>

              ${BGEmailFooter}
            </td>
          </tr>
        </tbody>
      </table>
    </body>
    </html>
  `
}
