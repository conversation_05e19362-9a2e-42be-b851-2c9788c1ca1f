export const ETSY_CLIENT_ID = process.env.ETSY_CLIENT_ID || 'ikch5oozb9xwdddk0t6kotjn';
export const ETSY_CLIENT_SECRET = process.env.ETSY_CLIENT_SECRET || '8wxfxqjbo0';
const path = require('path');
const fs = require('fs');
const FormData = require('form-data');

import axios from "axios";
import <PERSON>Helper from "./FileHelper";

class Etsy {
  constructor(accessToken: string, refreshToken: string) {
    this.accessToken = accessToken;
    this.refreshToken = refreshToken;
  }
  accessToken: string;
  refreshToken: string;
  private lastRequestTime: number = 0;
  private readonly MIN_REQUEST_INTERVAL: number = 800; // 800ms minimum between requests
  private readonly MAX_CONCURRENT_REQUESTS: number = 2; // Maximum number of concurrent requests
  private activeRequests: number = 0;

  private async waitForRateLimit() {
    const currentTime = Date.now();
    const timeSinceLastRequest = currentTime - this.lastRequestTime;

    // Wait if we're making requests too quickly
    if (timeSinceLastRequest < this.MIN_REQUEST_INTERVAL) {
      await new Promise(resolve => setTimeout(resolve, this.MIN_REQUEST_INTERVAL - timeSinceLastRequest));
    }

    // Wait if we have too many concurrent requests
    while (this.activeRequests >= this.MAX_CONCURRENT_REQUESTS) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    this.activeRequests++;
    this.lastRequestTime = Date.now();
  }

  private releaseRequest() {
    this.activeRequests--;
  }

  async getNewToken() {
    try {
      const res = await axios.request<any>({
        url: 'https://api.etsy.com/v3/public/oauth/token',
        method: 'post',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        data: `grant_type=refresh_token&refresh_token=${this.refreshToken}&preserve_refresh_token=true&client_id=${ETSY_CLIENT_ID}`,
      });
      this.accessToken = res.data.access_token;
      this.refreshToken = res.data.refresh_token;
      return {
        accessToken: this.accessToken,
        refreshToken: this.refreshToken,
      };
    } catch (err) {
      console.error('Attention please: Etsy refresh token is broken');
      console.error(err?.response?.data?.error);
      // throw err;
    }
  }

  async getStoreTokenFromCode({ code, code_verifier, redirect_uri }: {
    code: string,
    code_verifier: string,
    redirect_uri: string,
  }) {
    const res = await axios.request<any>({
      url: 'https://api.etsy.com/v3/public/oauth/token',
      method: 'post',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      data: `grant_type=authorization_code&client_id=${ETSY_CLIENT_ID}&code=${code}&code_verifier=${code_verifier}&redirect_uri=${redirect_uri}`,
    });
    return res.data;
  }

  async apiCall(path: string, options: { method: string, payload?: any }) {
    try {
      await this.waitForRateLimit();

      const headers = {
        'Authorization': `Bearer ${this.accessToken}`,
        'x-api-key': ETSY_CLIENT_ID,
        'Content-Type': ['post', 'put', 'patch'].includes(options.method) ? 'application/json' : undefined
      };
      if (!headers['Content-Type']) {
        delete headers['Content-Type'];
      }
      const res = await axios.request({
        url: `https://api.etsy.com/v3/application${path}`,
        method: options.method as any,
        headers: headers,
        data: !options.payload ? undefined : JSON.stringify(options.payload),
      });
      return res.data;
    } catch (err) {
      if (err.response && err.response.status === 401) {
        const newToken = await this.getNewToken();
        if (newToken) {
          return this.apiCall(path, options);
        }
        console.error("Etsy token is broken:", this.accessToken);
        // throw new Error('Etsy token is broken');
      } else {
        console.error('Attention please: Etsy api call is broken');
        console.error(err?.response?.data?.error);
        // throw err;
      }
    } finally {
      this.releaseRequest();
    }
  }

  async apiUploadCall(path: string, data: any) {
    try {
      await this.waitForRateLimit();

      const headers = {
        'Authorization': `Bearer ${this.accessToken}`,
        'x-api-key': ETSY_CLIENT_ID,
        'Content-Type': 'multipart/form-data',
        ...(data.getHeaders ? data.getHeaders() : {})
      };
      const res = await axios.post(`https://api.etsy.com/v3/application${path}`, data, {
        headers: headers,
      });
      return res.data;
    } catch (err) {
      if (err.response && err.response.status === 401) {
        const newToken = await this.getNewToken();
        if (newToken) {
          return this.apiUploadCall(path, data);
        }
        console.error("Etsy token is broken:", this.accessToken);
        // throw new Error('Etsy token is broken');
      } else {
        console.error('Attention please: Etsy api call is broken');
        console.error(err?.response);
        // throw err;
      }
    } finally {
      this.releaseRequest();
    }
  }

  async getMe(): Promise<{ user_id: number, shop_id: number }> {
    return this.apiCall('/users/me', { method: 'get' });
  }

  async getShop(): Promise<{ shop_id: number, shop_name: string, title: string, url: string, icon_url_fullxfull?: string }> {
    const { user_id, shop_id } = await this.getMe() || {};
    if (!shop_id) {
      throw new Error('Shop not found');
    }
    return this.apiCall('/shops/' + shop_id, { method: 'get' });
  }

  async getShopListings(): Promise<Array<{ listing_id: number, title: string, description: string, price: { amount: number, divisor: number }, quantity: number }>> {
    const { shop_id } = await this.getMe() || {};
    if (!shop_id) {
      throw new Error('Shop not found');
    }
    const data = await this.apiCall(`/shops/${shop_id}/listings`, { method: 'get' });
    return data.results;
  }

  async getShopShippingProfiles({ shopId }: { shopId?: number }) {
    let _shopId = shopId;
    if (!_shopId) {
      const shopInfo: any = await this.getMe() || {};
      _shopId = shopInfo?.shop_id;
    }
    if (!_shopId) {
      throw new Error('Shop not found');
    }
    const data = await this.apiCall(`/shops/${_shopId}/shipping-profiles`, { method: 'get' });
    return data.results;
  }

  async deleteListing(id: string | number) {
    const data = await this.apiCall(`/listings/${id}`, { method: 'delete' });
    return data;
  }

  async createDraftListing({ name, description, price, shopId, sku }: { name: string, description: string, price: number, shopId?: number, sku: string }) {
    let _shopId = shopId;
    if (!_shopId) {
      const shopInfo: any = await this.getMe() || {};
      _shopId = shopInfo?.shop_id;
    }
    if (!_shopId) {
      throw new Error('Shop not found');
    }
    const shippingProfiles = await this.getShopShippingProfiles({ shopId: _shopId });
    if (!shippingProfiles.length) {
      throw new Error('Your Etsy shop doesn\'t have any shipping profiles. Please create one first.');
    }
    let shippingProfileId = shippingProfiles.find(i => i.title.includes('Standard'))?.shipping_profile_id;
    if (!shippingProfileId) shippingProfileId = shippingProfiles[0]?.shipping_profile_id;

    const payload = {
      title: name,
      description,
      price,
      quantity: 999,
      who_made: 'i_did',
      when_made: 'made_to_order',
      taxonomy_id: 66, // 2856,
      shipping_profile_id: shippingProfileId, // standard
      sku,
      tags: [sku],
    }
    try {
      const data = await this.apiCall(`/shops/${_shopId}/listings`, { method: 'post', payload });
      return data;
    } catch (error) {
      throw new Error(error?.response?.data?.error)
    }
  }

  async getTaxonomyNodes({ shopId }: { shopId?: number }) {
    let _shopId = shopId;
    if (!_shopId) {
      const shopInfo: any = await this.getMe() || {};
      _shopId = shopInfo?.shop_id;
    }
    if (!_shopId) {
      throw new Error('Shop not found');
    }
    try {
      const data = await this.apiCall(`/seller-taxonomy/nodes`, { method: 'get' });
      // {
      //   id: 1,
      //   level: 1,
      //   name: 'Accessories',
      //   parent_id: null,
      //   children: [Array],
      //   full_path_taxonomy_ids: [Array]
      // },
      // {
      //   id: 66,
      //   level: 1,
      //   name: 'Art & Collectibles',
      //   parent_id: null,
      //   children: [Array],
      //   full_path_taxonomy_ids: [Array]
      // },
      // {
      //   id: 132,
      //   level: 1,
      //   name: 'Bags & Purses',
      //   parent_id: null,
      //   children: [Array],
      //   full_path_taxonomy_ids: [Array]
      // },
      // {
      //   id: 199,
      //   level: 1,
      //   name: 'Bath & Beauty',
      //   parent_id: null,
      //   children: [Array],
      //   full_path_taxonomy_ids: [Array]
      // },
      // {
      //   id: 323,
      //   level: 1,
      //   name: 'Books, Movies & Music',
      //   parent_id: null,
      //   children: [Array],
      //   full_path_taxonomy_ids: [Array]
      // },
      // {
      //   id: 374,
      //   level: 1,
      //   name: 'Clothing',
      //   parent_id: null,
      //   children: [Array],
      //   full_path_taxonomy_ids: [Array]
      // },
      // {
      //   id: 562,
      //   level: 1,
      //   name: 'Craft Supplies & Tools',
      //   parent_id: null,
      //   children: [Array],
      //   full_path_taxonomy_ids: [Array]
      // },
      // {
      //   id: 825,
      //   level: 1,
      //   name: 'Electronics & Accessories',
      //   parent_id: null,
      //   children: [Array],
      //   full_path_taxonomy_ids: [Array]
      // },
      // {
      //   id: 891,
      //   level: 1,
      //   name: 'Home & Living',
      //   parent_id: null,
      //   children: [Array],
      //   full_path_taxonomy_ids: [Array]
      // },
      // {
      //   id: 1179,
      //   level: 1,
      //   name: 'Jewelry',
      //   parent_id: null,
      //   children: [Array],
      //   full_path_taxonomy_ids: [Array]
      // },
      // {
      //   id: 1250,
      //   level: 1,
      //   name: 'Paper & Party Supplies',
      //   parent_id: null,
      //   children: [Array],
      //   full_path_taxonomy_ids: [Array]
      // },
      // {
      //   id: 1351,
      //   level: 1,
      //   name: 'Pet Supplies',
      //   parent_id: null,
      //   children: [Array],
      //   full_path_taxonomy_ids: [Array]
      // },
      // {
      //   id: 1429,
      //   level: 1,
      //   name: 'Shoes',
      //   parent_id: null,
      //   children: [Array],
      //   full_path_taxonomy_ids: [Array]
      // },
      // {
      //   id: 1552,
      //   level: 1,
      //   name: 'Toys & Games',
      //   parent_id: null,
      //   children: [Array],
      //   full_path_taxonomy_ids: [Array]
      // },
      // {
      //   id: 1633,
      //   level: 1,
      //   name: 'Weddings',
      //   parent_id: null,
      //   children: [Array],
      //   full_path_taxonomy_ids: [Array]
      // }
      return data;
    } catch (error) {
      throw new Error(error?.response?.data?.error)
    }
  }

  async updateListing({ data, id, shopId }: { data: any, id: string | number, shopId?: number }) {
    let _shopId = shopId;
    if (!_shopId) {
      const shopInfo: any = await this.getMe() || {};
      _shopId = shopInfo?.shop_id;
    }
    if (!_shopId) {
      throw new Error('Shop not found');
    }
    const payload = {
      ...data,
    }
    try {
      const data = await this.apiCall(`/shops/${_shopId}/listings/${id}`, { method: 'patch', payload });
      return data;
    } catch (error) {
      throw new Error(error?.response?.data?.error)
    }
  }

  async uploadListingImage({ url, id, shopId, rank }: { url: string, id: string | number, shopId?: number, rank?: number }) {
    let _shopId = shopId;
    if (!_shopId) {
      const shopInfo: any = await this.getMe() || {};
      _shopId = shopInfo?.shop_id;
    }
    if (!_shopId) {
      throw new Error('Shop not found');
    }
    const filePath = `etsy-image-${new Date().getTime()}.png`;
    await FileHelper.downloadFile(url, filePath);
    const form = new FormData();
    form.append('image', fs.createReadStream(filePath));
    form.append('name', `etsy-image-${new Date().getTime()}`);
    if (rank) form.append('rank', rank);
    try {
      const data = await this.apiUploadCall(`/shops/${_shopId}/listings/${id}/images`, form);
      fs.unlinkSync(filePath);
      return data;
    } catch (error) {
      throw new Error(error?.response?.data?.error)
    }
  }

  async getListing({ shopId, listingId }: { shopId?: number, listingId: string | number }) {
    let _shopId = shopId;
    if (!_shopId) {
      const shopInfo: any = await this.getMe() || {};
      _shopId = shopInfo?.shop_id;
    }
    if (!_shopId) {
      throw new Error('Shop not found');
    }
    try {
      const data = await this.apiCall(`/listings/${listingId}`, { method: 'get' });
      return data;
    } catch (error) {
      throw new Error(error?.response?.data?.error)
    }
  }
  async getReceipts({ shopId, minCreated }: { shopId?: number, minCreated?: number }) {
    let _shopId = shopId;
    if (!_shopId) {
      const shopInfo: any = await this.getMe() || {};
      _shopId = shopInfo?.shop_id;
    }
    if (!_shopId) {
      throw new Error('Shop not found');
    }
    try {
      const thirtyDaysAgo = Math.floor(Date.now() / 1000) - (30 * 24 * 60 * 60);
      const data = await this.apiCall(`/shops/${_shopId}/receipts?limit=100&min_created=${minCreated || thirtyDaysAgo}`, { method: 'get' });
      return data?.results;
    } catch (error) {
      throw new Error(error?.response?.data?.error)
    }
  }

  async getAllReceipts({ shopId, minCreated }: { shopId?: number, minCreated?: number }) {
    let _shopId = shopId;
    if (!_shopId) {
      const shopInfo: any = await this.getMe() || {};
      _shopId = shopInfo?.shop_id;
    }
    if (!_shopId) {
      throw new Error('Shop not found');
    }

    try {
      const thirtyDaysAgo = Math.floor(Date.now() / 1000) - (30 * 24 * 60 * 60);
      const limit = 100;
      let offset = 0;
      let allResults = [];
      let hasMore = true;
      while (hasMore) {
        const data = await this.apiCall(
          `/shops/${_shopId}/receipts?limit=${limit}&offset=${offset}&min_created=${minCreated || thirtyDaysAgo}`,
          { method: 'get' }
        );
        if (data?.results?.length) {
          allResults = [...allResults, ...data.results];
          offset += limit;
          if (data.results.length < limit) {
            hasMore = false;
          }
        } else {
          hasMore = false;
        }
      }
      return allResults;
    } catch (error) {
      throw new Error(error?.response?.data?.error)
    }
  }

  async getReceiptById({ id, shopId }: { id?: number, shopId?: number }) {
    try {
      let _shopId = shopId;
      if (!_shopId) {
        const shopInfo: any = await this.getMe() || {};
        _shopId = shopInfo?.shop_id;
      }
      if (!_shopId) {
        throw new Error('Shop not found');
      }
      const data = await this.apiCall(`/shops/${_shopId}/receipts/${id}`, { method: 'get' });
      return data;
    } catch (error) {
      throw new Error(error?.response?.data?.error)
    }
  }

  async getTransactions({ shopId }: { shopId?: number }) {
    let _shopId = shopId;
    if (!_shopId) {
      const shopInfo: any = await this.getMe() || {};
      _shopId = shopInfo?.shop_id;
    }
    if (!_shopId) {
      throw new Error('Shop not found');
    }
    try {
      const data = await this.apiCall(`/shops/${_shopId}/transactions`, { method: 'get' });
      return data?.results;
    } catch (error) {
      throw new Error(error?.response?.data?.error)
    }
  }

  async getShopSection({ shopId }: { shopId?: number }) {
    let _shopId = shopId;
    if (!_shopId) {
      const shopInfo: any = await this.getMe() || {};
      _shopId = shopInfo?.shop_id;
    }
    if (!_shopId) {
      throw new Error('Shop not found');
    }
    try {
      const data = await this.apiCall(`/shops/${_shopId}/sections`, { method: 'get' });
      return data;
    } catch (error) {
      throw new Error(error?.response?.data?.error)
    }
  }

  async createReceiptShipment({ shopId, receiptId, trackingNumber }: { shopId?: number, receiptId: string | number, trackingNumber: string }) {
    let _shopId = shopId;
    if (!_shopId) {
      const shopInfo: any = await this.getMe() || {};
      _shopId = shopInfo?.shop_id;
    }
    if (!_shopId) {
      throw new Error('Shop not found');
    }
    try {
      const data = await this.apiCall(`/shops/${_shopId}/receipts/${receiptId}/tracking`, {
        method: 'post',
        payload: {
          tracking_code: trackingNumber,
          carrier_name: 'royal-mail', // https://developer.etsy.com/documentation/tutorials/fulfillment/#tracking-updates-for-shipping-carriers
          send_bcc: true,
        }
      });
      return data;
    } catch (error) {
      throw new Error(error?.response?.data?.error)
    }
  }

  async updateReceipt({ shopId, receiptId, was_shipped, was_paid }: { shopId?: number, receiptId: string | number, was_shipped?: boolean, was_paid?: boolean }) {
    let _shopId = shopId;
    if (!_shopId) {
      const shopInfo: any = await this.getMe() || {};
      _shopId = shopInfo?.shop_id;
    }
    if (!_shopId) {
      throw new Error('Shop not found');
    }
    try {
      const payload: any = {};
      if (typeof was_shipped === 'boolean') payload.was_shipped = was_shipped;
      if (typeof was_paid === 'boolean') payload.was_paid = was_paid;
      const data = await this.apiCall(`/shops/${_shopId}/receipts/${receiptId}`, { method: 'put', payload });
      return data;
    } catch (error) {
      throw new Error(error?.response?.data?.error)
    }
  }

  async updateExistingListing({
    listingId,
    shopId,
    title,
    description,
    price,
    images,
    tags,
  }: {
    listingId: string | number;
    shopId?: number;
    title?: string;
    description?: string;
    price?: number;
    images?: Array<{ url: string; rank?: number }>;
    tags?: string[];
  }) {
    let _shopId = shopId;
    if (!_shopId) {
      const shopInfo: any = await this.getMe() || {};
      _shopId = shopInfo?.shop_id;
    }
    if (!_shopId) {
      throw new Error('Shop not found');
    }

    try {
      // Build the update payload with only provided fields
      const payload: any = {};

      if (title !== undefined) payload.title = title;
      if (description !== undefined) payload.description = description;
      if (price !== undefined) payload.price = price;
      if (tags !== undefined) payload.tags = tags;

      // Update the listing
      const updatedListing = await this.apiCall(`/shops/${_shopId}/listings/${listingId}`, {
        method: 'patch',
        payload
      });

      // Handle image updates if provided
      // if (images && images.length > 0) {
      //   // First, get existing images to potentially delete them
      //   const existingListing = await this.getListing({ shopId: _shopId, listingId });
      //   const existingImages = existingListing.images || [];

      //   console.log('Etsy existing images', existingImages);
      //   // Delete existing images if we're replacing them
      //   for (const image of existingImages) {
      //     try {
      //       await this.apiCall(`/shops/${_shopId}/listings/${listingId}/images/${image.listing_image_id}`, {
      //         method: 'delete'
      //       });
      //     } catch (error) {
      //       console.warn(`Failed to delete existing image ${image.listing_image_id}:`, error);
      //     }
      //   }

      //   // Upload new images
      //   for (let i = 0; i < images.length; i++) {
      //     const image = images[i];
      //     try {
      //       await this.uploadListingImage({
      //         url: image.url,
      //         id: listingId,
      //         shopId: _shopId,
      //         rank: image.rank || i + 1
      //       });
      //     } catch (error) {
      //       console.error(`Failed to upload image ${i + 1}:`, error);
      //       throw new Error(`Failed to upload image: ${error.message}`);
      //     }
      //   }
      // }

      return updatedListing;
    } catch (error) {
      console.error('Error updating Etsy listing:', error);
      throw new Error(error?.response?.data?.error || error.message || 'Failed to update Etsy listing');
    }
  }
}

export default Etsy;
