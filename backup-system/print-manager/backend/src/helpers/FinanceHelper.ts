import { DB } from "db";
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, { apiVersion: null });

class FinanceHelper {
  reduceUserTotalsAfterRefund = async (params: {
    userId: string;
    orderId: string;
    stripeInvoiceId: string;
  }) => {
    const { userId, orderId, stripeInvoiceId } = params;
    const invoice = await DB.Invoice.findOne({
      where: {
        orderId,
      },
    });
    const stripeInvoice = await stripe.invoices.retrieve(stripeInvoiceId);
    const user = await DB.User.findByPk(userId);
    if (user) {
      let salesTotal = 0;
      let shippingTotal = 0;
      let vatTotal = 0;
      let sampleSalesTotal = 0;
      let wholesaleTotal = 0;

      // Calculate totals from invoice line items
      for (const item of stripeInvoice.lines.data) {
        if (item.description?.includes("Shipping fee")) {
          shippingTotal += item.amount;
        } else if (item.description?.includes("VAT")) {
          vatTotal += item.amount;
        } else {
          if (invoice.data?.isWholesale) {
            wholesaleTotal += item.amount;
          } else if (invoice.data?.isSampleRequest) {
            sampleSalesTotal += item.amount;
          } else {
            salesTotal += item.amount;
          }
        }
      }

      user.salesTotal = (user.salesTotal || 0) - salesTotal;
      user.shippingTotal = (user.shippingTotal || 0) - shippingTotal;
      user.vatTotal = (user.vatTotal || 0) - vatTotal;
      user.sampleSalesTotal = (user.sampleSalesTotal || 0) - sampleSalesTotal;
      user.wholesaleTotal = (user.wholesaleTotal || 0) - wholesaleTotal;
      user.refundTotal = (user.refundTotal || 0) + (invoice.data?.amountPaid || stripeInvoice.total);
      await user.save();
    }
  }

  addUserTotalsAfterCharge = async (params: {
    userId: string;
    orderId: string;
    stripeInvoiceId: string;
  }) => {
    const { userId, orderId, stripeInvoiceId } = params;
    const invoice = await DB.Invoice.findOne({
      where: {
        orderId,
      },
    });
    const stripeInvoice = await stripe.invoices.retrieve(stripeInvoiceId);
    const user = await DB.User.findByPk(userId);
    if (user) {
      let salesTotal = 0;
      let shippingTotal = 0;
      let vatTotal = 0;
      let sampleSalesTotal = 0;
      let wholesaleTotal = 0;

      // Calculate totals from invoice line items
      for (const item of stripeInvoice.lines.data) {
        if (item.description?.includes("Shipping fee")) {
          shippingTotal += item.amount;
        } else if (item.description?.includes("VAT")) {
          vatTotal += item.amount;
        } else {
          if (invoice.data?.isWholesale) {
            wholesaleTotal += item.amount;
          } else if (invoice.data?.isSampleRequest) {
            sampleSalesTotal += item.amount;
          } else {
            salesTotal += item.amount;
          }
        }
      }

      user.salesTotal = (user.salesTotal || 0) + salesTotal;
      user.shippingTotal = (user.shippingTotal || 0) + shippingTotal;
      user.vatTotal = (user.vatTotal || 0) + vatTotal;
      user.sampleSalesTotal = (user.sampleSalesTotal || 0) + sampleSalesTotal;
      user.wholesaleTotal = (user.wholesaleTotal || 0) + wholesaleTotal;
      await user.save();
    }
  }
}

export default new FinanceHelper();
