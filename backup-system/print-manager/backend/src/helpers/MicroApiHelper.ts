const MICRO_SERVICES_HOST = process.env.DEV ? 'https://dev.services.personify.tech' : 'https://services.personify.tech';

export const requestMicroApi = async (url: string, data?: { method?: string, body?: any, headers?: any }) => {
  let completedUrl = url;
  if (!completedUrl.startsWith('http')) {
    completedUrl = `${MICRO_SERVICES_HOST}${url}`;
  }
  const res = await fetch(completedUrl, {
    method: data?.method || "get",
    headers: {
      ...(data?.headers || {}),
      "Content-Type": "application/json",
      "env": process.env.DEV ? "dev" : "prod",
      "X-Auth-Token": "P8UeTt92HS98",
    },
    body: data?.body,
  });
  return res.json()
}

export const objectToQueryString = (obj) => {
  return Object.keys(obj).map((key) => {
    return encodeURIComponent(key) + '=' + encodeURIComponent(obj[key]);
  }).join('&')
};
