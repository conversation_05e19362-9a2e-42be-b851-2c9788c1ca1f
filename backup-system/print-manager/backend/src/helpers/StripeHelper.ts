import { TAX_ONLY_RATE } from 'api/payment/utils';
import { DB } from 'db';
import Strip<PERSON> from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, { apiVersion: null });

class StripeHelper {

  getInvoiceDetail = async id => {
    const data = await stripe.invoices.retrieve(id);
    return data;
  }

  refundInvoice = async (customer, total, metadata) => {
    const transactions = await stripe.customers.createBalanceTransaction(customer, {
      amount: -total,
      currency: 'gbp',
      metadata: {
        type: "refund",
        ...metadata,
      }
    });
    return transactions;
  }

  updateInvoiceProductPrice = async (stripeInvoice: Stripe.Invoice) => {
    const lineItems = stripeInvoice.lines.data;
    let totalChanged = 0;

    for (let i = 0; i < lineItems.length; i++) {
      const line = lineItems[i];
      const designId = line.metadata?.designId;

      if (designId) {
        const design = await DB.Design.findByPk(designId);
        if (design) {
          const productId = design.productId;
          const product = await DB.Product.findByPk(productId);

          // Get prices in pence/cents for accurate comparison
          const currentPriceInPence = line.amount;
          const newPriceInPence = Math.round(product.price * 100 * line.quantity);

          const productPriceChanged = currentPriceInPence !== newPriceInPence && Math.abs(currentPriceInPence - newPriceInPence) > 1;
          const isDiscountedProduct = !!product.originalPrice && product.originalPrice !== product.price;

          if (productPriceChanged) {
            console.log("[Update Stripe Invoice] productPriceChanged", currentPriceInPence, newPriceInPence, Math.abs(currentPriceInPence - newPriceInPence))
            // Create new price using the product price
            const newPrice = await stripe.prices.create({
              currency: 'gbp',
              product_data: {
                name: line.description,
              },
              unit_amount: newPriceInPence,
            });

            // Create new line item
            await stripe.invoiceItems.create({
              invoice: stripeInvoice.id,
              customer: stripeInvoice.customer as string,
              price: newPrice.id,
              metadata: {
                ...(line.metadata || {}),
                ...(isDiscountedProduct ? {
                  originalPrice: product.originalPrice,
                } : {})
              },
              quantity: line.quantity,
            });

            // Delete old line item
            await stripe.invoiceItems.del(line?.id);

            // Calculate price difference in pence/cents
            totalChanged += (currentPriceInPence - newPriceInPence);
          }
        }
      }
    }

    // Update VAT if total changed
    if (totalChanged !== 0) {
      const taxLine = lineItems.find(i => i.description?.includes("VAT "));
      if (taxLine) {
        // Calculate new VAT amount in pence/cents
        const newVatAmount = Math.round(taxLine.amount - totalChanged * TAX_ONLY_RATE);

        const _taxPrice = await stripe.prices.create({
          currency: 'gbp',
          product_data: {
            name: taxLine.description,
          },
          unit_amount: newVatAmount,
        });

        await stripe.invoiceItems.create({
          invoice: stripeInvoice.id,
          customer: stripeInvoice.customer as string,
          price: _taxPrice.id,
          quantity: 1,
        });

        await stripe.invoiceItems.del(taxLine?.id);
      }

      // Return updated invoice
      return await stripe.invoices.retrieve(stripeInvoice.id);
    }

    return stripeInvoice;
  }
};


export default new StripeHelper();
