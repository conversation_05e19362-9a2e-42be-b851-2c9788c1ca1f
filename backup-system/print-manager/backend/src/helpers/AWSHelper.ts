import * as AWS from 'aws-sdk';
const fs = require('fs');
const axios = require('axios');
const sharp = require('sharp');

class AWSHelper {
  constructor() {
    this.init();
    this.checkIfAWSWorking();
  }

  s3Info = {
    access_key: process.env.AWS_ACCESS_KEY_ID,
    access_secret: process.env.AWS_SECRET_ACCESS_KEY,
    bucket: process.env.AWS_S3_BUCKET,
    region: process.env.AWS_REGION
  };
  s3
  rekognition: AWS.Rekognition

  init() {
    AWS.config.update({
      region: process.env.AWS_REGION,
      accessKeyId: this.s3Info.access_key,
      secretAccessKey: this.s3Info.access_secret,
    });
    this.rekognition = new AWS.Rekognition();
    this.s3 = new AWS.S3();
  }

  checkIfAWSWorking = () => {
    const sts = new AWS.STS();
    sts.getCallerIdentity({}, function (err, data) {
      if (err) {
        console.log("Error", err);
      } else {
        console.log("Successfully get the identity");
        console.log(data);
      }
    });
  }

  getSignedUrl(key) {
    const url = this.s3.getSignedUrl('getObject', {
      Bucket: this.s3Info.bucket,
      Key: key,
      Expires: 60 * 15,
    })
    return url;
  }

  upload = async ({ filePath, key }, shouldLog = false) => {
    const objectParams = {
      ACL: 'public-read',
      Bucket: this.s3Info.bucket,
      Key: key,
      Body: fs.createReadStream(filePath),
    };
    const res = await this.s3.putObject(objectParams).promise();
    if (shouldLog) console.log(res);
    return `https://${this.s3Info.bucket}.s3.eu-west-1.amazonaws.com/${key}`;
  }

  uploadRaw = async ({ data, key, contentType }, shouldLog = false) => {
    const objectParams = {
      ACL: 'public-read',
      Bucket: this.s3Info.bucket,
      Key: key,
      Body: data,
      ContentType: contentType,
    };
    const res = await this.s3.putObject(objectParams).promise();
    if (shouldLog) console.log(res);
    return `https://${this.s3Info.bucket}.s3.eu-west-1.amazonaws.com/${key}`;
  }

  getSignedUrlForUpload(key, contentType) {
    const url = this.s3.getSignedUrl('putObject', {
      Bucket: this.s3Info.bucket,
      Key: key,
      Expires: 60 * 15,
      ACL: 'public-read',
      ContentType: contentType,
    })
    return url;
  }

  detectModerationLabel = (url: string, name?: string) => new Promise(async (resolve, reject) => {
    // if (!url && !name) return;
    // const isS3Image = name ? true : url.startsWith(`https://${this.s3Info.bucket}.s3.eu-west-1.amazonaws.com/`);
    // let params;
    // if (isS3Image) {
    //   params = {
    //     Image: {
    //       S3Object: {
    //         Bucket: this.s3Info.bucket,
    //         Name: name || url.replace(`https://${this.s3Info.bucket}.s3.eu-west-1.amazonaws.com/`, ''),
    //       }
    //     },
    //   }
    // } else {
    if (!url) return reject('No image url');
    const response = await axios.get(url, { responseType: 'arraybuffer' });
    if (!response.data || response.data.length === 0) {
      return reject('Empty image data');
    }
    const imageBuffer = Buffer.from(response.data, 'binary');
    if (imageBuffer.length === 0) {
      return reject('Empty image buffer');
    }

    // AWS log - Member must have length less than or equal to 5242880
    const maxSizeInBytes = 5 * 1024 * 1024;
    // Resize and compress the image
    let quality = 100;
    let resizedBuffer = imageBuffer;
    let width = 1920;

    while (resizedBuffer.length > maxSizeInBytes) {
      resizedBuffer = await sharp(imageBuffer)
        .resize({ width })
        .png({ quality })
        .toBuffer();
      width = Math.floor(width * 0.9); // decrease width by 10%
      quality -= 10; // decrease quality by 10
    }

    const params = {
      Image: {
        Bytes: resizedBuffer,
      },
    }
    const res = await sharp(resizedBuffer).stats();
    const sharpness = res.sharpness;
    // }

    this.rekognition.detectModerationLabels(params, (err, data) => {
      if (err) {
        reject(err);
      } else {
        resolve({
          labels: data?.ModerationLabels || [],
          sharpness
        });
      }
    });
  })
}

export default new AWSHelper();
