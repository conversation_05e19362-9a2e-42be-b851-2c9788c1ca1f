import { TokenSet, XeroClient, Invoice } from "xero-node";
const clientId = process.env.XERO_CLIENT_ID || 'F1551D4BD1E34AB2BE552A07C01F61E5';
const clientSecret = process.env.XERO_CLIENT_SECRET || 'hNpI4FjcHqhv5xz-txN9Sjh8aAdtxZ62eXcgGm_FaKjN_BEk';
import axios from 'axios';
import { TUser } from "type";
import VarHelper from "./VarHelper";
const moment = require('moment');

class XeroHelper {
  constructor() {
    this.init();
  }

  xero;
  tokenSet;
  tenantId = process.env.XERO_TENANT_ID || '';
  accountCode = process.env.ACCOUNT_CODE || '200';

  async init() {
    this.xero = new XeroClient({
      clientId,
      clientSecret,
      grantType: "client_credentials",
    });
    await this.xero.initialize();
    this.tokenSet = await this.xero.getClientCredentialsToken();
    this.getTenentIdIfNotExists();
  }

  async checkAuthen() {
    console.log('this.tokenSet.expired()', this.tokenSet.expired());
    if (this.tokenSet.expired()) {
      await this.init();
    }
  }

  async apiCall(path, method, payload = undefined): Promise<any> {
    try {
      console.log('API CALL', `https://api.xero.com/api.xro/2.0${path}`);
      const res = await axios.get(`https://api.xero.com/api.xro/2.0${path}`, {
        method,
        headers: {
          Accept: 'application/json',
          Authorization: `Bearer ${this.tokenSet.access_token}`,
          ...(payload ? { 'Content-Type': 'application/json' } : {}),
        },
        data: payload ? JSON.stringify(payload) : undefined,
      })
      let json = {};
      try {
        json = typeof res.data === 'string' ? JSON.parse(res.data) : res.data;
      } catch (err) { }
      return json;
    } catch (err) {
      console.log('API CALL ERROR', err);
      return {};
    }
  }

  async getTenentIdIfNotExists() {
    if (!this.tenantId) {
      try {
        const res = await axios.get('https://api.xero.com/connections', {
          headers: {
            Authorization: `Bearer ${this.tokenSet.access_token}`
          },
        });
        const json = res.data;
        if (json && json[0]) {
          console.log('json[0].tenantId', json[0].tenantId);
          this.tenantId = json[0].tenantId;
        }
      } catch (err) { }

    }
  }

  async getContacts(where: string | undefined = undefined) {
    const whereQuery = where ? `where=${where}` : undefined;
    try {
      // const conRes = await this.xero.accountingApi.getContacts(
      //   this.tenantId,
      // )
      // return conRes;
      const res = await axios.get(`https://api.xero.com/api.xro/2.0/Contacts${whereQuery ? '?' + whereQuery : ''}`, {
        method: 'GET',
        headers: {
          Accept: 'application/json',
          Authorization: `Bearer ${this.tokenSet.access_token}`
        }
      })
      const json = await this.apiCall(`/Contacts${whereQuery ? '?' + whereQuery : ''}`, 'get');
      console.log('json', json);
      return json.Contacts;
    } catch (err) {
      console.log('ERROR getContacts', err);
      throw err;
    }
  }

  async getInvoicesFromContactID(contactID) {
    try {
      const invRes = await this.xero.accountingApi.getInvoices(
        this.tenantId,
        undefined,
        undefined,
        "DueDate DESC",
        undefined,
        undefined,
        [contactID],
        ["AUTHORISED", "PAID"]
      );
      return invRes;
    } catch (err) {
      console.log(err);
    }
  }

  async getInvoicePublicURL(InvoiceID) {
    try {
      const res = await this.apiCall(`/Invoices/${InvoiceID}/OnlineInvoice`, 'get');
      return res.OnlineInvoices?.[0]?.OnlineInvoiceUrl;
    } catch (err) {
      console.log(err);
    }
  }

  async saveInvoicePDF(InvoiceID, savePath) {
    try {
      await this.xero.accountingApi.invoices.savePDF({
        InvoiceID,
        savePath
      });
    } catch (err) {
      console.log(err);
    }
  }

  async createContactIfNotExist(email, fullUserData: TUser) {
    console.log('createContactIfNotExist', email, fullUserData)
    const contactsFind = await this.getContacts(`EmailAddress="${email}"`);
    console.log('contactsFind.length', contactsFind.length);
    const contacts = contactsFind.filter(v => v.EmailAddress === email);
    if (contacts.length > 0) {
      return contacts[0];
    }
    // create new
    console.log('creating new contact');
    const contact = {
      Name: fullUserData.accountName || `${fullUserData.firstName} ${fullUserData.lastName}`,
      EmailAddress: email,
      FirstName: fullUserData.firstName,
      LastName: fullUserData.lastName,
      ContactStatus: "ACTIVE",
      ContactNumber: fullUserData.id,
    };
    console.log('contact', JSON.stringify(contact));
    const res = await this.apiCall('/Contacts', 'post', contact);
    const Contacts = res?.Contacts || [];
    require('fs').writeFileSync('contacts.json', JSON.stringify(res.Contacts, null, 2));
    return Contacts.length === 0 ? undefined : Contacts[Contacts.length - 1];

    // const contact = {
    //   name: fullUserData.accountName || `${fullUserData.firstName} ${fullUserData.lastName}`,
    //   EmailAddress: email,
    //   firstName: fullUserData.firstName,
    //   lastName: fullUserData.lastName,
    //   contactStatus: "ACTIVE",
    //   contactNumber: fullUserData.id,
    // };
    // const response = await this.xero.accountingApi.createContacts(this.tenantId, {
    //   Contacts: [contact],
    // });
    // return response;
  }

  async createInvoice(contactId, lineItems) {
    console.log('create Invoice from ', contactId, JSON.stringify(lineItems));
    /*
    const invoices = [
      {
        type: Invoice.TypeEnum.ACCREC,
        contact: {
          contactID: contactId
        },
        lineItems: [
          {
            description: "Acme Tires",
            quantity: 2.0,
            unitAmount: 20.0,
            accountCode: "500",
            taxType: "NONE",
            lineAmount: 40.0
          }
        ],
        date: "2019-03-11",
        dueDate: "2018-12-10",
        reference: "Website Design",
        status: Invoice.StatusEnum.AUTHORISED
      }
    ];
    */
    //  const invoices = [
    //   {
    //     type: Invoice.TypeEnum.ACCREC,
    //     contact: {
    //       contactID: contactId
    //     },
    //     lineItems: lineItems.map(v => ({
    //       ...v,
    //       accountCode: this.accountCode,
    //     })),
    //     date: moment().format('YYYY-MM-DD'),
    //     dueDate: moment().format('YYYY-MM-DD'),
    //     status: Invoice.StatusEnum.AUTHORISED
    //   }
    // ]
    const invoices = [
      {
        Type: "ACCREC",
        Contact: {
          ContactID: contactId
        },
        LineItems: VarHelper.convertObjKeysToCapital(lineItems.map(v => ({
          ...v,
          accountCode: this.accountCode,
        }))),
        Date: moment().format('YYYY-MM-DD'),
        DueDate: moment().format('YYYY-MM-DD'),
        Status: "AUTHORISED",
        LineAmountTypes: 'Exclusive',
      }
    ];
    
    console.log('invoices', invoices);
    try {
      // const createdInvoicesResponse = await this.xero.accountingApi.createInvoices(this.tenantId, {
      //   invoices
      // });
      // return createdInvoicesResponse;
      const res = await this.apiCall('/Invoices', 'post', {
        ...invoices[0]
      });
      // console.log('res', res);
      require('fs').writeFileSync('invoices.json', JSON.stringify(res.Invoices, null, 2));
      const resInvoices = res?.Invoices || [];
      const Invoices = resInvoices.filter(v => {
        return v.Contact.ContactID === contactId && moment(v.DateString).format('YYYY-MM-DD') === moment().format('YYYY-MM-DD');
      });
      return Invoices.length === 0 ? resInvoices[resInvoices.length -1] : Invoices[Invoices.length - 1];
    } catch (err) {
      console.log(err);
    }
  }

  async markInvoiceAsPaid(invoice) {
    const invoiceId = invoice.InvoiceID;
    const total = invoice.Total;
    const payments = [
      {
        Invoice: {
          InvoiceID: invoiceId
        },
        Account: {
          Code: this.accountCode
        },
        Amount: total,
        Date: moment().format('YYYY-MM-DD'),
      }
    ];
    try {
      const res = await this.apiCall('/Payments', 'post', payments);
      require('fs').writeFileSync('payments.json', JSON.stringify(res.Payments, null, 2));
    } catch (err) {
      console.log(err);
    }
  }
}

export default new XeroHelper();