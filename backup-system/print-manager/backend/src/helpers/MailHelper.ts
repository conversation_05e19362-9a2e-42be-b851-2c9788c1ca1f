const nodemailer = require('nodemailer');

interface IMailPayload {
  to: string,
  cc?: Array<string>,
  subject: string,
  text?: string,
  html: string,
}

class MailHelper {


  sendSMTPEmail = ({ to, cc, subject, text, html} : IMailPayload) =>  new Promise((resolve, reject) => {
    let transport = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: process.env.SMTP_PORT,
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASSWORD
      }
    })
    const message : any = {
      from: `Bottled Goose <${process.env.SENDER_EMAIL}>`,
      to,
      subject,
      html
    };
    if (cc) message.cc = cc;
    transport.sendMail(message, (err, info) => {
      if(err) {
        console.log('err sendMail', err);
        return reject(err)
      } else {
        console.log(info);
        return resolve(info);
      }
    })
  })

  genForgotPasswordEmail = ({ firstName, lastName, link }) => {
    return `
      <p>Hi <b>${firstName} ${lastName},</b></p>
      <p>You have requested a password reset. Please click here to reset your password:</p>
      <a target="_blank" href="${link}">${link}</a>
      <p>If you did not request a password reset, please contact your Bottle Goose Administrator.</p>
      <p>Best regards!</p>
    `
  }

  genOutOfStockEmail = ({ firstName, lastName, link, productName }) => {
    return `
      <p>Hi <b>${firstName} ${lastName},</b></p>
      <p>We would like to inform you that the product <a target="_blank" href="${link}">${productName}</a> is out of stock.</p>
      <p>Best regards!</p>
    `
  }
}

export default new MailHelper()
