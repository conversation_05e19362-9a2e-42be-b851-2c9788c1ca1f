const bcrypt = require('bcrypt');
const saltRounds = 10;
const jwt = require('jwt-simple');
const secret = '0687166018768776945407964778425083503250334896304578'; // just a random string

import { InMemory as RedisCache } from 'db';

const BEHAVIOUR = {
  LOGOUT_WILL_EXPIRE_ALL: {
    afterLogin: async ({ userId, token }) => {
      const listToken = await RedisCache.hgetAsync('JWTs', userId);
      if (listToken) {
        const parsedList = JSON.parse(listToken);
        if (!parsedList.includes(token)) {
          parsedList.push(token);
          await RedisCache.hsetAsync('JWTs', userId, JSON.stringify(parsedList));
        }
      }
    },
    afterLogout: async ({ userId }) => {
      const listToken = await RedisCache.hgetAsync('JWTs', userId);
      if (listToken) {
        const parsedList = JSON.parse(listToken);
        parsedList.forEach((eachToken) => {
          RedisCache.hdelAsync('JWT', eachToken);
        });
      }
      RedisCache.hsetAsync('JWTs', userId, JSON.stringify([]));
    },
  },
  LOGOUT_WILL_EXPIRE_ONE: {
    afterLogin: null,
    afterLogout: null,
  },
}

class AuthenHelper {

  BEHAVIOUR=BEHAVIOUR;

  currentBehaviour = BEHAVIOUR.LOGOUT_WILL_EXPIRE_ALL;

  setBehaviour(behaviour) {
    this.currentBehaviour = behaviour;
  }

  hashPassword = (pass) : Promise<string> => new Promise((resolve, reject) => {
    bcrypt.hash(pass, saltRounds, function (err, hash) {
      if (err) reject(err);
      else resolve(hash);
    });
  });

  comparePassword = (pass, hash) => new Promise((resolve, reject) => {
    bcrypt.compare(pass, hash, function (err, res) {
      if (err) reject(err);
      else resolve(res);
    });
  });

  genJWTToken(payload) {
    if (!payload.createdAt) {
      payload.createdAt = new Date().getTime();
    }
    var token = jwt.encode(payload, secret);
    return token;
  }

  decodeJWT(token) {
    try {
      var decoded = jwt.decode(token, secret);
      return decoded;
    } catch {
      return null;
    }
  }
}

export default new AuthenHelper();