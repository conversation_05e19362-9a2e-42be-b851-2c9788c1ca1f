import axios from 'axios';
import FileHelper from './FileHelper';
const path = require('path');
const fs = require('fs');

const ShopifyToken = require('shopify-token');
const shopifyToken = new ShopifyToken({
  sharedSecret: '86f696d0411bddf060b5105f580514de',
  redirectUri: `${process.env.BACKEND_CMS_URL || 'https://dev.bg-production.personify.tech'}/api/online-stores/shopify-app-connect-callback`,
  apiKey: '70b1f373cbb7ea3281c47eb4e6d181c3'
});


class ShopifyHelper {

  getAccessToken = (shop, code) => shopifyToken.getAccessToken(shop, code);
  verifyHmac = (query) => shopifyToken.verifyHmac(query);
  generateNonce = () => shopifyToken.generateNonce();
  generateAuthUrl = (shop, nonce) => shopifyToken.generateAuthUrl(shop, undefined, nonce);

  async apiCall(url, method, token, body = undefined): Promise<any> {
    if (!url) throw new Error('ShopifyHelper - url is required');
    const res = await axios.request({
      url,
      method: method,
      headers: {
        'X-Shopify-Access-Token': token,
        ...(method === 'get' ? {} : {
          'Content-Type': 'application/json',
        }),
      },
      data: !body ? undefined : JSON.stringify(body),
    });
    return res.data || {};
  }

  async getShopinfo(url, token) {
    const data = await this.apiCall(`${url}/admin/api/2023-04/shop.json`, 'get', token);
    const { shop } = data;
    return {
      id: shop?.id,
      name: shop?.name,
      domain: shop?.domain,
      myshopify_domain: shop?.myshopify_domain,
    }
  }

  async getProducts(url: string, token: string) {
    const data = await this.apiCall(
      `${url}/admin/api/2023-04/products.json?limit=10`,
      'get',
      token
    );
    return data.products || [];
  }

  async getRecentOrders(url: string, token: string) {
    const data = await this.apiCall(
      `${url}/admin/api/2023-04/orders.json?limit=50&status=any`,
      'get',
      token
    );
    return data.orders || [];
  }

  async getOrdersFromDate(url: string, token: string, fromDate: string) {
    const data = await this.apiCall(
      `${url}/admin/api/2023-04/orders.json?status=any&created_at_min=${fromDate}&limit=250`,
      'get',
      token
    );
    return data.orders || [];
  }

  async upsertOrderWebhook(url: string, token: string, webhooks = [], store: any, clientName: string) {
    const originalCallback = `${process.env.BACKEND_CMS_URL}/api/bg/shopify-webhook?clientId=${store.resellerId}&env=${process.env.DEV ? 'dev' : 'prod'}&clientName=${clientName}&storeId=${store.id}`;
    const webhookAddress = `${process.env.RELAY_WEBHOOK_URL || 'https://rlw.personify.tech/webhook'}/${originalCallback}`;
    const listWebhook = await (async () => {
      if (webhooks.length > 0) return webhooks;
      const { webhooks: apiCallWebhooks } = await this.apiCall(`${url}/admin/api/2023-04/webhooks.json`, 'get', token);
      return apiCallWebhooks;
    })();
    const currentWebhooks = [...listWebhook];

    const existingOrderWebhook = currentWebhooks.find(webhook => webhook.topic === 'orders/create');

    if (!existingOrderWebhook) {
      await this.apiCall(
        `${url}/admin/api/2023-04/webhooks.json`,
        'post',
        token,
        {
          webhook: {
            address: webhookAddress,
            topic: "orders/create",
            format: "json"
          }
        }
      );
    } else if (existingOrderWebhook.address !== webhookAddress) {
      await this.apiCall(
        `${url}/admin/api/2023-04/webhooks/${existingOrderWebhook.id}.json`,
        'put',
        token,
        {
          webhook: {
            address: webhookAddress,
            topic: "orders/create",
            format: "json"
          }
        }
      );
    }
    const duplicateWebhooks = currentWebhooks.filter(
      webhook => webhook.topic === 'orders/create' && webhook.id !== existingOrderWebhook?.id
    );

    for (const webhook of duplicateWebhooks) {
      await this.apiCall(
        `${url}/admin/api/2023-04/webhooks/${webhook.id}.json`,
        'delete',
        token
      );
    }
  }

  async deleteOrderWebhook(url, token) {
    const { webhooks: listWebhook } = await this.apiCall(`${url}/admin/api/2023-04/webhooks.json`, 'get', token);
    const listRemoveWebhook = listWebhook.filter(val => val.topic === 'orders/create' && val.address.includes(`/api/bg/shopify-webhook`));
    if (listRemoveWebhook.length > 0) {
      await Promise.all(listRemoveWebhook.map(webhook =>
        this.apiCall(`${url}/admin/api/2023-04/webhooks/${webhook.id}.json`, 'delete', token)
      ));
    }
  }

  async uploadImageToShopifyStore(imageUrl, { storeUrl, token, productId, variantId = undefined }) {
    console.log('uploadImageToShopifyStore', imageUrl, storeUrl, token, productId);
    // const randomName = `p-${productId}-${path.basename(imageUrl)}`;
    // await FileHelper.downloadFile(imageUrl, randomName);
    // const base64 = FileHelper.fileToBase64(randomName);
    // fs.unlink(randomName, () => { });
    let params: any = { src: imageUrl };
    if (variantId) params.variant_ids = [variantId];
    const res = await this.apiCall(
      `${storeUrl}/admin/api/2023-04/products/${productId}/images.json`,
      'post',
      token,
      {
        image: params
      }
    );
    // const res = await axios.request({
    //   url: `${storeUrl}/admin/api/2022-07/products/${productId}/images.json`,
    //   method: 'post',
    //   headers: {
    //     'Content-Type': 'application/json',
    //     'X-Shopify-Access-Token': token,
    //   },
    //   data: JSON.stringify({
    //     image: {
    //       attachment: base64,
    //       filename: randomName,
    //     }
    //   })
    // });
    console.log('uploadImageToShopifyStore', res);
    return res; // { image: { id } }
  }

  async markOrderAsFulfilled({ storeUrl, token, orderId, lineIds, trackingNumber }) {
    const res = await this.apiCall(`${storeUrl}/admin/api/2023-04/orders/${orderId}/fulfillment_orders.json`, 'get', token);
    const fulfillment_orders = res.fulfillment_orders;
    console.log("markOrderAsFulfilled_orders", fulfillment_orders);
    if (!fulfillment_orders || fulfillment_orders.length === 0) return {};
    const findFulfillmentLines = fulfillment_orders[0].line_items.filter(v => lineIds.includes(v.line_item_id));
    console.log("markOrderAsFulfilled_lineItems", findFulfillmentLines);
    if (findFulfillmentLines?.length) {
      const res2 = await this.apiCall(
        `${storeUrl}/admin/api/2023-01/fulfillments.json`,
        'post',
        token,
        {
          fulfillment: {
            message: 'The package was shipped.',
            notify_customer: false,
            tracking_info: {
              number: trackingNumber,
              url: `https://www.royalmail.com/track-your-item#/tracking-results/${trackingNumber}`,
              company: "royalmail"
            },
            line_items_by_fulfillment_order: [
              {
                fulfillment_order_id: fulfillment_orders[0].id,
                fulfillment_order_line_items: findFulfillmentLines.map(line => ({
                  id: line.id,
                  quantity: line.quantity,
                }))
              }
            ]
          }
        }
      );
      return res2;
    }
    throw new Error("Line items not found")
  }

  async cancelOrder({ storeUrl, token, orderId }) {
    try {
      const res = await this.apiCall(`${storeUrl}/admin/api/2024-07/orders/${orderId}/cancel.json`, 'post', token);
      console.log("cancelShopifyOrder_res", res?.notice);
      return res;
    } catch (error) {
      console.log("cancelShopifyOrder_error", error.response?.data || error);
    }
  }

  async checkFulfillmentService(url, token) {
    const apiCall = await this.apiCall(`${url}/admin/api/2023-04/fulfillment_services.json?scope=all`, 'get', token);
    const { fulfillment_services } = apiCall;
    const findBGFulfillment = fulfillment_services.find(v => v.name === "BG Fulfillment");
    if (!findBGFulfillment) {
      const createRes = await this.apiCall(`${url}/admin/api/2023-04/fulfillment_services.json`, 'post', token, {
        "fulfillment_service": {
          "name": "BG Fulfillment",
          "callback_url": `https://${process.env.DEV ? 'bg-dev.' : 'bg-production.'}bottledgoose.co.uk/api/online-stores/shopify-app-fulfillment-service`,
          "inventory_management": false,
          "tracking_support": false,
          "requires_shipping_method": true,
          "format": "json",
          "permits_sku_sharing": false,
          "fulfillment_orders_opt_in": true,
        }
      });
    }
  }

  async getProductInventoryStatus({ storeUrl, token, productId }) {
    try {
      // Get product details including inventory information
      const productData = await this.apiCall(
        `${storeUrl}/admin/api/2023-04/products/${productId}.json`,
        'get',
        token
      );

      const { product } = productData;

      if (!product) {
        throw new Error('Product not found');
      }

      return {
        product_id: product.id,
        product_title: product.title,
        variants: product.variants,
        // Product is considered tracked if any variant is tracked
        is_inventory_tracked: product.variants.some(i => i.inventory_management === 'shopify')
      };

    } catch (error) {
      console.log("getProductInventoryStatus_error", error.response?.data || error);
      throw error;
    }
  }

  async disableProductInventoryTracking({ storeUrl, token, productId, variantId }) {
    try {
      // Get the variant's inventory item ID first
      const productData = await this.apiCall(
        `${storeUrl}/admin/api/2023-04/products/${productId}/variants/${variantId}.json`,
        'get',
        token
      );

      const inventoryItemId = productData.variant.inventory_item_id;

      // Update the inventory tracking
      const response = await this.apiCall(
        `${storeUrl}/admin/api/2023-04/inventory_items/${inventoryItemId}.json`,
        'put',
        token,
        {
          inventory_item: {
            id: inventoryItemId,
            tracked: false
          }
        }
      );

      return response.inventory_item;
    } catch (error) {
      console.log("setProductInventoryTracking_error", error.response?.data || error);
      throw error;
    }
  }
}

export default new ShopifyHelper();
