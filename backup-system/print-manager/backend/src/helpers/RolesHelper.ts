class RolesHelper {
  isAdmin = (user: any) => user.role === 'admin';

  isReseller = (user: any) => user.role === 'reseller';

  isUser = (user: any) => user.role === 'user';

  isGuess = (user: any) => user.role === 'guess';

  isNoRoles = (user: any) => !this.isAdmin(user) && !this.isReseller(user) && !this.isUser(user);

  isUserOrReseller = (user: any) => this.isUser(user) || this.isReseller(user);

  getResellerId = (user: any) => this.isUser(user) ? user.resellerId : user.id;
}

export default new RolesHelper();
