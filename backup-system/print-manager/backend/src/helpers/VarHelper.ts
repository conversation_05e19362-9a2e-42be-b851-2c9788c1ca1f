import { TAPIValidationError, TCandleStickerInfo, TOnlineStore, TProduct, TUser } from 'type';
import { TPackingSlip } from 'type/TPackingSlip';
import { createApi } from 'unsplash-js';

require('es6-promise').polyfill();
require('isomorphic-fetch');
const { uniqueNamesGenerator, adjectives, animals } = require('unique-names-generator');

const UNSPLASH_KEY = {
  ACCESS: 'TPQ2_JT-TMUEisMVsjTn0HhDkdTav5-G8Iv3jREikOw',
  SECRET: 'oD2yaritKTZ2QuhVvEkOz8UCG0NvhOimplGli4XJwKI',
}

export const ITLProductShopifyToBG = {
  '14920479506808': {
    productId: '455823563335',
    designId: '904840447042',
  },
  '14920479539576': {
    productId: '240045286973',
    designId: '28861878434',
  },
}

class VarHelper {

  unsplash;

  genId() {
    const id = Math.floor(Math.random() * 1000000) + '' + Math.floor(Math.random() * 1000000);
    return id;
  }

  validateVar(input, joiSchema): undefined | TAPIValidationError {
    // console.log('validateVar input', input);
    // console.log('validateVar joiSchema', joiSchema);
    if (!joiSchema || !joiSchema.validate)
      return {
        details: [
          {
            message: "invalid schema",
            path: ["joiSchema"],
            type: "any",
            context: undefined,
          },
        ],
      };
    const { error } = joiSchema.validate(input);
    return error;
  }

  getRandomImages = async (category, number) => {
    if (!this.unsplash) {
      this.unsplash = createApi({
        accessKey: UNSPLASH_KEY.ACCESS,
      });
    }

    const res = await this.unsplash.search.getPhotos({
      query: category || 'animals',
      page: 1,
      perPage: number,
      color: 'green',
      orientation: 'portrait',
    });
    // console.log('res', res);

    if (res.response.results) {
      return res.response.results.map(val => ({
        id: val.id,
        url: val.urls.small.replace('w=400', 'w=300'),
        height: val.height * 300 / val.width,
        width: 300,
      }));
    }

    return [];
  }

  randomName = () => {
    const shortName = uniqueNamesGenerator({
      dictionaries: [adjectives, animals],
      separator: ' ',
      length: 2
    });

    return shortName;
  }

  removeUndefinedField = (obj) => {
    for (let key in obj) {
      if (obj[key] === undefined) {
        delete obj[key];
      }
    }
    return obj;
  }

  fourDigitsNumber = (n) => {
    if (n > 1000) return String(n);
    if (n > 100) return '0' + String(n);
    if (n > 10) return '00' + String(n);
    return '000' + String(n);
  }

  validateEmail = (email) => {
    return String(email)
      .toLowerCase()
      .match(
        /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|.(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
      );
  };

  genShortId = (length) => {
    let result = '';
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_';
    const charactersLength = characters.length;
    let counter = 0;
    while (counter < length) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength));
      counter += 1;
    }
    return result;
  }

  convertObjKeysToCapital = (obj) => {
    // convert key from camelCase to CapitalCase
    const newObj = {};
    for (const key in obj) {
      newObj[key.charAt(0).toUpperCase() + key.slice(1)] = obj[key];
    }
    return newObj;
  }

  formatBalance = (balance: number) => (Math.round(balance * 100) / 100).toFixed(2);

  calculateDPI = (widthMM: number, heightMM: number): number => {
    const minDPI = 300;
    const inchToMM = 25.4;

    let dpi = minDPI;
    while (true) {
      const pixelPerMM = dpi / inchToMM;
      const calculatedWidth = widthMM * pixelPerMM;
      const calculatedHeight = heightMM * pixelPerMM;
      const roundWidth = Math.round(calculatedWidth);
      const roundHeight = Math.round(calculatedHeight);

      const convertBackWidth = (roundWidth / pixelPerMM).toFixed(2);
      const convertBackHeight = (roundHeight / pixelPerMM).toFixed(2);

      if (convertBackWidth === widthMM.toFixed(2) && convertBackHeight === heightMM.toFixed(2)) {
        return dpi;
      }

      dpi += 1;
      if (dpi >= 500) return minDPI;
    }
  }

  calculateDiscountedPrice = (
    data: {
      price: number,
      quantity: number,
      discountType: string,
    },
    packPrices: {
      size: string | number,
      discount?: number,
      price?: number
    }[]
  ) => {
    const { price, quantity, discountType } = data;
    if (!packPrices) return price;
    const sortedPackPrices = packPrices.sort((a, b) => Number(b.size) - Number(a.size));
    const packPrice = sortedPackPrices.find((packPrice) => quantity >= Number(packPrice.size));
    if (!packPrice) return price;
    if (discountType === 'price') {
      return packPrice?.price || price;
    }
    return Math.round((price * (1 - (packPrice.discount || 0) / 100)) * 100) / 100;
  }

  getDefaultCandleStickerInfo = (params: {
    candleInfo: TCandleStickerInfo,
    product: TProduct,
    user: TUser,
    packingSlip: TPackingSlip,
  }): TCandleStickerInfo => {
    const { candleInfo, product, user, packingSlip } = params || {};
    const { scentName, defaultScentName, companyName, logo, address, country, address2, state, postCode } = candleInfo || {};

    const defaultName = product?.data?.candleTemplate?.includes('black') ? 'Amber Oud' : (
      product?.data?.candleTemplate?.includes('white') ? `Surf's Up` : 'English Garden'
    )

    const removeDuplicateAddress = () => {
      let fullAddress = packingSlip?.address || user?.addressLine1
      if (!fullAddress) return "";
      if (user?.country && fullAddress.includes(user?.country)) {
        fullAddress = fullAddress.replace(user?.country, '');
      }
      if (user?.town && fullAddress.includes(user?.town)) {
        fullAddress = fullAddress.replace(user?.town, '');
      }
      if (user?.postCode && fullAddress.includes(user?.postCode)) {
        fullAddress = fullAddress.replace(user?.postCode, '');
      }
      return fullAddress.split(',').filter(i => !!i.trim()).join(', ');
    }

    return {
      scentName: scentName,
      defaultScentName: defaultScentName || defaultName,
      companyName: companyName || packingSlip?.companyName || [user?.firstName, user?.lastName].filter(Boolean).join(' '),
      logo: logo || packingSlip?.companyLogo,
      address: address || removeDuplicateAddress(),
      country: country || (address ? '' : user?.country),
      address2: address2 || (address ? '' : user?.addressLine2),
      state: state || (address ? '' : user?.town),
      postCode: postCode || (address ? '' : user?.postCode),
    }
  }

  formatOriginalPrice = (originalPrice: number) => {
    const symbols = {
      '1': '1̶',
      '2': '2̶',
      '3': '3̶',
      '4': '4̶',
      '5': '5̶',
      '6': '6̶',
      '7': '7̶',
      '8': '8̶',
      '9': '9̶',
      '.': '.̶',
      ',': ',̶',
    }
    return String(this.formatBalance(originalPrice))?.split('').map(i => symbols[i] || i).join('');
  }

  formatProductNamesForStripe = (productNames: string[]): string => {
    // If a single product name is too long, truncate it
    const truncatedProductNames = productNames.map(name => {
      if (name.length > 100) {
        return name.substring(0, 100) + '...';
      }
      return name;
    });

    // Join with newlines and ensure total length is under 400
    let productNamesStr = truncatedProductNames.join('\n');
    if (productNamesStr.length > 400) {
      // If still too long, take only the first few products
      const maxProducts = Math.floor(400 / (truncatedProductNames[0]?.length || 100));
      productNamesStr = truncatedProductNames.slice(0, maxProducts).join('\n');
    }

    return productNamesStr;
  }

}

export default new VarHelper();
