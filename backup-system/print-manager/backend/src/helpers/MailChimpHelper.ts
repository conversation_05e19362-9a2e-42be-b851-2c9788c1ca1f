import { TUser } from "type";
const fs = require('fs');
const path = require('path');
const mailchimp = require("@mailchimp/mailchimp_marketing");

mailchimp.setConfig({
  apiKey: process.env.MAILCHIMP_API_KEY,
  server: process.env.MAILCHIMP_SERVER,
});

class MailChimp {
  getListInfo = async (listId: string = process.env.MAILCHIMP_LIST_ID) => {
    try {
      const response = await mailchimp.lists.getList(listId);
      return response;
    } catch (error) {
      console.error(error);
      return null;
    }
  }
  batchSubscribe = async (users: Array<TUser>, listId: string = process.env.MAILCHIMP_LIST_ID) => {
    const members = users.map(user => {
      return {
        email_address: user.email,
        status: "subscribed",
        merge_fields: {
          FNAME: user.firstName,
          LNAME: user.lastName
        }
      }
    });
    const body = {
      members,
      update_existing: true
    };
    // fs.writeFileSync(path.join(__dirname, 'batchSubscribe.json'), JSON.stringify(body, null, 2));
    const res = await mailchimp.lists.batchListMembers(listId, body);
    console.log(res);
    return res;
  }
}

export default new MailChimp();