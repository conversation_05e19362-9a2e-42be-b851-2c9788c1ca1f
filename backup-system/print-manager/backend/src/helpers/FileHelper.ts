const fs = require('fs');
const axios = require('axios');
const path = require('path');
const probe = require('probe-image-size');

class FileHelper {

  fileToBase64(filePath) {
    const buffer = fs.readFileSync(filePath);
    const base64 = Buffer.from(buffer).toString('base64');
    return base64;
  }

  base64ToFile(base64Data, filePath) {
    fs.writeFileSync(filePath, base64Data, 'base64');
  }

  async downloadFile(fileUrl, outputLocationPath, otherAxiosOptions = {}) {
    if (!fileUrl) return;
    const writer = fs.createWriteStream(outputLocationPath);

    return new Promise((resolve, reject) => {
      axios({
        method: 'get',
        url: fileUrl,
        responseType: 'stream',
        ...otherAxiosOptions,
      }).then(response => {
        response.data.pipe(writer);
        let error = null;
        writer.on('error', err => {
          error = err;
          writer.close();
          reject(err);
        });
        writer.on('close', () => {
          if (!error) {
            resolve(true);
          }
        });
      }).catch(err => {
        reject(err);
      });
    });
  }

  async getPDFFromLoadBalance(payload) {
    let s3Url = '';
    let requestId;

    // NEVER USE PUPPETEER TO GENERATE PDF FOR BG
    
    // if (!payload?.dropletUrl) {
    //   const imgSize = await probe(payload.url);

    //   const pdfRes = await axios.request({
    //     url: 'https://puppeteer.personify.tech/api/urlImage2pdf',
    //     headers: { 'Content-Type': 'application/json' },
    //     method: 'post',
    //     data: JSON.stringify({
    //       images: [{
    //         url: payload.url,
    //         width: imgSize.width,
    //         height: imgSize.height,
    //         id: 1
    //       }]
    //     }),
    //   });
    //   return pdfRes.data?.data?.[0]?.pdfUrl;
    // }

    const apiCall = await axios.request({
      url: process.env.SERVICES_HOST + '/api/bg/pts-load-balance',
      method: 'post',
      headers: {
        'Authorization': 'kgmdqtveva',
        'Content-Type': 'application/json',
        'X-Auth-Token': process.env.MICRO_API_TOKEN,
      },
      data: JSON.stringify(payload),
    });
    console.log('apiCall.data', apiCall.data);
    if (typeof apiCall.data.data === 'string') {
      s3Url = apiCall.data.data;
      return s3Url;
    }

    if (apiCall.data.data.requestId) {
      requestId = apiCall.data.data.requestId;
      await new Promise((resolve, reject) => {
        let _timeout;
        let _interval;
        let didReturn = false;
        _interval = setInterval(async() => {
          try {
            const intervalCheck = await axios.request({
              url: process.env.NOCODB_HOST + '/api/v1/db/data/v1/pmk6g5joaa863j9/PTS Requests/'+requestId,
              method: 'get',
              headers: {
                'xc-token': 'efoDr21CkrSUuEW4hSmYtwION6ZiHPRShILhTOha'
              }
            });
            const ptsRequets = intervalCheck.data;
            console.log(`Interval check: ${requestId} - ${ptsRequets.Status} - ${ptsRequets['S3 PDF']}`);
            if (['Done', 'Failed'].includes(ptsRequets.Status)) {  
              clearInterval(_interval);
              clearTimeout(_timeout);
              s3Url = ptsRequets['S3 PDF'];
              if (didReturn) return;
              didReturn = true;
              resolve(s3Url);
            }
          } catch (error) {
            console.log('Interval check PDF Balance error');
          }
        }, 1000);
        _timeout = setTimeout(async () => {
          clearInterval(_interval);
          if (didReturn) return;
          didReturn = true;
          resolve(s3Url);
        }, 1000 * 60 * 5);
      });
    }

    return s3Url;
  }
}

export default new FileHelper();
