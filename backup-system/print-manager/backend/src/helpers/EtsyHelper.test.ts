import "dotenv/config";
import Etsy from './EtsyHelper';
const fs = require('fs');
const path = require('path');

const etsy = new Etsy(
  '72049961.671tBBHHA7AxIPdEARfF_lZhFWoPo8psFGFP1jROcMEUcorvusWFSKgCRCPd-DhuLIyKC-LCfeeyHmRNdWGnhnN3lEh',
  '72049961.rwwblmogv8hr8S5Zq-UGqbzTzRuIj5JmmnBDgG7mt6Zjfc-4-4AUmihsHMXKEYn9QDpAkBPclm1XdrbyFalQ7rcMAH5'
);

describe('API call should work', () => {
  test('Call API get shop successfully', async () => {
    const data = await etsy.getShop();
    expect(data).toBeDefined();
  });
  test('Call API get listings successfully', async () => {
    const data = await etsy.getShopListings();
    // console.log('LISTING LENGTHG', data.length);
    // fs.writeFileSync(path.join(__dirname, 'etsy-listings.json'), JSON.stringify(data, null, 2));
    expect(data).toBeDefined();
  });
  test('Get receipts', async () => {
    const data = await etsy.getReceipts({});
    expect(data).toBeDefined();
    // fs.writeFileSync(path.join(__dirname, 'etsy-receipts.json'), JSON.stringify(data, null, 2));
    const dataListing = await etsy.getListing({
      listingId: data[0]?.transactions[0]?.listing_id,
    })
    expect(dataListing).toBeDefined();
  });
  test('Create draft listing and remove', async () => {
    // const listing = await etsy.createDraftListing({
    //   name: "Test listing",
    //   description: "Lorm ipsum",
    //   price: 10,
    //   sku: "d-12345678",
    // });
    // expect(listing.listing_id).toBeDefined();
    // console.log("createDraftListing", listing);
    // const data = await etsy.uploadListingImage({
    //   id: listing.listing_id,
    //   url: 'https://fastly.picsum.photos/id/240/536/354.jpg?hmac=A0X8iD7Qs5SlIlEEpYD_s5NUsoDMF1MR5VezmDV4268'
    // });
    // expect(data).toBeDefined();
    // const data = await etsy.deleteListing('1755735138');
    // console.log("xxxx", data);
  });
});
