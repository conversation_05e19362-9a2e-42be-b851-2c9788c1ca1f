import "dotenv/config";
import VarHelper from './VarHelper';

const packPrices = [
  { size: 1, price: 10, discount: 0 },
  { size: 3, price: 8, discount: 30 },
  { size: 4, price: 8, discount: 33.3333 },
  { size: 5, price: 6, discount: 50 },
  { size: 7, price: 5, discount: 60 },
  { size: 9, price: 5, discount: 66.6666 },
]

describe('VarHelper functions', () => {
  test('calculateDiscountedPrice', async () => {
    expect(VarHelper.calculateDiscountedPrice({
      price: 10, quantity: 1, discountType: 'price'
    }, packPrices)).toEqual(10);
    expect(VarHelper.calculateDiscountedPrice({
      price: 10, quantity: 3, discountType: 'price'
    }, packPrices)).toEqual(8);
    expect(VarHelper.calculateDiscountedPrice({
      price: 10, quantity: 5, discountType: 'price'
    }, packPrices)).toEqual(6);
    expect(VarHelper.calculateDiscountedPrice({
      price: 10, quantity: 7, discountType: 'price'
    }, packPrices)).toEqual(5);

    expect(VarHelper.calculateDiscountedPrice({
      price: 10, quantity: 1, discountType: 'percentage'
    }, packPrices)).toEqual(10);
    expect(VarHelper.calculateDiscountedPrice({
      price: 10, quantity: 3, discountType: 'percentage'
    }, packPrices)).toEqual(7);
    expect(VarHelper.calculateDiscountedPrice({
      price: 10, quantity: 5, discountType: 'percentage'
    }, packPrices)).toEqual(5);
    expect(VarHelper.calculateDiscountedPrice({
      price: 10, quantity: 7, discountType: 'percentage'
    }, packPrices)).toEqual(4);
    expect(VarHelper.calculateDiscountedPrice({
      price: 10, quantity: 9, discountType: 'percentage'
    }, packPrices)).toEqual(3.33);
    expect(VarHelper.calculateDiscountedPrice({
      price: 10, quantity: 4, discountType: 'percentage'
    }, packPrices)).toEqual(6.67);
  });
});
