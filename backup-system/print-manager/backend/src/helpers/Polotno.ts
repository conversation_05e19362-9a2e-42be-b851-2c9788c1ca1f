import AWSHelper from "./AWSHelper";
import FileHelper from "./FileHelper";
const fs = require('fs');
const { createInstance } = require('polotno-node');

class Polotno {

  constructor() {
    (async() => {
      this.instance = await createInstance({
        key: 'LXWGO97IZx0UfVQg4uH9',
      });
    })();
  }
  instance;

  jsonToImageBase64 = async (json) => {
    const imageBase64 = await this.instance.jsonToImageBase64(json);
    return imageBase64;
  }
  jsonToS3 = async (json, keyPath) => {
    const imageBase64 = await this.instance.jsonToImageBase64(json);
    const filePath = new Date().getTime() + '-temp.png';
    FileHelper.base64ToFile(imageBase64, filePath);
    const url = await AWSHelper.upload({
      filePath,
      key: keyPath,
    });
    setTimeout(() => {
      fs.unlink(filePath, () => {});
    }, 1000);
    return url;
  }
}

export default new Polotno();
