import "dotenv/config";
import AWSHelper from "./AWSHelper";

describe('AWSHelper check image quality', () => {
  test('Handle empty image url without crash', async () => {
    let error
    try {
      const res = await AWSHelper.detectModerationLabel('https://print-manager-media.s3.eu-west-1.amazonaws.com/cm5vdbzrw00023p6l63mqw03h-0.png');
    } catch (err) {
      error = err
    }
    await new Promise((resolve) => setTimeout(resolve, 1000))
    expect(error).toBeDefined();
  });
});
