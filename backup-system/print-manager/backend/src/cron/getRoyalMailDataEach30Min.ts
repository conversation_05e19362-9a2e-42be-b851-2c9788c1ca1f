var CronJob = require('cron').CronJob;
const moment = require('moment-timezone');
const fs = require('fs');
const path = require('path');
const axios = require('axios');
const { exec } = require('child_process');

import { listOrder, listPipeline, updateOrderStatus } from 'api/bg/services';
import { fetchRoyalMailOrders } from 'api/online-stores/getRoyalMailOrders';
import { handleUpdateInProductionOrders } from 'api/online-stores/updateInProductionOrders';

let retryTimeout = setTimeout(() => { }, 0)
let runCypressTimeout = setTimeout(() => { }, 0)

export const getDeliveredStatus = async (orders) => {
  try {
    const res = fs.readFileSync(path.join(__dirname, '../../interceptedRequest.json'), 'utf8');
    const { request } = JSON.parse(res);
    let body = { ...request.body };
    body.paging.pageSize = 500;
    const apiCallRes = await axios.request({
      url: request.url,
      method: request.method,
      headers: request.headers,
      data: JSON.stringify(body)
    })
    const rmOrders = apiCallRes.data.data;
    await Promise.all(orders.map(async o => {
      let order = { ...o };
      if (order.Pipelines.length === 0) return;
      const last = order.Pipelines[order.Pipelines.length - 1];
      if (last.SharedData?.trackingNumber && order.StageStatus !== "Delivered") {
        const RMorder = rmOrders.find(rm => String(rm.trackingOrConfirmationNumber) === String(last.SharedData?.trackingNumber).trim());
        if (RMorder?.shippingTrackingStatus === "Delivered") {
          console.log("cron/getRoyalMailDataEach30Min.ts/updateDelivered", order.Id, last.SharedData?.trackingNumber);
          await updateOrderStatus({
            id: order.Id,
            StageStatus: "Delivered",
          })
        }
      }
      return;
    }));
  } catch (error) {
    console.log("getDeliveredStatus_err", error);
    clearTimeout(runCypressTimeout)
    runCypressTimeout = setTimeout(() => {
      exec('npx cypress run');
    }, 5000)
    clearTimeout(retryTimeout);
    retryTimeout = setTimeout(() => {
      getDeliveredStatus(orders);
    }, 5 * 60 * 1000)
  }
}

export const execute = async () => {
  try {
    const params: any = {
      supported: true,
      offset: 0,
      limit: 999,
      stage: "In Production,Fulfillment",
      startDate: moment().subtract(30, 'd').format('YYYY-MM-DD'),
      endDate: moment().format('YYYY-MM-DD'),
      dateType: 'UpdatedAt',
    }
    const res = await listOrder({
      ...params,
    })
    let orders = res.withPipelines;
    for (let i = 0; i < orders.length; i++) {
      if (orders[i]?.Pipelines?.length) continue;
      const resPipeline = await listPipeline({
        limit: 10,
        offset: 0,
        orderId: orders[i]?.['Order ID'],
      })
      console.log('cron/getRoyalMailDataEach30Min.ts/resPipeline', resPipeline?.rows.length);
      orders[i] = {
        ...orders[i],
        Pipelines: resPipeline?.data.list
      }
    }
    const updatedOrders = await handleUpdateInProductionOrders(orders);
    console.log("updatedOrders", updatedOrders);
    await fetchRoyalMailOrders();
    getDeliveredStatus(orders);
  } catch (error) {
    console.error("getRoyalMailDataEach30Min_err", error);
  }

  // resubmit RM orders
  // const missingRMIdOrders = orders.filter(i => {
  //   if (i.Pipelines.length) {
  //     const last = i.Pipelines[i.Pipelines.length - 1];
  //     return !last.SharedData?.royalMailOrderIdentifier && !last.SharedData?.isTestOrder && !last.SharedData?.skipped;
  //   }
  //   return false;
  // })
  // console.log("missingRMIdOrders", missingRMIdOrders?.map(i => i['Order ID']));
  // for (let i = 0; i < missingRMIdOrders.length; i++) {
  //   const res = await requestMicroApi(`/api/bg/trigger-dispatch-royalmail?orderId=${missingRMIdOrders[i]['Order ID']}&force=1`, {
  //     method: 'POST',
  //   });
  // }
};

export const getRoyalMailDataEach30Min = () => {
  if (process.env.DEV) return;
  var job = new CronJob('*/30 * * * *', function () {
    console.log('getRoyalMailDataEach30Min');
    execute();
  }, null, false, 'Europe/London');
  job.start();
  // execute();
};
