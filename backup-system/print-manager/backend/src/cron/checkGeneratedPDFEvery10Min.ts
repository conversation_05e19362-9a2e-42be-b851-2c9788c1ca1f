var CronJob = require('cron').CronJob;
const moment = require('moment-timezone');
const fs = require('fs');
const path = require('path');
const axios = require('axios');
const { exec } = require('child_process');

import { listOrder, listPipeline, rerunJob } from 'api/bg/services';

let warningOrders = {};
export const handleCheckGeneratedPDFOrders = async (orders) => {
  for (let i = 0; i < orders.length; i++) {
    try {
      let order = { ...orders[i] };
      if (order.Pipelines.length === 0) continue;
      const last = order.Pipelines[order.Pipelines.length - 1];
      // @ts-ignore
      const generatePDFJob = Object.keys(last.Jobs || {}).find(jobId => last.Jobs?.[jobId]?.Title === "Generate PDF");
      const canBeProcessedItems = last.SharedData?.canBeProcessedItems || [];
      for (let i = 0; i < canBeProcessedItems.length; i++) {
        const item = canBeProcessedItems[i];
        if (!item?.pdf && !warningOrders[order['Order ID']]) {
          warningOrders[order['Order ID']] = true;
          if (generatePDFJob) {
            await rerunJob({
              jobId: generatePDFJob,
              pipelineId: last.Id,
              thisJobOnly: true,
            })
            console.error("Found empty pdf, rerun job", generatePDFJob, order['Order ID']);
          }
        }
        if (!item?.pdf && warningOrders[order['Order ID']]) {
          console.error("Found empty pdf, rerun failed", order['Order ID']);
          try {
            // Save failed order to log file
            const logDir = path.join(__dirname, '../../logs');
            if (!fs.existsSync(logDir)) {
              fs.mkdirSync(logDir, { recursive: true });
            }
            const logFile = path.join(logDir, `orders_${moment().format('YYYY-MM-DD_HH-mm')}.json`);
            let existingData = [];
            if (fs.existsSync(logFile)) {
              existingData = JSON.parse(fs.readFileSync(logFile));
            }
            existingData.push(order);
            fs.writeFileSync(logFile, JSON.stringify(existingData, null, 2));
          } catch (error) {
            console.error("checkGeneratedPDFEvery10Min_err", error);
          }
          console.log(`Rerun PDF job failed - Order ID: ${order['Order ID']}`, order);
          await axios.request({
            url: 'https://chat.personify.tech/sendMessage',
            method: 'post',
            headers: { 'Content-Type': 'application/json' },
            data: JSON.stringify({
              "message_thread_id": 1348,
              "text": `Rerun PDF job failed - Order ID: ${order['Order ID']}`
            }),
          })
        }
      }
    } catch (error) {
      console.log("handleCheckGeneratedPDFOrders/error", error?.message || error);
    }
  }
}

export const execute = async () => {
  try {
    const params: any = {
      supported: true,
      offset: 0,
      limit: 99,
      stage: "Pre Production,In Production",
      startDate: moment().subtract(10, 'd').format('YYYY-MM-DD'),
      endDate: moment().format('YYYY-MM-DD'),
      dateType: 'UpdatedAt',
    }
    const res = await listOrder({
      ...params,
    })
    let orders = res.withPipelines;
    for (let i = 0; i < orders.length; i++) {
      if (orders[i]?.Pipelines?.length) continue;
      const resPipeline = await listPipeline({
        limit: 10,
        offset: 0,
        orderId: orders[i]?.['Order ID'],
      })
      console.log('cron/checkGeneratedPDFEvery10Min.ts/resPipeline', resPipeline?.rows.length);
      orders[i] = {
        ...orders[i],
        Pipelines: resPipeline?.rows
      }
    }
    await handleCheckGeneratedPDFOrders(orders);
  } catch (error) {
    console.error("checkGeneratedPDFEvery10Min_err", error);
  }
};

export const checkGeneratedPDFEvery10Min = () => {
  // if (process.env.DEV) return;
  var job = new CronJob('*/10 * * * *', function () {
    console.log('checkGeneratedPDFEvery10Min');
    execute();
  }, null, false, 'Europe/London');
  job.start();
  // execute();
};
