var CronJob = require('cron').CronJob;
import { Op, Sequelize } from 'sequelize';
import { DB } from 'db';
import { MailChimp } from 'helpers';

export const execute = async () => {
  if (!process.env.MAILCHIMP_API_KEY) {
    console.log('MAILCHIMP_API_KEY is not set, skipping...');
    return;
  }
  const resellers = await DB.User.findAll({
    where: {
      role: 'reseller',
    },
    raw: true,
  });

  const resellerEmails = resellers.filter(reseller => Boolean(reseller.email));
   
  // split to chunk of 500
  const chunks = [];
  const chunkSize = 500;
  for (let i = 0; i < resellerEmails.length; i += chunkSize) {
    chunks.push(resellerEmails.slice(i, i + chunkSize));
  }

  console.log('list resellers', resellers.length);
  for (let i=0; i<chunks.length; i++) {
    await MailChimp.batchSubscribe(chunks[i]);
  }
};

export const syncResellerToMailChimpMailListEveryDay = () => {
  var job = new CronJob('00 00 00 * * *', function() {
    console.log('syncResellerToMailChimpMailListEveryDay');
    execute();
  }, null, false, 'Europe/London');
  job.start();
  execute();
};