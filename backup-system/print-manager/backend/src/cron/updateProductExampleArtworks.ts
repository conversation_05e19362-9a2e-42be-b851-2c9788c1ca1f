var CronJob = require('cron').CronJob;
import { Op, Sequelize } from 'sequelize';
import { DB } from 'db';
import _ from 'lodash';

const fatherDayImages = [
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/DaddyCool-SmallThermosFlask.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/DadJokes-SmallThermosFlask.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/DadJoke-SmallThermosFlask.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/DadFuel-SmallThermosFlask.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/KingofJokes-LargeThermosFlask.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/DaddyCool-LargeThermosFlask.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/DadJoke-LargeThermosFlask.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/DadFuel-LargeThermosFlask.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/tennis-thermosbottle.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/rugby-thermosbottle.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/kingofdads-thermosbottle.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/golf-thermosbottle.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/football-thermosbottle.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/dadjokes-thermosbottle.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/dadjoke-thermosbottle.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/dadfuel-thermosbottle.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/daddycool-thermosbottle.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/KingofDads-Scuba.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/DaddyCool-Scuba.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/DadJokes-Scuba.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/DadJoke-Scuba.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/DadFuel-Scuba.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/tennis-PintGlass.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/rugby-PintGlass.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/golf-pint-glass-PintGlass.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/football-pint-glass-PintGlass.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/BeerTime-PintGlass.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/King-of-Dads-PintGlass.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/King-of-Dad-Jokes-PintGlass.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/Daddy-Cool-PintGlass.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/Dad-joke-loading-PintGlass.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/Dad-Fuel-PintGlass.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/tennis-highball.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/rugby-highball.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/kingofdads-highball.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/golf-highball.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/football-highball.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/dadjokes-highball.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/dadjoke-highball.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/dadfuel-highball.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/daddycool-highball.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/beertime-highball.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/Tennis-Carafe.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/Rugby-Carafe.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/KingofDads-Carafe.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/Golf-Carafe.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/Football-Carafe.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/DaddyCool-Carafe.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/DadJokes-Carafe.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/DadJoke-Carafe.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/KingofDads-CanGlass.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/DaddyCool-CanGlass.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/DadJokes-CanGlass.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/DadJoke-CanGlass.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/DadFuel-CanGlass.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/BeerTime-CanGlass.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/KingofJokes-bistro.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/KingofDads-bistro.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/DaddyCool-bistro.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/KingofDads-Aqua.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/DaddyCool-Aqua.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/DadJokes-Aqua.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/DadJoke-Aqua.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/DadFuel-Aqua.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/DaddyCool-Akaw.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/DadJokes-Akaw.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/DadJoke-Akaw.png",
  "https://print-manager-media.s3.eu-west-1.amazonaws.com/files/bg-example-artworks/DadFuel-Akaw.png",
]

const defaultImages = [
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/Candle-Brand-white.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/pinkfloral-ThermosWaterBottle.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/Mummy-ThermosWaterBottle.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/MumFloral-ThermosWaterBottle.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/Mum4-ThermosWaterBottle.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/Mum-ThermosWaterBottle.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/Mum-floral-SmallThermosFlask.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/floralsnowdrops-SmallThermosFlask.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/FloralMum-SmallThermosFlask.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/Mummy-SmallThermosFlask.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/Mummy-scubabottle.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/Mum4-SmallThermosFlask.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/Mum-SmallThermosFlask.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/Mum-PintGlass.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/Mummy-largethermos.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/Mumfloral-PintGlass.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/Mum4-PintGlass.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/Mum4-largethermos.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/Mum-LargeThermosFlask.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/Mum-largethermos.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/floralsnowdrops-HighballGlass.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/floralsnowdrop-largethermos.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/FloralMum-largethermos.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/MumStamp-HighballGlass.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/Mum-HighballGlass.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/FloralMum-HighballGlass.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/floralmum-Carafe.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/Floral%20Stamp-Carafe.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/Floral%20Snowdrops%20Carafe.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/PinkFloral-Carafe.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/mumstamps-Candle.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/MumStamp-Carafe.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/Mum4-Carafe.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/Mummy-Candle.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/Mum4-Candle.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/Mum-hearts-Candle.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/Mum-Candle.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/Mumstamp-CanGlass.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/MummyHearts-CanGlass.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/floralMum-Candle.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/floralmothersday-Candle.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/floralsnowdrop-CanGlass.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/Mum-CanGlass.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/mum4-CanGlass.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/mumstamps-bistro.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/MumHearts-CanGlass.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/Mumfloral-CanGlass.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/MumFloral-AquaBottle.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/floral-CanGlass.png",

  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/ThermosBottle-Wrap.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/ThermosBottle-Brand.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/SmallThermosFlask-Brand.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/PintGlass-wrap.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/PintGlass-Brand.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/LargeThermosFlask-Wrap.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/LargeThermosFlask-Brand.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/Highball-Wrap.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/Highball-Brand.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/ClearCandle-Brand.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/Carafe-Wrap.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/Carafe-Brand.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/CanGlass-Wrap.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/CanGlass-Brand.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/Candle-Wrap.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/Candle-Brand.png",
  "https://print-manager-media.s3.amazonaws.com/bg-default-images-design/Carafe-Mum.png",
]

const getDefaultArtworks = (productName: string) => {
  const productNameLower = productName.toLowerCase();
  const filtered = defaultImages.filter(url => {
    const urlLower = url.toLowerCase();
    if (urlLower.includes('candle')) {
      return productNameLower.includes('candle');
    }
    if (urlLower.includes('highball')) {
      return productNameLower.includes('highball');
    }
    if (urlLower.includes('thermosbottle') || urlLower.includes('thermoswaterbottle')) {
      return productNameLower.includes('thermos') && productNameLower.includes('bottle');
    }
    if (urlLower.includes('smallthermosflask') || urlLower.includes('smallthermos')) {
      return productNameLower.includes('thermos') && productNameLower.includes('small');
    }
    if (urlLower.includes('largerthermosflask') || urlLower.includes('largethermos')) {
      return productNameLower.includes('thermos') && productNameLower.includes('large');
    }
    if (urlLower.includes('pintglass')) {
      return productNameLower.includes('pint') && productNameLower.includes('glass');
    }
    if (urlLower.includes('clearcandle')) {
      return productNameLower.includes('candle') && productNameLower.includes('clear');
    }
    if (urlLower.includes('carafe')) {
      return productNameLower.includes('carafe');
    }
    if (urlLower.includes('canglass')) {
      return productNameLower.includes('can') && productNameLower.includes('glass');
    }
    if (urlLower.includes('scubabottle')) {
      return productNameLower.includes('scuba') && productNameLower.includes('bottle');
    }
    if (urlLower.includes('aquabottle')) {
      return productNameLower.includes('aqua') && productNameLower.includes('bottle');
    }
    if (urlLower.includes('bistro')) {
      return productNameLower.includes('bistro');
    }

    return false;
  });
  return filtered
}

const getFatherDayArtworks = (productName: string) => {
  const productNameLower = productName.toLowerCase();
  const filtered = fatherDayImages.filter(url => {
    const urlLower = url.toLowerCase();
    if (urlLower.includes('akaw')) {
      return productNameLower.includes('akaw');
    }
    if (urlLower.includes('aqua')) {
      return productNameLower.includes('aqua');
    }
    if (urlLower.includes('bistro')) {
      return productNameLower.includes('bistro');
    }
    if (urlLower.includes('canglass')) {
      return productNameLower.includes('can') && productNameLower.includes('glass');
    }
    if (urlLower.includes('carafe')) {
      return productNameLower.includes('carafe');
    }
    if (urlLower.includes('candle')) {
      return productNameLower.includes('candle');
    }
    if (urlLower.includes('highball')) {
      return productNameLower.includes('highball');
    }
    if (urlLower.includes('pintglass')) {
      return productNameLower.includes('pint') && productNameLower.includes('glass');
    }
    if (urlLower.includes('scuba')) {
      return productNameLower.includes('scuba');
    }
    if (urlLower.includes('thermosbottle') || urlLower.includes('thermoswaterbottle')) {
      return productNameLower.includes('thermos') && productNameLower.includes('bottle');
    }
    if (urlLower.includes('smallthermosflask') || urlLower.includes('smallthermos')) {
      return productNameLower.includes('thermos') && productNameLower.includes('small');
    }
    if (urlLower.includes('largerthermosflask') || urlLower.includes('largethermos')) {
      return productNameLower.includes('thermos') && productNameLower.includes('large');
    }
    if (urlLower.includes('clearcandle')) {
      return productNameLower.includes('candle') && productNameLower.includes('clear');
    }
    return false;
  });
  return filtered
}

export const execute = async () => {

  const products = await DB.Product.findAll({
    where: {}
  });

  for (let i = 0; i < products.length; i++) {
    const product = products[i];
    // if (product?.data?.exampleArtworks?.length) continue;
    console.log(product.name);
    const defaultArtworks = getDefaultArtworks(product.name);
    const fatherDayArtworks = getFatherDayArtworks(product.name);
    if (defaultArtworks.length === 0 && fatherDayArtworks.length === 0) continue;
    const exampleArtworks = [...fatherDayArtworks, ...(product?.data?.exampleArtworks || []), ...defaultArtworks];
    const uniqueArtworks = [...new Set(exampleArtworks)];

    console.log(uniqueArtworks);
    await product.update({
      data: {
        ...(product.data || {}),
        exampleArtworks: uniqueArtworks
      }
    });
    await product.save();
  }
};

export const updateProductExampleArtworks = () => {
  // var job = new CronJob('00 00 00 * * *', function() {
  //   console.log('removeEmptyDesignEveryDay');
  //   execute();
  // }, null, false, 'Europe/London');
  // job.start();
  execute();
};