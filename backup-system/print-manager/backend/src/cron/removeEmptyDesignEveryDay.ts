var CronJob = require('cron').CronJob;
import { Op, Sequelize } from 'sequelize';
import { DB } from 'db';

export const execute = async () => {
  const list = await DB.Design.findAll({
    where: {
      data: null,
      // @ts-ignore
      createdAt: {
        [Op.lt]: Sequelize.literal("NOW() - INTERVAL '24 HOURS'"),
      }
    }
  });
  console.log(list.length);
  for (let i=0; i<list.length; i++) {
    await list[i].destroy();
  }
  if (list.length > 0) {
    console.log(`CRON: DELETED ${list.length} EMPTY DESIGN`);
  }
};

export const removeEmptyDesignEveryDay = () => {
  var job = new CronJob('00 00 00 * * *', function() {
    console.log('removeEmptyDesignEveryDay');
    execute();
  }, null, false, 'Europe/London');
  job.start();
  execute();
};