import { getRoyalMailDataEach30Min } from './getRoyalMailDataEach30Min';
import { removeEmptyDesignEveryDay } from './removeEmptyDesignEveryDay';
import { updateWalletBalanceEach10Min } from './updateWalletBalanceEach10Min';
import { syncResellerToMailChimpMailListEveryDay } from './syncResellerToMailChimpMailListEveryDay';
import { getEtsyReceiptEvery5Min } from './getEtsyReceiptsEvery5Min';
import { checkGeneratedPDFEvery10Min } from './checkGeneratedPDFEvery10Min';
import { checkPublishRoyalMailEvery5Min } from './checkPublishRoyalMailEvery5Min';
import { updateResellerSalesTotalEachHour } from './updateResellerSalesTotalEachHour';
import { updateUserLastLoggedIn } from './updateUserLastLoggedIn';
import { verifyEtsyReceiptsEvery30Min } from './verifyEtsyReceiptsEvery30Min';
import { updateProductExampleArtworks } from './updateProductExampleArtworks';
// import { syncOrdersFromShopify } from './syncOrdersFromShopify';

export const startCron = () => {
  if (!process.env.DEV) {
    verifyEtsyReceiptsEvery30Min();
    getRoyalMailDataEach30Min();
    // syncResellerToMailChimpMailListEveryDay();
    getEtsyReceiptEvery5Min();
    checkGeneratedPDFEvery10Min();
    checkPublishRoyalMailEvery5Min();
  }
  removeEmptyDesignEveryDay();
  updateWalletBalanceEach10Min();
  updateResellerSalesTotalEachHour();
  updateUserLastLoggedIn();
  // updateProductExampleArtworks();
};
