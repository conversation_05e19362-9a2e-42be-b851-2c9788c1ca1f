var CronJob = require('cron').CronJob;
const fs = require('fs');
import axios from 'axios';
import { DB } from 'db';
import { OnlineStoreModel } from 'db/Schema.OnlineStore';
import { objectToQueryString, request<PERSON><PERSON><PERSON><PERSON><PERSON>, VarHelper } from 'helpers';
import Etsy from 'helpers/EtsyHelper';
import { TEtsyListing, TEtsyReceipt } from 'type';

const sendTeleNoti = async (message: string) => {
  await axios.request({
    url: 'https://chat.personify.tech/sendMessage',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: JSON.stringify({
      "message_thread_id": 1348,
      "text": message
    }),
  })
}

const setupEtsyClient = async (store: OnlineStoreModel) => {
  const { etsyAccessToken, etsyRefreshToken } = store.data || {};
  let needRefreshToken = Date.now() - new Date(store.updatedAt).getTime() > 24 * 60 * 60 * 1000;
  let etsy = new Etsy(etsyAccessToken, etsyRefreshToken);
  if (needRefreshToken) {
    const newTokens = await etsy.getNewToken();
    if (newTokens?.accessToken) {
      store.data = {
        ...store.data,
        etsyAccessToken: newTokens.accessToken,
        etsyRefreshToken: newTokens.refreshToken,
      }
      etsy = new Etsy(newTokens.accessToken, newTokens.refreshToken);
      await store.save();
    }
  }
  return etsy;
}

const handleEachStore = async (store: OnlineStoreModel) => {
  try {
    const { etsyAccessToken, etsyRefreshToken } = store.data || {};
    if (!etsyAccessToken || !etsyRefreshToken) {
      console.log(`Etsy access token or refresh token not found - ${store.url}`);
      return;
    }
    const etsy = await setupEtsyClient(store)
    await new Promise(resolve => setTimeout(resolve, 500));
    const shopInfo = await etsy.getMe();
    const shopId = shopInfo?.shop_id;
    if (!shopId) {
      return;
    }
    const sevenDaysAgo = Math.floor(Date.now() / 1000) - (7 * 24 * 60 * 60);
    await new Promise(resolve => setTimeout(resolve, 500));
    const receipts: TEtsyReceipt[] = await etsy.getAllReceipts({ shopId, minCreated: sevenDaysAgo });
    console.log(`getEtsyReceiptsEvery5Min - ${store.url} - receipts: ${receipts?.length}`);
    if (!receipts?.length) {
      return;
    }

    const checkedReceipts = await DB.GeneralData.findOne({
      where: {
        name: 'checked-etsy-receipts',
        field1: String(store.id),
      }
    })
    let checkedReceiptsCache = checkedReceipts?.data || {};

    let receiptsWithListing = []
    for (const receipt of receipts) {
      if (checkedReceiptsCache[receipt.receipt_id]) continue;
      let failedFlag = false;
      const allListings = await Promise.all(receipt.transactions.map(async (transaction) => {
        try {
          await new Promise(resolve => setTimeout(resolve, 500));
          const listing: TEtsyListing = await etsy.getListing({
            listingId: transaction.listing_id,
            shopId,
          });
          if (!listing) {
            failedFlag = true;
            return;
          }
          // if (!!manualListingMapping[String(listing.listing_id)]) {
          //   return listing;
          // }
          // if (listing.tags?.some(tag => String(tag).startsWith("d-"))) {
          //   return listing;
          // }
          return {
            ...listing,
            transaction_id: transaction.transaction_id,
          };
        } catch (error) {
          console.log('getEtsyReceiptsEvery5Min - getListingError', error?.message || JSON.stringify(error));
          failedFlag = true;
          return;
        }
      }));
      if (!failedFlag) {
        checkedReceiptsCache[receipt.receipt_id] = true;
        if (allListings.filter(Boolean)?.length) {
          receiptsWithListing.push({
            ...receipt,
            supportedListings: allListings.filter(Boolean),
          })
        }
      } else {
        sendTeleNoti(`getEtsyReceiptFailed - ${store.url} - receipt: ${receipt.receipt_id} - failed`);
      }
    }

    // // only keep last 2000 receipts
    // let newCheckedReceiptsData = {};
    // Object.keys(checkedReceiptsData)
    //   .sort((a, b) => Number(b) - Number(a))
    //   .slice(0, 2000)
    //   .forEach((key) => {
    //     newCheckedReceiptsData[key] = true;
    //   })
    // save checked receipts
    if (checkedReceipts?.id) {
      checkedReceipts.data = checkedReceiptsCache;
      await checkedReceipts.save();
    } else {
      await DB.GeneralData.create({
        id: VarHelper.genId(),
        type: 'etsy',
        name: 'checked-etsy-receipts',
        field1: String(store.id),
        field2: '',
        userId: '1',
        data: checkedReceiptsCache,
        publicPermission: {
          c: false, r: false, u: false, d: false,
        }
      })
    }

    const reseller = await DB.User.findByPk(store.resellerId);
    if (!reseller) return;
    const clientName = reseller.accountName || [reseller.firstName, reseller.lastName].filter(Boolean).join(' ');
    const params = {
      clientId: store.resellerId,
      env: process.env.DEV ? 'dev' : 'prod',
      clientName: clientName,
      storeId: store.id,
    }
    // call micro service
    const submitReceipts = receiptsWithListing.filter(Boolean)
    for (let i = 0; i < submitReceipts.length; i++) {
      const receipt = submitReceipts[i];
      console.log("callEtsyWebhook", receipt.receipt_id, `/api/bg/etsy-webhook?${objectToQueryString(params)}`);
      await requestMicroApi(`http://localhost:3000/api/bg/etsy-webhook?${objectToQueryString(params)}`, {
        method: 'post',
        body: JSON.stringify(receipt),
      })
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  } catch (error) {
    console.error('Error in getEtsyReceiptEvery5Min', store?.url, store?.resellerId);
    console.error(error?.message || JSON.stringify(error));
    sendTeleNoti(`getEtsyReceiptEvery10MinFailed - ${store?.url} - ${error?.message}`);
  }
}

let isRunning = false;
export const execute = async () => {
  if (isRunning) return;
  isRunning = true;
  const stores = await DB.OnlineStore.findAll({
    where: {
      type: 'etsy',
    },
  })
  if (!stores?.length) return;
  let startTime = new Date().getTime();
  console.log(`getEtsyReceiptsEvery5Min - ${stores.length} stores - start`);
  for (const store of stores) {
    await handleEachStore(store);
  }
  let endTime = new Date().getTime();
  console.log(`getEtsyReceiptsEvery5Min - end - ${(endTime - startTime) / (1000 * 60)}m`);
  isRunning = false;
};

export const getEtsyReceiptEvery5Min = () => {
  if (process.env.DEV) return;
  var job = new CronJob('*/10 * * * *', function () {
    console.log('getEtsyReceiptEvery5Min');
    execute();
  }, null, false, 'Europe/London');
  job.start();
  // execute();
};
