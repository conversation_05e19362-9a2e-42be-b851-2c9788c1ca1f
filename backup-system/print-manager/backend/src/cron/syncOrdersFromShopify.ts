import Bg from "api/bg/@utils/Bg";
import { DB } from "db";
import ShopifyHelper from "helpers/ShopifyHelper";
import axios from "axios";

var CronJob = require('cron').CronJob;
const moment = require('moment-timezone');
const fs = require('fs');
const path = require('path');

const { exec } = require('child_process');

// get orders from n days ago
const step1 = async () => {
  const allShopifyStores = await DB.OnlineStore.findAll({
    where: {
      type: 'shopify',
    }
  })
  const validShopifyStores = allShopifyStores.filter(store => store?.data?.shopifyAccessToken);

  console.log(validShopifyStores.length);
  let failedStores = [];
  let successStores = [];
  for (let i = 0; i < validShopifyStores.length; i++) {
    const store = validShopifyStores[i];
    try {
      const orders = await ShopifyHelper.getOrdersFromDate(store.url, store.data.shopifyAccessToken, moment().subtract(4, 'day').format('YYYY-MM-DD'));
      console.log(orders.length);
      successStores.push({
        storeId: store.id,
        orders: orders,
      });
    } catch (error) {
      failedStores.push({
        id: store.id,
        url: store.url,
        error: error.message,
      });
    }
  }
  console.log(failedStores);

  fs.writeFileSync(path.join(__dirname, 'failedStores.json'), JSON.stringify(failedStores, null, 2));
  fs.writeFileSync(path.join(__dirname, 'successStores.json'), JSON.stringify(successStores, null, 2));
};

// check missing orders
const step2 = async () => {
  const successStores = JSON.parse(fs.readFileSync(path.join(__dirname, 'successStores.json'), 'utf8'));
  // for (let i = 0; i < successStores.length; i++) {
  //   const store = successStores[i];
  //   console.log(store.storeId, store.orders.length);
  // }
  let missingOrderStores = [];
  for (let i = 0; i < successStores.length; i++) {
    const store = successStores[i];
    const orders = store.orders;
    let missingOrders = [];
    for (let j = 0; j < orders.length; j++) {
      const order = orders[j];
      let existedOrder;
      try {
        existedOrder = await Bg.Order.findByOrderSourceID(order.id);
      } catch (error) {
        existedOrder = null;
      }
      if (!existedOrder) {
        missingOrders.push(order);
      }
    }
    if (missingOrders.length > 0) {
      missingOrderStores.push({
        storeId: store.storeId,
        missingOrders: missingOrders,
      });
    }
  }
  fs.writeFileSync(path.join(__dirname, 'missingOrderStores.json'), JSON.stringify(missingOrderStores, null, 2));
}

// submit missing orders
const step3 = async () => {
  const missingOrderStores = JSON.parse(fs.readFileSync(path.join(__dirname, 'missingOrderStores.json'), 'utf8'));
  const failedStores = JSON.parse(fs.readFileSync(path.join(__dirname, 'failedSubmitStores.json'), 'utf8'));

  const failedSubmitStores = [];
  let successOrders = 0;
  for (let i = 0; i < missingOrderStores.length; i++) {
    const store = missingOrderStores[i];
    if (failedStores.find(failedStore => failedStore.storeId === store.storeId)) {
      console.log("skip success store", store.storeId);
      continue;
    }
    const storeData = await DB.OnlineStore.findByPk(store.storeId);
    const { webhooks: apiCallWebhooks } = await ShopifyHelper.apiCall(`${storeData.url}/admin/api/2023-04/webhooks.json`, 'get', storeData.data.shopifyAccessToken);
    const foundWebhook = apiCallWebhooks.find(webhook => webhook.topic === 'orders/create' && webhook.address.includes("api/bg/shopify-webhook?clientId"));
    if (!foundWebhook) {
      failedSubmitStores.push({
        storeId: store.storeId,
        error: "Webhook not found",
      });
      continue;
    }
    let errorOrders = [];
    for (let j = 0; j < store.missingOrders.length; j++) {
      const order = store.missingOrders[j];
      try {
        await axios.request({
          url: foundWebhook.address,
          method: 'post',
          headers: { 'Content-Type': 'application/json' },
          data: JSON.stringify(order),
        })
        console.log("submit order success", order.id);
        successOrders++;
        await new Promise(resolve => setTimeout(resolve, 12000));
      } catch (error) {
        errorOrders.push({
          orderId: order.id,
          error: error.message,
        });
      }
    }
    if (errorOrders.length > 0) {
      failedSubmitStores.push({
        storeId: store.storeId,
        errorOrders: errorOrders,
      });
    }
  }
  console.log("successOrders:", successOrders);
  fs.writeFileSync(path.join(__dirname, 'failedSubmitStores.json'), JSON.stringify(failedSubmitStores, null, 2));
}

// check failed submit stores
const step4 = async () => {
  const failedSubmitStores = JSON.parse(fs.readFileSync(path.join(__dirname, 'failedSubmitStores.json'), 'utf8'));
  console.log(failedSubmitStores.length);
  for (let i = 0; i < failedSubmitStores.length; i++) {
    const store = failedSubmitStores[i];
    const storeData = await DB.OnlineStore.findByPk(store.storeId);
    const { webhooks: apiCallWebhooks } = await ShopifyHelper.apiCall(`${storeData.url}/admin/api/2023-04/webhooks.json`, 'get', storeData.data.shopifyAccessToken);
    console.log(apiCallWebhooks);
    const foundWebhook = apiCallWebhooks.find(webhook => webhook.topic === 'orders/create' && webhook.address.includes("api/bg/shopify-webhook?clientId"));
    console.log("foundWebhook", foundWebhook);
  }
}

export const syncOrdersFromShopify = async () => {
  await Bg.initDB();
  // step1();
  // step2();
  step3();
  // step4();
};
