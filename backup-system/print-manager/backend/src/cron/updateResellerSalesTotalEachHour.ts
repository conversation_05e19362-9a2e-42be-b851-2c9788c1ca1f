var CronJob = require('cron').CronJob;
import { Op, Sequelize } from 'sequelize';
import { DB } from 'db';
import Stripe from 'stripe';
import { VarHelper } from 'helpers';
import Bg from 'api/bg/@utils/Bg';
const fs = require('fs');
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, { apiVersion: null });

export const execute = async () => {
  const list = await DB.Invoice.findAll({
    where: {
      paidAt: {
        [Op.not]: null,
      }
    },
    raw: true,
  });

  // Group invoices by resellerId
  const invoicesByReseller = {};
  const designIdQuantity = {};
  const productIdQuantity = {};
  const errorInvoices = [];
  for (const invoice of list) {
    const resellerId = invoice.resellerId;
    if (!invoicesByReseller[resellerId]) {
      invoicesByReseller[resellerId] = [];
    }
    invoicesByReseller[resellerId].push(invoice);
    if (!invoice.lineItems?.length) continue;

    const isSampleRequest = invoice.data?.isSampleRequest;
    const lineItems = typeof invoice.lineItems === 'string' ? JSON.parse(invoice.lineItems) : invoice.lineItems;

    if (isSampleRequest) {
      for (let lineItem of lineItems) {
        const productId = lineItem.product_id;
        const designId = lineItem.properties?.find((property) => property.name === 'BG Product Number')?.value;
        if (productId) {
          productIdQuantity[productId] = (productIdQuantity[productId] || 0) + lineItem.quantity;
        } else {
          console.log("__productId is null__", invoice.id);
          errorInvoices.push({
            invoiceId: invoice.id,
            message: "can't find productId",
          });
        }
        if (designId) {
          designIdQuantity[designId] = (designIdQuantity[designId] || 0) + lineItem.quantity;
        } else {
          console.log("__designId is null__", invoice.id);
          errorInvoices.push({
            invoiceId: invoice.id,
            message: "can't find designId",
          });
        }
      }
    } else {
      for (let lineItem of lineItems) {
        const printJobId = lineItem.properties?.find(i => i.name === "Print Job")?.value;
        let designId = lineItem.properties?.find(val => val.name === 'BG Product Number')?.value;
        let productId = lineItem.properties?.find(val => val.name === 'BG Product Library')?.value;

        if (!designId && printJobId) {
          const printJob = await DB.PrintJob.findByPk(printJobId);
          if (!!printJob) {
            designId = printJob.designId;
            productId = printJob.productId;
          }
        }
        if (!productId && printJobId) {
          const printJob = await DB.PrintJob.findByPk(printJobId);
          if (!!printJob) {
            productId = printJob.productId;
          }
        }
        if (!productId && designId) {
          const design = await DB.Design.findByPk(designId);
          if (!!design) {
            productId = design.productId;
          }
        }
        if (!productId && !designId && !printJobId) {
          console.log("__productId and designId are null__", invoice.id);
          errorInvoices.push({
            invoiceId: invoice.id,
            message: "can't find productId and designId",
          });
        } else {
          if (productId) {
            productIdQuantity[productId] = (productIdQuantity[productId] || 0) + lineItem.quantity;
          } else {
            console.log("__productId is null__", invoice.id);
            errorInvoices.push({
              invoiceId: invoice.id,
              message: "can't find productId",
            });
          }
          if (designId) {
            designIdQuantity[designId] = (designIdQuantity[designId] || 0) + lineItem.quantity;
          } else {
            console.log("__designId is null__", invoice.id);
            errorInvoices.push({
              invoiceId: invoice.id,
              message: "can't find designId",
            });
          }
        }
      }
    }
  }

  const record = await DB.GeneralData.findOne({
    where: {
      type: 'reseller-sales-report',
    }
  });
  if (record) {
    record.data = {
      designIdQuantity,
      productIdQuantity,
    }
    await record.save()
  } else {
    await DB.GeneralData.create({
      type: 'reseller-sales-report',
      field1: '',
      field2: '',
      data: {
        designIdQuantity,
        productIdQuantity,
      },
      id: VarHelper.genId(),
      name: 'reseller-sales-report',
      userId: '1',
      publicPermission: {
        c: false, r: false, u: false, d: false,
      }
    });
  }

  if (errorInvoices.length) {
    fs.writeFileSync(`./error-report-invoices.json`, JSON.stringify(errorInvoices, null, 2));
  }

  // await Bg.initDB();
  // const results = {};
  // // Calculate totals for each reseller
  // for (const resellerId in invoicesByReseller) {
  //   let salesTotal = 0;
  //   let shippingTotal = 0;
  //   let vatTotal = 0;
  //   let sampleSalesTotal = 0;
  //   let wholesaleTotal = 0;
  //   let refundTotal = 0;

  //   for (const invoice of invoicesByReseller[resellerId]) {
  //     try {
  //       let invoiceData = invoice.data;
  //       invoiceData = typeof invoiceData === "string" ? JSON.parse(invoiceData) : invoiceData;

  //       if (invoice.refundAt) {
  //         refundTotal += invoice.data?.amountPaid;
  //         continue;
  //       }
  //       if (invoiceData?.stripeInvoiceID) {
  //         const stripeInvoice = await stripe.invoices.retrieve(invoiceData.stripeInvoiceID);

  //         // Calculate totals from invoice line items
  //         for (const item of stripeInvoice.lines.data) {
  //           if (item.description?.includes("Shipping fee")) {
  //             shippingTotal += item.amount;
  //           } else if (item.description?.includes("VAT")) {
  //             vatTotal += item.amount;
  //           } else {
  //             const orderType = await getOrderTypeById(invoice.orderId);
  //             if (orderType === 'Wholesale') {
  //               wholesaleTotal += item.amount;
  //             } else if (orderType === 'Sample') {
  //               sampleSalesTotal += item.amount;
  //             } else {
  //               salesTotal += item.amount;
  //             }
  //           }
  //         }
  //       }
  //     } catch (error) {

  //     }
  //   }

  //   results[resellerId] = {
  //     salesTotal,
  //     shippingTotal,
  //     vatTotal,
  //     sampleSalesTotal,
  //     wholesaleTotal,
  //     refundTotal,
  //   };
  // }

  // fs.writeFileSync(`./reseller-report-totals.json`, JSON.stringify(results, null, 2));

};

const updateToDB = async (force = false) => {
  // read from reseller-totals.json
  const results = JSON.parse(fs.readFileSync(`./reseller-report-totals.json`, 'utf8'));
  for (const resellerId in results) {
    if (resellerId) {
      const user = await DB.User.findByPk(resellerId);

      if (user) {
        if (results[resellerId].salesTotal) {
          if (!force && user.salesTotal && user.salesTotal != results[resellerId].salesTotal) {
            console.error(`Update sales total warning: ${user.salesTotal} -> ${results[resellerId].salesTotal}`);
          } else {
            user.salesTotal = results[resellerId].salesTotal;
          }
        }
        if (results[resellerId].shippingTotal) {
          if (!force && user.shippingTotal && user.shippingTotal != results[resellerId].shippingTotal) {
            console.error(`Update shipping total warning: ${user.shippingTotal} -> ${results[resellerId].shippingTotal}`);
          } else {
            user.shippingTotal = results[resellerId].shippingTotal;
          }
        }
        if (results[resellerId].vatTotal) {
          if (!force && user.vatTotal && user.vatTotal != results[resellerId].vatTotal) {
            console.error(`Update vat total warning: ${user.vatTotal} -> ${results[resellerId].vatTotal}`);
          } else {
            user.vatTotal = results[resellerId].vatTotal;
          }
        }
        if (results[resellerId].sampleSalesTotal) {
          if (!force && user.sampleSalesTotal && user.sampleSalesTotal != results[resellerId].sampleSalesTotal) {
            console.error(`Update sample sales total warning: ${user.sampleSalesTotal} -> ${results[resellerId].sampleSalesTotal}`);
          } else {
            user.sampleSalesTotal = results[resellerId].sampleSalesTotal;
          }
        }
        if (results[resellerId].wholesaleTotal) {
          if (!force && user.wholesaleTotal && user.wholesaleTotal != results[resellerId].wholesaleTotal) {
            console.error(`Update wholesale total warning: ${user.wholesaleTotal} -> ${results[resellerId].wholesaleTotal}`);
          } else {
            user.wholesaleTotal = results[resellerId].wholesaleTotal;
          }
        }
        if (results[resellerId].refundTotal) {
          if (!force && user.refundTotal && user.refundTotal != results[resellerId].refundTotal) {
            console.error(`Update refund total warning: ${user.refundTotal} -> ${results[resellerId].refundTotal}`);
          } else {
            user.refundTotal = results[resellerId].refundTotal;
          }
        }
        await user.save();
      }
    }
  }
}

const getOrderTypeById = async (orderId: string) => {
  const order = await Bg.Order.findByOrderID(orderId);
  return order.OrderType;
}

export const updateResellerSalesTotalEachHour = () => {
  var job = new CronJob('0 * * * *', function() {
    console.log('updateResellerSalesTotalEachHour');
    execute();
  }, null, false, 'Europe/London');
  job.start();
  // updateToDB(true);
  execute();
};
