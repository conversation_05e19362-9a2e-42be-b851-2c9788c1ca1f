var CronJob = require('cron').CronJob;
const moment = require('moment-timezone');
const axios = require('axios');

import { listOrder, listPipeline, triggerDispatchRoyalMail } from 'api/bg/services';

export const checkPublishRMSuccess = async (order) => {
  if (!order || !order?.Pipelines?.length) return;
  const last = order.Pipelines[order.Pipelines.length - 1];
  const sharedData = last?.SharedData
  if (!sharedData) return;
  if (order.Stage === "In Production" && order.StageStatus === "On Time") {
    if (!sharedData?.royalMailOrderIdentifier) {
      let message = `Publish Royal Mail failed - Order ID: ${order['Order ID']}`
      console.log(message)
      triggerDispatchRoyalMail({
        orderId: order['Order ID'],
        force: true,
      })
      await axios.request({
        url: 'https://chat.personify.tech/sendMessage',
        method: 'post',
        headers: { 'Content-Type': 'application/json' },
        data: JSON.stringify({
          "message_thread_id": 1348,
          "text": message
        }),
      })
    }
  }
}

export const handleRoyalMailOrders = async (orders) => {
  for (let i = 0; i < orders.length; i++) {
    try {
      let order = { ...orders[i] };
      await checkPublishRMSuccess(order);
    } catch (error) {
      console.log("handleCheckGeneratedPDFOrders/error", error?.message || error);
    }
  }
}

export const execute = async () => {
  try {
    const params: any = {
      supported: true,
      offset: 0,
      limit: 99,
      stage: "In Production",
      startDate: moment().subtract(10, 'd').format('YYYY-MM-DD'),
      endDate: moment().format('YYYY-MM-DD'),
      dateType: 'UpdatedAt',
    }
    const res = await listOrder({
      ...params,
    })
    let orders = res.withPipelines;
    for (let i = 0; i < orders.length; i++) {
      if (orders[i]?.Pipelines?.length) continue;
      const resPipeline = await listPipeline({
        limit: 10,
        offset: 0,
        orderId: orders[i]?.['Order ID'],
      })
      console.log('cron/checkPublishRoyalMailEvery5Min.ts/resPipeline', resPipeline?.rows.length);
      orders[i] = {
        ...orders[i],
        Pipelines: resPipeline?.rows
      }
    }
    await handleRoyalMailOrders(orders);
  } catch (error) {
    console.error("checkPublishRoyalMailEvery5Min_err", error);
  }
};

export const checkPublishRoyalMailEvery5Min = () => {
  if (process.env.DEV) return;
  var job = new CronJob('*/5 * * * *', function () {
    console.log('checkPublishRoyalMailEvery5Min');
    execute();
  }, null, false, 'Europe/London');
  job.start();
  // execute();
};
