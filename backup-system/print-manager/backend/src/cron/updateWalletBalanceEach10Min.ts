var CronJob = require('cron').CronJob;
import { Op, Sequelize } from 'sequelize';
import { DB } from 'db';
import Stripe from 'stripe';
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, { apiVersion: null });

export const execute = async () => {
  const list = await DB.User.findAll({
    where: {
      role: 'reseller'
    }
  });
  for (let i=0; i<list.length; i++) {
    const reseller = list[i];
    if (!reseller.resellerStripeId) {
      continue;
    }
    const balances = await stripe.customers.retrieve(reseller.resellerStripeId, {
      expand: ['cash_balance'],
    });
    // @ts-ignore
    const _balance = (balances.balance * -1) || 0;
    const walletBalance = (_balance) / 100;
    reseller.walletBalance = walletBalance;
    // console.log(`Updaing reseller ${reseller.email} wallet balance to £${walletBalance}`);
    await reseller.save();
  }
};

export const updateWalletBalanceEach10Min = () => {
  var job = new CronJob('*/10 * * * *', function() {
    console.log('updateWalletBalanceEach30Min');
    execute();
  }, null, false, 'Europe/London');
  job.start();
  // execute();
};
