var CronJob = require('cron').CronJob;
const moment = require('moment-timezone');
import axios from 'axios';
import { DB } from 'db';
import { OnlineStoreModel } from 'db/Schema.OnlineStore';
import { Var<PERSON><PERSON>per } from 'helpers';
import Etsy from 'helpers/EtsyHelper';
import { TCMSOrder } from 'type';
import { listOrder, listPipeline } from 'api/bg/services';
import Bg from 'api/bg/@utils/Bg';
import { LOG_TYPES } from 'const';

const sendTeleNoti = async (message: string) => {
  await axios.request({
    url: 'https://chat.personify.tech/sendMessage',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: JSON.stringify({
      "message_thread_id": 1348,
      "text": message
    }),
  })
}

const setupEtsyClient = async (store: OnlineStoreModel) => {
  const { etsyAccessToken, etsyRefreshToken } = store.data || {};
  let needRefreshToken = Date.now() - new Date(store.updatedAt).getTime() > 24 * 60 * 60 * 1000;
  let etsy = new Etsy(etsyAccessToken, etsyRefreshToken);
  if (needRefreshToken) {
    const newTokens = await etsy.getNewToken();
    if (newTokens?.accessToken) {
      store.data = {
        ...store.data,
        etsyAccessToken: newTokens.accessToken,
        etsyRefreshToken: newTokens.refreshToken,
      }
      etsy = new Etsy(newTokens.accessToken, newTokens.refreshToken);
      await store.save();
    }
  }
  return etsy;
}

const handleOrdersOfEachStore = async (orders: TCMSOrder[], storeId: string) => {
  const store = await DB.OnlineStore.findByPk(storeId);
  if (!store) return;
  const { etsyAccessToken, etsyRefreshToken } = store.data || {};
  if (!etsyAccessToken || !etsyRefreshToken) {
    console.log(`Etsy access token or refresh token not found - ${store.url}`);
    return;
  }
  const etsy = await setupEtsyClient(store)
  await new Promise(resolve => setTimeout(resolve, 500));
  const shopInfo = await etsy.getMe();
  const shopId = shopInfo?.shop_id;
  if (!shopId) {
    return;
  }
  for (let i = 0; i < orders.length; i++) {
    const receipt = await etsy.getReceiptById({ id: Number(orders[i]['Order Source ID']), shopId })
    if (!receipt) {
      sendTeleNoti(`Cancelled Etsy Order - ${store?.url} - ${orders[i]['Order ID']}`);
      await Bg.Order.updateByOrderId(orders[i]['Order ID'], {
        Status: "Rejected",
        Stage: "Cancelled",
        StageStatus: "Rejected",
      });
      await DB.Log.create({
        id: VarHelper.genId(),
        type: LOG_TYPES.REJECT_ORDER,
        userId: '1',
        body: {
          orderId: orders[i]['Order ID'],
          changes: {
            Status: {
              from: orders[i].Status,
              to: "Rejected",
            },
            Stage: {
              from: orders[i].Stage,
              to: "Cancelled",
            },
            StageStatus: {
              from: orders[i].StageStatus,
              to: "Rejected",
            }
          }
        },
        otherData: {
          user: {
            email: "<EMAIL>",
            name: "Dev System",
          }
        }
      })
    }
  }
}

let isRunning = false;
export const execute = async () => {
  if (isRunning) return;
  isRunning = true;
  await Bg.initDB();
  const params: any = {
    supported: true,
    offset: 0,
    limit: 100,
    stage: "Pre Production",
    startDate: moment().subtract(7, 'd').format('YYYY-MM-DD'),
    endDate: moment().format('YYYY-MM-DD'),
    dateType: 'UpdatedAt',
    orderType: 'Etsy',
  }
  const res = await listOrder({
    ...params,
  })
  let orders = res.withPipelines;
  for (let i = 0; i < orders.length; i++) {
    if (orders[i]?.Pipelines?.length) continue;
    const resPipeline = await listPipeline({
      limit: 10,
      offset: 0,
      orderId: orders[i]?.['Order ID'],
    })
    orders[i] = {
      ...orders[i],
      Pipelines: resPipeline?.data.list
    }
  }
  // Group orders by Store ID
  const ordersByStore = orders.reduce((acc, order) => {
    const storeId = order['Store ID'];
    if (!acc[storeId]) {
      acc[storeId] = [];
    }
    acc[storeId].push(order);
    return acc;
  }, {});

  for (const storeId in ordersByStore) {
    await handleOrdersOfEachStore(ordersByStore[storeId], storeId);
  }
  isRunning = false;
};

export const verifyEtsyReceiptsEvery30Min = () => {
  if (process.env.DEV) return;
  var job = new CronJob('*/30 * * * *', function () {
    console.log('verifyEtsyReceiptsEvery30Min');
    execute();
  }, null, false, 'Europe/London');
  job.start();
  // execute();
};
