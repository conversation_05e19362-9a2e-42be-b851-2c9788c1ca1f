var CronJob = require('cron').CronJob;
const fs = require('fs');
import Bg from "api/bg/@utils/Bg";
import moment from 'moment';
import { runPipeline, triggerDispatchRoyalMail, updateOrderStatus } from 'api/bg/services';

const SIGG_PRODUCTS = {
  TROPICAL: {
    productId: 87482572566,
    designId: 352511632716,
  },
  WATERMELON: {
    productId: 97482572567,
    designId: 415030110499,
  },
  PEPPERMINT: {
    productId: 57482572563,
    designId: 50230316303,
  },
  SPEARMINT: {
    productId: 67482572564,
    designId: **********,
  },
  BUBBLEMINT: {
    productId: 47482572562,
    designId: 864649332392,
  },
  STRAWBERRYLEMON: {
    productId: 77482572565,
    designId: 878959232515,
  },
  STRAWBERRY_LEMON: {
    productId: 77482572565,
    designId: 878959232515,
  }
}

const generateRawOrderData = ({
  first_name,
  last_name,
  preferred_name,
  flavor,
  street_address,
  city,
  zipcode,
  orderId,
}) => {
  const { productId, designId } = SIGG_PRODUCTS[flavor];
  if (!productId || !designId) {
    throw new Error(`Invalid flavor: ${flavor}`);
  }
  const defaultAddress = {
    id: 1,
    customer_id: 1,
    first_name: first_name || "",
    last_name: last_name || "",
    company: null,
    address1: street_address,
    address2: "",
    city: city,
    province: "",
    country: 'United Kingdom',
    zip: zipcode,
    phone: null,
    name: [first_name, last_name].filter(Boolean).join(' '),
    province_code: "",
    country_code: "GB",
    country_name: "United Kingdom",
    default: true,
  };
  return {
    id: orderId,
    created_at: moment().format('YYYY-MM-DDTHH:mm:ssZ'),
    currency: "GBP",
    contact_email: "",
    // @ts-ignore
    customer: {
      email: "",
      first_name: first_name || "",
      last_name: last_name || "",
      // @ts-ignore
      default_address: defaultAddress
    },
    billing_address: defaultAddress,
    shipping_address: defaultAddress,
    // @ts-ignore
    line_items: [{
      id: Math.floor(Math.random() * *********),
      grams: 1,
      name: flavor,
      price: "0.00",
      price_set: {
        shop_money: {
          amount: "0.00",
          currency_code: "GBP"
        },
        presentment_money: {
          amount: "0.00",
          currency_code: "GBP"
        },
      },
      product_exists: true,
      product_id: productId,
      properties: designId ? [
        {
          name: "Design ID",
          value: String(designId)
        }
      ] : [],
      quantity: 1,
      requires_shipping: false,
      sku: "",
      taxable: true,
      title: flavor,
      total_discount: "0.00",
      tax_lines: [],
      duties: [],
      discount_allocations: []
    }],
    name: `sigg-${orderId}`,
    order_number: orderId,
    current_subtotal_price: "0.00",
    current_total_price: "0.00",
    total_price: "0.00",
  }
}

const submitOrder = async ({
  first_name,
  last_name,
  preferred_name,
  flavor,
  street_address,
  city,
  zipcode,
}) => {

  try {
    const storeId = "123781293930";
    const clientId = "654950309294";
    const { productId, designId } = SIGG_PRODUCTS[flavor];
    if (!productId || !designId) {
      throw new Error(`Invalid flavor: ${flavor}`);
    }

    const { orderNumber, orderId } = await (async () => {
      try {
        let countStoreOrder = await Bg.Order.count(storeId ? {
          'Store ID': storeId,
        } : {
          'Client ID': clientId,
        });
        console.log('countStoreOrder', countStoreOrder);
        const orderNumber = countStoreOrder + 1000;
        let countSameOrderNo = await Bg.Order.count({
          'Order Number': orderNumber,
        });
        const numberToBase26 = (val, tail = '') => {
          if (val <= 26) {
            return `${String.fromCharCode(val + 64)}${tail}`;
          }
          const remainder = val % 26 || 26;
          const division = Math.trunc(val / 26) - (remainder === 26 ? 1 : 0);
          return numberToBase26(division, `${String.fromCharCode(remainder + 64)}${tail}`);
        };
        let orderNo = countSameOrderNo + 1;
        let orderId = `${orderNumber}${numberToBase26(orderNo)}`;
        let isUnique = false;
        console.log('orderNumber', orderNumber);
        while (!isUnique) {
          let countSameOrderId = await Bg.Order.count({
            'Order ID': orderId,
          });
          console.log('countSameOrderId', countSameOrderId);
          if (countSameOrderId) {
            orderNo += 1;
            orderId = `${orderNumber}${numberToBase26(orderNo)}`;
          } else {
            isUnique = true;
          }
        }
        return {
          orderNumber,
          orderId,
        };
      } catch (err) {
        console.log('err orderUniqueId', err);
        return {
          orderNumber: `9${String(Math.floor(Math.random() * 999)).padStart(3, '0')}`,
          orderId: 'ES_' + Math.random().toString(36).substring(7),
        };
      }
    })();

    const rawOrderData = generateRawOrderData({
      first_name,
      last_name,
      preferred_name,
      flavor,
      street_address,
      city,
      zipcode,
      orderId,
    })

    const order = await Bg.Order.create({
      'Order ID': orderId,
      'Order Source ID': orderId,
      'Order Name': `#${orderNumber}`,
      'Order Number': orderNumber,
      'Customer Email': "",
      'Customer Name': `${first_name} ${last_name}`,
      'Raw Data': rawOrderData as any,
      'All Item IDs': productId,
      'All Product Names': flavor,
      'Status': 'Pending',
      'Client ID': String(clientId),
      'Client Name': `SIGG`,
      'Store ID': String(storeId),
      'env': 'prod',
      'Stage': 'Pre Production',
      'StageStatus': "Awaiting Payment",
      'OrderType': 'Imported',
    });

    await runPipeline({ order });

    let failedMessage = '';

    await new Promise(resolve => setTimeout(resolve, 10000));
    const pipeline = await Bg.Pipeline.findByOrderId(orderId);
    if (!pipeline?.SharedData?.pdfs?.length) {
      failedMessage = 'PDF generation failed';
    } else {
      await updateOrderStatus({
        orderId,
        Status: "Accepted",
        Stage: "In Production",
        StageStatus: "On Time",
      });

      await triggerDispatchRoyalMail({
        orderId,
      });
    }

    // Log success to import-result.json
    fs.appendFileSync('import-result.json', JSON.stringify(failedMessage ? {
      success: false,
      error: failedMessage,
      orderDetails: {
        firstName: first_name,
        lastName: last_name,
        flavor
      }
    } : {
      success: true,
      orderId,
      orderNumber,
    }) + ',\n');

  } catch (error) {
    // Log error to import-result.json
    fs.appendFileSync('import-result.json', JSON.stringify({
      success: false,
      error: error.message,
      orderDetails: {
        firstName: first_name,
        lastName: last_name,
        flavor
      }
    }) + ',\n');
  }
}

export const execute = async () => {
  await Bg.initDB();
  if (!fs.existsSync('import-result.json')) {
    fs.writeFileSync('import-result.json', '');
  }
  const orders = JSON.parse(fs.readFileSync(__dirname + '/imported-orders.json', 'utf8'));
  for (let i = 0; i < orders.length; i++) {
    const order = orders[i];
    if (order.flavor !== 'STRAWBERRY_LEMON') {
      continue;
    }
    await submitOrder(order);
  }
};

export const importSiggOrders = () => {
  execute();
};
