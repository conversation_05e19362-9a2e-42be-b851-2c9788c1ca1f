import { DB } from "db";
import { Op } from "sequelize";

const fs = require('fs');
const path = require('path');
const moment = require('moment');
const inMemoryPath = path.join(__dirname, '../db/in-memory.json');

let userLastLogged = {}

export const execute = async () => {
  try {
    // const existedData = await DB.User.findOne({
    //   where: {
    //     loggedInAt: {
    //       [Op.not]: null
    //     }
    //   }
    // })
    // if (existedData?.id) return;

    let inMemoryData: any = {};
    if (fs.existsSync(inMemoryPath)) {
      const fileContent = fs.readFileSync(inMemoryPath, 'utf8');
      if (fileContent) {
        inMemoryData = JSON.parse(fileContent);
      }
    }
    const tokens = inMemoryData.JWT || {};
    const users = Object.values(tokens).map((userStr: string) => JSON.parse(userStr));

    for (let i = 0; i < users.length; i++) {
      const user = users[i];
      const lastLogged = moment(user.updatedAt).isAfter(moment(user.createdAt)) ? user.updatedAt : user.createdAt;
      if (userLastLogged[user.id]) {
        if (moment(lastLogged).isAfter(moment(userLastLogged[user.id]))) {
          userLastLogged[user.id] = lastLogged;
        }
      } else {
        userLastLogged[user.id] = lastLogged;
      }
    }

    const designs = await DB.Design.findAll({})

    for (let i = 0; i < designs.length; i++) {
      const design: any = designs[i];
      if (design.createdByUserId) {
        if (design.updatedAt) {
          if (moment(design.updatedAt).isAfter(moment(userLastLogged[design.createdByUserId]))) {
            userLastLogged[design.createdByUserId] = design.updatedAt;
          }
        }
      }
    }

    const allUsers = await DB.User.findAll()

    for (let i = 0; i < allUsers.length; i++) {
      const user = allUsers[i];
      const logRes = await DB.Log.findOne({
        where: {
          userId: user.id,
        },
        order: [['createdAt', 'DESC']]
      })
      if (logRes) {
        console.log("Log Record", logRes.createdAt);
        userLastLogged[user.id] = logRes.createdAt;
      }
      if (user.loggedInAt) continue;

      if (userLastLogged[user.id]) {
        if (moment(user.updatedAt).isAfter(moment(userLastLogged[user.id]))) {
          userLastLogged[user.id] = user.updatedAt;
        }
        user.loggedInAt = userLastLogged[user.id];
      } else {
        user.loggedInAt = user.updatedAt || user.createdAt;
      }
      console.log("update user", user.id, user.loggedInAt);
      await user.save();
    }

    // console.log(`Found ${users.length} users with active sessions`);
  } catch (error) {
    console.error("checkGeneratedPDFEvery10Min_err", error);
  }
};

export const updateUserLastLoggedIn = () => {
  execute();
};
