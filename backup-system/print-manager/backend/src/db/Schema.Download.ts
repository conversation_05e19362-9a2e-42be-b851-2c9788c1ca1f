import { BuildOptions, Model, DataTypes, Sequelize } from "sequelize";
import { stringifyDataType } from "./Utils.Schema";
import { TDownload } from 'type';

export interface DownloadSchema extends TDownload {

}

// For Typescript type stuff
export interface DownloadModel extends Model<DownloadSchema>, DownloadSchema { }
export type DownloadStatic = typeof Model & {
  new(values?: object, options?: BuildOptions): DownloadModel;
};
export const tableDefine = {
  name: "downloads",
  columns: {
    id: {
      type: DataTypes.TEXT,
      primaryKey: true,
    },
    queueId: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
    },
    resellerId: {
      type: DataTypes.TEXT,
    },
    productId: {
      type: DataTypes.TEXT,
    },
    designId: {
      type: DataTypes.TEXT,
    },
    linkedOrderId: {
      type: DataTypes.TEXT,
    },
    variationName: {
      type: DataTypes.TEXT,
    },
    pdf: {
      type: DataTypes.TEXT,
    },
  }
};

export const createDownload = async (instance: Sequelize): Promise<DownloadStatic> => {
  const Download = <DownloadStatic>instance.define(
    tableDefine.name,
    tableDefine.columns,
    {
      indexes: [],
    }
  );

  Download.beforeSave((Download: DownloadModel, options) => { });

  await Download.sync({ force: false });
  return Download;
};
