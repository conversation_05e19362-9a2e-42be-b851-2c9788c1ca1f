const fs = require('fs');
const path = require('path');

const localFilePath = path.join(__dirname, './in-memory.json');
class InMemory {
  constructor() {
    if (!global.inMemoryData) global.inMemoryData = {};
    this.syncFromLocal();
  }

  get data() {
    return global.inMemoryData;
  }

  async hgetAsync(category, key) {
    if (!global.inMemoryData[category]) return undefined;
    return global.inMemoryData[category][key];
  }

  async hsetAsync(category, key, value) {
    if (!global.inMemoryData[category]) global.inMemoryData[category] = {};
    global.inMemoryData[category][key] = value;
    this.saveToLocal();
  }

  async hdelAsync(category, key) {
    if (!global.inMemoryData[category]) return;
    delete global.inMemoryData[category][key];
    this.saveToLocal();
  }

  syncFromLocal() {
    // if (!process.env.DEV) return;
    if (fs.existsSync(localFilePath)) {
      try {
        const localObj = JSON.parse(fs.readFileSync(localFilePath).toString());
        global.inMemoryData = localObj;
      } catch(err) {}
    }
  }

  saveToLocal() {
    // if (!process.env.DEV) return;
    fs.writeFileSync(localFilePath, JSON.stringify(global.inMemoryData, undefined, 2));
  }
}

export default new InMemory();
