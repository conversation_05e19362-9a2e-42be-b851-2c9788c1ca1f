import { BuildOptions, Model, DataTypes, Sequelize } from "sequelize";
import { stringifyDataType } from "./Utils.Schema";
import { TOnlineStore } from 'type';

export interface OnlineStoreSchema extends TOnlineStore {
  
}

// For Typescript type stuff
export interface OnlineStoreModel extends Model<OnlineStoreSchema>, OnlineStoreSchema {}
export type OnlineStoreStatic = typeof Model & {
  new (values?: object, options?: BuildOptions): OnlineStoreModel;
};
export const tableDefine = {
  name: "online_stores",
  columns: {
    id: {
      type: DataTypes.TEXT,
      primaryKey: true,
    },
    name: {
      type: DataTypes.TEXT,
    },
    url: {
      type: DataTypes.TEXT,
    },
    type: {
      type: DataTypes.TEXT,
    },
    data: {
      type: DataTypes.TEXT,
      ...stringifyDataType('data'),
    },
    resellerId: {
      type: DataTypes.TEXT,
    },
    inactive: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
  }
};

export const createOnlineStore = async (instance: Sequelize): Promise<OnlineStoreStatic> => {
  const OnlineStore = <OnlineStoreStatic>instance.define(
    tableDefine.name,
    tableDefine.columns,
    {
      indexes: [],
    }
  );

  OnlineStore.beforeSave((OnlineStore: OnlineStoreModel, options) => {});

  await OnlineStore.sync({ force: false });
  return OnlineStore;
};
