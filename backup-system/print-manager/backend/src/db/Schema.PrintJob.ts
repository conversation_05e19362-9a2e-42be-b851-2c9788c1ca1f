import { BuildOptions, Model, DataTypes, Sequelize } from "sequelize";
import { stringifyDataType } from "./Utils.Schema";
import { TPrintJob } from 'type';

export interface PrintJobSchema extends TPrintJob {
  
}

// For Typescript type stuff
export interface PrintJobModel extends Model<PrintJobSchema>, PrintJobSchema {}
export type PrintJobStatic = typeof Model & {
  new (values?: object, options?: BuildOptions): PrintJobModel;
};
export const tableDefine = {
  name: "print_jobs",
  columns: {
    id: {
      type: DataTypes.TEXT,
      primaryKey: true,
    },
    clientId: {
      type: DataTypes.TEXT,
    },
    designId: {
      type: DataTypes.TEXT,
    },
    entityId: {
      type: DataTypes.TEXT,
    },
    productId: {
      type: DataTypes.TEXT,
    },
    productName: {
      type: DataTypes.TEXT,
    },
    previewUrl: {
      type: DataTypes.TEXT,
    },
    artworkUrls: {
      type: DataTypes.TEXT,
      ...stringifyDataType("artworkUrls"),
    },
    quantity: {
      type: DataTypes.INTEGER,
    },
    isPaid: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    isPrinted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    isPDFDownloaded: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    isRePrinted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    data: {
      type: DataTypes.TEXT,
      ...stringifyDataType("data"),
    },
    readyForPrint: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    productVariantionName: {
      type: DataTypes.TEXT,
      defaultValue: '',
    },
  }
};

export const createPrintJob = async (instance: Sequelize): Promise<PrintJobStatic> => {
  const PrintJob = <PrintJobStatic>instance.define(
    tableDefine.name,
    tableDefine.columns,
    {
      indexes: [],
    }
  );

  PrintJob.beforeSave((PrintJob: PrintJobModel, options) => {});

  await PrintJob.sync({ force: false });
  return PrintJob;
};
