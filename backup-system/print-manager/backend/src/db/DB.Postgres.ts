import { QueryInterface } from 'sequelize';
const Sequelize = require('sequelize');
export const Op = Sequelize.Op;
const GlobalEvent = require('js-events-listener');

import { LogStatic, createLog } from './Schema.Log';
import { UserStatic, createUser } from './Schema.User';
import { GeneralDataStatic, createGeneralData } from './Schema.GeneralData';
import { PrintJobStatic, createPrintJob } from './Schema.PrintJob';
import { ProductStatic, createProduct } from './Schema.Product';
import { DesignStatic, createDesign } from './Schema.Design';
import { ProductInstanceStatic, createProductInstance } from './Schema.ProductInstance';
import { OnlineStoreStatic, createOnlineStore } from './Schema.OnlineStore';
import { OrderStatic, createOrder } from './Schema.Order';
import { DownloadStatic, createDownload } from './Schema.Download';
import { InvoiceStatic, createInvoice } from './Schema.Invoice';
import { seed } from './seed';
import { migration } from './migration';
import { createSticker, StickerStatic } from './Schema.Sticker';
import { PackingSlipStatic, createPackingSlip } from './Schema.PackingSlip';
import { UserEditorImageStatic, createUserEditorImage } from './Schema.UserEditorImage';
import { createDraftInvoice, DraftInvoiceStatic } from './Schema.DraftInvoice';
import { SurveyAnwserStatic, createSurveyAnwser } from './Schema.SurveyAnwser';
import Bg from 'api/bg/@utils/Bg';

interface IOtherConfig {
  database?: string,
  username?: string,
  password?: string,
  host?: string,
  port?: string,
}

class DB {

  instance;

  _ready = false;
  makeReady = () => {
    console.log('MAKE READY');
    this._ready = true;
    GlobalEvent.emit('DB_READY', undefined);
  }
  onReady = () => new Promise((resolve, reject) => {
    if (this._ready) return resolve(undefined);
    GlobalEvent.on('DB_READY', () => {
      resolve(undefined);
    });
  })

  async init(otherConfig: IOtherConfig = {}) {

    if (this.instance) return this.instance;
    const DB_CONNECTION_STRING = process.env.DEV ? process.env.DB_CONNECTION_STRING_DEV : process.env.DB_CONNECTION_STRING_PROD;
    this.instance = new Sequelize(`${DB_CONNECTION_STRING}?sslmode=require`, {
      logging: false,
    });
    await this.migrateTables();
    await Bg.initDB();
    return this.instance;
  }

  Log: LogStatic;
  User: UserStatic;
  GeneralData: GeneralDataStatic;
  PrintJob: PrintJobStatic;
  Product: ProductStatic;
  Design: DesignStatic;
  ProductInstance: ProductInstanceStatic;
  OnlineStore: OnlineStoreStatic;
  Order: OrderStatic;
  Download: DownloadStatic;
  Sticker: StickerStatic;
  PackingSlip: PackingSlipStatic;
  Invoice: InvoiceStatic;
  UserEditorImage: UserEditorImageStatic;
  DraftInvoice: DraftInvoiceStatic;
  SurveyAnwser: SurveyAnwserStatic;

  queryInterface: any;

  async microApisDescribeTableFromSchema(slug: string, prefix: string, obj: any) {
    console.log('microApisDescribeTableFromSchema', slug, prefix);
    if (!this[slug]) this[slug] = {};
    for (let key in obj) {
      if (!!this[slug][key]) continue;
      const columns = {};
      const timestampsKey = {};
      let isColumnIdExists = false;

      const table_name = `${prefix}_${obj[key].table_name}`;
      for (let col of obj[key].columns) {

        if (col.db.timestamps) {
          Object.keys(col.db).filter(v => v !== 'timestamps').forEach(v => {
            timestampsKey[v] = col.db[v];
          });
        } else {
          columns[col.nocodb.column_name] = {
            ...col.db,
          };
          if (col.nocodb.column_name === 'id') {
            isColumnIdExists = true;
          }
        }
      }
      this[slug][key] = this.instance.define(table_name, columns, {
        freezeTableName: true,
        ...(Object.keys(timestampsKey).length === 0 ? { timestamps: false } : timestampsKey),
      });
      await this[slug][key].sync({ force: false });
      console.log('slug', Object.keys(this[slug]))
      // reset sequence
      try {
        if (isColumnIdExists) {
          await this.instance.query(`SELECT setval('"${table_name}_id_seq"', (SELECT MAX(id) FROM "${table_name}")+1)`);
        }
      } catch (err) {
        console.log(err);
      }
    }
  }

  async migrateTables() {
    this.Log = await createLog(this.instance);
    this.User = await createUser(this.instance);
    this.GeneralData = await createGeneralData(this.instance);
    this.PrintJob = await createPrintJob(this.instance);
    this.Product = await createProduct(this.instance);
    this.Design = await createDesign(this.instance);
    this.ProductInstance = await createProductInstance(this.instance);
    this.OnlineStore = await createOnlineStore(this.instance);
    this.Order = await createOrder(this.instance);
    this.Download = await createDownload(this.instance);
    this.Sticker = await createSticker(this.instance);
    this.PackingSlip = await createPackingSlip(this.instance);
    this.Invoice = await createInvoice(this.instance);
    this.UserEditorImage = await createUserEditorImage(this.instance);
    this.DraftInvoice = await createDraftInvoice(this.instance);
    this.SurveyAnwser = await createSurveyAnwser(this.instance);

    // FK
    this.OnlineStore.hasOne(this.User, {
      foreignKey: 'onlineStoreId'
    });
    this.User.belongsTo(this.OnlineStore, {
      foreignKey: 'onlineStoreId',
      as: 'onlineStore'
    });

    this.Product.hasMany(this.Design, {
      foreignKey: 'productId'
    });
    this.Design.belongsTo(this.Product, {
      foreignKey: 'productId',
      as: 'product'
    });

    this.User.hasMany(this.SurveyAnwser, { foreignKey: 'userId' });
    this.SurveyAnwser.belongsTo(this.User, { foreignKey: 'userId' });

    this.queryInterface = this.instance.getQueryInterface();
    console.log('Migrating tables');
    await migration(this);
    console.log('Seeding tables')
    await seed(this);
    this.makeReady();
  }
}

export default new DB();
