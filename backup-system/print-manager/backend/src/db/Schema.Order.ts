import { BuildOptions, Model, DataTypes, Sequelize } from "sequelize";
import { stringifyDataType } from "./Utils.Schema";
import { TOrder } from 'type';

export interface OrderSchema extends TOrder {
  
}

// For Typescript type stuff
export interface OrderModel extends Model<OrderSchema>, OrderSchema {}
export type OrderStatic = typeof Model & {
  new (values?: object, options?: BuildOptions): OrderModel;
};
export const tableDefine = {
  name: "orders",
  columns: {
    id: {
      type: DataTypes.TEXT,
      primaryKey: true,
    },
    resellerId: {
      type: DataTypes.TEXT,
    },
    productId: {
      type: DataTypes.TEXT,
    },
    price: {
      type: DataTypes.FLOAT,
    },
    amount: {
      type: DataTypes.INTEGER,
    },
    variationName: {
      type: DataTypes.TEXT,
    },
    status: {
      type: DataTypes.TEXT,
    },
    didUseAllDownload: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    downloadIds: {
      type: DataTypes.TEXT,
      ...stringifyDataType("downloadIds"),
    },
  }
};

export const createOrder = async (instance: Sequelize): Promise<OrderStatic> => {
  const Order = <OrderStatic>instance.define(
    tableDefine.name,
    tableDefine.columns,
    {
      indexes: [],
    }
  );

  Order.beforeSave((Order: OrderModel, options) => {});

  await Order.sync({ force: false });
  return Order;
};
