import { BuildOptions, Model, DataTypes, Sequelize } from "sequelize";
import { stringifyDataType } from "./Utils.Schema";
import { TPackingSlip } from "type/TPackingSlip";

export interface PackingSlipSchema extends TPackingSlip {
  
}

// For Typescript type stuff
export interface PackingSlipModel extends Model<PackingSlipSchema>, PackingSlipSchema {}
export type PackingSlipStatic = typeof Model & {
  new (values?: object, options?: BuildOptions): PackingSlipModel;
};
export const tableDefine = {
  name: "packing_slips",
  columns: {
    id: {
      type: DataTypes.TEXT,
      primaryKey: true,
    },
    resellerId: {
      type: DataTypes.TEXT,
    },
    storeId: {
      type: DataTypes.TEXT,
    },
    companyName: {
      type: DataTypes.TEXT,
      unique: true,
    },
    phone: {
      type: DataTypes.TEXT,
    },
    email: {
      type: DataTypes.TEXT,
    },
    address: {
      type: DataTypes.TEXT,
    },
    companyLogo: {
      type: DataTypes.TEXT,
    }
  }
};

export const createPackingSlip = async (instance: Sequelize): Promise<PackingSlipStatic> => {
  const PackingSlip = <PackingSlipStatic>instance.define(
    tableDefine.name,
    tableDefine.columns,
    {
      indexes: [],
    }
  );

  PackingSlip.beforeSave((User: PackingSlipModel, options) => {});

  await PackingSlip.sync({ force: false });
  return PackingSlip;
};
