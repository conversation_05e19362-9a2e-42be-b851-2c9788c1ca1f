import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Shopify } from 'helpers';
import dbInstace from './DB.Postgres';
import { Op } from 'sequelize';
import { UserModel } from './Schema.User';
import { OnlineStoreModel } from './Schema.OnlineStore';
const sharp = require('sharp');
const url = require("url");
const fs = require("fs");
const path = require("path");
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

export const seed = async (DB: typeof dbInstace) => {

  const createIfNotExistAdmin = async () => {
    if (!process.env.SEED_ADMINS) return;
    const admins = process.env.SEED_ADMINS.slice(1, -1).split('][')
      .map(item => {
        const [email, pass, firstName, lastName] = item.split('|');
        return { email, pass, firstName, lastName };
      });

    for (let i = 0; i < admins.length; i++) {
      const admin = await DB.User.findOne({
        where: {
          email: admins[i].email,
        }
      })
      if (!admin?.id) {
        const hashedPassword = await AuthenHelper.hashPassword(admins[i].pass);
        console.log("Seeding admin account: " + admins[i].email)
        await DB.User.create({
          id: VarHelper.genId(),
          firstName: admins[i].firstName,
          lastName: admins[i].lastName,
          email: admins[i].email,
          role: 'admin',
          password: hashedPassword as string,
        })
      }
    }
  }

  const assignClientId = async () => {
    const printJobs = await DB.PrintJob.findAll({
      where: {
        data: {
          [Op.like]: '%storeUrl%'
        }
      }
    });
    const mathcesStoreUrlToIds = {};
    const matchesPrintJobToResellerIds = {};
    for (let i = 0; i < printJobs.length; i++) {
      const job = printJobs[i];
      // console.log('job: ', job.id, job.designId,  job.productId);
      const productVariantionName = (() => {
        const { data } = job;
        if (!data) return '';
        if (!data.design) return '';
        const { settings } = data.design;
        if (!settings) return '';
        if (settings.mirrored && settings.varnish) return 'Var + Mirr';
        if (!settings.mirrored && settings.varnish) return 'Var + No Mirr';
        if (!settings.mirrored && !settings.varnish) return 'No Var + No Mirr';
      })();
      const storeUrl = job.data.storeUrl;
      const store: OnlineStoreModel = !!mathcesStoreUrlToIds[storeUrl] ? mathcesStoreUrlToIds[storeUrl] : await DB.OnlineStore.findOne({
        where: {
          url: storeUrl,
        }
      });
      if (!store) continue;
      // console.log('store: ', store.id);
      mathcesStoreUrlToIds[storeUrl] = store;
      matchesPrintJobToResellerIds[job.id] = store.resellerId;
      job.clientId = store.resellerId;
      job.productVariantionName = productVariantionName;
      if (job.designId && !job.productId) {
        const design = await DB.Design.findOne({
          where: { id: job.designId },
          attributes: ['productId'],
        });
        console.log('design', design);
        if (design) job.productId = design.productId;
        else console.log('design not found');
      }
      await job.save();
    }
  };

  const updateProductTypeForDesigns = async () => {
    const list = await DB.Design.findAll({
      where: {
        wholeSale: {
          [Op.or]: [false, null]
        },
        printOnDemand: {
          [Op.or]: [false, null]
        },
        customProduct: {
          [Op.or]: [false, null]
        },
      }
    });
    console.log('list', list.length);
    const cacheP: any = {};
    for (let i = 0; i < list.length; i++) {
      const { productId } = list[i];
      console.log('productId', productId);
      const product = !!cacheP[productId]
        ? cacheP[productId]
        : await DB.Product.findByPk(productId);
      if (!product) continue;
      cacheP[productId] = product;
      list[i].printOnDemand = Boolean(product.printOnDemand);
      list[i].customProduct = Boolean(product.customProduct);
      list[i].wholeSale = Boolean(product.wholeSale);
      await list[i].save();
    }
  }

  const resizeSingle = async (url) => {
    if (!url) return '';
    const parse = path.parse(url);
    const basename = parse.base;
    const tempPath = new Date().getTime() + '_' + basename;
    const tempPathResized = new Date().getTime() + '_resized_' + basename;
    await FileHelper.downloadFile(url, tempPath);
    await sharp(tempPath).resize(500).toFile(tempPathResized);
    const newUrl = await AWSHelper.upload({
      filePath: tempPathResized,
      key: 'optimized/bg/' + new Date().getTime() + '_img' + parse.ext,
    }, true);
    setTimeout(() => {
      fs.unlink(tempPath, () => { });
      fs.unlink(tempPathResized, () => { });
    }, 500);
    return newUrl;
  }

  const resizeImages = async () => {
    const products = await DB.Product.findAll();
    for (let i = 0; i < products.length; i++) {
      const p = products[i];
      if (!!p.originalImages) {
        p.image = p.originalImages.image;
        p.galleries = p.originalImages.galleries.slice();
        p.bluePrintImage = p.originalImages.bluePrint;
      }
      try {
        const image = await resizeSingle(p.image);
        const galleries = [];
        const originalGalleries = p.galleries || [];
        for (let i = 0; i < originalGalleries.length; i++) {
          const thisGallery = await resizeSingle(originalGalleries[i]);
          galleries[i] = thisGallery;
        }
        const bluePrint = await resizeSingle(p.bluePrintImage);
        p.originalImages = {
          image: p.image,
          galleries: (p.galleries || []).slice(),
          bluePrint: p.bluePrintImage,
        };
        p.galleries = galleries;
        p.image = image;
        p.bluePrintImage = bluePrint;

        await p.save();
        console.log('optimized p.galleries', p.galleries);
        console.log('optimized p.image', p.image);
        console.log('optimized p.bluePrintImage', p.bluePrintImage);
      } catch (err) {
        console.log('resizeImages err at', p.id);
        console.log('resizeImages err', err);
      }
    }
  };

  const removeTestData = async () => {
    const emails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ];

    const logs = await DB.GeneralData.findAll({
      where: {
        data: {
          [Op.or]: emails.map(e => ((
            { [Op.like]: `%${e}%` }
          )))
        }
      },
      logging: true,
    });
    console.log('logs', logs.length);
    for (let i = 0; i < logs.length; i++) {
      const log = logs[i];
      const orderId = log.data?.id;
      console.log(`processing log: ${log.id} orderId: ${orderId} ...`);
      if (!!orderId) {
        const res = await DB.PrintJob.destroy({
          where: {
            data: {
              [Op.like]: `%${orderId}%`,
            }
          }
        });
        console.log(res);
      }
      const res = await log.destroy();
      console.log(res);
    }
    console.log('done removeTestData');
  }

  const removeStripeData = async () => {
    const users = await DB.User.findAll({
      where: {
        resellerStripeId: {
          [Op.not]: "",
        },
        updatedAt: {
          [Op.lt]: "2024-01-24",
        }
      }
    })

    for (let i = 0; i < users.length; i++) {
      const user = users[i];
      console.log(`processing user: ${user.email} ... ${user.resellerStripeId}, ${user.updatedAt}`);
      user.resellerStripeId = "";
      await user.save();
    }
  }

  const updateWebhookClientName = async () => {
    const stores = await DB.OnlineStore.findAll({
      where: {
        inactive: false,
      }
    });
    for (let i = 0; i < stores.length; i++) {
      const store = stores[i];
      const url = store.url;
      const token = store.data?.shopifyAccessToken;
      const reseller = await DB.User.findByPk(store.resellerId);
      if (!reseller) continue;
      const clientName = reseller.accountName || [reseller.firstName, reseller.lastName].filter(Boolean).join(' ');
      console.log('clientName', clientName);
      //  SET UP WEBHOOK
      try {
        await Shopify.upsertOrderWebhook(url, token, [], store, clientName);
      } catch (err) {
        console.log('updateDevWebhookToLive err', String(err));
      }
    }
  }

  const updateStripeCustomer = async () => {
    const resellers = await DB.User.findAll({
      where: {
        resellerStripeId: {
          [Op.ne]: '',
        }
      }
    });
    for (let i = 0; i < resellers.length; i++) {
      const r = resellers[i];
      if (!r.resellerStripeId) continue;
      const resellerName = r.accountName || [r.firstName, r.lastName].filter(Boolean).join(' ');
      console.log(`Updating Stripe Customer: ${r.resellerStripeId}`);
      console.log(`Add name: ${resellerName} Add email: ${r.email}`);
      await stripe.customers.update(
        r.resellerStripeId,
        {
          name: resellerName,
          email: r.email,
        }
      );
    }
  }

  await Promise.all([
    createIfNotExistAdmin(),
    // assignClientId(),
    // updateProductTypeForDesigns(),
    // resizeImages(),
    // removeTestData(),
    // removeStripeData(),
    // updateWebhookClientName(),
    // updateStripeCustomer(),
  ])

}
