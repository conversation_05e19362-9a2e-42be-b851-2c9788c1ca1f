import { BuildOptions, Model, DataTypes, Sequelize } from "sequelize";
import { stringifyDataType } from "./Utils.Schema";
import { TDesign } from 'type';

export interface DesignSchema extends TDesign {

}

// For Typescript type stuff
export interface DesignModel extends Model<DesignSchema>, DesignSchema { }
export type DesignStatic = typeof Model & {
  new(values?: object, options?: BuildOptions): DesignModel;
};
export const tableDefine = {
  name: "designs",
  columns: {
    id: {
      type: DataTypes.TEXT,
      primaryKey: true,
    },
    productId: {
      type: DataTypes.TEXT,
    },
    name: {
      type: DataTypes.TEXT,
    },
    description: {
      type: DataTypes.TEXT,
    },
    image: {
      type: DataTypes.TEXT,
    },
    unit: {
      type: DataTypes.TEXT,
      defaultValue: 'mm',
    },
    width: {
      type: DataTypes.FLOAT,
      defaultValue: 0,
    },
    height: {
      type: DataTypes.FLOAT,
      defaultValue: 0,
    },
    printAreas: {
      type: DataTypes.TEXT,
      ...stringifyDataType("printAreas"),
    },
    data: {
      type: DataTypes.TEXT,
      ...stringifyDataType("data"),
    },
    otherData: {
      type: DataTypes.TEXT,
      ...stringifyDataType("otherData"),
    },
    isCustomizable: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    createdByUserId: {
      type: DataTypes.TEXT,
    },
    createdByUserType: {
      type: DataTypes.TEXT,
    },
    availableForResellerIds: {
      type: DataTypes.JSONB,
    },
    products: {
      type: DataTypes.TEXT,
      ...stringifyDataType('products'),
      defaultValue: '[]',
    },
    wholeSale: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    printOnDemand: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    customProduct: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    resalePrice: {
      type: DataTypes.FLOAT,
      defaultValue: 0,
    },
    inactive: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    brands: {
      type: DataTypes.TEXT,
      ...stringifyDataType('brands'),
      defaultValue: '[]',
    },
    galleries: {
      type: DataTypes.TEXT,
      ...stringifyDataType("galleries"),
    },
    parentDesignId: {
      type: DataTypes.TEXT,
      defaultValue: '',
    },
    variants: {
      type: DataTypes.TEXT,
      ...stringifyDataType('variants'),
      defaultValue: '[]',
    },
    editorWidth: {
      type: DataTypes.FLOAT,
      defaultValue: 0,
    },
    editorHeight: {
      type: DataTypes.FLOAT,
      defaultValue: 0,
    }
  }
};

export const createDesign = async (instance: Sequelize): Promise<DesignStatic> => {
  const Design = <DesignStatic>instance.define(
    tableDefine.name,
    tableDefine.columns,
    {
      indexes: [],
    }
  );

  Design.beforeSave((Design: DesignModel, options) => {});

  await Design.sync({ force: false });
  return Design;
};
