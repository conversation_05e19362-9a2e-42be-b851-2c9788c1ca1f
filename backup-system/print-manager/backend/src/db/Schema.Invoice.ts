import { BuildOptions, Model, DataTypes, Sequelize } from "sequelize";
import { stringifyDataType } from "./Utils.Schema";
import { TInvoice } from 'type';

export interface InvoiceSchema extends TInvoice {
  
}

// For Typescript type stuff
export interface InvoiceModel extends Model<InvoiceSchema>, InvoiceSchema {}
export type InvoiceStatic = typeof Model & {
  new (values?: object, options?: BuildOptions): InvoiceModel;
};

export const tableDefine = {
  name: "invoices",
  columns: {
    id: {
      type: DataTypes.TEXT,
      primaryKey: true,
    },
    orderId: {
      type: DataTypes.TEXT,
    },
    orderNumber: {
      type: DataTypes.TEXT,
    },
    store: {
      type: DataTypes.TEXT,
    },
    lineItems: {
      type: DataTypes.TEXT,
      ...stringifyDataType("lineItems"),
    },
    customerInfo: {
      type: DataTypes.TEXT,
      ...stringifyDataType("customerInfo"),
    },
    paidAt: {
      type: DataTypes.DATE,
    },
    refundAt: {
      type: DataTypes.DATE,
    },
    fulfilledAt: {
      type: DataTypes.DATE,
    },
    resellerId: {
      type: DataTypes.TEXT,
    },
    total: {
      type: DataTypes.FLOAT,
    },
    taxes: {
      type: DataTypes.FLOAT,
    },
    prices: {
      type: DataTypes.TEXT,
      ...stringifyDataType("prices"),
    },
    data: {
      type: DataTypes.TEXT,
      ...stringifyDataType("data"),
    },
    shippingAddress: {
      type: DataTypes.TEXT,
      ...stringifyDataType("shippingAddress"),
    },
    manualInvoicePdf: {
      type: DataTypes.TEXT,
      defaultValue: '',
    },
    manualInvoiceData: {
      type: DataTypes.TEXT,
      ...stringifyDataType("manualInvoiceData"),
      defaultValue: '{}',
    },
  }
};

export const createInvoice = async (instance: Sequelize): Promise<InvoiceStatic> => {
  const Invoice = <InvoiceStatic>instance.define(
    tableDefine.name,
    tableDefine.columns,
    {
      indexes: [],
    }
  );

  Invoice.beforeSave((Invoice: InvoiceModel, options) => {});

  await Invoice.sync({ force: false });
  return Invoice;
};

