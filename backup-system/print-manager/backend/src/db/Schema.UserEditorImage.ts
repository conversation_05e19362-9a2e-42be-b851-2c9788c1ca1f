import { BuildOptions, Model, DataTypes, Sequelize } from "sequelize";
import { stringifyDataType } from "./Utils.Schema";
import { TUserEditorImage } from 'type';

export interface UserEditorImageSchema extends TUserEditorImage {
  
}

// For Typescript type stuff
export interface UserEditorImageModel extends Model<UserEditorImageSchema>, UserEditorImageSchema {}
export type UserEditorImageStatic = typeof Model & {
  new (values?: object, options?: BuildOptions): UserEditorImageModel;
};

export const tableDefine = {
  name: "userEditorImages",
  columns: {
    id: {
      type: DataTypes.TEXT,
      primaryKey: true,
    },
    userId: {
      type: DataTypes.TEXT,
    },
    images: {
      type: DataTypes.TEXT,
      ...stringifyDataType("images"),
    },
  }
};

export const createUserEditorImage = async (instance: Sequelize): Promise<UserEditorImageStatic> => {
  const UserEditorImage = <UserEditorImageStatic>instance.define(
    tableDefine.name,
    tableDefine.columns,
    {
      indexes: [],
    }
  );

  UserEditorImage.beforeSave((UserEditorImage: UserEditorImageModel, options) => {});

  await UserEditorImage.sync({ force: false });
  return UserEditorImage;
};
