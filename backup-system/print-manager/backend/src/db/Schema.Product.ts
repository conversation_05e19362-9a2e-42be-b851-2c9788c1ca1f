import { BuildOptions, Model, DataTypes, Sequelize } from "sequelize";
import { stringifyDataType } from "./Utils.Schema";
import { TProduct } from 'type';

export interface ProductSchema extends TProduct {
  
}

// For Typescript type stuff
export interface ProductModel extends Model<ProductSchema>, ProductSchema {}
export type ProductStatic = typeof Model & {
  new (values?: object, options?: BuildOptions): ProductModel;
};
export const tableDefine = {
  name: "products",
  columns: {
    id: {
      type: DataTypes.TEXT,
      primaryKey: true,
    },
    name: {
      type: DataTypes.TEXT,
    },
    description: {
      type: DataTypes.TEXT,
    },
    image: {
      type: DataTypes.TEXT,
    },
    banner: {
      type: DataTypes.TEXT,
    },
    label: {
      type: DataTypes.TEXT,
    },
    secondLabel: {
      type: DataTypes.TEXT,
      defaultValue: '',
    },
    estimatedLeadTime: {
      type: DataTypes.TEXT,
    },
    volume: {
      type: DataTypes.TEXT,
    },
    material: {
      type: DataTypes.TEXT,
    },
    bluePrintImage: {
      type: DataTypes.TEXT,
    },
    galleries: {
      type: DataTypes.TEXT,
      ...stringifyDataType("galleries"),
    },
    category: {
      type: DataTypes.TEXT,
    },
    unit: {
      type: DataTypes.TEXT,
      defaultValue: 'mm',
    },
    physicalWidth: {
      type: DataTypes.FLOAT,
      defaultValue: 0,
    },
    physicalHeight: {
      type: DataTypes.FLOAT,
      defaultValue: 0,
    },
    printAreas: {
      type: DataTypes.TEXT,
      ...stringifyDataType("printAreas"),
    },
    data: {
      type: DataTypes.TEXT,
      ...stringifyDataType("data"),
    },
    previewData: {
      type: DataTypes.TEXT,
      ...stringifyDataType("previewData"),
    },
    createdByUserId: {
      type: DataTypes.TEXT,
    },
    createdByUserType: {
      type: DataTypes.TEXT,
    },
    availableForResellerIds: {
      type: DataTypes.JSONB,
    },
    tags: {
      type: DataTypes.TEXT,
    },
    variations: {
      type: DataTypes.TEXT,
      defaultValue: '[]',
      ...stringifyDataType("variations"),
    },
    originalImages: {
      type: DataTypes.TEXT,
      ...stringifyDataType("originalImages"),
    },
    wholeSale: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    printOnDemand: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    customProduct: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    price: {
      type: DataTypes.FLOAT,
      defaultValue: 0,
    },
    originalPrice: {
      type: DataTypes.FLOAT,
      defaultValue: 0,
    },
    packagingDescription: {
      type: DataTypes.TEXT,
      defaultValue: '',
    },
    packagingImage: {
      type: DataTypes.TEXT,
      defaultValue: '',
    },
    artboardUrl: {
      type: DataTypes.TEXT,
      defaultValue: '',
    },
    dropletUrl: {
      type: DataTypes.TEXT,
      defaultValue: '',
    },
    printerIdentificatorCode: {
      type: DataTypes.TEXT,
      defaultValue: '',
    },
    editorWidth: {
      type: DataTypes.FLOAT,
      defaultValue: 0,
    },
    editorHeight: {
      type: DataTypes.FLOAT,
      defaultValue: 0,
    },
    productWidth: {
      type: DataTypes.FLOAT,
      defaultValue: 0,
    },
    productHeight: {
      type: DataTypes.FLOAT,
      defaultValue: 0,
    },
    dpi: {
      type: DataTypes.FLOAT,
      defaultValue: 0,
    },
    packPrices: {
      type: DataTypes.TEXT,
      defaultValue: '[]',
      ...stringifyDataType("packPrices"),
    },
  }
};

export const createProduct = async (instance: Sequelize): Promise<ProductStatic> => {
  const Product = <ProductStatic>instance.define(
    tableDefine.name,
    tableDefine.columns,
    {
      indexes: [],
    }
  );

  Product.beforeSave((Product: ProductModel, options) => {});

  await Product.sync({ force: false });
  return Product;
};
