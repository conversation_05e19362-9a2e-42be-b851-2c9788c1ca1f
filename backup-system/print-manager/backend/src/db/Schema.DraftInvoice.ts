import { BuildOptions, Model, DataTypes, Sequelize } from "sequelize";
import { stringifyDataType } from "./Utils.Schema";
import { TDraftInvoice } from 'type';

export interface DraftInvoiceSchema extends TDraftInvoice {
  
}

// For Typescript type stuff
export interface DraftInvoiceModel extends Model<DraftInvoiceSchema>, DraftInvoiceSchema {}
export type DraftInvoiceStatic = typeof Model & {
  new (values?: object, options?: BuildOptions): DraftInvoiceModel;
};
export const tableDefine = {
  name: "draft_invoices",
  columns: {
    id: {
      type: DataTypes.TEXT,
      primaryKey: true,
    },
    resellerId: {
      type: DataTypes.TEXT,
    },
    productId: {
      type: DataTypes.TEXT,
    },
    designId: {
      type: DataTypes.TEXT,
    },
    quantity: {
      type: DataTypes.INTEGER,
    },
    status: {
      type: DataTypes.TEXT,
    },
    type: {
      type: DataTypes.TEXT,
    },
    data: {
      type: DataTypes.TEXT,
      ...stringifyDataType("data"),
    },
  }
};

export const createDraftInvoice = async (instance: Sequelize): Promise<DraftInvoiceStatic> => {
  const DraftInvoice = <DraftInvoiceStatic>instance.define(
    tableDefine.name,
    tableDefine.columns,
    {
      indexes: [],
    }
  );

  DraftInvoice.beforeSave((DraftInvoice: DraftInvoiceModel, options) => {});

  await DraftInvoice.sync({ force: false });
  return DraftInvoice;
};
