import { BuildOptions, Model, DataTypes, Sequelize } from "sequelize";
import { stringifyDataType } from "./Utils.Schema";
import { TProductInstance } from 'type';

export interface ProductInstanceSchema extends TProductInstance {
  
}

// For Typescript type stuff
export interface ProductInstanceModel extends Model<ProductInstanceSchema>, ProductInstanceSchema {}
export type ProductInstanceStatic = typeof Model & {
  new (values?: object, options?: BuildOptions): ProductInstanceModel;
};
export const tableDefine = {
  name: "product_instances",
  columns: {
    id: {
      type: DataTypes.TEXT,
      primaryKey: true,
    },
    name: {
      type: DataTypes.TEXT,
    },
    image: {
      type: DataTypes.TEXT,
    },
    productId: {
      type: DataTypes.TEXT,
    },
    designId: {
      type: DataTypes.TEXT,
    },
    price: {
      type: DataTypes.TEXT,
    },
    skuNumber: {
      type: DataTypes.TEXT,
    },
    isCustomizable: {
      type: DataTypes.BOOLEAN,
    },
    createdByUserId: {
      type: DataTypes.TEXT,
    },
    createdByUserType: {
      type: DataTypes.TEXT,
    },
  }
};

export const createProductInstance = async (instance: Sequelize): Promise<ProductInstanceStatic> => {
  const ProductInstance = <ProductInstanceStatic>instance.define(
    tableDefine.name,
    tableDefine.columns,
    {
      indexes: [],
    }
  );

  ProductInstance.beforeSave((ProductInstance: ProductInstanceModel, options) => {});

  await ProductInstance.sync({ force: false });
  return ProductInstance;
};
