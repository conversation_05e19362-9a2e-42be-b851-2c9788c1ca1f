import dbInstace from './DB.Postgres';

import * as LogSchema from './Schema.Log';
import * as UserSchema from './Schema.User';
import * as GeneralDataSchema from './Schema.GeneralData';
import * as PrintJobSchema from './Schema.PrintJob';
import * as ProductSchema from './Schema.Product';
import * as DesignSchema from './Schema.Design';
import * as ProductInstanceSchema from './Schema.ProductInstance';
import * as OnlineStoreSchema from './Schema.OnlineStore';
import * as OrderSchema from './Schema.Order';
import * as DownloadSchema from './Schema.Download';
import * as InvoiceSchema from './Schema.Invoice';
import * as StickerSchema from './Schema.Sticker';
import * as PackingSlipSchema from './Schema.PackingSlip';
import * as UserEditorImageSchema from './Schema.UserEditorImage';
import * as DraftInvoiceSchema from './Schema.DraftInvoice';
import * as SurveyAnwserSchema from './Schema.SurveyAnwser';

const ALL_SCHEMAS = [
  LogSchema, UserSchema, GeneralDataSchema, PrintJobSchema, ProductSchema, DesignSchema,
  ProductInstanceSchema, OnlineStoreSchema, OrderSchema, DownloadSchema,
  InvoiceSchema, StickerSchema, PackingSlipSchema, UserEditorImageSchema, DraftInvoiceSchema,
  SurveyAnwserSchema,
];

export const migration = async (DB: typeof dbInstace) => {
  await Promise.all(
    ALL_SCHEMAS.map(async (schema) => {
      const tableName = schema.tableDefine.name;
      const tableDefinition = await DB.instance.queryInterface.describeTable(tableName);
      await Promise.all(
        Object.keys(schema.tableDefine.columns).map(async (colName) => {
          if (!tableDefinition[colName]) {
            const { type, defaultValue } = schema.tableDefine.columns[colName]
            await DB.instance.queryInterface.addColumn(tableName, colName, {
              type,
              defaultValue: defaultValue || null,
            });
            console.log(`Auto migrate: Add column ${tableName}.${colName}`)
          }
        })
      )
    })
  )
}
