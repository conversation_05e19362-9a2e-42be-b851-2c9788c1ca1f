import { BuildOptions, Model, DataTypes, Sequelize } from "sequelize";
import { stringifyDataType } from "./Utils.Schema";
import { TSurveyAnwser } from 'type';

export interface SurveyAnwserSchema extends TSurveyAnwser {
  
}

// For Typescript type stuff
export interface SurveyAnwserModel extends Model<SurveyAnwserSchema>, SurveyAnwserSchema {}
export type SurveyAnwserStatic = typeof Model & {
  new (values?: object, options?: BuildOptions): SurveyAnwserModel;
};
export const tableDefine = {
  name: "survey_anwsers",
  columns: {
    id: {
      type: DataTypes.TEXT,
      primaryKey: true,
    },
    userId: {
      type: DataTypes.TEXT,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    type: {
      type: DataTypes.TEXT,
    },
    field1: {
      type: DataTypes.TEXT,
    },
    field2: {
      type: DataTypes.TEXT,
    },
    field3: {
      type: DataTypes.TEXT,
    },
    field4: {
      type: DataTypes.TEXT,
    },
    field5: {
      type: DataTypes.TEXT,
    },
    field6: {
      type: DataTypes.TEXT,
    },
    field7: {
      type: DataTypes.TEXT,
    },
    field8: {
      type: DataTypes.TEXT,
    },
    field9: {
      type: DataTypes.TEXT,
    },
    data: {
      type: DataTypes.TEXT,
      ...stringifyDataType("data"),
    },
  }
};

export const createSurveyAnwser = async (instance: Sequelize): Promise<SurveyAnwserStatic> => {
  const SurveyAnwser = <SurveyAnwserStatic>instance.define(
    tableDefine.name,
    tableDefine.columns,
    {
      indexes: [],
    }
  );

  SurveyAnwser.beforeSave((SurveyAnwser: SurveyAnwserModel, options) => {});

  await SurveyAnwser.sync({ force: false });
  return SurveyAnwser;
};
