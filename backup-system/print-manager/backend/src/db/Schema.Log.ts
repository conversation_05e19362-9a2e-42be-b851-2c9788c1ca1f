import { BuildOptions, Model, DataTypes, Sequelize } from "sequelize";
import { stringifyDataType } from "./Utils.Schema";
import { TLog } from 'type';

export interface LogSchema extends TLog { }

// For Typescript type stuff
export interface LogModel extends Model<LogSchema>, LogSchema { }
export type LogStatic = typeof Model & {
  new(values?: object, options?: BuildOptions): LogModel;
};

export const tableDefine = {
  name: "logs",
  columns: {
    id: {
      type: DataTypes.TEXT,
      primaryKey: true,
    },
    url: {
      type: DataTypes.TEXT,
    },
    type: {
      type: DataTypes.TEXT,
    },
    body: {
      type: DataTypes.TEXT,
      ...stringifyDataType("body"),
    },
    otherData: {
      type: DataTypes.TEXT,
      ...stringifyDataType("otherData"),
    },
    userId: {
      type: DataTypes.TEXT,
    },
  }
}

export const createLog = async (instance: Sequelize): Promise<LogStatic> => {
  const Log = <LogStatic>instance.define(
    tableDefine.name,
    tableDefine.columns,
    {
      indexes: [],
    }
  );

  Log.beforeSave((Log: LogModel, options) => { });

  await Log.sync({ force: false });
  return Log;
};
