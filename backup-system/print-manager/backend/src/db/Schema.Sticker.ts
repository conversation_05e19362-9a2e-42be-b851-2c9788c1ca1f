import { BuildOptions, Model, DataTypes, Sequelize } from "sequelize";
import { TSticker } from 'type';

export interface StickerSchema extends TSticker {
  
}

// For Typescript type stuff
export interface StickerModel extends Model<StickerSchema>, StickerSchema {}
export type StickerStatic = typeof Model & {
  new (values?: object, options?: BuildOptions): StickerModel;
};

export const tableDefine = {
  name: "stickers",
  columns: {
    id: {
      type: DataTypes.TEXT,
      primaryKey: true,
    },
    resellerId: {
      type: DataTypes.TEXT,
      defaultValue: '',
    },
    url: {
      type: DataTypes.TEXT,
      defaultValue: '',
    }
  }
};

export const createSticker = async (instance: Sequelize): Promise<StickerStatic> => {
  const Sticker = <StickerStatic>instance.define(
    tableDefine.name,
    tableDefine.columns,
    {
      indexes: [],
    }
  );

  Sticker.beforeSave((Sticker: StickerModel, options) => {});

  await Sticker.sync({ force: false });
  return Sticker;
};
