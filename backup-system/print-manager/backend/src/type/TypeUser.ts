
export type TUserType = 'guess' | 'admin' | 'user' | 'reseller';

export type TUser = {
  id: string;
  clientId?: string;
  role: TUserType;
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  photoUrl?: string;
  otherData?: any;

  addressLine1?: string;
  addressLine2?: string;
  town?: string;
  country?: string;
  postCode?: string;

  resellerStripeId?: string;
  resellerId?: string;
  onlineStoreId?: string;
  onlineStore?: any;
  // db fields
  createdAt?: string;
  updatedAt?: string;

  isAutoAccept?: boolean;
  currency?: string;

  accountName?: string;
  walletBalance?: number;
  salesTotal?: number;
  vatTotal?: number;
  shippingTotal?: number;
  refundTotal?: number;
  sampleSalesTotal?: number;
  wholesaleTotal?: number;
  loggedInAt?: string;
}

export type TUserEditorImage = {
  id: string;
  userId: string;
  images: string[];
}
