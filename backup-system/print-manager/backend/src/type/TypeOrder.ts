

export type TOrder = {
  id: string;
  resellerId: string;
  productId: string;
  price: number;
  amount: number;
  variationName: string;
  status: string;
  downloadIds: {
    [downloadId: string]: number;
  };
  didUseAllDownload: boolean;
}

export type TDownload = {
  id: string; // printJobId
  queueId?: number;
  resellerId: string;
  productId: string;
  designId: string;
  linkedOrderId: string;
  variationName: string;
  pdf: string;
}

type TTodoUpdateLater = any;

export type TSingleLineItem = {
  id: number,
  name: string,
  title: string,
  price: string,
  product_id: string,
  properties: Array<{ name: string, value: string }>,
  quantity: number,
  sku: string,
  [other: string]: TTodoUpdateLater,
}

export type TCustomer = {
  id: number,
  email: string,
  accepts_marketing: boolean,
  first_name: string,
  last_name: string,
  state: string,
  note?: string,
  verified_email: boolean,
  multipass_identifier?: string,
  tax_exempt: boolean,
  tags: string,
  currency: string,
  phone?: string,
  admin_graphql_api_id: string,
}

export type TShopifyOrder = {
  id: number,
  created_at: string,
  currency: string,
  contact_email: string,
  customer: TCustomer,
  billing_address: TTodoUpdateLater,
  shipping_address: TTodoUpdateLater,
  line_items: Array<TSingleLineItem>,
  name: string,
  order_number: number,
  [other: string]: TTodoUpdateLater,
}

export type TShopifyListOrder = {
  limit: number;
  offset: number;
  filter: string;
};

type CommonStatus = 'New' | 'Processing' | 'Done' | 'Error'
type BGStatus = 'Accepted' | 'Pending' | 'Fulfilled' | 'Error'
export type OrderStageStatus = 'Awaiting Payment' | 'Queued For Production' | 'Held' | 'Held By Admin' | 'On Time' | 'Delayed'
export type OrderStage = 'Pre Production' | 'In Production' | 'Fulfillment' | 'Cancelled'
export type OrderType = 'Shopify' | 'Sample'

export type TCMSOrder = {
  'Order ID': string | number,
  'Order Source ID': string | number,
  'Order Name': string,
  'Order Number': number,
  'Customer Email': string,
  'Customer Name': string,
  'Item Processed': string,
  'Raw Data': TShopifyOrder,
  'All Item IDs': string,
  'Status': CommonStatus | BGStatus | string,
  [other: string]: TTodoUpdateLater,
  // CMS autogenerated
  Id: number,
  CreatedAt: string,
  UpdatedAt: string,
  ApiSlug?: string,
}
