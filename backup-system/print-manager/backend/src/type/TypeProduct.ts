

export type TProduct = {
  id: string,
  name: string,
  description?: string,
  image: string,
  banner?: string,
  label?: string,
  secondLabel?: string,
  estimatedLeadTime?: string,
  volume?: string,
  material?: string,
  bluePrintImage: string,
  galleries: Array<string>,
  category?: string,
  unit?: 'mm' | 'inch',
  editorWidth: number,
  editorHeight: number,
  physicalWidth: number,
  physicalHeight: number,
  printAreas: Array<{
    width: number,
    height: number,
    top: number,
    left: number,
  }>,
  data?: any,
  previewData?: Array<{
    groupTitle: string,
    previewType: string,
    mask?: string,
    droplet?: string,
    previewItems?: Array<{
      title: string,
      image: string,
      data: any,
    }>,
  }>,
  availableForResellerIds: {
    [resellerId: string]: boolean,
  },
  tags?: string,
  createdByUserId: string,
  createdByUserType: string,
  variations?: Array<{
    variant: string,
    prices: Array<{
      amount: string | number,
      price: string | number,
    }>,
  }>,
  originalImages?: {
    bluePrint: string,
    galleries: Array<string>,
    image: string,
  },
  wholeSale?: boolean,
  printOnDemand?: boolean,
  customProduct?: boolean,
  price?: number,
  originalPrice?: number,
  packagingDescription?: string,
  packagingImage?: string,
  artboardUrl?: string,
  dropletUrl?: string,
  printerIdentificatorCode?: string,
  productWidth: number,
  productHeight: number,
  dpi?: number,
  packPrices?: Array<{
    size: number,
    price?: number,
    discount?: number,
  }>,
}

export type TDesign = {
  id: string,
  productId: string,
  name: string,
  description?: string,
  image: string,
  unit?: 'mm' | 'inch',
  width: number,
  height: number,
  editorWidth: number,
  editorHeight: number,
  printAreas: TProduct['printAreas'],
  data?: any,
  availableForResellerIds: {
    [resellerId: string]: boolean,
  },
  isCustomizable: boolean,
  createdByUserId: string,
  createdByUserType: string,
  products?: Array<{
    url: string,
    storeId: string,
    productId: string,
    productAdminUrl: string,
    brand: string,
    matchDesignId?: {
      [variantId: string]: string, // shopify variant id: design Id
    }
    matchImageId?: {
      [url: string]: string,
    }
  }>,
  wholeSale?: boolean,
  printOnDemand?: boolean,
  customProduct?: boolean,
  resalePrice?: number,
  brands: Array<{
    storeId: string,
    name: string,
  }>,
  createdByUserName?: string,
  inactive?: boolean,
  otherData?: any,
  galleries?: Array<string>,
  parentDesignId?: string,
  variants?: Array<{
    style?: string,
    variantDesignId: string,
    image: string,
    galleries?: Array<string>,
    price?: number,
  }>,
}

export type TProductInstance = {
  id: string,
  designId: string,
  productId: string,
  name: string,
  image: string,
  isCustomizable: boolean,
  createdByUserId: string,
  createdByUserType: string,
  price: string,
  skuNumber: string,
}
