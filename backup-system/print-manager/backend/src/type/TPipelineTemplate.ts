import { TCMSOrder } from "./TypeOrder";

export interface TJobPayload {
  order?: TCMSOrder,
  additionData?: {
    [key: string]: any,
  }
}

export interface TJobTemplate {
  title: string,
  callbackUrl?: string,
  handler: (payload: TJobPayload, store: any) => Promise<any>,
  getOrderId?: (apiPayload: any) => string,
  handlerBeforeCallback?: (payload: TJobPayload, store: any) => Promise<any>,
}

export interface TStageTemplate {
  title: string,
  jobs: TJobTemplate[],
}
