

export type TProduct = {
  id: string,
  name: string,
  description?: string,
  image: string,
  bluePrintImage: string,
  galleries: Array<string>,
  category?: string,
  unit?: 'mm' | 'inch',
  physicalWidth: number,
  physicalHeight: number,
  printAreas: Array<{
    width: number,
    height: number,
    top: number,
    left: number,
  }>,
  data?: any,
  previewData?: Array<{
    groupTitle: string,
    previewType: string,
    mask?: string,
    droplet?: string,
    previewItems?: Array<{
      title: string,
      image: string,
      data: any,
    }>,
  }>,
  availableForResellerIds: {
    [resellerId: string]: boolean,
  },
  tags?: string,
  createdByUserId: string,
  createdByUserType: string,
  variations?: Array<{
    variant: string,
    prices: Array<{
      amount: string | number,
      price: string | number,
    }>,
  }>,
  originalImages?: {
    bluePrint: string,
    galleries: Array<string>,
    image: string,
  },
}
