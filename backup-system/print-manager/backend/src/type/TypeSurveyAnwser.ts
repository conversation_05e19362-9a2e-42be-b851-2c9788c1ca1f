
export type TSurveyAnwser = {
  id: string;
  userId: string;
  type: string;
  field1?: string;
  field2?: string;
  field3?: string;
  field4?: string;
  field5?: string;
  field6?: string;
  field7?: string;
  field8?: string;
  field9?: string;
  data: any;
  // db fields
  createdAt?: string;
  updatedAt?: string;
}

export type TSurveyAnwserData = TSurveyAnwser & {
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    accountName: string;
  }
}

export type TSurveyFormModel = {
  pages: Array<{
    elements: Array<{
      name: string;
      title: string;
      description: string;
      type: "radiogroup" | "dropdown" | "imagepicker";
      choices: Array<{
        value: string;
        text: string;
        imageLink?: string;
      } | string>;
      multiSelect: boolean;
      isRequired: boolean;
      showLabel?: boolean;
    }>;
  }>;
}
