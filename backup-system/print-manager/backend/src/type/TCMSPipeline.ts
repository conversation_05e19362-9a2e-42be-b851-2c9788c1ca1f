export type TJobStatus = 'Pending' | 'Running' | 'Failed' | 'Success' | 'Cancelled' | any;

export type TArtifact = {
  url: string,
  name: string,
  data?: any,
}

export type TCMSStage = {
  Title?: string,
  Jobs?: string[], // Job title
  StartTime?: number,
  EndTime?: number,
  Status?: TJobStatus,
}

export type TCMSJob = {
  StageId?: string,
  Idx?: number,
  Title?: string,
  Log?: string,
  Files?: TArtifact[],
  StartTime?: number,
  EndTime?: number,
  Status?: TJobStatus,
}

export type TCMSPipeline = {
  OrderId?: number | string,
  'Order Source ID'?: string,
  Stages?: {
    [key: string]: TCMSStage
  },
  Jobs?: {
    [key: string]: TCMSJob
  },
  SharedData?: {
    [key: string]: any
  },
  DeploymentAddress?: string,
  Status?: TJobStatus,
  StartTime?: number,
  EndTime?: number,
  // CMS autogenerated
  Id?: number,
  CreatedAt?: string,
  UpdatedAt?: string,
}

export type TCMSPipelineQuery = {
  limit: number;
  offset: number;
  startDate: string;
  endDate: string;
  orderId: string;
  searchByOrderId?: string;
  searchByOrderNumber?: string;
  sort?: string;
};
