

export type TInvoice = {
    id: string;
    resellerId?: string;
    orderId: string;
    total: number;
    taxes: number;
    data?: {
        [key: string]: any;
    };
    prices: {
        [productId: string]: number;
    };
    orderNumber?: string;
    store?: string;
    lineItems?: Array<any>;
    customerInfo?: {
        [key: string]: any;
    };
    shippingAddress?: {
        [key: string]: any;
    };
    paidAt?: string;
    refundAt?: string;
    fulfilledAt?: string;
    manualInvoicePdf?: string;
    manualInvoiceData?: any;
    // db fields
    createdAt?: string;
    updatedAt?: string;
}
