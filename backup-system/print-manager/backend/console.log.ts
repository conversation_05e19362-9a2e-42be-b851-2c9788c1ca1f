var log = console.log;
const axios = require('axios');

const sendToLogTail = (message: string) => {
    if (!process.env.LOG_TAIL_TOKEN) return;
    let data = JSON.stringify({
        "dt": "$(date -u +'%Y-%m-%d %T UTC')",
        "message": message
    });

    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://in.logs.betterstack.com',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + process.env.LOG_TAIL_TOKEN,
        },
        data: data
    };

    axios.request(config)
        .then((response) => {
            console.log(JSON.stringify(response.data));
        })
        .catch((error) => {
            console.log(error);
        });
}


console.log = function () {
    var first_parameter = arguments[0];
    var other_parameters = Array.prototype.slice.call(arguments, 1);

    function formatConsoleDate(date) {
        var hour = date.getHours();
        var minutes = date.getMinutes();
        var seconds = date.getSeconds();
        var milliseconds = date.getMilliseconds();

        return '[' +
            ((hour < 10) ? '0' + hour : hour) +
            ':' +
            ((minutes < 10) ? '0' + minutes : minutes) +
            ':' +
            ((seconds < 10) ? '0' + seconds : seconds) +
            '.' +
            ('00' + milliseconds).slice(-3) +
            '] ';
    }

    log.apply(console, [formatConsoleDate(new Date()), first_parameter].concat(other_parameters));
    try {
        const allArgs = Array.from(arguments);
        sendToLogTail(allArgs.map(arg => JSON.stringify(arg)).join(' '));
    } catch(err) {}
};