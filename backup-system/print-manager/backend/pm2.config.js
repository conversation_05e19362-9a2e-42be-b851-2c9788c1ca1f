module.exports = {
  apps: [
    {
      name: "bottled-goose",
      script: "./node_modules/.bin/ts-node ./index.ts",
      env: {
        NODE_PATH: "./src",
        PORT: 3000,
        FIREBASE_PROJECT_ID: "bottled-goose",
        FIREBASE_PRIVATE_KEY_ID: "6bff8635fdc09011f82a9ad86705a49848cf797c",
        FIREBASE_PRIVATE_KEY: "-----BEGIN PRIVATE KEY-----\nMIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQCo7HokGmbp5E1p\nGLkB3qNULPmnIt4gtYJwkmEsqe3+zoUDLxvDUDADVqeDIdko00eOKfG4ZbSvPAAq\naF/ocJLxDNuppyiR+zaC7WPaoaxMAZZRn0k/7/MyYrTUotVJ6g8iRobiOk<PERSON>22bi\nTX9vS1jcEH5fb4j+viL3SwWQVz5SlslI6hPSa4Z4E4KP56tt9+8DKFXQYaExy68B\nz1V3Nla2rZqXw8IaZB0EsFsISkecLrJseszCKlUMAnl2/Ju2JAlMQ+XUpcP2gZSt\nvrsoauUZYTloHk4kFhY7OMqUtQ1MkX9CEAx+wrLgBroG3uVqhkPsyYUg3u9JVTE1\n84pM5LmhAgMBAAECggEADHDB/AevvZjxNIS8F02otKnNRwhT2I/Vh+nX2zy2iNX3\nyMi+4C73Ij/O/WLQReftdKRVJ3HiESe3kWf3bDxiLft27/MeE0YveQv3lyHh+QUC\n54b2E43d2L+ZNrG5SITC0ZDlHSMSRROAa7kuYeloJWjXjbq+mZgyJgkx4zPxpI5U\nlRdKX3r4nGHNQT+Z1Vtw/LVzDgMy4wMjLEWnQKaYG8fRi9A4+MQPLvsK6flsZ5S4\n3XMXwUj4b3iLk9YEa6Ir8tTVYE2DDnOtWebaCxpyZaMzs7VO8flIVDMqkTKHoDfF\nn7xwR/3gL2OS35ONr+WgO2gfoFmxmUxCo5I2IhW7CwKBgQDf0nV//5SvMgnpBLTe\nz58dZXj6MOHEsrdlm0URvw28VFIyjwo1uNB/PQnhP9nBHVokxQSzbxSgWsQaVVfR\nNcjfkQT3jGZQA/gMq4+zPXy8rW1cO8j7j0tOPGHles2lBLQXwFhTo8XyHbq2Qa2f\nypeIlEAzemLs08XIQlWXmiqMbwKBgQDBNYphBd9ZXPtuQpEuNx2UsOOWvRvwN14A\n0cVMHDN9N/R9j8BtsqW7ev6pQNmhra9SX01Mx8MOmWBeNaKMukxH4oHiJ71ufHTo\nsnrGkv1anrvPBCDHH22L5skrd3ZVmOqKTQTDM7c7561AJiC8YpMY9epNP9WEpcgP\ngfxSoflC7wKBgQC3r8hAsOc8VOuvMe6igXBLNtDvO5i9zhIXMDOiGdLTAbynw56U\nckWidtyZ4MX75/PoA00VWY9q/rK8DdhRWTuuxxx1cOMyvd8fvtjjOA4b5pS+wk3y\nrioWXhXVuE/3IwIOEAwtMFYumtl15Dby0dhE/a4m0UVIqZBDBrWqApryvwKBgQCe\n+RLXKiHQQbWPz63tPORWCAX7bbajPKtYOUIBkeK70WKWPcWzfR7o2BbZ5MYtRd/Q\nAyJzXpqcVI76dV8+M7u4GiOMle5EDpuTK+fho+hau+kEOP1PWgik6Ev2O+FeJOg2\ny5BznQXTgJkJA1ZEK0elbhJPZYqmDlJCkE2xIB0ceQKBgQDeHXiCrJCwBS2PM5U/\nSVn53nkC2LB/WufmqUddy1TE9A0enTI28M282UBQFcEkc9XHiDAj0A9rZzwmFTyt\nFiVKPNlR7sr/BAWq09KjZAQ9u47Ir7Ei5owvVkB6I1+OrULGzq4/njc3ZR2+8frj\nXEnbn+un7XlMGB3TCwxKvDpN0A==\n-----END PRIVATE KEY-----\n",
        FIREBASE_CLIENT_EMAIL: "<EMAIL>",
        FIREBASE_CLIENT_ID: 103277970770245784776,
        FIREBASE_CLIENT_CERT_URL: "https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-fbsvc%40bottled-goose.iam.gserviceaccount.com"
      },
    },
    {
      name: "bottled-goose-cron",
      script: "./node_modules/.bin/ts-node ./index.cron.ts",
      env: {
        NODE_PATH: "./src",
        PORT: 3001,
      },
    },
  ],
};
