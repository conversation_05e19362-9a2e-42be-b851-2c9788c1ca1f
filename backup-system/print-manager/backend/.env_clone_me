DEV=1
PORT=3001
DB_CONNECTION_STRING_PROD=postgresql://print-manager:print-manager@127.0.0.1:5422/print-manager
DB_CONNECTION_STRING_DEV=postgresql://print-manager:print-manager@127.0.0.1:17422/print-manager
SEED_ADMIN=<EMAIL>
SEED_PASSWORD=CD%2022
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=KpHHeDHu102sKq8bR6kbLUtDIuRLBvUGdTl9AK6z
AWS_S3_BUCKET=print-manager-media
AWS_REGION=eu-west-1
GENERATE_API_CLIENT=1
SHOPIFY_ACCESS_TOKEN=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
SHOPIFY_SECRET=e5f81eec65a3416435af0f63d9fca727
MICRO_API_TOKEN=P8UeTt92HS98
SEED_ADMINS=[<EMAIL>|CD%2022|Tom|BG][<EMAIL>|CD%2022|Martha|BG][<EMAIL>|CD%2022|Dev|CD][<EMAIL>|CD%2022|Admin|CD][<EMAIL>|BG%2022|Tom|BG]
