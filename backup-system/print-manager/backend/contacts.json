[{"ContactID": "e87dcf91-8b17-42d0-9025-4603cb30d0dd", "ContactStatus": "ACTIVE", "Name": "Luna Cafe", "Addresses": [{"AddressType": "POBOX"}, {"AddressType": "STREET"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": false, "IsCustomer": true, "Balances": {"AccountsReceivable": {"Outstanding": 145, "Overdue": 0}, "AccountsPayable": {"Outstanding": 0, "Overdue": 0}}, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "4544fff7-d5fd-4500-bed7-93cdcf681cab", "ContactStatus": "ACTIVE", "Name": "Eastside Club", "Addresses": [{"AddressType": "POBOX"}, {"AddressType": "STREET"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": false, "IsCustomer": false, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "120bee57-d9ae-46c5-8765-bdc726f765d2", "ContactStatus": "ACTIVE", "Name": "RITE Agency", "Addresses": [{"AddressType": "POBOX"}, {"AddressType": "STREET"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": false, "IsCustomer": false, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "de9dbd75-7914-4d14-9d48-895d3fa90b4d", "ContactStatus": "ACTIVE", "Name": "Abby & Wells", "Addresses": [{"AddressType": "POBOX"}, {"AddressType": "STREET"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(1709636969183+0000)/", "ContactGroups": [], "IsSupplier": false, "IsCustomer": false, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "148be4b2-2d98-4f7f-8a79-9165b9d96c10", "ContactNumber": "d7ac3cf2bb4b6f777c4b3a791b71d8d20122cef3fe4fec32b4", "ContactStatus": "ACTIVE", "Name": "<PERSON>", "FirstName": "<PERSON>", "LastName": "<PERSON><PERSON><PERSON><PERSON>", "EmailAddress": "<EMAIL>", "Addresses": [{"AddressType": "POBOX"}, {"AddressType": "STREET"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": true, "IsCustomer": false, "Balances": {"AccountsReceivable": {"Outstanding": 0, "Overdue": 0}, "AccountsPayable": {"Outstanding": 18.9, "Overdue": 18.9}}, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "de5a76b0-81e4-494d-9eb7-d94d22092abb", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON>'s <PERSON><PERSON>", "Addresses": [{"AddressType": "POBOX"}, {"AddressType": "STREET"}], "Phones": [{"PhoneType": "DDI"}, {"PhoneType": "DEFAULT"}, {"PhoneType": "FAX"}, {"PhoneType": "MOBILE"}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": false, "IsCustomer": false, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "adf801de-a142-4670-906f-0d46c5d7c693", "ContactStatus": "ACTIVE", "Name": "Gable Print", "EmailAddress": "", "BankAccountDetails": "", "Addresses": [{"AddressType": "POBOX", "City": "", "Region": "", "PostalCode": "", "Country": ""}, {"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": false, "IsCustomer": false, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "74ea95ea-6e1e-435d-9c30-0dff8ae1bd80", "ContactStatus": "ACTIVE", "Name": "Office Supplies Company", "EmailAddress": "", "BankAccountDetails": "", "Addresses": [{"AddressType": "POBOX", "City": "", "Region": "", "PostalCode": "", "Country": ""}, {"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": false, "IsCustomer": false, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "bc446de5-971e-48b5-8efd-1745149844ef", "ContactStatus": "ACTIVE", "Name": "Wilson Periodicals", "EmailAddress": "", "BankAccountDetails": "", "Addresses": [{"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}, {"AddressType": "POBOX", "City": "", "Region": "", "PostalCode": "", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "289373e2-745c-4743-8b58-3008351f83dd", "ContactStatus": "ACTIVE", "Name": "Fulton Airport Parking", "EmailAddress": "", "BankAccountDetails": "", "Addresses": [{"AddressType": "POBOX", "City": "", "Region": "", "PostalCode": "", "Country": ""}, {"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": false, "IsCustomer": false, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "2cac4876-91fb-4cb7-b969-340aa548f64a", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON> A <PERSON>", "EmailAddress": "", "BankAccountDetails": "", "Addresses": [{"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}, {"AddressType": "POBOX", "City": "", "Region": "", "PostalCode": "", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "5a83dcee-ee21-4ee8-b53b-381b93346256", "ContactStatus": "ACTIVE", "Name": "Brunswick Petals", "EmailAddress": "", "BankAccountDetails": "", "Addresses": [{"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}, {"AddressType": "POBOX", "City": "", "Region": "", "PostalCode": "", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": false, "IsCustomer": false, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "737982b0-2811-44c9-bdb3-3b26a3a6ef8c", "ContactStatus": "ACTIVE", "Name": "Ridgeway Bank", "EmailAddress": "", "BankAccountDetails": "", "Addresses": [{"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}, {"AddressType": "POBOX", "City": "", "Region": "", "PostalCode": "", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": false, "IsCustomer": false, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "f1c18eb1-cdc5-4f11-9867-666cbbc397fe", "ContactStatus": "ACTIVE", "Name": "Coco Cafe", "EmailAddress": "", "BankAccountDetails": "", "Addresses": [{"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}, {"AddressType": "POBOX", "City": "", "Region": "", "PostalCode": "", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": false, "IsCustomer": false, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "e4c9d0e2-c285-4e85-b579-6d28b180c730", "ContactStatus": "ACTIVE", "Name": "24 Locks", "EmailAddress": "", "BankAccountDetails": "", "Addresses": [{"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}, {"AddressType": "POBOX", "City": "", "Region": "", "PostalCode": "", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": false, "IsCustomer": false, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "b9d4332a-26a3-4577-8db2-6e830d4b07cd", "ContactStatus": "ACTIVE", "Name": "<PERSON>", "EmailAddress": "", "BankAccountDetails": "", "Addresses": [{"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}, {"AddressType": "POBOX", "City": "", "Region": "", "PostalCode": "", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": false, "IsCustomer": false, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "0b787601-82f5-438f-9fb7-7eeb0a09b2a0", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON>ing", "EmailAddress": "", "BankAccountDetails": "", "Addresses": [{"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}, {"AddressType": "POBOX", "City": "", "Region": "", "PostalCode": "", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": false, "IsCustomer": false, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "c82900a5-064c-46e1-9d8b-86404c6bfd01", "ContactStatus": "ACTIVE", "Name": "Espresso 31", "EmailAddress": "", "BankAccountDetails": "", "Addresses": [{"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}, {"AddressType": "POBOX", "City": "", "Region": "", "PostalCode": "", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": false, "IsCustomer": false, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "bab779bc-c5fb-42cb-a888-953e8309711c", "ContactStatus": "ACTIVE", "Name": "7-Eleven", "EmailAddress": "", "BankAccountDetails": "", "Addresses": [{"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}, {"AddressType": "POBOX", "City": "", "Region": "", "PostalCode": "", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": false, "IsCustomer": false, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "4b0dd2ab-7cce-4a8a-8bdb-d49c3e2faf97", "ContactStatus": "ACTIVE", "Name": "Dimples Warehouse", "Addresses": [{"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}, {"AddressType": "POBOX", "City": "", "Region": "", "PostalCode": "", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "be45660b-46fe-412b-9c2e-f667fc5007c3", "ContactStatus": "ACTIVE", "Name": "Woolworths Market", "EmailAddress": "", "BankAccountDetails": "", "Addresses": [{"AddressType": "POBOX", "City": "", "Region": "", "PostalCode": "", "Country": ""}, {"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": false, "IsCustomer": false, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "4acf731f-af81-4ed3-96b2-0c408b2d98c0", "ContactStatus": "ACTIVE", "Name": "Carlton Functions", "EmailAddress": "", "BankAccountDetails": "", "Addresses": [{"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}, {"AddressType": "POBOX", "City": "", "Region": "", "PostalCode": "", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "9179945", "PhoneAreaCode": "02", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": true, "IsCustomer": false, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "f5a77e82-50e3-4340-a6e0-13d6a482a08a", "ContactStatus": "ACTIVE", "Name": "SMART Agency", "EmailAddress": "", "BankAccountDetails": "", "Addresses": [{"AddressType": "POBOX", "City": "", "Region": "", "PostalCode": "", "Country": ""}, {"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "9159889", "PhoneAreaCode": "02", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": true, "IsCustomer": false, "Balances": {"AccountsReceivable": {"Outstanding": 0, "Overdue": 0}, "AccountsPayable": {"Outstanding": 4500, "Overdue": 4500}}, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "aca6e01a-1815-474c-bd0f-18adfd95cfcb", "ContactStatus": "ACTIVE", "Name": "Truxton Property Management", "EmailAddress": "", "BankAccountDetails": "", "Addresses": [{"AddressType": "POBOX", "City": "", "Region": "", "PostalCode": "", "Country": ""}, {"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "9157357", "PhoneAreaCode": "02", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": true, "IsCustomer": false, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "cf8fa320-a527-496c-823e-22dd069d29e6", "ContactStatus": "ACTIVE", "Name": "PC Complete", "EmailAddress": "", "BankAccountDetails": "", "Addresses": [{"AddressType": "POBOX", "City": "", "Region": "", "PostalCode": "", "Country": ""}, {"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "322600", "PhoneAreaCode": "800", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": true, "IsCustomer": false, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "3a0d40a2-2698-4cf5-b7b2-30133c632ab6", "ContactStatus": "ACTIVE", "Name": "Swanston Security", "EmailAddress": "", "BankAccountDetails": "", "Addresses": [{"AddressType": "POBOX", "City": "", "Region": "", "PostalCode": "", "Country": ""}, {"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "112113", "PhoneAreaCode": "800", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": true, "IsCustomer": false, "Balances": {"AccountsReceivable": {"Outstanding": 0, "Overdue": 0}, "AccountsPayable": {"Outstanding": 93.64, "Overdue": 93.64}}, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "8a593982-291c-4ec3-9a42-3dbccbc6e3c8", "ContactStatus": "ACTIVE", "Name": "MCO Cleaning Services", "EmailAddress": "", "BankAccountDetails": "", "Addresses": [{"AddressType": "POBOX", "City": "", "Region": "", "PostalCode": "", "Country": ""}, {"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "5119119", "PhoneAreaCode": "02", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": true, "IsCustomer": false, "Balances": {"AccountsReceivable": {"Outstanding": 0, "Overdue": 0}, "AccountsPayable": {"Outstanding": 119.08, "Overdue": 119.08}}, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "699f0091-b127-4796-9f15-41a2f42abeb2", "ContactStatus": "ACTIVE", "Name": "ABC Furniture", "FirstName": "Trish", "LastName": "Rawlings", "EmailAddress": "<EMAIL>", "BankAccountDetails": "", "Addresses": [{"AddressType": "POBOX", "City": "", "Region": "", "PostalCode": "", "Country": ""}, {"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "124578", "PhoneAreaCode": "800", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": true, "IsCustomer": false, "DefaultCurrency": "GBP", "Balances": {"AccountsReceivable": {"Outstanding": 0, "Overdue": 0}, "AccountsPayable": {"Outstanding": 1200, "Overdue": 1200}}, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "f559658a-68da-42b1-81ae-42a96b6c6953", "ContactStatus": "ACTIVE", "Name": "Gateway Motors", "EmailAddress": "", "BankAccountDetails": "", "Addresses": [{"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}, {"AddressType": "POBOX", "City": "", "Region": "", "PostalCode": "", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "349227", "PhoneAreaCode": "800", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": true, "IsCustomer": false, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "8894cc85-85c2-4fd2-9eb6-537179d1a12d", "ContactStatus": "ACTIVE", "Name": "Hoyt Productions", "EmailAddress": "", "BankAccountDetails": "", "Addresses": [{"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}, {"AddressType": "POBOX", "City": "", "Region": "", "PostalCode": "", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "7411234", "PhoneAreaCode": "02", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": true, "IsCustomer": false, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "dec56ceb-65e9-43b3-ac98-7fe09eb37e31", "ContactStatus": "ACTIVE", "Name": "PowerDirect", "FirstName": "", "LastName": "", "EmailAddress": "", "BankAccountDetails": "34-56-78 *********", "AccountsPayableTaxType": "RRINPUT", "Addresses": [{"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": "", "AttentionTo": ""}, {"AddressType": "POBOX", "AddressLine1": "P O Box 8900", "AddressLine2": "Central Mailing Centre", "City": "Oaktown", "Region": "", "PostalCode": "OK12 8TN", "Country": "", "AttentionTo": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "887612", "PhoneAreaCode": "800", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "887613", "PhoneAreaCode": "800", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": true, "IsCustomer": false, "BatchPayments": {"BankAccountNumber": "34-56-78 *********", "BankAccountName": "PowerDirect Holdings", "Details": "DEMO-UK", "Code": "", "Reference": ""}, "Balances": {"AccountsReceivable": {"Outstanding": 0, "Overdue": 0}, "AccountsPayable": {"Outstanding": 244.45, "Overdue": 244.45}}, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "3828f379-afa5-4b2a-9000-9c53d75ba1c6", "ContactStatus": "ACTIVE", "Name": "Central Copiers", "EmailAddress": "", "BankAccountDetails": "", "Addresses": [{"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}, {"AddressType": "POBOX", "City": "", "Region": "", "PostalCode": "", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "244844", "PhoneAreaCode": "800", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": true, "IsCustomer": false, "Balances": {"AccountsReceivable": {"Outstanding": 0, "Overdue": 0}, "AccountsPayable": {"Outstanding": 1063.56, "Overdue": 1063.56}}, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "ac48c67d-3eea-44eb-96b1-9f7a89d9b761", "ContactStatus": "ACTIVE", "Name": "Xero", "EmailAddress": "", "BankAccountDetails": "", "Addresses": [{"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}, {"AddressType": "POBOX", "City": "", "Region": "", "PostalCode": "", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "9376", "PhoneAreaCode": "438", "PhoneCountryCode": "0800"}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": true, "IsCustomer": false, "Balances": {"AccountsReceivable": {"Outstanding": 0, "Overdue": 0}, "AccountsPayable": {"Outstanding": 56.35, "Overdue": 56.35}}, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "1c842b26-90b8-4791-b2d5-a9aea8078aa5", "ContactStatus": "ACTIVE", "Name": "HMRC", "EmailAddress": "", "BankAccountDetails": "", "Addresses": [{"AddressType": "POBOX", "City": "", "Region": "", "PostalCode": "", "Country": ""}, {"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": false, "IsCustomer": false, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "2dc0ef7c-582f-4542-963b-dbdc069e4819", "ContactStatus": "ACTIVE", "Name": "Bayside Wholesale", "EmailAddress": "", "BankAccountDetails": "", "Addresses": [{"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}, {"AddressType": "POBOX", "City": "", "Region": "", "PostalCode": "", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "55669", "PhoneAreaCode": "850", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": true, "IsCustomer": false, "Balances": {"AccountsReceivable": {"Outstanding": 0, "Overdue": 0}, "AccountsPayable": {"Outstanding": 840, "Overdue": 840}}, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "97cc88ca-f89b-41f0-b8b9-e750b6f2f1d9", "ContactStatus": "ACTIVE", "Name": "Net Connect", "FirstName": "", "LastName": "", "EmailAddress": "", "BankAccountDetails": "23-45-67 *********", "AccountsPayableTaxType": "INPUT2", "Addresses": [{"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": "", "AttentionTo": ""}, {"AddressType": "POBOX", "AddressLine1": "P O Box 7900", "AddressLine2": "South Mailing Centre", "City": "Oaktown", "Region": "", "PostalCode": "OK14 3TN", "Country": "", "AttentionTo": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "500998", "PhoneAreaCode": "800", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "500999", "PhoneAreaCode": "800", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": true, "IsCustomer": false, "BatchPayments": {"BankAccountNumber": "23-45-67 *********", "BankAccountName": "Net Connect Holdings", "Details": "DEMO-UK", "Code": "", "Reference": ""}, "Balances": {"AccountsReceivable": {"Outstanding": 0, "Overdue": 0}, "AccountsPayable": {"Outstanding": 54.13, "Overdue": 54.13}}, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "51cbbfb0-8dc9-41aa-aad6-eb93b3cc40c6", "ContactStatus": "ACTIVE", "Name": "Capital Cab Co", "FirstName": "", "LastName": "", "EmailAddress": "", "BankAccountDetails": "12-34-56 *********", "Addresses": [{"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": "", "AttentionTo": ""}, {"AddressType": "POBOX", "City": "", "Region": "", "PostalCode": "", "Country": "", "AttentionTo": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "235689", "PhoneAreaCode": "800", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": true, "IsCustomer": false, "BatchPayments": {"BankAccountNumber": "12-34-56 *********", "BankAccountName": "Capital Cab Holdings", "Details": "DEMO-UK", "Code": "", "Reference": ""}, "Balances": {"AccountsReceivable": {"Outstanding": 0, "Overdue": 0}, "AccountsPayable": {"Outstanding": 242, "Overdue": 242}}, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "af0091a9-82ef-4cac-9fd6-22c095ac6a58", "ContactStatus": "ACTIVE", "Name": "Hamilton Smith Ltd", "EmailAddress": "<EMAIL>", "BankAccountDetails": "", "Addresses": [{"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}, {"AddressType": "POBOX", "AddressLine1": "3 Park Street Industrial Village", "City": "Oaktown", "Region": "", "PostalCode": "OK13 3TN", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "2345678", "PhoneAreaCode": "01", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": false, "IsCustomer": true, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "362819c9-f285-4d09-ac95-26327863adac", "ContactStatus": "ACTIVE", "Name": "Bayside Club", "FirstName": "<PERSON>", "LastName": "Partridge", "EmailAddress": "<EMAIL>", "BankAccountDetails": "10-20-30 *********", "TaxNumber": "GB *********", "AccountsReceivableTaxType": "OUTPUT2", "AccountsPayableTaxType": "INPUT2", "Addresses": [{"AddressType": "POBOX", "AddressLine1": "P O Box 3354", "AddressLine2": "South Mailing Centre", "City": "Oaktown", "Region": "Madeupville", "PostalCode": "MA12 VL3", "Country": "United Kingdom", "AttentionTo": "Club Secretary"}, {"AddressType": "STREET", "AddressLine1": "148 Bay Harbour Road", "City": "Ridge Heights", "Region": "Madeupville", "PostalCode": "MA12 VL9", "Country": "United Kingdom", "AttentionTo": "Club Secretary"}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "2024418", "PhoneAreaCode": "02", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "2024455", "PhoneAreaCode": "02", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "2025566", "PhoneAreaCode": "02", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "7774455", "PhoneAreaCode": "01", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": true, "IsCustomer": true, "BrandingTheme": {"BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Name": "Standard"}, "BatchPayments": {"BankAccountNumber": "10-20-30 *********", "BankAccountName": "BSA", "Details": "OIT2-UK", "Code": "", "Reference": ""}, "Balances": {"AccountsReceivable": {"Outstanding": 3434, "Overdue": 3434}, "AccountsPayable": {"Outstanding": 130, "Overdue": 130}}, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "c523e12f-8b74-4d3a-bbd8-32d7a2f598b4", "ContactStatus": "ACTIVE", "Name": "City Limousines", "FirstName": "<PERSON>", "LastName": "<PERSON>", "EmailAddress": "<EMAIL>", "BankAccountDetails": "", "Addresses": [{"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}, {"AddressType": "POBOX", "AddressLine1": "1206 Harbour Tower", "AddressLine2": "59-65 South Street", "City": "Coppertown", "Region": "", "PostalCode": "CP89 TN4", "Country": "", "AttentionTo": "Accounts Dept"}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "8004001", "PhoneAreaCode": "01", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "7774001", "PhoneAreaCode": "01", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": false, "IsCustomer": true, "Balances": {"AccountsReceivable": {"Outstanding": 1169.95, "Overdue": 1169.95}, "AccountsPayable": {"Outstanding": 0, "Overdue": 0}}, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "a852a44c-3d8f-4c4b-a628-3a2c2121b9b1", "ContactStatus": "ACTIVE", "Name": "Bank West", "EmailAddress": "", "BankAccountDetails": "", "Addresses": [{"AddressType": "POBOX", "AddressLine1": "Procurement Services", "AddressLine2": "GPO 1234", "City": "Pinehaven", "Region": "", "PostalCode": "PI98 7HV", "Country": ""}, {"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "2023456", "PhoneAreaCode": "02", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": false, "IsCustomer": true, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "7eccc5ab-cee8-4af4-b2a9-3f9b17f03095", "ContactStatus": "ACTIVE", "Name": "<PERSON><PERSON> McLoud Watson & Associates", "FirstName": "<PERSON>", "LastName": "<PERSON>", "EmailAddress": "<EMAIL>", "BankAccountDetails": "", "Addresses": [{"AddressType": "POBOX", "AddressLine1": "9 Branyard Terrace", "City": "Coppertown", "Region": "", "PostalCode": "CP89 TN3", "Country": ""}, {"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "4561", "PhoneAreaCode": "915", "PhoneCountryCode": "02"}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": false, "IsCustomer": true, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "1262c350-fe0f-40ec-aeff-41c95b4a45af", "ContactStatus": "ACTIVE", "Name": "DIISR - Small Business Services", "EmailAddress": "<EMAIL>", "BankAccountDetails": "", "Addresses": [{"AddressType": "POBOX", "AddressLine1": "GPO 9566", "City": "Pinehaven", "Region": "", "PostalCode": "PI98 2HV", "Country": "", "AttentionTo": "Corporate Accounting"}, {"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "8009001", "PhoneAreaCode": "01", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "8009002", "PhoneAreaCode": "01", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": true, "IsCustomer": true, "Balances": {"AccountsReceivable": {"Outstanding": 4470.63, "Overdue": 4470.63}, "AccountsPayable": {"Outstanding": 0, "Overdue": 0}}, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "847933f0-7c35-4e5b-b884-5f9df64c8e4b", "ContactStatus": "ACTIVE", "Name": "Port & Philip Freight", "FirstName": "", "LastName": "", "EmailAddress": "<EMAIL>", "BankAccountDetails": "", "TaxNumber": "FR12123456789", "Addresses": [{"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": "", "AttentionTo": ""}, {"AddressType": "POBOX", "AddressLine1": "900 Harbourside Drive", "City": "Oaktown", "Region": "", "PostalCode": "OK14 7TN", "Country": "", "AttentionTo": "Corporate Services"}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "2323434", "PhoneAreaCode": "01", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": false, "IsCustomer": true, "Balances": {"AccountsReceivable": {"Outstanding": 2245, "Overdue": 2245}, "AccountsPayable": {"Outstanding": 0, "Overdue": 0}}, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "94a82e91-53da-4f87-a417-63d6a1607ced", "ContactStatus": "ACTIVE", "Name": "Young Bros Transport", "EmailAddress": "<EMAIL>", "BankAccountDetails": "", "Addresses": [{"AddressType": "POBOX", "AddressLine1": "P O Box 5678", "AddressLine2": "Central Mailing Centre", "City": "Oaktown", "Region": "", "PostalCode": "OK12 5TN", "Country": ""}, {"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "3435678", "PhoneAreaCode": "02", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": true, "IsCustomer": true, "Balances": {"AccountsReceivable": {"Outstanding": 0, "Overdue": 0}, "AccountsPayable": {"Outstanding": 125.03, "Overdue": 125.03}}, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "9ce626d2-14ea-463c-9fff-6785ab5f9bfb", "ContactStatus": "ACTIVE", "Name": "Boom FM", "EmailAddress": "", "BankAccountDetails": "", "Addresses": [{"AddressType": "POBOX", "AddressLine1": "P O Box 3955", "AddressLine2": "South Mailing Centre", "City": "Oaktown", "Region": "", "PostalCode": "OK12 3TN", "Country": "", "AttentionTo": "Human Resources Manager"}, {"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "9191", "PhoneAreaCode": "555", "PhoneCountryCode": "01"}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": false, "IsCustomer": true, "Balances": {"AccountsReceivable": {"Outstanding": 1623.75, "Overdue": 1623.75}, "AccountsPayable": {"Outstanding": 0, "Overdue": 0}}, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "a871a956-05b5-4e2a-9419-7aeb478ca647", "ContactStatus": "ACTIVE", "Name": "Ridgeway University", "EmailAddress": "", "BankAccountDetails": "", "Addresses": [{"AddressType": "POBOX", "AddressLine1": "9-123 Lambda Ave", "City": "Coppertown", "Region": "", "PostalCode": "CP89 TN2", "Country": "", "AttentionTo": "Accounts Dept"}, {"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "8005001", "PhoneAreaCode": "01", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "7775001", "PhoneAreaCode": "01", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": false, "IsCustomer": true, "Balances": {"AccountsReceivable": {"Outstanding": 6187.5, "Overdue": 6187.5}, "AccountsPayable": {"Outstanding": 0, "Overdue": 0}}, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "2dd82f49-e818-4dd0-955b-b637ccaa5597", "ContactStatus": "ACTIVE", "Name": "City Agency", "EmailAddress": "", "BankAccountDetails": "", "Addresses": [{"AddressType": "POBOX", "AddressLine1": "P O Box 4321", "AddressLine2": "Central Mailing Centre", "City": "Oaktown", "Region": "", "PostalCode": "OK12 7TN", "Country": ""}, {"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "9173555", "PhoneAreaCode": "01", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": false, "IsCustomer": true, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "305ca5cf-497d-4fee-a161-cdb30e6be989", "ContactStatus": "ACTIVE", "Name": "Basket Case", "FirstName": "<PERSON>", "LastName": "<PERSON><PERSON>", "EmailAddress": "<EMAIL>", "BankAccountDetails": "", "Addresses": [{"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": "", "AttentionTo": ""}, {"AddressType": "POBOX", "AddressLine1": "Shop 14 Ridgeway Mall", "AddressLine2": "500 River Road", "City": "Pinehaven", "Region": "", "PostalCode": "PI98 8HV", "Country": "", "AttentionTo": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "9176665", "PhoneAreaCode": "02", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "7773001", "PhoneAreaCode": "01", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": false, "IsCustomer": true, "BrandingTheme": {"BrandingThemeID": "04cd7b5f-daf5-4e96-a6ee-b9a862a0e4eb", "Name": "Very orange invoice!"}, "Balances": {"AccountsReceivable": {"Outstanding": 914.55, "Overdue": 914.55}, "AccountsPayable": {"Outstanding": 0, "Overdue": 0}}, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "b2c1f980-96c9-45ff-a42b-dca141936c6c", "ContactStatus": "ACTIVE", "Name": "Rex Media Group", "EmailAddress": "<EMAIL>", "BankAccountDetails": "", "Addresses": [{"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}, {"AddressType": "POBOX", "AddressLine1": "L9, <PERSON><PERSON>", "AddressLine2": "120-130 Flinders Street", "City": "Oaktown", "Region": "", "PostalCode": "OK12 6TN", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "4546789", "PhoneAreaCode": "01", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": false, "IsCustomer": true, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "94cb6d7b-5291-49f3-a0bc-fc0c01e68575", "ContactStatus": "ACTIVE", "Name": "Marine Systems", "EmailAddress": "", "BankAccountDetails": "", "Addresses": [{"AddressType": "POBOX", "AddressLine1": "145 Longdale Way", "City": "Coppertown", "Region": "", "PostalCode": "CP89 TN8", "Country": "", "AttentionTo": "Accounts Dept"}, {"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "9786456", "PhoneAreaCode": "02", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": false, "IsCustomer": true, "Balances": {"AccountsReceivable": {"Outstanding": 396, "Overdue": 396}, "AccountsPayable": {"Outstanding": 0, "Overdue": 0}}, "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "afd4093b-c655-4847-8ee2-10a4f2c3eae3", "ContactStatus": "ACTIVE", "Name": "Maddox Publishing Group", "FirstName": "", "LastName": "", "EmailAddress": "", "BankAccountDetails": "", "Addresses": [{"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": "", "AttentionTo": ""}, {"AddressType": "POBOX", "AddressLine1": "L9 BWA Tower", "AddressLine2": "150 Bourke Street", "AddressLine3": "", "AddressLine4": "", "City": "London", "Region": "", "PostalCode": "LL31 33LL", "Country": "", "AttentionTo": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": false, "IsCustomer": true, "DefaultCurrency": "GBP", "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}, {"ContactID": "c3c66d43-72d2-490d-b231-96bd5c3355f9", "ContactStatus": "ACTIVE", "Name": "Mobil", "EmailAddress": "", "BankAccountDetails": "", "Addresses": [{"AddressType": "STREET", "City": "", "Region": "", "PostalCode": "", "Country": ""}, {"AddressType": "POBOX", "City": "", "Region": "", "PostalCode": "", "Country": ""}], "Phones": [{"PhoneType": "DDI", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "DEFAULT", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "FAX", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}, {"PhoneType": "MOBILE", "PhoneNumber": "", "PhoneAreaCode": "", "PhoneCountryCode": ""}], "UpdatedDateUTC": "/Date(*************+0000)/", "ContactGroups": [], "IsSupplier": false, "IsCustomer": false, "DefaultCurrency": "GBP", "ContactPersons": [], "HasAttachments": false, "HasValidationErrors": false}]