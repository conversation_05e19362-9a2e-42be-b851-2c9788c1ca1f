{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "pm2 start pm2.config.js && pm2 log", "status": "pm2 status", "log": "pm2 log", "dev": "nodemon", "dev-cron": "NODE_PATH=./src ./node_modules/.bin/ts-node -- ./index.cron.ts", "test": "NODE_PATH=./src npx jest", "format": "prettier -w src/", "run-local-db": "cd ../ && docker-compose up api_db", "postinstall": "cd ./node_modules/puppeteer && node install", "docker-build": "docker build --platform linux/amd64 -f Dockerfile -t bg-backend:latest .", "update-webhooks": "TS_NODE_PROJECT=./tsconfig.json NODE_PATH=./src ts-node -r tsconfig-paths/register scripts/update-webhooks.js", "cleanup-webhooks": "TS_NODE_PROJECT=./tsconfig.json NODE_PATH=./src ts-node -r tsconfig-paths/register scripts/cleanup-webhooks.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@fastify/formbody": "^6.0.1", "@fastify/multipart": "6.0.0", "@fastify/static": "5.0.2", "@mailchimp/mailchimp_marketing": "^3.0.80", "@types/aws-sdk": "^2.7.0", "aws-sdk": "^2.1039.0", "axios": "^0.23.0", "bcrypt": "^5.0.1", "cron": "^1.8.2", "cross-fetch": "^3.1.5", "cypress": "^13.14.1", "device-detector-js": "^3.0.3", "dotenv": "^10.0.0", "es6-promise": "^4.2.8", "exceljs": "^4.3.0", "fastify": "^3.21.6", "fastify-cors": "^6.0.2", "fastify-file-upload": "^4.0.0", "fastify-multipart": "^5.4.0", "firebase-admin": "^10.0.0", "fs-extra": "^10.1.0", "isomorphic-fetch": "^3.0.0", "joi": "^17.6.0", "joi-to-typescript": "^4.0.5", "js-events-listener": "^1.1.6", "js-htmlencode": "^0.3.0", "jwt-simple": "^0.5.6", "liquidjs": "^10.8.3", "lodash": "^4.17.21", "nodemailer": "^6.7.1", "parse-multipart-data": "^1.3.0", "pdfkit": "github:sajjad-shirazy/pdfkit.js", "pg": "^8.7.1", "pg-hstore": "^2.3.4", "polotno-node": "^1.1.0", "probe-image-size": "^7.2.3", "puppeteer": "^18.2.1", "qs": "^6.10.1", "sequelize": "^6.9.0", "sharp": "^0.32.1", "shopify-token": "^4.1.0", "stripe": "^11.5.0", "svg-to-pdfkit": "github:sajjad-shirazy/SVG-to-PDFKit#0.1.8", "unique-names-generator": "^4.7.1", "unsplash-js": "^7.0.15", "xero-node": "^5.1.0"}, "devDependencies": {"@types/jest": "^29.5.12", "@types/node": "^16.10.2", "husky": "^7.0.2", "increase-version": "^1.0.4", "jest": "^28.1.3", "nodemon": "^2.0.13", "prettier": "^2.4.1", "ts-jest": "^28.0.8", "ts-node": "^10.2.1", "tsconfig-paths": "^4.2.0", "typescript": "^4.4.3"}, "husky": {"hooks": {"pre-commit": "if git status -s | grep \"backend\"; then echo \"INCREASING VERSION\" && node ./scripts/increase-version.js && git add . ; fi"}}}