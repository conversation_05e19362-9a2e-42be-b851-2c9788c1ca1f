[{"Type": "ACCPAY", "InvoiceID": "2175c381-d323-4e20-8c94-7680ea7f85d3", "InvoiceNumber": "RPT445-1", "Reference": "", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 108.6, "AmountPaid": 0, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "619e3696-7b65-4c6e-a790-305de97ca9d9", "InvoicePaymentServices": [], "Contact": {"ContactID": "dec56ceb-65e9-43b3-ac98-7fe09eb37e31", "Name": "PowerDirect", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-02-26T00:00:00", "Date": "/Date(1708905600000+0000)/", "DueDateString": "2024-03-07T00:00:00", "DueDate": "/Date(1709*********+0000)/", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 103.43, "TotalTax": 5.17, "Total": 108.6, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP"}, {"Type": "ACCREC", "InvoiceID": "7ea31cd8-045c-4871-8cda-c0420953a39c", "InvoiceNumber": "INV-0003", "Reference": "RPT200-1", "Payments": [{"PaymentID": "eea216c6-29c6-4de2-83f4-4196ae3bfaac", "Date": "/Date(*************+0000)/", "Amount": 500, "Reference": "INV-0003", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 500, "AmountCredited": 0, "SentToContact": true, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "abef034a-9498-4af3-9831-365f08f7b13a", "InvoicePaymentServices": [], "Contact": {"ContactID": "a871a956-05b5-4e2a-9419-7aeb478ca647", "Name": "Ridgeway University", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-04-26T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2023-05-06T00:00:00", "DueDate": "/Date(*************+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 416.67, "TotalTax": 83.33, "Total": 500, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCREC", "InvoiceID": "4f9d9bcc-3c75-4884-93a4-19d3b5a4b5f8", "InvoiceNumber": "INV-0004", "Reference": "RPT200-1", "Payments": [{"PaymentID": "6190117a-7849-423c-a155-398aed253fa3", "Date": "/Date(*************+0000)/", "Amount": 1000, "Reference": "INV-0004", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 1000, "AmountCredited": 0, "SentToContact": true, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "abef034a-9498-4af3-9831-365f08f7b13a", "InvoicePaymentServices": [], "Contact": {"ContactID": "a871a956-05b5-4e2a-9419-7aeb478ca647", "Name": "Ridgeway University", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-05-26T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2023-06-05T00:00:00", "DueDate": "/Date(*************+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 833.34, "TotalTax": 166.66, "Total": 1000, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCREC", "InvoiceID": "208c87e9-3721-435d-b6b0-c13ff8ff06b4", "InvoiceNumber": "INV-0005", "Reference": "RPT200-1", "Payments": [{"PaymentID": "168d6616-3a4e-4f58-b8e4-adaeccd00347", "Date": "/Date(*************+0000)/", "Amount": 500, "Reference": "INV-0005", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 500, "AmountCredited": 0, "SentToContact": true, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "abef034a-9498-4af3-9831-365f08f7b13a", "InvoicePaymentServices": [], "Contact": {"ContactID": "a871a956-05b5-4e2a-9419-7aeb478ca647", "Name": "Ridgeway University", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-06-26T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2023-07-06T00:00:00", "DueDate": "/Date(*************+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 416.67, "TotalTax": 83.33, "Total": 500, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCREC", "InvoiceID": "379d60f2-56b9-4043-af41-3f8775b87dd4", "InvoiceNumber": "INV-0006", "Reference": "RPT200-1", "Payments": [{"PaymentID": "222a628d-0381-4ffc-b245-66041305a64d", "Date": "/Date(*************+0000)/", "Amount": 500, "Reference": "INV-0006", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 500, "AmountCredited": 0, "SentToContact": true, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "abef034a-9498-4af3-9831-365f08f7b13a", "InvoicePaymentServices": [], "Contact": {"ContactID": "a871a956-05b5-4e2a-9419-7aeb478ca647", "Name": "Ridgeway University", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-07-27T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2023-08-06T00:00:00", "DueDate": "/Date(*************+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 416.67, "TotalTax": 83.33, "Total": 500, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCREC", "InvoiceID": "08f0630c-24c1-4433-a98b-76967fc1b7cc", "InvoiceNumber": "INV-0007", "Reference": "RPT200-1", "Payments": [{"PaymentID": "7ea3068a-9d86-4ae0-9021-7c3cb6238e00", "Date": "/Date(*************+0000)/", "Amount": 1500, "Reference": "INV-0007", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 1500, "AmountCredited": 0, "SentToContact": true, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "abef034a-9498-4af3-9831-365f08f7b13a", "InvoicePaymentServices": [], "Contact": {"ContactID": "a871a956-05b5-4e2a-9419-7aeb478ca647", "Name": "Ridgeway University", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-08-26T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2023-09-05T00:00:00", "DueDate": "/Date(*************+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 1250, "TotalTax": 250, "Total": 1500, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCREC", "InvoiceID": "0f235364-1d3a-452d-b2c0-7900c7ad14ca", "InvoiceNumber": "INV-0008", "Reference": "RPT200-1", "Payments": [{"PaymentID": "bb78bb43-4877-433d-9622-a259975a5dc6", "Date": "/Date(*************+0000)/", "Amount": 500, "Reference": "INV-0008", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 500, "AmountCredited": 0, "SentToContact": true, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "abef034a-9498-4af3-9831-365f08f7b13a", "InvoicePaymentServices": [], "Contact": {"ContactID": "a871a956-05b5-4e2a-9419-7aeb478ca647", "Name": "Ridgeway University", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-09-26T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2023-10-06T00:00:00", "DueDate": "/Date(*************+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 416.67, "TotalTax": 83.33, "Total": 500, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCREC", "InvoiceID": "3cfaee38-bfdd-4ca8-afcd-b7a23568ddc3", "InvoiceNumber": "INV-0009", "Reference": "RPT200-1", "Payments": [{"PaymentID": "de5b0dfa-8e7d-4da9-9024-e32d806ded6e", "Date": "/Date(*************+0000)/", "Amount": 1200, "Reference": "INV-0009", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 1200, "AmountCredited": 0, "SentToContact": true, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "abef034a-9498-4af3-9831-365f08f7b13a", "InvoicePaymentServices": [], "Contact": {"ContactID": "a871a956-05b5-4e2a-9419-7aeb478ca647", "Name": "Ridgeway University", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-10-26T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2023-11-05T00:00:00", "DueDate": "/Date(*************+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 1000, "TotalTax": 200, "Total": 1200, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCREC", "InvoiceID": "6e98bbda-baa4-465f-84cb-36403be578b9", "InvoiceNumber": "INV-0010", "Reference": "RPT200-1", "Payments": [{"PaymentID": "700205b5-a81a-4e64-be08-9540eedffd3c", "Date": "/Date(*************+0000)/", "Amount": 500, "Reference": "INV-0010", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 500, "AmountCredited": 0, "SentToContact": true, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "abef034a-9498-4af3-9831-365f08f7b13a", "InvoicePaymentServices": [], "Contact": {"ContactID": "a871a956-05b5-4e2a-9419-7aeb478ca647", "Name": "Ridgeway University", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-11-26T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2023-12-06T00:00:00", "DueDate": "/Date(*************+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 416.67, "TotalTax": 83.33, "Total": 500, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCPAY", "InvoiceID": "24935f36-ff96-4e45-beae-5fd04f007d60", "InvoiceNumber": "RPT445-1", "Reference": "", "Payments": [{"PaymentID": "bb14131f-8521-48bb-a156-64572c9db1fc", "Date": "/Date(*************+0000)/", "Amount": 92, "Reference": "DD", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 92, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "619e3696-7b65-4c6e-a790-305de97ca9d9", "InvoicePaymentServices": [], "Contact": {"ContactID": "dec56ceb-65e9-43b3-ac98-7fe09eb37e31", "Name": "PowerDirect", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-04-28T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2023-05-08T00:00:00", "DueDate": "/Date(*************+0000)/", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 87.62, "TotalTax": 4.38, "Total": 92, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCPAY", "InvoiceID": "cfad6d00-66ea-4952-bab2-613595e2bce7", "InvoiceNumber": "RPT445-1", "Reference": "", "Payments": [{"PaymentID": "ea65fc61-13d5-4d0f-ac0f-59ea54c603de", "Date": "/Date(*************+0000)/", "Amount": 89, "Reference": "DD", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 89, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "619e3696-7b65-4c6e-a790-305de97ca9d9", "InvoicePaymentServices": [], "Contact": {"ContactID": "dec56ceb-65e9-43b3-ac98-7fe09eb37e31", "Name": "PowerDirect", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-05-28T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2023-06-07T00:00:00", "DueDate": "/Date(*************+0000)/", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 84.76, "TotalTax": 4.24, "Total": 89, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCPAY", "InvoiceID": "20c485f3-6da4-40e0-8320-86bf73b07417", "InvoiceNumber": "RPT445-1", "Reference": "", "Payments": [{"PaymentID": "f87edb11-c9ac-4000-842a-a0eef6a19051", "Date": "/Date(*************+0000)/", "Amount": 91, "Reference": "DD", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 91, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "619e3696-7b65-4c6e-a790-305de97ca9d9", "InvoicePaymentServices": [], "Contact": {"ContactID": "dec56ceb-65e9-43b3-ac98-7fe09eb37e31", "Name": "PowerDirect", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-06-28T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2023-07-08T00:00:00", "DueDate": "/Date(*************+0000)/", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 86.67, "TotalTax": 4.33, "Total": 91, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCPAY", "InvoiceID": "cfa7a41f-a108-4ddb-a95a-d883f4858297", "InvoiceNumber": "RPT445-1", "Reference": "", "Payments": [{"PaymentID": "ac4e1db9-9dc7-4ada-ad21-8dfc8b533efd", "Date": "/Date(*************+0000)/", "Amount": 96.25, "Reference": "DD", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 96.25, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "619e3696-7b65-4c6e-a790-305de97ca9d9", "InvoicePaymentServices": [], "Contact": {"ContactID": "dec56ceb-65e9-43b3-ac98-7fe09eb37e31", "Name": "PowerDirect", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-07-29T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2023-08-08T00:00:00", "DueDate": "/Date(*************+0000)/", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 91.67, "TotalTax": 4.58, "Total": 96.25, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCPAY", "InvoiceID": "6168eab0-e3bc-41ba-ac49-2d2f7116f2bc", "InvoiceNumber": "RPT445-1", "Reference": "", "Payments": [{"PaymentID": "eae0fae4-ef8a-4c77-9830-f66987daff4b", "Date": "/Date(*************+0000)/", "Amount": 100.6, "Reference": "DD", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 100.6, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "619e3696-7b65-4c6e-a790-305de97ca9d9", "InvoicePaymentServices": [], "Contact": {"ContactID": "dec56ceb-65e9-43b3-ac98-7fe09eb37e31", "Name": "PowerDirect", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-08-28T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2023-09-07T00:00:00", "DueDate": "/Date(*************+0000)/", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 95.81, "TotalTax": 4.79, "Total": 100.6, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCPAY", "InvoiceID": "96145b5f-a34b-4b93-bc5d-0d8c2d08eb23", "InvoiceNumber": "RPT445-1", "Reference": "", "Payments": [{"PaymentID": "1e1b4dd1-e0de-4851-ad62-257997062ec2", "Date": "/Date(*************+0000)/", "Amount": 105.75, "Reference": "DD", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 105.75, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "619e3696-7b65-4c6e-a790-305de97ca9d9", "InvoicePaymentServices": [], "Contact": {"ContactID": "dec56ceb-65e9-43b3-ac98-7fe09eb37e31", "Name": "PowerDirect", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-09-28T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2023-10-08T00:00:00", "DueDate": "/Date(*************+0000)/", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 100.71, "TotalTax": 5.04, "Total": 105.75, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCPAY", "InvoiceID": "a81179d0-f4a7-4150-8f36-803dd75d6fbb", "InvoiceNumber": "RPT445-1", "Reference": "", "Payments": [{"PaymentID": "09bc82a3-5871-4cb5-92bf-6f102af6cdd8", "Date": "/Date(*************+0000)/", "Amount": 106.5, "Reference": "DD", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 106.5, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "619e3696-7b65-4c6e-a790-305de97ca9d9", "InvoicePaymentServices": [], "Contact": {"ContactID": "dec56ceb-65e9-43b3-ac98-7fe09eb37e31", "Name": "PowerDirect", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-10-28T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2023-11-07T00:00:00", "DueDate": "/Date(*************+0000)/", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 101.43, "TotalTax": 5.07, "Total": 106.5, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCPAY", "InvoiceID": "bb4b5ebb-489e-4bc9-911c-db65ad2c8564", "InvoiceNumber": "RPT445-1", "Reference": "", "Payments": [{"PaymentID": "209e03ad-e636-42d9-9163-b5a3e8d7b16a", "Date": "/Date(*************+0000)/", "Amount": 119.08, "Reference": "DD", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 119.08, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "619e3696-7b65-4c6e-a790-305de97ca9d9", "InvoicePaymentServices": [], "Contact": {"ContactID": "dec56ceb-65e9-43b3-ac98-7fe09eb37e31", "Name": "PowerDirect", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-11-28T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2023-12-08T00:00:00", "DueDate": "/Date(*************+0000)/", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 113.41, "TotalTax": 5.67, "Total": 119.08, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCPAY", "InvoiceID": "6db739d7-3d12-4c74-9906-81ac467979b1", "InvoiceNumber": "RPT489-1", "Reference": "", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 54.13, "AmountPaid": 0, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "5d7ad8cd-00c4-41fe-97e0-12a53f68c976", "InvoicePaymentServices": [], "Contact": {"ContactID": "97cc88ca-f89b-41f0-b8b9-e750b6f2f1d9", "Name": "Net Connect", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-02-28T00:00:00", "Date": "/Date(1709078400000+0000)/", "DueDateString": "2024-03-09T00:00:00", "DueDate": "/Date(*************+0000)/", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 45.11, "TotalTax": 9.02, "Total": 54.13, "UpdatedDateUTC": "/Date(1301991704233+0000)/", "CurrencyCode": "GBP"}, {"Type": "ACCREC", "InvoiceID": "318fbe79-0db3-4473-87f1-fd7381dc9185", "InvoiceNumber": "INV-0017", "Reference": "P/O 9711", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 250, "AmountPaid": 0, "AmountCredited": 0, "SentToContact": true, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "c523e12f-8b74-4d3a-bbd8-32d7a2f598b4", "Name": "City Limousines", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-12-08T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2023-12-18T00:00:00", "DueDate": "/Date(1702857600000+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 208.33, "TotalTax": 41.67, "Total": 250, "UpdatedDateUTC": "/Date(1302064202127+0000)/", "CurrencyCode": "GBP"}, {"Type": "ACCREC", "InvoiceID": "26be16b6-2287-4e6c-a735-8527c05043f6", "InvoiceNumber": "INV-0023", "Reference": "P/O 9711", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 250, "AmountPaid": 0, "AmountCredited": 0, "SentToContact": true, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "c523e12f-8b74-4d3a-bbd8-32d7a2f598b4", "Name": "City Limousines", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-01-08T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2024-01-18T00:00:00", "DueDate": "/Date(*************+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 208.33, "TotalTax": 41.67, "Total": 250, "UpdatedDateUTC": "/Date(1302064534760+0000)/", "CurrencyCode": "GBP"}, {"Type": "ACCPAY", "InvoiceID": "aaa49f06-c09e-452e-86ee-a14e8453ba3c", "InvoiceNumber": "710", "Reference": "", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 1200, "AmountPaid": 0, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "699f0091-b127-4796-9f15-41a2f42abeb2", "Name": "ABC Furniture", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-02-28T00:00:00", "Date": "/Date(1709078400000+0000)/", "DueDateString": "2024-03-09T00:00:00", "DueDate": "/Date(*************+0000)/", "Status": "AUTHORISED", "LineAmountTypes": "Exclusive", "LineItems": [], "SubTotal": 1000, "TotalTax": 200, "Total": 1200, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP"}, {"Type": "ACCPAY", "InvoiceID": "0ccc0148-8d9d-4200-aeac-ade429ebe58c", "InvoiceNumber": "449", "Reference": "", "Payments": [{"PaymentID": "236f6a68-dd04-425e-b167-1debd0b97a84", "Date": "/Date(*************+0000)/", "Amount": 411.35, "Reference": "Ref 02761", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 411.35, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "f559658a-68da-42b1-81ae-42a96b6c6953", "Name": "Gateway Motors", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-03-01T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2024-03-08T00:00:00", "DueDate": "/Date(*************+0000)/", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 342.79, "TotalTax": 68.56, "Total": 411.35, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCPAY", "InvoiceID": "b1b4f969-a6e3-478c-b0e9-e232d6d81724", "InvoiceNumber": "GB1-White", "Reference": "", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 840, "AmountPaid": 0, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "2dc0ef7c-582f-4542-963b-dbdc069e4819", "Name": "Bayside Wholesale", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-03-01T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2024-03-11T00:00:00", "DueDate": "/Date(1710115200000+0000)/", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 700, "TotalTax": 140, "Total": 840, "UpdatedDateUTC": "/Date(1302124724803+0000)/", "CurrencyCode": "GBP"}, {"Type": "ACCPAY", "InvoiceID": "925cfb10-37ce-43d5-a473-3b329271c810", "InvoiceNumber": "CS815", "Reference": "", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 242, "AmountPaid": 0, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "51cbbfb0-8dc9-41aa-aad6-eb93b3cc40c6", "Name": "Capital Cab Co", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-03-03T00:00:00", "Date": "/Date(1709424000000+0000)/", "DueDateString": "2024-03-21T00:00:00", "DueDate": "/Date(*************+0000)/", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 201.67, "TotalTax": 40.33, "Total": 242, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP"}, {"Type": "ACCPAY", "InvoiceID": "5e82d005-4159-4400-8b68-a6b5f45c5e36", "InvoiceNumber": "720-1", "Reference": "", "Payments": [{"PaymentID": "ec86f207-a372-4529-b37d-aa96fdd418fe", "Date": "/Date(*************+0000)/", "Amount": 1900, "Reference": "FP089876", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 1900, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "cf8fa320-a527-496c-823e-22dd069d29e6", "Name": "PC Complete", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-11-30T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2023-12-10T00:00:00", "DueDate": "/Date(*************+0000)/", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 1583.33, "TotalTax": 316.67, "Total": 1900, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCPAY", "InvoiceID": "788212e4-88fd-4c06-83a5-45a501463c19", "InvoiceNumber": "RPT469-1", "Reference": "", "Payments": [{"PaymentID": "616f7668-f56e-41cd-b78e-ff70336c24d9", "Date": "/Date(*************+0000)/", "Amount": 1181.25, "Reference": "FP089876", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 1181.25, "AmountCredited": 0, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "a78c8f3e-e895-4295-92c9-dd0f67819b71", "InvoicePaymentServices": [], "Contact": {"ContactID": "aca6e01a-1815-474c-bd0f-18adfd95cfcb", "Name": "Truxton Property Management", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-11-26T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2023-11-26T00:00:00", "DueDate": "/Date(*************+0000)/", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 984.38, "TotalTax": 196.87, "Total": 1181.25, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCPAY", "InvoiceID": "9d1e6da7-2f6f-4d37-87e6-d1b64f9c4874", "InvoiceNumber": "RPT489-1", "Reference": "", "Payments": [{"PaymentID": "69f6d12d-af29-4cf5-849c-5cf69f11fbb7", "Date": "/Date(*************+0000)/", "Amount": 44.92, "Reference": "FP089876", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 44.92, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "5d7ad8cd-00c4-41fe-97e0-12a53f68c976", "InvoicePaymentServices": [], "Contact": {"ContactID": "97cc88ca-f89b-41f0-b8b9-e750b6f2f1d9", "Name": "Net Connect", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-11-30T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2023-12-10T00:00:00", "DueDate": "/Date(*************+0000)/", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 37.43, "TotalTax": 7.49, "Total": 44.92, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCREC", "InvoiceID": "eb58c08e-782e-4aa9-924e-ba0c918a4b45", "InvoiceNumber": "INV-0012", "Reference": "Monthly Support", "Payments": [{"PaymentID": "5cef4833-2015-44c7-8e20-4d6081870be8", "Date": "/Date(*************+0000)/", "Amount": 541.25, "Reference": "0012", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 541.25, "AmountCredited": 0, "SentToContact": true, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "af0091a9-82ef-4cac-9fd6-22c095ac6a58", "Name": "Hamilton Smith Ltd", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-12-05T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2023-12-15T00:00:00", "DueDate": "/Date(*************+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 451.04, "TotalTax": 90.21, "Total": 541.25, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCREC", "InvoiceID": "fcecfa38-ae41-4d5f-8816-6c374732684c", "InvoiceNumber": "INV-0013", "Reference": "Monthly Support", "Payments": [{"PaymentID": "4707e491-f2d8-4f6e-885c-1ba53a03d024", "Date": "/Date(*************+0000)/", "Amount": 541.25, "Reference": "0031", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 541.25, "AmountCredited": 0, "SentToContact": true, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "94a82e91-53da-4f87-a417-63d6a1607ced", "Name": "Young Bros Transport", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-12-05T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2023-12-15T00:00:00", "DueDate": "/Date(*************+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 451.04, "TotalTax": 90.21, "Total": 541.25, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCREC", "InvoiceID": "9dd1fb78-5794-4307-86fb-dd588d4eee13", "InvoiceNumber": "INV-0014", "Reference": "Monthly Support", "Payments": [{"PaymentID": "7b57af4f-e828-4921-9923-6d3714c872fe", "Date": "/Date(*************+0000)/", "Amount": 541.25, "Reference": "0014", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 541.25, "AmountCredited": 0, "SentToContact": true, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "847933f0-7c35-4e5b-b884-5f9df64c8e4b", "Name": "Port & Philip Freight", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-12-05T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2023-12-15T00:00:00", "DueDate": "/Date(*************+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 451.04, "TotalTax": 90.21, "Total": 541.25, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCREC", "InvoiceID": "2a88c529-47ae-4345-8098-a7312585c4a3", "InvoiceNumber": "INV-0015", "Reference": "Monthly Support", "Payments": [{"PaymentID": "fe5b909f-81b8-45b0-ab7b-f3923b038be3", "Date": "/Date(*************+0000)/", "Amount": 541.25, "Reference": "0015", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 541.25, "AmountCredited": 0, "SentToContact": true, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "b2c1f980-96c9-45ff-a42b-dca141936c6c", "Name": "Rex Media Group", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-12-05T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2023-12-15T00:00:00", "DueDate": "/Date(*************+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 451.04, "TotalTax": 90.21, "Total": 541.25, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCREC", "InvoiceID": "773b421f-a764-4035-8664-625e4335d0e3", "InvoiceNumber": "INV-0016", "Reference": "Monthly Support", "Payments": [], "CreditNotes": [{"CreditNoteID": "47fb825b-3c43-4284-905f-c6d97acc6b37", "CreditNoteNumber": "CN-0025", "ID": "47fb825b-3c43-4284-905f-c6d97acc6b37", "HasErrors": false, "AppliedAmount": 541.25, "DateString": "2023-12-07T00:00:00", "Date": "/Date(*************+0000)/", "LineItems": [], "Total": 541.25}], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 0, "AmountCredited": 541.25, "SentToContact": true, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "af0091a9-82ef-4cac-9fd6-22c095ac6a58", "Name": "Hamilton Smith Ltd", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-12-06T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2023-12-16T00:00:00", "DueDate": "/Date(*************+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 451.04, "TotalTax": 90.21, "Total": 541.25, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCREC", "InvoiceID": "7502615a-d62f-423c-8e27-fd8eddc40c70", "InvoiceNumber": "INV-0019", "Reference": "Training", "Payments": [{"PaymentID": "3e1cf12e-5c5c-49f8-8520-d03196686f71", "Date": "/Date(*************+0000)/", "Amount": 1500, "Reference": "0019", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 1500, "AmountCredited": 0, "SentToContact": true, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "a852a44c-3d8f-4c4b-a628-3a2c2121b9b1", "Name": "Bank West", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-12-16T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2023-12-26T00:00:00", "DueDate": "/Date(*************+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 1250.01, "TotalTax": 249.99, "Total": 1500, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCPAY", "InvoiceID": "dcf32f80-0eb3-4666-830d-1c48266cf3c1", "InvoiceNumber": "RPT402-1", "Reference": "", "Payments": [{"PaymentID": "d2097100-b8a0-4e26-a0cc-e3be19d8a723", "BatchPaymentID": "3f071282-c892-4005-ae7a-90bd357109a3", "Date": "/Date(*************+0000)/", "Amount": 56.35, "Reference": "", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 56.35, "AmountCredited": 0, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "709624ff-739b-4bb3-a4c1-1b56ead7a67f", "InvoicePaymentServices": [], "Contact": {"ContactID": "ac48c67d-3eea-44eb-96b1-9f7a89d9b761", "Name": "Xero", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-12-10T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2023-12-10T00:00:00", "DueDate": "/Date(*************+0000)/", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 46.96, "TotalTax": 9.39, "Total": 56.35, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCREC", "InvoiceID": "8c4a3981-0865-46ee-8038-62d270f952b3", "InvoiceNumber": "INV-0021", "Reference": "Training", "Payments": [], "CreditNotes": [{"CreditNoteID": "caba470e-962f-4168-9c57-35caa289204d", "CreditNoteNumber": "CN-0026", "ID": "caba470e-962f-4168-9c57-35caa289204d", "HasErrors": false, "AppliedAmount": 500, "DateString": "2024-01-30T00:00:00", "Date": "/Date(*************+0000)/", "LineItems": [], "Total": 500}], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 0, "AmountCredited": 500, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "9ce626d2-14ea-463c-9fff-6785ab5f9bfb", "Name": "Boom FM", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-01-27T00:00:00", "Date": "/Date(1706313600000+0000)/", "DueDateString": "2024-02-06T00:00:00", "DueDate": "/Date(*************+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 416.67, "TotalTax": 83.33, "Total": 500, "UpdatedDateUTC": "/Date(1302144424663+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCREC", "InvoiceID": "dc55bc82-652a-4538-b50f-62dd0ae3a89c", "InvoiceNumber": "INV-0028", "Reference": "Book", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 19.95, "AmountPaid": 0, "AmountCredited": 0, "SentToContact": true, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "c523e12f-8b74-4d3a-bbd8-32d7a2f598b4", "Name": "City Limousines", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-01-13T00:00:00", "Date": "/Date(1705104000000+0000)/", "DueDateString": "2024-01-23T00:00:00", "DueDate": "/Date(*************+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 16.62, "TotalTax": 3.33, "Total": 19.95, "UpdatedDateUTC": "/Date(1302145738257+0000)/", "CurrencyCode": "GBP"}, {"Type": "ACCREC", "InvoiceID": "dfaf06a3-21d8-4637-abf9-f4a8d635d1ee", "InvoiceNumber": "INV-0034", "Reference": "P/O 9711", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 650, "AmountPaid": 0, "AmountCredited": 0, "SentToContact": true, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "c523e12f-8b74-4d3a-bbd8-32d7a2f598b4", "Name": "City Limousines", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-02-28T00:00:00", "Date": "/Date(1709078400000+0000)/", "DueDateString": "2024-03-09T00:00:00", "DueDate": "/Date(*************+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 541.67, "TotalTax": 108.33, "Total": 650, "UpdatedDateUTC": "/Date(1302147481477+0000)/", "CurrencyCode": "GBP"}, {"Type": "ACCREC", "InvoiceID": "c0b7d42c-5229-4e03-97cb-d65ebb9bbc7b", "InvoiceNumber": "INV-0035", "Reference": "P/O CRM08-12", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 6187.5, "AmountPaid": 0, "AmountCredited": 0, "SentToContact": true, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "a871a956-05b5-4e2a-9419-7aeb478ca647", "Name": "Ridgeway University", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-02-21T00:00:00", "Date": "/Date(1708473600000+0000)/", "DueDateString": "2024-03-02T00:00:00", "DueDate": "/Date(1709337600000+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 5156.25, "TotalTax": 1031.25, "Total": 6187.5, "UpdatedDateUTC": "/Date(1302147524377+0000)/", "CurrencyCode": "GBP"}, {"Type": "ACCREC", "InvoiceID": "b16c6503-b49c-4289-8bbb-0c0d33655583", "InvoiceNumber": "INV-0036", "Reference": "", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 914.55, "AmountPaid": 0, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "305ca5cf-497d-4fee-a161-cdb30e6be989", "Name": "Basket Case", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-02-29T00:00:00", "Date": "/Date(1709164800000+0000)/", "DueDateString": "2024-03-10T00:00:00", "DueDate": "/Date(1710028800000+0000)/", "BrandingThemeID": "04cd7b5f-daf5-4e96-a6ee-b9a862a0e4eb", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 762.12, "TotalTax": 152.43, "Total": 914.55, "UpdatedDateUTC": "/Date(1302147603800+0000)/", "CurrencyCode": "GBP"}, {"Type": "ACCREC", "InvoiceID": "8f3c09ae-7b12-4391-b64a-5d74ca25fdaf", "InvoiceNumber": "INV-0037", "Reference": "Ref MK815", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 396, "AmountPaid": 0, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "94cb6d7b-5291-49f3-a0bc-fc0c01e68575", "Name": "Marine Systems", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-03-01T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2024-03-11T00:00:00", "DueDate": "/Date(1710115200000+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 330, "TotalTax": 66, "Total": 396, "UpdatedDateUTC": "/Date(1302148059577+0000)/", "CurrencyCode": "GBP"}, {"Type": "ACCPAY", "InvoiceID": "b3962578-11e7-42f7-b2e8-da900bed7a96", "InvoiceNumber": "RPT429-1", "Reference": "", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 59.54, "AmountPaid": 0, "AmountCredited": 0, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "a41cbca7-49fa-494c-bb0f-164abce26a39", "InvoicePaymentServices": [], "Contact": {"ContactID": "3a0d40a2-2698-4cf5-b7b2-30133c632ab6", "Name": "Swanston Security", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-02-23T00:00:00", "Date": "/Date(1708646400000+0000)/", "DueDateString": "2024-03-01T00:00:00", "DueDate": "/Date(*************+0000)/", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 49.62, "TotalTax": 9.92, "Total": 59.54, "UpdatedDateUTC": "/Date(1302160905653+0000)/", "CurrencyCode": "GBP"}, {"Type": "ACCPAY", "InvoiceID": "69a7ab27-b220-4b2a-9c85-66ec14840db8", "InvoiceNumber": "ABC", "Reference": "", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 125.03, "AmountPaid": 0, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "94a82e91-53da-4f87-a417-63d6a1607ced", "Name": "Young Bros Transport", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-02-29T00:00:00", "Date": "/Date(1709164800000+0000)/", "DueDateString": "2024-03-08T00:00:00", "DueDate": "/Date(*************+0000)/", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 104.19, "TotalTax": 20.84, "Total": 125.03, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP"}, {"Type": "ACCPAY", "InvoiceID": "b86e37bb-446f-4b51-b391-b9f71728aef7", "InvoiceNumber": "RPT469-1", "Reference": "", "Payments": [{"PaymentID": "a6924c45-73f8-441e-ba22-0401fc6b4325", "Date": "/Date(*************+0000)/", "Amount": 1181.25, "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 1181.25, "AmountCredited": 0, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "a78c8f3e-e895-4295-92c9-dd0f67819b71", "InvoicePaymentServices": [], "Contact": {"ContactID": "aca6e01a-1815-474c-bd0f-18adfd95cfcb", "Name": "Truxton Property Management", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-02-24T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2024-02-24T00:00:00", "DueDate": "/Date(*************+0000)/", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 984.38, "TotalTax": 196.87, "Total": 1181.25, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCREC", "InvoiceID": "6bf54d50-b3ce-4f00-b13f-6216b66ed6ee", "InvoiceNumber": "INV-0038", "Reference": "GB1-White", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 234, "AmountPaid": 0, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "362819c9-f285-4d09-ac95-26327863adac", "Name": "Bayside Club", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-03-02T00:00:00", "Date": "/Date(1709337600000+0000)/", "DueDateString": "2024-03-12T00:00:00", "DueDate": "/Date(1710201600000+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 196.67, "TotalTax": 37.33, "Total": 234, "UpdatedDateUTC": "/Date(1302481903300+0000)/", "CurrencyCode": "GBP"}, {"Type": "ACCPAY", "InvoiceID": "41bcdfc5-c52f-4e1d-bf84-61b8155506f7", "InvoiceNumber": "SM0195", "Reference": "", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 2000, "AmountPaid": 0, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "f5a77e82-50e3-4340-a6e0-13d6a482a08a", "Name": "SMART Agency", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-01-17T00:00:00", "Date": "/Date(1705449600000+0000)/", "DueDateString": "2024-01-27T00:00:00", "DueDate": "/Date(1706313600000+0000)/", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 1666.67, "TotalTax": 333.33, "Total": 2000, "UpdatedDateUTC": "/Date(1337379530723+0000)/", "CurrencyCode": "GBP"}, {"Type": "ACCPAY", "InvoiceID": "3d9bc4f4-d3d2-48a1-b5c2-c8eb313d199c", "InvoiceNumber": "SM0210", "Reference": "", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 2500, "AmountPaid": 0, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "f5a77e82-50e3-4340-a6e0-13d6a482a08a", "Name": "SMART Agency", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-02-17T00:00:00", "Date": "/Date(1708128000000+0000)/", "DueDateString": "2024-02-27T00:00:00", "DueDate": "/Date(1708992000000+0000)/", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 2083.33, "TotalTax": 416.67, "Total": 2500, "UpdatedDateUTC": "/Date(1337379564920+0000)/", "CurrencyCode": "GBP"}, {"Type": "ACCPAY", "InvoiceID": "f9eb4838-d48b-4931-8dac-ed5ec55f6ad8", "InvoiceNumber": "", "Reference": "", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 130, "AmountPaid": 0, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "362819c9-f285-4d09-ac95-26327863adac", "Name": "Bayside Club", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-02-23T00:00:00", "Date": "/Date(1708646400000+0000)/", "DueDateString": "2024-03-04T00:00:00", "DueDate": "/Date(1709510400000+0000)/", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 108.33, "TotalTax": 21.67, "Total": 130, "UpdatedDateUTC": "/Date(1358206327507+0000)/", "CurrencyCode": "GBP"}, {"Type": "ACCREC", "InvoiceID": "ffb382c7-4aea-4aaa-9a6d-a5dc82d411fa", "InvoiceNumber": "INV-0041", "Reference": "", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 250, "AmountPaid": 0, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "847933f0-7c35-4e5b-b884-5f9df64c8e4b", "Name": "Port & Philip Freight", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-02-03T00:00:00", "Date": "/Date(1706918400000+0000)/", "DueDateString": "2024-03-04T00:00:00", "DueDate": "/Date(1709510400000+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 250, "TotalTax": 0, "Total": 250, "UpdatedDateUTC": "/Date(1431291450967+0000)/", "CurrencyCode": "GBP"}, {"Type": "ACCREC", "InvoiceID": "2423fa22-c7cd-408c-8319-49b5a2a07fac", "InvoiceNumber": "INV-0042", "Reference": "", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 1995, "AmountPaid": 0, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "847933f0-7c35-4e5b-b884-5f9df64c8e4b", "Name": "Port & Philip Freight", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-02-04T00:00:00", "Date": "/Date(1707004800000+0000)/", "DueDateString": "2024-03-05T00:00:00", "DueDate": "/Date(1709596800000+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 1995, "TotalTax": 0, "Total": 1995, "UpdatedDateUTC": "/Date(1431291719433+0000)/", "CurrencyCode": "GBP"}, {"Type": "ACCREC", "InvoiceID": "3f14ffc4-b990-45d8-9872-0719fda945f8", "InvoiceNumber": "INV-0040", "Reference": "Monthly support", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 550, "AmountPaid": 0, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "b2c1f980-96c9-45ff-a42b-dca141936c6c", "Name": "Rex Media Group", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-03-01T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2024-03-16T00:00:00", "DueDate": "/Date(1710547200000+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "DRAFT", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 458.33, "TotalTax": 91.67, "Total": 550, "UpdatedDateUTC": "/Date(1466787700440+0000)/", "CurrencyCode": "GBP"}, {"Type": "ACCREC", "InvoiceID": "44aa0707-f718-4f1c-8d53-f2da9ca59533", "InvoiceNumber": "INV-0039", "Reference": "Monthly support", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 550, "AmountPaid": 0, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "af0091a9-82ef-4cac-9fd6-22c095ac6a58", "Name": "Hamilton Smith Ltd", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-03-01T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2024-03-16T00:00:00", "DueDate": "/Date(1710547200000+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "DRAFT", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 458.33, "TotalTax": 91.67, "Total": 550, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP"}, {"Type": "ACCREC", "InvoiceID": "321bfed3-2da2-4969-9358-4de97821181b", "InvoiceNumber": "INV-001-0", "Reference": "", "Payments": [{"PaymentID": "65b746c8-9a5d-4a95-8096-d2316fcff592", "Date": "/Date(*************+0000)/", "Amount": 4200, "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 4200, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "afd4093b-c655-4847-8ee2-10a4f2c3eae3", "Name": "Maddox Publishing Group", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-01-06T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2023-02-03T00:00:00", "DueDate": "/Date(*************+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 3500, "TotalTax": 700, "Total": 4200, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCREC", "InvoiceID": "5c8a6eb6-7a9b-4ac1-ac81-60043f0fdb43", "InvoiceNumber": "INV-0043", "Reference": "", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 3200, "AmountPaid": 0, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "362819c9-f285-4d09-ac95-26327863adac", "Name": "Bayside Club", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-02-29T00:00:00", "Date": "/Date(1709164800000+0000)/", "DueDateString": "2024-03-14T00:00:00", "DueDate": "/Date(1710374400000+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 2666.67, "TotalTax": 533.33, "Total": 3200, "UpdatedDateUTC": "/Date(1609620273177+0000)/", "CurrencyCode": "GBP"}, {"Type": "ACCREC", "InvoiceID": "c378c9e2-4111-43bb-9548-4d6dfa313499", "InvoiceNumber": "INV-0044", "Reference": "", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 4200, "AmountPaid": 0, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "1262c350-fe0f-40ec-aeff-41c95b4a45af", "Name": "DIISR - Small Business Services", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-03-02T00:00:00", "Date": "/Date(1709337600000+0000)/", "DueDateString": "2024-03-26T00:00:00", "DueDate": "/Date(1711411200000+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 3500, "TotalTax": 700, "Total": 4200, "UpdatedDateUTC": "/Date(1609620497347+0000)/", "CurrencyCode": "GBP"}, {"Type": "ACCREC", "InvoiceID": "24221629-2e0c-452e-a407-28ba20e1241e", "InvoiceNumber": "INV-0045", "Reference": "", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 1623.75, "AmountPaid": 0, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "9ce626d2-14ea-463c-9fff-6785ab5f9bfb", "Name": "Boom FM", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-03-05T00:00:00", "Date": "/Date(1709596800000+0000)/", "DueDateString": "2024-03-19T00:00:00", "DueDate": "/Date(1710806400000+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 1353.13, "TotalTax": 270.62, "Total": 1623.75, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP"}, {"Type": "ACCPAY", "InvoiceID": "09aa506f-cb81-4cd3-a1ea-6a0b91804d5a", "InvoiceNumber": "", "Reference": "", "Payments": [{"PaymentID": "f2062172-b444-4c8b-b797-9f4c7a4244c7", "Date": "/Date(*************+0000)/", "Amount": 7267.2, "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 7267.2, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "1262c350-fe0f-40ec-aeff-41c95b4a45af", "Name": "DIISR - Small Business Services", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-02-03T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2023-03-06T00:00:00", "DueDate": "/Date(*************+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "PAID", "LineAmountTypes": "Exclusive", "LineItems": [], "SubTotal": 6056, "TotalTax": 1211.2, "Total": 7267.2, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCPAY", "InvoiceID": "3945273c-cc04-479f-a4b5-1e3dc7a55ed1", "InvoiceNumber": "", "Reference": "", "Payments": [{"PaymentID": "********-82a1-4810-aaca-6ce575af94b4", "Date": "/Date(*************+0000)/", "Amount": 7267.2, "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 7267.2, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "1262c350-fe0f-40ec-aeff-41c95b4a45af", "Name": "DIISR - Small Business Services", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2022-12-06T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2023-01-06T00:00:00", "DueDate": "/Date(*************+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "PAID", "LineAmountTypes": "Exclusive", "LineItems": [], "SubTotal": 6056, "TotalTax": 1211.2, "Total": 7267.2, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCPAY", "InvoiceID": "51d523ef-2d0f-4afa-aa32-0558f68b06a6", "InvoiceNumber": "RPT445-1", "Reference": "", "Payments": [{"PaymentID": "9555efd0-37e0-46ad-a819-035a1e9c28e1", "Date": "/Date(*************+0000)/", "Amount": 95.5, "Reference": "", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 95.5, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "619e3696-7b65-4c6e-a790-305de97ca9d9", "InvoicePaymentServices": [], "Contact": {"ContactID": "dec56ceb-65e9-43b3-ac98-7fe09eb37e31", "Name": "PowerDirect", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-02-26T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2023-03-08T00:00:00", "DueDate": "/Date(*************+0000)/", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 90.95, "TotalTax": 4.55, "Total": 95.5, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCPAY", "InvoiceID": "2a7a8ec4-ac5a-4690-a136-cc2dc9c76def", "InvoiceNumber": "RPT445-1", "Reference": "", "Payments": [{"PaymentID": "bb2d5c6f-6866-429b-85ae-12491f1a23db", "Date": "/Date(*************+0000)/", "Amount": 97, "Reference": "", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 97, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "619e3696-7b65-4c6e-a790-305de97ca9d9", "InvoicePaymentServices": [], "Contact": {"ContactID": "dec56ceb-65e9-43b3-ac98-7fe09eb37e31", "Name": "PowerDirect", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-03-28T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2023-04-07T00:00:00", "DueDate": "/Date(*************+0000)/", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 92.38, "TotalTax": 4.62, "Total": 97, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCREC", "InvoiceID": "4b6d0c8f-10fa-42cd-a6e5-53b175e90005", "InvoiceNumber": "INV-0001", "Reference": "RPT200-1", "Payments": [{"PaymentID": "8c481b70-b5d7-4de4-a7f7-9e161be2c472", "Date": "/Date(*************+0000)/", "Amount": 500, "Reference": "", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 500, "AmountCredited": 0, "SentToContact": true, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "abef034a-9498-4af3-9831-365f08f7b13a", "InvoicePaymentServices": [], "Contact": {"ContactID": "a871a956-05b5-4e2a-9419-7aeb478ca647", "Name": "Ridgeway University", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-02-24T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2023-03-06T00:00:00", "DueDate": "/Date(*************+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 416.67, "TotalTax": 83.33, "Total": 500, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCREC", "InvoiceID": "5d91be3d-6c7c-4885-acbc-2d1ca7b9c06e", "InvoiceNumber": "INV-0002", "Reference": "RPT200-1", "Payments": [{"PaymentID": "37d4d2dd-7a1b-49c6-98c4-d6f3a7c05ced", "Date": "/Date(*************+0000)/", "Amount": 500, "Reference": "", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 500, "AmountCredited": 0, "SentToContact": true, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "abef034a-9498-4af3-9831-365f08f7b13a", "InvoicePaymentServices": [], "Contact": {"ContactID": "a871a956-05b5-4e2a-9419-7aeb478ca647", "Name": "Ridgeway University", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-03-26T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2023-04-05T00:00:00", "DueDate": "/Date(*************+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 416.67, "TotalTax": 83.33, "Total": 500, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCPAY", "InvoiceID": "51f1a93e-c23b-4515-b4f0-e00ccf0f9ea7", "InvoiceNumber": "945-<PERSON><PERSON>", "Reference": "", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 1063.56, "AmountPaid": 0, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "3828f379-afa5-4b2a-9000-9c53d75ba1c6", "Name": "Central Copiers", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-12-31T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2024-01-10T00:00:00", "DueDate": "/Date(*************+0000)/", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 886.3, "TotalTax": 177.26, "Total": 1063.56, "UpdatedDateUTC": "/Date(1609710977440+0000)/", "CurrencyCode": "GBP"}, {"Type": "ACCPAY", "InvoiceID": "444d5808-d8dc-408f-945d-974fe8441dfa", "InvoiceNumber": "408", "Reference": "", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 119.08, "AmountPaid": 0, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "8a593982-291c-4ec3-9a42-3dbccbc6e3c8", "Name": "MCO Cleaning Services", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-01-02T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2024-01-12T00:00:00", "DueDate": "/Date(1705017600000+0000)/", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 99.23, "TotalTax": 19.85, "Total": 119.08, "UpdatedDateUTC": "/Date(1609710979910+0000)/", "CurrencyCode": "GBP"}, {"Type": "ACCPAY", "InvoiceID": "8e65df54-4bbd-41f3-b241-8da2588be341", "InvoiceNumber": "RPT429-1", "Reference": "", "Payments": [], "CreditNotes": [{"CreditNoteID": "ee8bec08-2be8-40ba-acd0-d53d5df11235", "CreditNoteNumber": "RPT429-1", "ID": "ee8bec08-2be8-40ba-acd0-d53d5df11235", "HasErrors": false, "AppliedAmount": 25.44, "DateString": "2023-12-29T00:00:00", "Date": "/Date(1703808000000+0000)/", "LineItems": [], "Total": 25.44}], "Prepayments": [], "Overpayments": [], "AmountDue": 34.1, "AmountPaid": 0, "AmountCredited": 25.44, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "a41cbca7-49fa-494c-bb0f-164abce26a39", "InvoicePaymentServices": [], "Contact": {"ContactID": "3a0d40a2-2698-4cf5-b7b2-30133c632ab6", "Name": "Swanston Security", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-12-26T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2024-01-02T00:00:00", "DueDate": "/Date(*************+0000)/", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 49.62, "TotalTax": 9.92, "Total": 59.54, "UpdatedDateUTC": "/Date(1609710979990+0000)/", "CurrencyCode": "GBP"}, {"Type": "ACCPAY", "InvoiceID": "59978bef-af2f-4a7e-9728-4997597c0980", "InvoiceNumber": "RPT445-1", "Reference": "", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 135.85, "AmountPaid": 0, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "619e3696-7b65-4c6e-a790-305de97ca9d9", "InvoicePaymentServices": [], "Contact": {"ContactID": "dec56ceb-65e9-43b3-ac98-7fe09eb37e31", "Name": "PowerDirect", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-12-29T00:00:00", "Date": "/Date(1703808000000+0000)/", "DueDateString": "2024-01-08T00:00:00", "DueDate": "/Date(*************+0000)/", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 129.38, "TotalTax": 6.47, "Total": 135.85, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP"}, {"Type": "ACCREC", "InvoiceID": "a12f582d-47e6-4706-a2e6-f8979f0aad7e", "InvoiceNumber": "INV-0011", "Reference": "RPT200-1", "Payments": [{"PaymentID": "f4602b23-e846-408a-90d1-1c35313c0bbc", "Date": "/Date(*************+0000)/", "Amount": 2500, "Reference": "", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 2500, "AmountCredited": 0, "SentToContact": true, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "abef034a-9498-4af3-9831-365f08f7b13a", "InvoicePaymentServices": [], "Contact": {"ContactID": "a871a956-05b5-4e2a-9419-7aeb478ca647", "Name": "Ridgeway University", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-12-27T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2024-01-06T00:00:00", "DueDate": "/Date(*************+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 2083.34, "TotalTax": 416.66, "Total": 2500, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCREC", "InvoiceID": "65c2b5a6-4767-4df6-97e0-f62b46f64e15", "InvoiceNumber": "INV-0018", "Reference": "Workshop", "Payments": [{"PaymentID": "f0613b50-c110-4cde-b11b-264485c61b66", "Date": "/Date(*************+0000)/", "Amount": 329.8, "Reference": "", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 329.8, "AmountCredited": 0, "SentToContact": true, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "2dd82f49-e818-4dd0-955b-b637ccaa5597", "Name": "City Agency", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-12-30T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2024-01-09T00:00:00", "DueDate": "/Date(*************+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 274.83, "TotalTax": 54.97, "Total": 329.8, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCPAY", "InvoiceID": "94f824a9-5346-4193-b0ec-1fde675df289", "InvoiceNumber": "720-2", "Reference": "", "Payments": [{"PaymentID": "2a3d61d2-be6c-401c-9d77-36c9290345fb", "Date": "/Date(*************+0000)/", "Amount": 1463.88, "Reference": "", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [{"CreditNoteID": "7d1f4e11-e909-43b0-901c-2c824dff1e82", "CreditNoteNumber": "720-2", "ID": "7d1f4e11-e909-43b0-901c-2c824dff1e82", "HasErrors": false, "AppliedAmount": 270.36, "DateString": "2024-01-02T00:00:00", "Date": "/Date(*************+0000)/", "LineItems": [], "Total": 270.36}], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 1463.88, "AmountCredited": 270.36, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "cf8fa320-a527-496c-823e-22dd069d29e6", "Name": "PC Complete", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-12-30T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2024-01-09T00:00:00", "DueDate": "/Date(*************+0000)/", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 1445.2, "TotalTax": 289.04, "Total": 1734.24, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCPAY", "InvoiceID": "81d2c35c-6bd0-4901-b084-630d0325f603", "InvoiceNumber": "RPT469-1", "Reference": "", "Payments": [{"PaymentID": "0e94b1f7-2f45-4d8b-a4aa-02a74fedf28a", "Date": "/Date(*************+0000)/", "Amount": 1181.25, "Reference": "", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 1181.25, "AmountCredited": 0, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "a78c8f3e-e895-4295-92c9-dd0f67819b71", "InvoicePaymentServices": [], "Contact": {"ContactID": "aca6e01a-1815-474c-bd0f-18adfd95cfcb", "Name": "Truxton Property Management", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-12-27T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2023-12-27T00:00:00", "DueDate": "/Date(*************+0000)/", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 984.38, "TotalTax": 196.87, "Total": 1181.25, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCPAY", "InvoiceID": "22ba8536-cb53-47f1-bfe8-8a17d00791ed", "InvoiceNumber": "RPT445-1", "Reference": "", "Payments": [{"PaymentID": "8ecba003-3357-424e-8f54-759f2c58f6ca", "Date": "/Date(*************+0000)/", "Amount": 108.6, "Reference": "", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 108.6, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "619e3696-7b65-4c6e-a790-305de97ca9d9", "InvoicePaymentServices": [], "Contact": {"ContactID": "dec56ceb-65e9-43b3-ac98-7fe09eb37e31", "Name": "PowerDirect", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-01-26T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2024-02-05T00:00:00", "DueDate": "/Date(*************+0000)/", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 103.43, "TotalTax": 5.17, "Total": 108.6, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCREC", "InvoiceID": "1160fdc8-300a-42a3-a9d9-2ee28fbe7a18", "InvoiceNumber": "INV-0020", "Reference": "P/O CRM08-12", "Payments": [{"PaymentID": "b9439c47-dd35-4fb5-805f-62dfdc2bdfdf", "Date": "/Date(*************+0000)/", "Amount": 6187.5, "Reference": "", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 6187.5, "AmountCredited": 0, "SentToContact": true, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "a871a956-05b5-4e2a-9419-7aeb478ca647", "Name": "Ridgeway University", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-01-26T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2024-02-05T00:00:00", "DueDate": "/Date(*************+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 5156.25, "TotalTax": 1031.25, "Total": 6187.5, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCPAY", "InvoiceID": "5eabc2aa-119b-407c-944e-5d1f7adc93b2", "InvoiceNumber": "RPT489-1", "Reference": "", "Payments": [{"PaymentID": "28dc822d-b35f-4d91-969a-cc9284f5fb5e", "BatchPaymentID": "4f7d624a-1899-469e-8121-2dc739fda3f7", "Date": "/Date(*************+0000)/", "Amount": 51.5, "Reference": "", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 51.5, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "5d7ad8cd-00c4-41fe-97e0-12a53f68c976", "InvoicePaymentServices": [], "Contact": {"ContactID": "97cc88ca-f89b-41f0-b8b9-e750b6f2f1d9", "Name": "Net Connect", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-01-28T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2024-02-07T00:00:00", "DueDate": "/Date(*************+0000)/", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 42.92, "TotalTax": 8.58, "Total": 51.5, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCPAY", "InvoiceID": "9feb8181-b9cd-43ef-a76f-6f86e7d9ff2e", "InvoiceNumber": "RPT489-1", "Reference": "", "Payments": [{"PaymentID": "e5889add-1be1-41a9-9f8b-662fb282ce0f", "BatchPaymentID": "4f7d624a-1899-469e-8121-2dc739fda3f7", "Date": "/Date(*************+0000)/", "Amount": 46.82, "Reference": "", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 46.82, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "5d7ad8cd-00c4-41fe-97e0-12a53f68c976", "InvoicePaymentServices": [], "Contact": {"ContactID": "97cc88ca-f89b-41f0-b8b9-e750b6f2f1d9", "Name": "Net Connect", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2023-12-31T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2024-01-10T00:00:00", "DueDate": "/Date(*************+0000)/", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 39.02, "TotalTax": 7.8, "Total": 46.82, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCPAY", "InvoiceID": "864be2b0-99e6-4f72-97f7-507c6ec32093", "InvoiceNumber": "RPT402-1", "Reference": "", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 56.35, "AmountPaid": 0, "AmountCredited": 0, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "709624ff-739b-4bb3-a4c1-1b56ead7a67f", "InvoicePaymentServices": [], "Contact": {"ContactID": "ac48c67d-3eea-44eb-96b1-9f7a89d9b761", "Name": "Xero", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-01-10T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2024-01-10T00:00:00", "DueDate": "/Date(*************+0000)/", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 46.96, "TotalTax": 9.39, "Total": 56.35, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP"}, {"Type": "ACCPAY", "InvoiceID": "4fe78373-1398-4018-8fe4-1af776f60c1b", "InvoiceNumber": "RPT429-1", "Reference": "", "Payments": [{"PaymentID": "dbf62814-809e-485a-a8a7-005f8c097921", "Date": "/Date(*************+0000)/", "Amount": 59.54, "Reference": "", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 59.54, "AmountCredited": 0, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "a41cbca7-49fa-494c-bb0f-164abce26a39", "InvoicePaymentServices": [], "Contact": {"ContactID": "3a0d40a2-2698-4cf5-b7b2-30133c632ab6", "Name": "Swanston Security", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-01-23T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2024-01-30T00:00:00", "DueDate": "/Date(*************+0000)/", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 49.62, "TotalTax": 9.92, "Total": 59.54, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCREC", "InvoiceID": "04e24ae7-98e7-4672-8377-335ef23bdaf0", "InvoiceNumber": "INV-0022", "Reference": "Portal Proj", "Payments": [{"PaymentID": "02f95dd2-fe53-47b0-8fa4-83c330acde1d", "Date": "/Date(*************+0000)/", "Amount": 1407.25, "Reference": "", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 1407.25, "AmountCredited": 0, "SentToContact": true, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "7eccc5ab-cee8-4af4-b2a9-3f9b17f03095", "Name": "<PERSON><PERSON> McLoud Watson & Associates", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-01-29T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2024-02-08T00:00:00", "DueDate": "/Date(*************+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 1172.71, "TotalTax": 234.54, "Total": 1407.25, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCREC", "InvoiceID": "749edbe7-e398-4002-9ccb-404658ffee2b", "InvoiceNumber": "INV-0024", "Reference": "Training", "Payments": [{"PaymentID": "3ba1ee6d-3007-4093-a9f7-30ffeb8c822c", "Date": "/Date(*************+0000)/", "Amount": 1000, "Reference": "", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 1000, "AmountCredited": 0, "SentToContact": true, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "9ce626d2-14ea-463c-9fff-6785ab5f9bfb", "Name": "Boom FM", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-01-30T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2024-02-09T00:00:00", "DueDate": "/Date(*************+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 833.34, "TotalTax": 166.66, "Total": 1000, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCREC", "InvoiceID": "4fe0cde0-7dda-4de9-b683-138829d54e80", "InvoiceNumber": "INV-0029", "Reference": "Monthly Support", "Payments": [{"PaymentID": "8e207277-d7af-4f47-9b58-8293df844b4e", "Date": "/Date(170*********0+0000)/", "Amount": 541.25, "Reference": "", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 541.25, "AmountCredited": 0, "SentToContact": true, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "af0091a9-82ef-4cac-9fd6-22c095ac6a58", "Name": "Hamilton Smith Ltd", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-02-05T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2024-02-15T00:00:00", "DueDate": "/Date(*************+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 451.04, "TotalTax": 90.21, "Total": 541.25, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(170*********0+0000)/"}, {"Type": "ACCREC", "InvoiceID": "80241a0e-c8f8-4010-9c18-d5d57a42d2de", "InvoiceNumber": "INV-0030", "Reference": "Monthly Support", "Payments": [{"PaymentID": "94e06b2c-e810-454b-ad55-8562aeb5f977", "Date": "/Date(170*********0+0000)/", "Amount": 541.25, "Reference": "", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 541.25, "AmountCredited": 0, "SentToContact": true, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "94a82e91-53da-4f87-a417-63d6a1607ced", "Name": "Young Bros Transport", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-02-05T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2024-02-15T00:00:00", "DueDate": "/Date(*************+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 451.04, "TotalTax": 90.21, "Total": 541.25, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(170*********0+0000)/"}, {"Type": "ACCREC", "InvoiceID": "9ca7145e-3c31-4b2a-90c4-b685fb73bf80", "InvoiceNumber": "INV-0031", "Reference": "Monthly Support", "Payments": [{"PaymentID": "057cc737-9efb-44a2-ad01-2e6dbd020ba7", "Date": "/Date(170*********0+0000)/", "Amount": 541.25, "Reference": "", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 541.25, "AmountCredited": 0, "SentToContact": true, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "847933f0-7c35-4e5b-b884-5f9df64c8e4b", "Name": "Port & Philip Freight", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-02-05T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2024-02-15T00:00:00", "DueDate": "/Date(*************+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 451.04, "TotalTax": 90.21, "Total": 541.25, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(170*********0+0000)/"}, {"Type": "ACCREC", "InvoiceID": "555386e7-fe5e-4683-b4d7-fab9d2461f3b", "InvoiceNumber": "INV-0032", "Reference": "Monthly Support", "Payments": [{"PaymentID": "fc54d079-9361-4916-af14-197251b2c8e2", "Date": "/Date(170*********0+0000)/", "Amount": 541.25, "Reference": "", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 541.25, "AmountCredited": 0, "SentToContact": true, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "b2c1f980-96c9-45ff-a42b-dca141936c6c", "Name": "Rex Media Group", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-02-05T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2024-02-15T00:00:00", "DueDate": "/Date(*************+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 451.04, "TotalTax": 90.21, "Total": 541.25, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(170*********0+0000)/"}, {"Type": "ACCREC", "InvoiceID": "710f39f5-8487-43b8-b929-216a6bd80102", "InvoiceNumber": "INV-0033", "Reference": "Yr Ref W08-143", "Payments": [{"PaymentID": "7eb20693-c58f-4e1e-9bd8-3d4388bbfaad", "Date": "/Date(*************+0000)/", "Amount": 250, "Reference": "", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [{"CreditNoteID": "5f45f9e7-fa37-41b6-a468-0bed968c3707", "CreditNoteNumber": "CN-0034", "ID": "5f45f9e7-fa37-41b6-a468-0bed968c3707", "HasErrors": false, "AppliedAmount": 19.95, "DateString": "2024-02-15T00:00:00", "Date": "/Date(*************+0000)/", "LineItems": [], "Total": 19.95}], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 250, "AmountCredited": 19.95, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "1262c350-fe0f-40ec-aeff-41c95b4a45af", "Name": "DIISR - Small Business Services", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-02-06T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2024-02-16T00:00:00", "DueDate": "/Date(*************+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 224.95, "TotalTax": 45, "Total": 269.95, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCPAY", "InvoiceID": "4fa687d4-2335-4ff2-9ab4-73510b59a5fe", "InvoiceNumber": "08-4123", "Reference": "", "Payments": [{"PaymentID": "4d3ec2b8-9978-4b34-87b8-dd2cd0401c1c", "Date": "/Date(*************+0000)/", "Amount": 5953.75, "Reference": "", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 5953.75, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "8894cc85-85c2-4fd2-9eb6-537179d1a12d", "Name": "Hoyt Productions", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-02-09T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2024-02-19T00:00:00", "DueDate": "/Date(*************+0000)/", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 4961.46, "TotalTax": 992.29, "Total": 5953.75, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCPAY", "InvoiceID": "b9336b12-7f25-489f-b42c-033bb279b418", "InvoiceNumber": "Dep", "Reference": "", "Payments": [{"PaymentID": "293a8e46-7945-4b45-bfaa-50b8f5bf1c97", "Date": "/Date(*************+0000)/", "Amount": 1500, "Reference": "", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 1500, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "4acf731f-af81-4ed3-96b2-0c408b2d98c0", "Name": "Carlton Functions", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-02-10T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2024-02-20T00:00:00", "DueDate": "/Date(*************+0000)/", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 1250, "TotalTax": 250, "Total": 1500, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCREC", "InvoiceID": "a633d5f2-396c-4e1f-ab3a-b4574d82ee1c", "InvoiceNumber": "INV-0027", "Reference": "Yr Ref W08-143", "Payments": [{"PaymentID": "e3994f99-114a-4f91-aca1-6dc4740389ed", "Date": "/Date(*************+0000)/", "Amount": 579.37, "Reference": "", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 270.63, "AmountPaid": 579.37, "AmountCredited": 0, "SentToContact": true, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "1262c350-fe0f-40ec-aeff-41c95b4a45af", "Name": "DIISR - Small Business Services", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-01-08T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2024-01-18T00:00:00", "DueDate": "/Date(*************+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "AUTHORISED", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 708.33, "TotalTax": 141.67, "Total": 850, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP"}, {"Type": "ACCPAY", "InvoiceID": "8f02561b-8cc3-4e37-b0b9-1a8eb53bbd14", "InvoiceNumber": "RPT402-1", "Reference": "", "Payments": [{"PaymentID": "********-73a8-4dba-a6f2-ed247f2964ce", "Date": "/Date(*************+0000)/", "Amount": 56.35, "Reference": "", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 56.35, "AmountCredited": 0, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "709624ff-739b-4bb3-a4c1-1b56ead7a67f", "InvoicePaymentServices": [], "Contact": {"ContactID": "ac48c67d-3eea-44eb-96b1-9f7a89d9b761", "Name": "Xero", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-02-07T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2024-02-07T00:00:00", "DueDate": "/Date(*************+0000)/", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 46.96, "TotalTax": 9.39, "Total": 56.35, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCPAY", "InvoiceID": "be30b6c1-a4e6-47ca-b2a6-04051f00f7b7", "InvoiceNumber": "RPT469-1", "Reference": "", "Payments": [{"PaymentID": "1dfca3a3-0498-4ebe-a747-a87a70eb7b39", "Date": "/Date(*************+0000)/", "Amount": 1181.25, "Reference": "", "CurrencyRate": 1, "HasAccount": false, "HasValidationErrors": false}], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 0, "AmountPaid": 1181.25, "AmountCredited": 0, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "RepeatingInvoiceID": "a78c8f3e-e895-4295-92c9-dd0f67819b71", "InvoicePaymentServices": [], "Contact": {"ContactID": "aca6e01a-1815-474c-bd0f-18adfd95cfcb", "Name": "Truxton Property Management", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-01-24T00:00:00", "Date": "/Date(*************+0000)/", "DueDateString": "2024-01-24T00:00:00", "DueDate": "/Date(*************+0000)/", "Status": "PAID", "LineAmountTypes": "Inclusive", "LineItems": [], "SubTotal": 984.38, "TotalTax": 196.87, "Total": 1181.25, "UpdatedDateUTC": "/Date(*************+0000)/", "CurrencyCode": "GBP", "FullyPaidOnDate": "/Date(*************+0000)/"}, {"Type": "ACCPAY", "InvoiceID": "50866ee1-e1f5-4773-94c3-3bf8b4e9c368", "InvoiceNumber": "Expense <PERSON>", "Reference": "", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 18.9, "AmountPaid": 0, "AmountCredited": 0, "Url": "https://expenses.xero.com/!Q!Sn2/detail/fc6159c6-017a-429d-b6b3-5b367ebf7045", "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "148be4b2-2d98-4f7f-8a79-9165b9d96c10", "ContactNumber": "d7ac3cf2bb4b6f777c4b3a791b71d8d20122cef3fe4fec32b4", "Name": "<PERSON>", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2017-07-19T00:00:00", "Date": "/Date(1500422400000+0000)/", "DueDateString": "2024-03-05T00:00:00", "DueDate": "/Date(1709596800000+0000)/", "Status": "AUTHORISED", "LineAmountTypes": "Exclusive", "LineItems": [], "SubTotal": 18.9, "TotalTax": 0, "Total": 18.9, "UpdatedDateUTC": "/Date(1709637208200+0000)/", "CurrencyCode": "GBP"}, {"Type": "ACCREC", "InvoiceID": "9b30e8c7-53fa-40cc-a64d-b5c2ab9961bc", "InvoiceNumber": "INV-0046", "Reference": "", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 29, "AmountPaid": 0, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "e87dcf91-8b17-42d0-9025-4603cb30d0dd", "Name": "Luna Cafe", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-04-02T00:00:00", "Date": "/Date(1712016000000+0000)/", "DueDateString": "2024-04-02T00:00:00", "DueDate": "/Date(1712016000000+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "AUTHORISED", "LineAmountTypes": "Exclusive", "LineItems": [], "SubTotal": 29, "TotalTax": 0, "Total": 29, "UpdatedDateUTC": "/Date(1712047637457+0000)/", "CurrencyCode": "GBP"}, {"Type": "ACCREC", "InvoiceID": "e5d658cb-19d7-4c4e-9bee-c044d45038fc", "InvoiceNumber": "INV-0047", "Reference": "", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 29, "AmountPaid": 0, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "e87dcf91-8b17-42d0-9025-4603cb30d0dd", "Name": "Luna Cafe", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-04-02T00:00:00", "Date": "/Date(1712016000000+0000)/", "DueDateString": "2024-04-02T00:00:00", "DueDate": "/Date(1712016000000+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "AUTHORISED", "LineAmountTypes": "Exclusive", "LineItems": [], "SubTotal": 29, "TotalTax": 0, "Total": 29, "UpdatedDateUTC": "/Date(1712048473600+0000)/", "CurrencyCode": "GBP"}, {"Type": "ACCREC", "InvoiceID": "59d70fce-3318-449f-a5d2-fa5febcea57c", "InvoiceNumber": "INV-0048", "Reference": "", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 29, "AmountPaid": 0, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "e87dcf91-8b17-42d0-9025-4603cb30d0dd", "Name": "Luna Cafe", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-04-02T00:00:00", "Date": "/Date(1712016000000+0000)/", "DueDateString": "2024-04-02T00:00:00", "DueDate": "/Date(1712016000000+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "AUTHORISED", "LineAmountTypes": "Exclusive", "LineItems": [], "SubTotal": 29, "TotalTax": 0, "Total": 29, "UpdatedDateUTC": "/Date(1712048646510+0000)/", "CurrencyCode": "GBP"}, {"Type": "ACCREC", "InvoiceID": "55a82f99-bf94-45b0-b2ff-9345747d0a9f", "InvoiceNumber": "INV-0049", "Reference": "", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 29, "AmountPaid": 0, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "e87dcf91-8b17-42d0-9025-4603cb30d0dd", "Name": "Luna Cafe", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-04-02T00:00:00", "Date": "/Date(1712016000000+0000)/", "DueDateString": "2024-04-02T00:00:00", "DueDate": "/Date(1712016000000+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "AUTHORISED", "LineAmountTypes": "Exclusive", "LineItems": [], "SubTotal": 29, "TotalTax": 0, "Total": 29, "UpdatedDateUTC": "/Date(1712049661183+0000)/", "CurrencyCode": "GBP"}, {"Type": "ACCREC", "InvoiceID": "8ba28a12-01ee-4979-aabb-55dd71b519e2", "InvoiceNumber": "INV-0050", "Reference": "", "Payments": [], "CreditNotes": [], "Prepayments": [], "Overpayments": [], "AmountDue": 29, "AmountPaid": 0, "AmountCredited": 0, "CurrencyRate": 1, "IsDiscounted": false, "HasAttachments": false, "InvoiceAddresses": [], "HasErrors": false, "InvoicePaymentServices": [], "Contact": {"ContactID": "e87dcf91-8b17-42d0-9025-4603cb30d0dd", "Name": "Luna Cafe", "Addresses": [], "Phones": [], "ContactGroups": [], "ContactPersons": [], "HasValidationErrors": false}, "DateString": "2024-04-02T00:00:00", "Date": "/Date(1712016000000+0000)/", "DueDateString": "2024-04-02T00:00:00", "DueDate": "/Date(1712016000000+0000)/", "BrandingThemeID": "5d4dd402-c851-497e-aae1-9ff265c0d15a", "Status": "AUTHORISED", "LineAmountTypes": "Exclusive", "LineItems": [], "SubTotal": 29, "TotalTax": 0, "Total": 29, "UpdatedDateUTC": "/Date(1712049858837+0000)/", "CurrencyCode": "GBP"}]