require("dotenv").config();
require('ts-node').register({
  transpileOnly: true,
  compilerOptions: {
    module: 'commonjs',
    allowJs: true,
  },
});

const { DB } = require("../src/db");
const { Op } = require("sequelize");
const ShopifyHelper = require("../src/helpers/ShopifyHelper").default;
const fs = require('fs');
const path = require('path');

const RELAY_WEBHOOK_URL = process.env.RELAY_WEBHOOK_URL || "https://rlw.personify.tech/webhook";

const LOG_DIR = path.join(__dirname, '../logs');
const ERROR_LOG_FILE = path.join(LOG_DIR, `webhook-update-errors-${new Date().toISOString().split('T')[0]}.json`);

if (!fs.existsSync(LOG_DIR)) {
    fs.mkdirSync(LOG_DIR, { recursive: true });
}

async function logError(log) {
    let logs = [];
    if (fs.existsSync(ERROR_LOG_FILE)) {
        const content = fs.readFileSync(ERROR_LOG_FILE, 'utf-8');
        logs = JSON.parse(content);
    }
    logs.push(log);
    fs.writeFileSync(ERROR_LOG_FILE, JSON.stringify(logs, null, 2));
    console.error(`Store ${log.storeName} (${log.storeId}): ${log.error}`);
}

async function updateStoreWebhook(store) {
    try {
        const url = store.url;
        const token = store.data?.shopifyAccessToken;

        if (!token) {
            await logError({
                storeId: store.id,
                storeName: store.name,
                error: 'Missing Shopify access token',
                timestamp: new Date().toISOString()
            });
            return false;
        }
        
        // Get current webhooks with error handling
        let webhooks;
        try {
            const response = await ShopifyHelper.apiCall(
                `${url}/admin/api/2023-04/webhooks.json`,
                'get',
                token
            );
            webhooks = response.webhooks;
            console.log(`Found ${webhooks.length} webhooks for store ${store.name} (${store.id})`);
        } catch (error) {
            await logError({
                storeId: store.id,
                storeName: store.name,
                error: 'Failed to fetch webhooks',
                errorDetail: error?.response?.data || error.message,
                timestamp: new Date().toISOString()
            });
            return false;
        }

        // Update orders/create webhooks
        for (const webhook of webhooks.filter(w => w.topic === 'orders/create')) {
            if (webhook.address.includes('/api/bg/shopify-webhook')) {
                try {
                    console.log(`Updating orders/create webhook: ${webhook.id}`);
                    await ShopifyHelper.apiCall(
                        `${url}/admin/api/2023-04/webhooks/${webhook.id}.json`,
                        'put',
                        token,
                        {
                            webhook: {
                                address: `${RELAY_WEBHOOK_URL}/${webhook.address}`,
                                topic: "orders/create",
                                format: "json"
                            }
                        }
                    );
                    console.log(`Successfully updated webhook ${webhook.id}`);
                } catch (error) {
                    await logError({
                        storeId: store.id,
                        storeName: store.name,
                        error: `Failed to update webhook ${webhook.id}`,
                        errorDetail: error?.response?.data || error.message,
                        timestamp: new Date().toISOString()
                    });
                }
            }
        }

        return true;
    } catch (error) {
        await logError({
            storeId: store.id,
            storeName: store.name,
            error: 'Unexpected error during webhook update',
            errorDetail: error.message,
            timestamp: new Date().toISOString()
        });
        return false;
    }
}

async function main() {
    try {
        await DB.init();
        
        // const testStore = await DB.OnlineStore.findOne({
        //     where: {
        //         id: '707857271149',
        //         inactive: {
        //             [Op.not]: true
        //         }
        //     }
        // });
        
        // if (testStore) {
        //     console.log(`Starting update for test store ${testStore.name} (${testStore.id})`);
        //     const result = await updateStoreWebhook(testStore);
        //     console.log(`Test store update ${result ? 'successful' : 'failed'}`);
        // }

        const stores = await DB.OnlineStore.findAll({
            where: {
                inactive: {
                    [Op.not]: true
                }
            }
        });

        for (const store of stores) {
            await updateStoreWebhook(store);
        }
    } catch (error) {
        console.error('Fatal error:', error);
        process.exit(1);
    }
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error('Script failed:', error);
        process.exit(1);
    }); 