const axios = require("axios")

const order1 = {
  "id": 5777821892832,
  "admin_graphql_api_id": "gid://shopify/Order/5777821892832",
  "app_id": 580111,
  "browser_ip": "2406:2d40:4159:2810:91b9:773c:a748:21e",
  "buyer_accepts_marketing": true,
  "cancel_reason": null,
  "cancelled_at": null,
  "cart_token": "2456342114d338b9904af9e48553435c",
  "checkout_id": 35100608299232,
  "checkout_token": "1a3b2837d46013cde896404c77803a77",
  "client_details": {
    "accept_language": "en-AU",
    "browser_height": null,
    "browser_ip": "2406:2d40:4159:2810:91b9:773c:a748:21e",
    "browser_width": null,
    "session_hash": null,
    "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/128.0.6613.98 Mobile/15E148 Safari/604.1"
  },
  "closed_at": null,
  "confirmation_number": "Y8GF0QIJ6",
  "confirmed": true,
  "contact_email": "<EMAIL>",
  "created_at": "2024-08-30T20:20:41+01:00",
  "currency": "GBP",
  "current_subtotal_price": "46.53",
  "current_subtotal_price_set": {
    "shop_money": {
      "amount": "46.53",
      "currency_code": "GBP"
    },
    "presentment_money": {
      "amount": "90.24",
      "currency_code": "AUD"
    }
  },
  "current_total_additional_fees_set": null,
  "current_total_discounts": "2.97",
  "current_total_discounts_set": {
    "shop_money": {
      "amount": "2.97",
      "currency_code": "GBP"
    },
    "presentment_money": {
      "amount": "5.76",
      "currency_code": "AUD"
    }
  },
  "current_total_duties_set": null,
  "current_total_price": "62.00",
  "current_total_price_set": {
    "shop_money": {
      "amount": "62.00",
      "currency_code": "GBP"
    },
    "presentment_money": {
      "amount": "120.24",
      "currency_code": "AUD"
    }
  },
  "current_total_tax": "0.00",
  "current_total_tax_set": {
    "shop_money": {
      "amount": "0.00",
      "currency_code": "GBP"
    },
    "presentment_money": {
      "amount": "0.00",
      "currency_code": "AUD"
    }
  },
  "customer_locale": "en-AU",
  "device_id": null,
  "discount_codes": [],
  "email": "<EMAIL>",
  "estimated_taxes": false,
  "financial_status": "paid",
  "fulfillment_status": null,
  "landing_site": "/products/pint-glass-delta-flag-keep-clear-of-me-i-am-manoeuvring-with-difficulty?shopify_email_activity_id=159914262752&syclid=cr91j89d8f4c73di7oj0&utm_campaign=emailmarketing_159914262752&utm_medium=email&utm_source=shopify_email",
  "landing_site_ref": null,
  "location_id": null,
  "merchant_of_record_app_id": null,
  "name": "#2166",
  "note": null,
  "note_attributes": [],
  "number": 1166,
  "order_number": 2166,
  "order_status_url": "https://www.greatharbourgifts.com/64237076704/orders/a7af6f2f50bb53999c223b92a50f4f24/authenticate?key=452c963542b9e3a197f6b95ea9fda4be&none=UgNdBFBDV1pXT1U",
  "original_total_additional_fees_set": null,
  "original_total_duties_set": null,
  "payment_gateway_names": [
    "shopify_payments"
  ],
  "phone": null,
  "po_number": null,
  "presentment_currency": "AUD",
  "processed_at": "2024-08-30T20:20:36+01:00",
  "reference": "a2d5ffbaffdcf2891cde6085a95c9b40",
  "referring_site": null,
  "source_identifier": "a2d5ffbaffdcf2891cde6085a95c9b40",
  "source_name": "web",
  "source_url": null,
  "subtotal_price": "46.53",
  "subtotal_price_set": {
    "shop_money": {
      "amount": "46.53",
      "currency_code": "GBP"
    },
    "presentment_money": {
      "amount": "90.24",
      "currency_code": "AUD"
    }
  },
  "tags": "",
  "tax_exempt": false,
  "tax_lines": [],
  "taxes_included": true,
  "test": false,
  "token": "a7af6f2f50bb53999c223b92a50f4f24",
  "total_discounts": "2.97",
  "total_discounts_set": {
    "shop_money": {
      "amount": "2.97",
      "currency_code": "GBP"
    },
    "presentment_money": {
      "amount": "5.76",
      "currency_code": "AUD"
    }
  },
  "total_line_items_price": "49.50",
  "total_line_items_price_set": {
    "shop_money": {
      "amount": "49.50",
      "currency_code": "GBP"
    },
    "presentment_money": {
      "amount": "96.00",
      "currency_code": "AUD"
    }
  },
  "total_outstanding": "0.00",
  "total_price": "62.00",
  "total_price_set": {
    "shop_money": {
      "amount": "62.00",
      "currency_code": "GBP"
    },
    "presentment_money": {
      "amount": "120.24",
      "currency_code": "AUD"
    }
  },
  "total_shipping_price_set": {
    "shop_money": {
      "amount": "15.47",
      "currency_code": "GBP"
    },
    "presentment_money": {
      "amount": "30.00",
      "currency_code": "AUD"
    }
  },
  "total_tax": "0.00",
  "total_tax_set": {
    "shop_money": {
      "amount": "0.00",
      "currency_code": "GBP"
    },
    "presentment_money": {
      "amount": "0.00",
      "currency_code": "AUD"
    }
  },
  "total_tip_received": "0.00",
  "total_weight": 0,
  "updated_at": "2024-08-30T20:20:44+01:00",
  "user_id": null,
  "billing_address": {
    "first_name": "Test order",
    "address1": "Capital Office, 124 City Road, London, EC1V 2NX",
    "phone": "7893929199",
    "city": "London",
    "zip": "EC1V 2NX",
    "province": "England",
    "country": "United Kingdom",
    "last_name": "Don't ship",
    "address2": "Ferguson House",
    "company": "Minh company 1",
    "latitude": 51.5266994,
    "longitude": -0.08822619999999999,
    "name": "Test order Don't ship",
    "country_code": "GB",
    "province_code": "ENG"
  },
  "customer": {
    "id": 8079691710747,
    "email": "<EMAIL>",
    "created_at": "2024-05-03T09:51:08+01:00",
    "updated_at": "2024-07-09T05:06:24+01:00",
    "first_name": "Dinh",
    "last_name": "Minh",
    "state": "disabled",
    "note": null,
    "verified_email": true,
    "multipass_identifier": null,
    "tax_exempt": false,
    "phone": null,
    "email_marketing_consent": {
      "state": "subscribed",
      "opt_in_level": "single_opt_in",
      "consent_updated_at": "2024-05-03T09:51:09+01:00"
    },
    "sms_marketing_consent": null,
    "tags": "",
    "currency": "GBP",
    "accepts_marketing": true,
    "accepts_marketing_updated_at": "2024-05-03T09:51:09+01:00",
    "marketing_opt_in_level": "single_opt_in",
    "tax_exemptions": [],
    "admin_graphql_api_id": "gid://shopify/Customer/8079691710747",
    "default_address": {
      "id": 10550199648539,
      "customer_id": 8079691710747,
      "first_name": "Test order",
      "last_name": "Don't ship",
      "company": "Minh company 1",
      "address1": "Capital Office, 124 City Road, London, EC1V 2NX",
      "address2": "Ferguson House",
      "city": "London",
      "province": "England",
      "country": "United Kingdom",
      "zip": "EC1V 2NX",
      "phone": "7893929199",
      "name": "Test order Don't ship",
      "province_code": "ENG",
      "country_code": "GB",
      "country_name": "United Kingdom",
      "default": true
    }
  },
  "discount_applications": [
    {
      "target_type": "line_item",
      "type": "automatic",
      "value": "6.0",
      "value_type": "percentage",
      "allocation_method": "across",
      "target_selection": "entitled",
      "title": "Pint glasses buy more, save more! (Discount)"
    }
  ],
  "fulfillments": [],
  "line_items": [
    {
      "id": 14289167220960,
      "admin_graphql_api_id": "gid://shopify/LineItem/14289167220960",
      "fulfillable_quantity": 3,
      "fulfillment_service": "bg-fulfillment",
      "fulfillment_status": null,
      "gift_card": false,
      "grams": 0,
      "name": "Pint Glass, (Delta flag) Keep clear of me; I am manoeuvring with difficulty",
      "price": "16.50",
      "price_set": {
        "shop_money": {
          "amount": "16.50",
          "currency_code": "GBP"
        },
        "presentment_money": {
          "amount": "32.00",
          "currency_code": "AUD"
        }
      },
      "product_exists": true,
      "product_id": 8658881544416,
      "properties": [
        {
          "name": "BG Product Number",
          "value": "915159964824"
        },
        {
          "name": "BG Product Variant Number",
          "value": "915159964824"
        },
        {
          "name": "BG Product Number",
          "value": "915159964824"
        },
        {
          "name": "BG Product Variant Number",
          "value": "915159964824"
        }
      ],
      "quantity": 3,
      "requires_shipping": true,
      "sku": "d-915159964824-915159964824",
      "taxable": false,
      "title": "Pint Glass, (Delta flag) Keep clear of me; I am manoeuvring with difficulty",
      "total_discount": "0.00",
      "total_discount_set": {
        "shop_money": {
          "amount": "0.00",
          "currency_code": "GBP"
        },
        "presentment_money": {
          "amount": "0.00",
          "currency_code": "AUD"
        }
      },
      "variant_id": 46164021903584,
      "variant_inventory_management": null,
      "variant_title": null,
      "vendor": "Great Harbour Gifts",
      "tax_lines": [],
      "duties": [],
      "discount_allocations": [
        {
          "amount": "2.97",
          "amount_set": {
            "shop_money": {
              "amount": "2.97",
              "currency_code": "GBP"
            },
            "presentment_money": {
              "amount": "5.76",
              "currency_code": "AUD"
            }
          },
          "discount_application_index": 0
        }
      ]
    }
  ],
  "payment_terms": null,
  "refunds": [],
  "shipping_address": {
    "first_name": "Test order",
    "address1": "Capital Office, 124 City Road, London, EC1V 2NX",
    "phone": "7893929199",
    "city": "London",
    "zip": "EC1V 2NX",
    "province": "England",
    "country": "United Kingdom",
    "last_name": "Don't ship",
    "address2": "Ferguson House",
    "company": "Minh company 1",
    "latitude": 51.5266994,
    "longitude": -0.08822619999999999,
    "name": "Test order Don't ship",
    "country_code": "GB",
    "province_code": "ENG"
  },
  "shipping_lines": [
    {
      "id": 4686635958496,
      "carrier_identifier": "650f1a14fa979ec5c74d063e968411d4",
      "code": "Standard International 7 to 11 working days",
      "discounted_price": "15.47",
      "discounted_price_set": {
        "shop_money": {
          "amount": "15.47",
          "currency_code": "GBP"
        },
        "presentment_money": {
          "amount": "30.00",
          "currency_code": "AUD"
        }
      },
      "phone": null,
      "price": "15.47",
      "price_set": {
        "shop_money": {
          "amount": "15.47",
          "currency_code": "GBP"
        },
        "presentment_money": {
          "amount": "30.00",
          "currency_code": "AUD"
        }
      },
      "requested_fulfillment_service_id": null,
      "source": "shopify",
      "title": "Standard International 7 to 11 working days",
      "tax_lines": [],
      "discount_allocations": []
    }
  ]
}

const order2 = {
  "id": 5777420648672,
  "admin_graphql_api_id": "gid://shopify/Order/5777420648672",
  "app_id": 580111,
  "browser_ip": "**************",
  "buyer_accepts_marketing": true,
  "cancel_reason": null,
  "cancelled_at": null,
  "cart_token": "Z2NwLXVzLWVhc3QxOjAxSjZIVEhNV0c1QzVFRU42WjcxSlcxMDFY",
  "checkout_id": 35099895628000,
  "checkout_token": "7669205d7e93ad3474cc3476ad40207e",
  "client_details": {
    "accept_language": "en-US",
    "browser_height": null,
    "browser_ip": "**************",
    "browser_width": null,
    "session_hash": null,
    "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1"
  },
  "closed_at": null,
  "confirmation_number": "XFINKZ66W",
  "confirmed": true,
  "contact_email": "<EMAIL>",
  "created_at": "2024-08-30T15:32:36+01:00",
  "currency": "GBP",
  "current_subtotal_price": "81.84",
  "current_subtotal_price_set": {
    "shop_money": {
      "amount": "81.84",
      "currency_code": "GBP"
    },
    "presentment_money": {
      "amount": "81.84",
      "currency_code": "GBP"
    }
  },
  "current_total_additional_fees_set": null,
  "current_total_discounts": "9.10",
  "current_total_discounts_set": {
    "shop_money": {
      "amount": "9.10",
      "currency_code": "GBP"
    },
    "presentment_money": {
      "amount": "9.10",
      "currency_code": "GBP"
    }
  },
  "current_total_duties_set": null,
  "current_total_price": "93.81",
  "current_total_price_set": {
    "shop_money": {
      "amount": "93.81",
      "currency_code": "GBP"
    },
    "presentment_money": {
      "amount": "93.81",
      "currency_code": "GBP"
    }
  },
  "current_total_tax": "0.00",
  "current_total_tax_set": {
    "shop_money": {
      "amount": "0.00",
      "currency_code": "GBP"
    },
    "presentment_money": {
      "amount": "0.00",
      "currency_code": "GBP"
    }
  },
  "customer_locale": "en-US",
  "device_id": null,
  "discount_codes": [
    {
      "code": "ES-F9BB422691FD",
      "amount": "9.10",
      "type": "percentage"
    }
  ],
  "email": "<EMAIL>",
  "estimated_taxes": false,
  "financial_status": "paid",
  "fulfillment_status": null,
  "landing_site": "/?srsltid=AfmBOorScCyi1WfLZgPfGPG_iZXK_FCGMVHxS_M12Z6oxt-aCaI0NuBw",
  "landing_site_ref": null,
  "location_id": null,
  "merchant_of_record_app_id": null,
  "name": "#2165",
  "note": null,
  "note_attributes": [],
  "number": 1165,
  "order_number": 2165,
  "order_status_url": "https://www.greatharbourgifts.com/64237076704/orders/ccede99aa8049404a6b4969b43626b00/authenticate?key=d001401451c1dbf9b2a239ffb3136655&none=UgNdBFBDV1pXT1U",
  "original_total_additional_fees_set": null,
  "original_total_duties_set": null,
  "payment_gateway_names": [
    "shopify_payments"
  ],
  "phone": null,
  "po_number": null,
  "presentment_currency": "GBP",
  "processed_at": "2024-08-30T15:32:32+01:00",
  "reference": "a39b196f03c29a84e646fee019de671b",
  "referring_site": "https://www.google.com/",
  "source_identifier": "a39b196f03c29a84e646fee019de671b",
  "source_name": "web",
  "source_url": null,
  "subtotal_price": "81.84",
  "subtotal_price_set": {
    "shop_money": {
      "amount": "81.84",
      "currency_code": "GBP"
    },
    "presentment_money": {
      "amount": "81.84",
      "currency_code": "GBP"
    }
  },
  "tags": "",
  "tax_exempt": false,
  "tax_lines": [],
  "taxes_included": true,
  "test": false,
  "token": "ccede99aa8049404a6b4969b43626b00",
  "total_discounts": "9.10",
  "total_discounts_set": {
    "shop_money": {
      "amount": "9.10",
      "currency_code": "GBP"
    },
    "presentment_money": {
      "amount": "9.10",
      "currency_code": "GBP"
    }
  },
  "total_line_items_price": "90.94",
  "total_line_items_price_set": {
    "shop_money": {
      "amount": "90.94",
      "currency_code": "GBP"
    },
    "presentment_money": {
      "amount": "90.94",
      "currency_code": "GBP"
    }
  },
  "total_outstanding": "0.00",
  "total_price": "93.81",
  "total_price_set": {
    "shop_money": {
      "amount": "93.81",
      "currency_code": "GBP"
    },
    "presentment_money": {
      "amount": "93.81",
      "currency_code": "GBP"
    }
  },
  "total_shipping_price_set": {
    "shop_money": {
      "amount": "11.97",
      "currency_code": "GBP"
    },
    "presentment_money": {
      "amount": "11.97",
      "currency_code": "GBP"
    }
  },
  "total_tax": "0.00",
  "total_tax_set": {
    "shop_money": {
      "amount": "0.00",
      "currency_code": "GBP"
    },
    "presentment_money": {
      "amount": "0.00",
      "currency_code": "GBP"
    }
  },
  "total_tip_received": "0.00",
  "total_weight": 551,
  "updated_at": "2024-08-30T15:32:41+01:00",
  "user_id": null,
  "billing_address": {
    "first_name": "Test order",
    "address1": "Capital Office, 124 City Road, London, EC1V 2NX",
    "phone": "7893929199",
    "city": "London",
    "zip": "EC1V 2NX",
    "province": "England",
    "country": "United Kingdom",
    "last_name": "Don't ship",
    "address2": "Ferguson House",
    "company": "Minh company 1",
    "latitude": 51.5266994,
    "longitude": -0.08822619999999999,
    "name": "Test order Don't ship",
    "country_code": "GB",
    "province_code": "ENG"
  },
  "customer": {
    "id": 8079691710747,
    "email": "<EMAIL>",
    "created_at": "2024-05-03T09:51:08+01:00",
    "updated_at": "2024-07-09T05:06:24+01:00",
    "first_name": "Dinh",
    "last_name": "Minh",
    "state": "disabled",
    "note": null,
    "verified_email": true,
    "multipass_identifier": null,
    "tax_exempt": false,
    "phone": null,
    "email_marketing_consent": {
      "state": "subscribed",
      "opt_in_level": "single_opt_in",
      "consent_updated_at": "2024-05-03T09:51:09+01:00"
    },
    "sms_marketing_consent": null,
    "tags": "",
    "currency": "GBP",
    "accepts_marketing": true,
    "accepts_marketing_updated_at": "2024-05-03T09:51:09+01:00",
    "marketing_opt_in_level": "single_opt_in",
    "tax_exemptions": [],
    "admin_graphql_api_id": "gid://shopify/Customer/8079691710747",
    "default_address": {
      "id": 10550199648539,
      "customer_id": 8079691710747,
      "first_name": "Test order",
      "last_name": "Don't ship",
      "company": "Minh company 1",
      "address1": "Capital Office, 124 City Road, London, EC1V 2NX",
      "address2": "Ferguson House",
      "city": "London",
      "province": "England",
      "country": "United Kingdom",
      "zip": "EC1V 2NX",
      "phone": "7893929199",
      "name": "Test order Don't ship",
      "province_code": "ENG",
      "country_code": "GB",
      "country_name": "United Kingdom",
      "default": true
    }
  },
  "discount_applications": [
    {
      "target_type": "line_item",
      "type": "discount_code",
      "value": "10.0",
      "value_type": "percentage",
      "allocation_method": "across",
      "target_selection": "all",
      "code": "ES-F9BB422691FD"
    }
  ],
  "fulfillments": [],
  "line_items": [
    {
      "id": 14288229138656,
      "admin_graphql_api_id": "gid://shopify/LineItem/14288229138656",
      "fulfillable_quantity": 1,
      "fulfillment_service": "bg-fulfillment",
      "fulfillment_status": null,
      "gift_card": false,
      "grams": 0,
      "name": "Pint Glass, (Delta flag) Keep clear of me; I am manoeuvring with difficulty",
      "price": "15.99",
      "price_set": {
        "shop_money": {
          "amount": "15.99",
          "currency_code": "GBP"
        },
        "presentment_money": {
          "amount": "15.99",
          "currency_code": "GBP"
        }
      },
      "product_exists": true,
      "product_id": 8658881544416,
      "properties": [
        {
          "name": "BG Product Number",
          "value": "915159964824"
        },
        {
          "name": "BG Product Variant Number",
          "value": "915159964824"
        },
        {
          "name": "BG Product Number",
          "value": "915159964824"
        },
        {
          "name": "BG Product Variant Number",
          "value": "915159964824"
        }
      ],
      "quantity": 1,
      "requires_shipping": true,
      "sku": "d-915159964824-915159964824",
      "taxable": false,
      "title": "Pint Glass, (Delta flag) Keep clear of me; I am manoeuvring with difficulty",
      "total_discount": "0.00",
      "total_discount_set": {
        "shop_money": {
          "amount": "0.00",
          "currency_code": "GBP"
        },
        "presentment_money": {
          "amount": "0.00",
          "currency_code": "GBP"
        }
      },
      "variant_id": 46164021903584,
      "variant_inventory_management": null,
      "variant_title": null,
      "vendor": "Great Harbour Gifts",
      "tax_lines": [],
      "duties": [],
      "discount_allocations": [
        {
          "amount": "1.61",
          "amount_set": {
            "shop_money": {
              "amount": "1.61",
              "currency_code": "GBP"
            },
            "presentment_money": {
              "amount": "1.61",
              "currency_code": "GBP"
            }
          },
          "discount_application_index": 0
        }
      ]
    },
    {
      "id": 14288229171424,
      "admin_graphql_api_id": "gid://shopify/LineItem/14288229171424",
      "fulfillable_quantity": 1,
      "fulfillment_service": "bg-fulfillment",
      "fulfillment_status": null,
      "gift_card": false,
      "grams": 0,
      "name": "Pint glass, SV flags (I am not seaworthy, due to shifting of cargo)",
      "price": "15.99",
      "price_set": {
        "shop_money": {
          "amount": "15.99",
          "currency_code": "GBP"
        },
        "presentment_money": {
          "amount": "15.99",
          "currency_code": "GBP"
        }
      },
      "product_exists": true,
      "product_id": 8659386925280,
      "properties": [
        {
          "name": "BG Product Number",
          "value": "946237583517"
        },
        {
          "name": "BG Product Variant Number",
          "value": "946237583517"
        },
        {
          "name": "BG Product Number",
          "value": "946237583517"
        },
        {
          "name": "BG Product Variant Number",
          "value": "946237583517"
        }
      ],
      "quantity": 1,
      "requires_shipping": true,
      "sku": "d-946237583517-946237583517",
      "taxable": false,
      "title": "Pint glass, SV flags (I am not seaworthy, due to shifting of cargo)",
      "total_discount": "0.00",
      "total_discount_set": {
        "shop_money": {
          "amount": "0.00",
          "currency_code": "GBP"
        },
        "presentment_money": {
          "amount": "0.00",
          "currency_code": "GBP"
        }
      },
      "variant_id": 46168129568992,
      "variant_inventory_management": null,
      "variant_title": null,
      "vendor": "Great Harbour Gifts",
      "tax_lines": [],
      "duties": [],
      "discount_allocations": [
        {
          "amount": "1.61",
          "amount_set": {
            "shop_money": {
              "amount": "1.61",
              "currency_code": "GBP"
            },
            "presentment_money": {
              "amount": "1.61",
              "currency_code": "GBP"
            }
          },
          "discount_application_index": 0
        }
      ]
    },
    {
      "id": 14288229204192,
      "admin_graphql_api_id": "gid://shopify/LineItem/14288229204192",
      "fulfillable_quantity": 1,
      "fulfillment_service": "inkthreadable-fulfillment",
      "fulfillment_status": null,
      "gift_card": false,
      "grams": 150,
      "name": "Nautical code flag mug, I have sprung a leak. - Ceramic / White / Orange",
      "price": "12.99",
      "price_set": {
        "shop_money": {
          "amount": "12.99",
          "currency_code": "GBP"
        },
        "presentment_money": {
          "amount": "12.99",
          "currency_code": "GBP"
        }
      },
      "product_exists": true,
      "product_id": 8051381174496,
      "properties": [],
      "quantity": 1,
      "requires_shipping": true,
      "sku": "MUG-CER-W/OR",
      "taxable": true,
      "title": "Nautical code flag mug, I have sprung a leak.",
      "total_discount": "0.00",
      "total_discount_set": {
        "shop_money": {
          "amount": "0.00",
          "currency_code": "GBP"
        },
        "presentment_money": {
          "amount": "0.00",
          "currency_code": "GBP"
        }
      },
      "variant_id": **************,
      "variant_inventory_management": null,
      "variant_title": "Ceramic / White / Orange",
      "vendor": "Great Harbour Gifts",
      "tax_lines": [],
      "duties": [],
      "discount_allocations": [
        {
          "amount": "1.30",
          "amount_set": {
            "shop_money": {
              "amount": "1.30",
              "currency_code": "GBP"
            },
            "presentment_money": {
              "amount": "1.30",
              "currency_code": "GBP"
            }
          },
          "discount_application_index": 0
        }
      ]
    },
    {
      "id": 14288229236960,
      "admin_graphql_api_id": "gid://shopify/LineItem/14288229236960",
      "fulfillable_quantity": 1,
      "fulfillment_service": "inkthreadable-fulfillment",
      "fulfillment_status": null,
      "gift_card": false,
      "grams": 150,
      "name": "Nautical code flag mug, I require a diver to examine my bottom - Ceramic / White / Antique Pink",
      "price": "12.99",
      "price_set": {
        "shop_money": {
          "amount": "12.99",
          "currency_code": "GBP"
        },
        "presentment_money": {
          "amount": "12.99",
          "currency_code": "GBP"
        }
      },
      "product_exists": true,
      "product_id": 8051567821024,
      "properties": [],
      "quantity": 1,
      "requires_shipping": true,
      "sku": "MUG-CER-W/PN",
      "taxable": true,
      "title": "Nautical code flag mug, I require a diver to examine my bottom",
      "total_discount": "0.00",
      "total_discount_set": {
        "shop_money": {
          "amount": "0.00",
          "currency_code": "GBP"
        },
        "presentment_money": {
          "amount": "0.00",
          "currency_code": "GBP"
        }
      },
      "variant_id": **************,
      "variant_inventory_management": null,
      "variant_title": "Ceramic / White / Antique Pink",
      "vendor": "Great Harbour Gifts",
      "tax_lines": [],
      "duties": [],
      "discount_allocations": [
        {
          "amount": "1.29",
          "amount_set": {
            "shop_money": {
              "amount": "1.29",
              "currency_code": "GBP"
            },
            "presentment_money": {
              "amount": "1.29",
              "currency_code": "GBP"
            }
          },
          "discount_application_index": 0
        }
      ]
    },
    {
      "id": 14288229269728,
      "admin_graphql_api_id": "gid://shopify/LineItem/14288229269728",
      "fulfillable_quantity": 1,
      "fulfillment_service": "inkthreadable-fulfillment",
      "fulfillment_status": null,
      "gift_card": false,
      "grams": 150,
      "name": "Nautical code flag mug, Patient has had much alcohol - Ceramic / White / Yellow",
      "price": "12.99",
      "price_set": {
        "shop_money": {
          "amount": "12.99",
          "currency_code": "GBP"
        },
        "presentment_money": {
          "amount": "12.99",
          "currency_code": "GBP"
        }
      },
      "product_exists": true,
      "product_id": 8051735462112,
      "properties": [],
      "quantity": 1,
      "requires_shipping": true,
      "sku": "MUG-CER-W/YL",
      "taxable": true,
      "title": "Nautical code flag mug, Patient has had much alcohol",
      "total_discount": "0.00",
      "total_discount_set": {
        "shop_money": {
          "amount": "0.00",
          "currency_code": "GBP"
        },
        "presentment_money": {
          "amount": "0.00",
          "currency_code": "GBP"
        }
      },
      "variant_id": **************,
      "variant_inventory_management": null,
      "variant_title": "Ceramic / White / Yellow",
      "vendor": "Great Harbour Gifts",
      "tax_lines": [],
      "duties": [],
      "discount_allocations": [
        {
          "amount": "1.29",
          "amount_set": {
            "shop_money": {
              "amount": "1.29",
              "currency_code": "GBP"
            },
            "presentment_money": {
              "amount": "1.29",
              "currency_code": "GBP"
            }
          },
          "discount_application_index": 0
        }
      ]
    },
    {
      "id": 14288229302496,
      "admin_graphql_api_id": "gid://shopify/LineItem/14288229302496",
      "fulfillable_quantity": 1,
      "fulfillment_service": "inkthreadable-fulfillment",
      "fulfillment_status": null,
      "gift_card": false,
      "grams": 100,
      "name": "Organic cotton unisex t-shirt (Merchant Navy mermaid) - White / Large",
      "price": "19.99",
      "price_set": {
        "shop_money": {
          "amount": "19.99",
          "currency_code": "GBP"
        },
        "presentment_money": {
          "amount": "19.99",
          "currency_code": "GBP"
        }
      },
      "product_exists": true,
      "product_id": 8603244003552,
      "properties": [],
      "quantity": 1,
      "requires_shipping": true,
      "sku": "STTU169-WHI-L",
      "taxable": true,
      "title": "Organic cotton unisex t-shirt (Merchant Navy mermaid)",
      "total_discount": "0.00",
      "total_discount_set": {
        "shop_money": {
          "amount": "0.00",
          "currency_code": "GBP"
        },
        "presentment_money": {
          "amount": "0.00",
          "currency_code": "GBP"
        }
      },
      "variant_id": **************,
      "variant_inventory_management": null,
      "variant_title": "White / Large",
      "vendor": "Great Harbour Gifts",
      "tax_lines": [],
      "duties": [],
      "discount_allocations": [
        {
          "amount": "2.00",
          "amount_set": {
            "shop_money": {
              "amount": "2.00",
              "currency_code": "GBP"
            },
            "presentment_money": {
              "amount": "2.00",
              "currency_code": "GBP"
            }
          },
          "discount_application_index": 0
        }
      ]
    }
  ],
  "payment_terms": null,
  "refunds": [],
  "shipping_address": {
    "first_name": "Test order",
    "address1": "Capital Office, 124 City Road, London, EC1V 2NX",
    "phone": "7893929199",
    "city": "London",
    "zip": "EC1V 2NX",
    "province": "England",
    "country": "United Kingdom",
    "last_name": "Don't ship",
    "address2": "Ferguson House",
    "company": "Minh company 1",
    "latitude": 51.5266994,
    "longitude": -0.08822619999999999,
    "name": "Test order Don't ship",
    "country_code": "GB",
    "province_code": "ENG"
  },
  "shipping_lines": [
    {
      "id": 4686366507232,
      "carrier_identifier": "650f1a14fa979ec5c74d063e968411d4",
      "code": "Royal Mail 1st class tracked (3-5 working days)",
      "discounted_price": "11.97",
      "discounted_price_set": {
        "shop_money": {
          "amount": "11.97",
          "currency_code": "GBP"
        },
        "presentment_money": {
          "amount": "11.97",
          "currency_code": "GBP"
        }
      },
      "phone": null,
      "price": "11.97",
      "price_set": {
        "shop_money": {
          "amount": "11.97",
          "currency_code": "GBP"
        },
        "presentment_money": {
          "amount": "11.97",
          "currency_code": "GBP"
        }
      },
      "requested_fulfillment_service_id": null,
      "source": "shopify",
      "title": "Royal Mail 1st class tracked (3-5 working days)",
      "tax_lines": [],
      "discount_allocations": []
    }
  ]
}

const MICRO_API_DOMAINS = "https://services.personify.tech";
const BACKEND_CMS_URL = "https://dev.bg-production.personify.tech";
const resellerId = "24922250924";

const triggerWebhook = async (order) => {
  const res = await axios.request({
    url: `${BACKEND_CMS_URL}/api/bg/shopify-webhook?clientId=${resellerId}&env=prod&clientName=Bottled Goose`,
    method: 'post',
    headers: {
      'content-type': 'application/json',
      'X-Auth-Token': "P8UeTt92HS98",
    },
    data: JSON.stringify(order)
  });
  console.log("triggerWebhook_res", res);
}

const main = () => {
  const id = new Date().getTime();
  triggerWebhook({ ...order1, id: id });
  triggerWebhook({ ...order2, id: id + 1 });
  setTimeout(() => {
    console.log("done")
  }, 5000)
}

main();
