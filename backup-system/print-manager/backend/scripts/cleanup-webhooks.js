require("dotenv").config();
require('ts-node').register({
  transpileOnly: true,
  compilerOptions: {
    module: 'commonjs',
    allowJs: true,
  },
});

const { DB } = require("../src/db");
const { Op } = require("sequelize");
const ShopifyHelper = require("../src/helpers/ShopifyHelper").default;
const fs = require('fs');
const path = require('path');

const LOG_DIR = path.join(__dirname, '../logs');
const WEBHOOK_LOG_FILE = path.join(LOG_DIR, `webhook-cleanup-${new Date().toISOString().split('T')[0]}.json`);
const ERROR_LOG_FILE = path.join(LOG_DIR, `webhook-cleanup-errors-${new Date().toISOString().split('T')[0]}.json`);
const API_LOG_FILE = path.join(LOG_DIR, `webhook-cleanup-api-${new Date().toISOString().split('T')[0]}.json`);

if (!fs.existsSync(LOG_DIR)) {
    fs.mkdirSync(LOG_DIR, { recursive: true });
}

async function logApiCall(log) {
    let logs = [];
    if (fs.existsSync(API_LOG_FILE)) {
        const content = fs.readFileSync(API_LOG_FILE, 'utf-8');
        logs = JSON.parse(content);
    }
    logs.push(log);
    fs.writeFileSync(API_LOG_FILE, JSON.stringify(logs, null, 2));
    console.log(`API Call: ${log.method} ${log.url}`);
}

async function logWebhookDeletion(log) {
    let logs = [];
    if (fs.existsSync(WEBHOOK_LOG_FILE)) {
        const content = fs.readFileSync(WEBHOOK_LOG_FILE, 'utf-8');
        logs = JSON.parse(content);
    }
    logs.push(log);
    fs.writeFileSync(WEBHOOK_LOG_FILE, JSON.stringify(logs, null, 2));
}

async function logError(log) {
    let logs = [];
    if (fs.existsSync(ERROR_LOG_FILE)) {
        const content = fs.readFileSync(ERROR_LOG_FILE, 'utf-8');
        logs = JSON.parse(content);
    }
    logs.push(log);
    fs.writeFileSync(ERROR_LOG_FILE, JSON.stringify(logs, null, 2));
    console.error(`Store ${log.storeName} (${log.storeId}): ${log.error}`);
}

async function cleanupStoreWebhooks(store) {
    try {
        const url = store.url;
        const token = store.data?.shopifyAccessToken;

        if (!token) {
            await logError({
                storeId: store.id,
                storeName: store.name,
                error: 'Missing Shopify access token',
                timestamp: new Date().toISOString()
            });
            return false;
        }
        
        // Get current webhooks with error handling
        let webhooks;
        try {
            const response = await ShopifyHelper.apiCall(
                `${url}/admin/api/2023-04/webhooks.json`,
                'get',
                token
            );
            webhooks = response.webhooks;
        } catch (error) {
            await logError({
                storeId: store.id,
                storeName: store.name,
                error: 'Failed to fetch webhooks',
                errorDetail: error?.response?.data || error.message,
                timestamp: new Date().toISOString()
            });
            return false;
        }

        console.log(`Processing ${webhooks.length} webhooks for store ${store.name} (${store.id})`);

        // Wrap each API call in try-catch
        for (const webhook of webhooks.filter(w => w.topic === 'orders/create')) {
            if (webhook.address.includes('/api/online-stores/') && webhook.address.includes('/shopify-webhook')) {
                try {
                    console.log(`Deleting old format orders/create webhook: ${webhook.id}`);
                    await ShopifyHelper.apiCall(
                        `${url}/admin/api/2023-04/webhooks/${webhook.id}.json`,
                        'delete',
                        token
                    );
                    
                    await logWebhookDeletion({
                        storeId: store.id,
                        storeName: store.name,
                        webhookId: webhook.id,
                        topic: webhook.topic,
                        address: webhook.address,
                        action: 'deleted',
                        timestamp: new Date().toISOString()
                    });
                } catch (error) {
                    await logError({
                        storeId: store.id,
                        storeName: store.name,
                        error: `Failed to delete webhook ${webhook.id}`,
                        errorDetail: error?.response?.data || error.message,
                        timestamp: new Date().toISOString()
                    });
                }
            }
        }

        const uninstallWebhooks = webhooks.filter(webhook => webhook.topic === 'app/uninstalled');
        
        const bottledgooseWebhooks = uninstallWebhooks.filter(webhook => 
            webhook.address.includes('bottledgoose.co.uk')
        );
        const productionWebhooks = uninstallWebhooks.filter(webhook => 
            webhook.address.includes('production.personify.tech')
        );

        if (bottledgooseWebhooks.length > 0) {
            for (const webhook of productionWebhooks) {
                const potentialAddress = webhook.address.replace(
                    process.env.OLD_BACKEND_CMS_URL || 'https://dev.bg-production.personify.tech',
                    process.env.BACKEND_CMS_URL || 'https://bg-dev.bottledgoose.co.uk'
                );

                if (bottledgooseWebhooks.some(bw => bw.address === potentialAddress)) {
                    console.log(`Deleting duplicate production webhook: ${webhook.id}`);
                    await ShopifyHelper.apiCall(
                        `${url}/admin/api/2023-04/webhooks/${webhook.id}.json`,
                        'delete',
                        token
                    );
                    webhooks = webhooks.filter(w => w.id !== webhook.id);
                    await logWebhookDeletion({
                        storeId: store.id,
                        storeName: store.name,
                        webhookId: webhook.id,
                        topic: webhook.topic,
                        address: webhook.address,
                        action: 'deleted',
                        timestamp: new Date().toISOString()
                    });
                } else {
                    console.log(`Updating production webhook ${webhook.id} to bottledgoose domain`);
                    await ShopifyHelper.apiCall(
                        `${url}/admin/api/2023-04/webhooks/${webhook.id}.json`,
                        'put',
                        token,
                        {
                            webhook: {
                                address: potentialAddress,
                                topic: "app/uninstalled",
                                format: "json"
                            }
                        }
                    );
                }
            }
        } 
        else if (productionWebhooks.length > 0) {
            const firstWebhook = productionWebhooks[0];
            const newAddress = firstWebhook.address.replace(
                process.env.OLD_BACKEND_CMS_URL || 'https://dev.bg-production.personify.tech',
                process.env.BACKEND_CMS_URL || 'https://bg-dev.bottledgoose.co.uk'
            );
            
            console.log(`Updating first production webhook ${firstWebhook.id} to bottledgoose domain`);
            await ShopifyHelper.apiCall(
                `${url}/admin/api/2023-04/webhooks/${firstWebhook.id}.json`,
                'put',
                token,
                {
                    webhook: {
                        address: newAddress,
                        topic: "app/uninstalled",
                        format: "json"
                    }
                }
            );
            for (let i = 1; i < productionWebhooks.length; i++) {
                console.log(`Deleting duplicate production webhook: ${productionWebhooks[i].id}`);
                await ShopifyHelper.apiCall(
                    `${url}/admin/api/2023-04/webhooks/${productionWebhooks[i].id}.json`,
                    'delete',
                    token
                );

                await logWebhookDeletion({
                    storeId: store.id,
                    storeName: store.name,
                    webhookId: productionWebhooks[i].id,
                    topic: productionWebhooks[i].topic,
                    address: productionWebhooks[i].address,
                    action: 'deleted',
                    timestamp: new Date().toISOString()
                });
            }
        }
        
        return true;
    } catch (error) {
        await logError({
            storeId: store.id,
            storeName: store.name,
            error: 'Unexpected error during webhook cleanup',
            errorDetail: error.message,
            timestamp: new Date().toISOString()
        });
        return false;
    }
}

async function convertRelayWebhooks(store) {
    try {
        const url = store.url;
        const token = store.data?.shopifyAccessToken;

        if (!token) {
            await logError({
                storeId: store.id,
                storeName: store.name,
                error: 'Missing Shopify access token',
                timestamp: new Date().toISOString()
            });
            return false;
        }

        // Get current webhooks
        let webhooks;
        try {
            const response = await ShopifyHelper.apiCall(
                `${url}/admin/api/2023-04/webhooks.json`, 
                'get',
                token
            );
            webhooks = response.webhooks;
        } catch (error) {
            await logError({
                storeId: store.id,
                storeName: store.name,
                error: 'Failed to fetch webhooks',
                errorDetail: error?.response?.data || error.message,
                timestamp: new Date().toISOString()
            });
            return false;
        }

        // Find webhooks using old relay URL
        const relayWebhooks = webhooks.filter(w => 
            w.address.includes('rlw.personify.tech/webhook/https://dev.services.personify.tech')
        );

        for (const webhook of relayWebhooks) {
            // Create new webhook with updated URL
            const newAddress = webhook.address.replace(
                'dev.services.personify.tech',
                'dev.bg-production.personify.tech'
            );

            try {
                // Create new webhook
                await logApiCall({
                    url: `${url}/admin/api/2023-04/webhooks.json`,
                    method: 'post',
                    token: token,
                    storeId: store.id,
                    storeName: store.name,
                    body: {
                        webhook: {
                            address: newAddress,
                            topic: webhook.topic,
                            format: "json"
                        }
                    },
                    timestamp: new Date().toISOString()
                });

                // await ShopifyHelper.apiCall(
                //     `${url}/admin/api/2023-04/webhooks.json`,
                //     'post',
                //     token,
                //     {
                //         webhook: {
                //             address: newAddress,
                //             topic: webhook.topic,
                //             format: "json"
                //         }
                //     }
                // );

                // Delete old webhook
                await logApiCall({
                    url: `${url}/admin/api/2023-04/webhooks/${webhook.id}.json`,
                    method: 'delete',
                    token: token,
                    storeId: store.id,
                    storeName: store.name,
                    webhookId: webhook.id,
                    timestamp: new Date().toISOString()
                });

                // await ShopifyHelper.apiCall(
                //     `${url}/admin/api/2023-04/webhooks/${webhook.id}.json`,
                //     'delete',
                //     token
                // );

                await logWebhookDeletion({
                    storeId: store.id,
                    storeName: store.name,
                    webhookId: webhook.id,
                    topic: webhook.topic,
                    address: webhook.address,
                    action: 'replaced',
                    newAddress: newAddress,
                    timestamp: new Date().toISOString()
                });

            } catch (error) {
                await logError({
                    storeId: store.id,
                    storeName: store.name,
                    error: `Failed to update webhook ${webhook.id}`,
                    errorDetail: error?.response?.data || error.message,
                    timestamp: new Date().toISOString()
                });
            }
        }

        return true;

    } catch (error) {
        await logError({
            storeId: store.id,
            storeName: store.name,
            error: 'Unexpected error during webhook conversion',
            errorDetail: error.message,
            timestamp: new Date().toISOString()
        });
        return false;
    }
}

async function main() {
    try {
        await DB.init();
        
        // const testStore = await DB.OnlineStore.findOne({
        //     where: {
        //         id: '272430163266',
        //         inactive: {
        //             [Op.not]: true
        //         }
        //     }
        // });
        
        // if (testStore) {
        //     console.log(`Starting cleanup for test store ${testStore.name} (${testStore.id})`);
        //     const result = await cleanupStoreWebhooks(testStore);
        //     console.log(`Test store cleanup ${result ? 'successful' : 'failed'}`);
        // }

        const stores = await DB.OnlineStore.findAll({
            where: {
                inactive: {
                    [Op.not]: true
                }
            }
        });

        for (const store of stores) {
            // await cleanupStoreWebhooks(store);
            await convertRelayWebhooks(store);
        }
    } catch (error) {
        console.error('Fatal error:', error);
        process.exit(1);
    }
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error('Script failed:', error);
        process.exit(1);
    }); 