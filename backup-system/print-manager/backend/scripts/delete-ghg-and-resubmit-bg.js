const axios = require('axios');
const fs = require('fs');
const path = require('path');
const jsonSavePath = path.join(__dirname, 'saveOrder.json');
const saveOrder = fs.existsSync(jsonSavePath) ? JSON.parse(
  fs.readFileSync(jsonSavePath).toString()
) : {};

const orderNumbers = [
  // '1842',
  '1843',
  '1844',
  '1845',
  '1846',
  '1847',
  '1848',
  '1849',
  '1850',
  '1851',
  '1852',
  '1853',
  '1854',
  '1855',
  '1856',
  '1857',
  '1858',
];
const orderMatchData = {
  ...saveOrder,
}

const apiCall = async (client, apiPath, method, data) => {
  const host = client === 'bg' ? 'https://nocodb.personify.tech/api/v1/db/data/v1/p_7yqohwae0b0i1h'
    : client === 'ghg' ? 'https://nocodb.personify.tech/api/v1/db/data/v1/p_bie0skhs153ob2'
    : '';
  return axios.request({
    method: method,
    url: host + apiPath,
    headers: {
      'xc-token': 'efoDr21CkrSUuEW4hSmYtwION6ZiHPRShILhTOha',
      ...(!!data ? {
        'Content-Type': 'application/json',
      } : {}),
    },
    data: !!data ? JSON.stringify(data) : undefined,
  });
}

const get = (client, apiPath) => apiCall(client, apiPath, 'get', undefined);

// const get = async (client, apiPath) => {
//   const host = client === 'bg' ? 'https://nocodb.personify.tech/api/v1/db/data/v1/p_7yqohwae0b0i1h'
//     : client === 'ghg' ? 'https://nocodb.personify.tech/api/v1/db/data/v1/p_bie0skhs153ob2'
//     : '';
//   return axios.request({
//     method: 'get',
//     url: host + apiPath,
//     headers: { 'xc-token': 'efoDr21CkrSUuEW4hSmYtwION6ZiHPRShILhTOha' },
//   });
// }

const deleteGhgAndResubmit = async (orderNumber) => {
  const res = await get('ghg', `/Orders/find-one?where=(Order Number,eq,${orderNumber})`);
  if (res.data && res.data.Id && +res.data['Order Number'] === +(orderNumber)) {
    const order = res.data['Raw Data'];
    const orderId = String(order.id);
    orderMatchData['#'+orderNumber] = order;
    fs.writeFileSync(jsonSavePath, JSON.stringify(orderMatchData, null, 2));
    console.log(`Processing order ${orderNumber} - ${orderId}...`);
    const findPipelineRes = await get('ghg', `/Pipelines/find-one?where=(OrderId,eq,${orderId})`);
    const ghgPipelineId = findPipelineRes.data.Id;
    if (ghgPipelineId && findPipelineRes.data.OrderId === orderId) {
      console.log(`Deleting GHG pipeline ${ghgPipelineId}...`);
      await apiCall('ghg', `/Pipelines/${ghgPipelineId}`, 'delete', undefined);
    }
    const findBgPipelineRes = await get('bg', `/Pipelines/find-one?where=(OrderId,eq,${orderId})`);
    const bgPipelineId = findBgPipelineRes.data.Id;
    if (bgPipelineId && findBgPipelineRes.data.OrderId === orderId) {
      console.log(`Deleting BG pipeline ${bgPipelineId}...`);
      await apiCall('bg', `/Pipelines/${bgPipelineId}`, 'delete', undefined);
    }

    const findBgOrderRes = await get('bg', `/Orders/find-one?where=(Order Number,eq,${orderNumber})`);
    const bgOrderId = findBgOrderRes.data.Id;
    if (bgOrderId && +findBgOrderRes.data['Order Number'] === +orderNumber) {
      console.log(`Deleting BG Order ${bgOrderId}...`);
      await apiCall('bg', `/Orders/${bgOrderId}`, 'delete', undefined);
    }
    console.log(`Deleting GHG Order... ${res.data.Id}`);
    await apiCall('ghg', `/Orders/${res.data.Id}`, 'delete', undefined);

    console.log('resubmitting order...');
    const resubmitRes = await axios.request({
      url: 'https://dev.bg-production.personify.tech/api/bg/shopify-webhook?clientId=946773517818&env=prod&clientName=Great%20Harbour%20Gifts&storeId=504272731660',
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
      },
      data: JSON.stringify(order)
    });

    if (resubmitRes.data.success) {
      console.log(`Resubmit ${orderNumber} successfully! New ID: ${resubmitRes.data.data.Id}`);
    } else {
      console.log(`Resubmit ${orderNumber} failed!`);
      console.log(resubmitRes.data);
    }
  }
};

const start = async () => {
  // deleteGhgAndResubmit(orderNumbers[0]);
  for (let i=0; i<orderNumbers.length; i++) {
    await deleteGhgAndResubmit(orderNumbers[i]);
  }
}

start();