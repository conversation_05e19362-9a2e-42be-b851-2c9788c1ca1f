
const axios = require('axios');
const fs = require('fs');

const url = `https://partner-in-wine-uk.myshopify.com/admin/api/2023-04/orders.json?processed_at_min=2023-01-01T19:12:19+01:00`;

const fromProperty = (properties, label, defaultValue) => {
  const findItem = (properties || []).find(val => val.name === label);
  return !findItem ? defaultValue : findItem.value;
}

const filterPersonalised = async () => {
  const res = await axios.request({
    url,
    method: 'get',
    headers: {
      'X-Shopify-Access-Token': '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'
    },
  });

  const { orders } = res.data;
  const filtered = orders.filter(v => {

    if(v.customer.email === "<EMAIL>") return false;
    if (typeof v.customer.first_name === 'string' && v.customer.first_name.toLowerCase().includes('test')) return false;
    if (typeof v.customer.last_name === 'string' && v.customer.last_name.toLowerCase().includes('test')) return false;

    for (let i=0; i<v.line_items.length; i++) {
      const item = v.line_items[i];
      const properties = item.properties;
      const text = fromProperty(properties, 'Personalise Text', '');
      if (!!text) return true;      
    }
    return false;
  });
  fs.writeFileSync('piw.json', JSON.stringify(filtered, undefined, 4));
}

const wait = (num) => new Promise((resolve, reject) => {
  setTimeout(() => {
    resolve();
  }, num);
});

const resend = async () => {
  const piw = require('./piw.json');
  for (let i=0; i<piw.length; i++) {
    const order = piw[i];
    await axios.request({
      url: 'https://dev.bg-production.personify.tech/api/online-stores/partner-in-wine/shopify-webhook',
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
      },
      data: JSON.stringify(order),
    });
    await wait(2000);
  }
};

resend();