const axios = require('axios');

// Simplified Etsy helper class for testing
class EtsyTest {
  constructor(accessToken, refreshToken) {
    this.accessToken = accessToken;
    this.refreshToken = refreshToken;
    this.lastRequestTime = 0;
    this.MIN_REQUEST_INTERVAL = 800;
    this.MAX_CONCURRENT_REQUESTS = 2;
    this.activeRequests = 0;
  }

  async waitForRateLimit() {
    const currentTime = Date.now();
    const timeSinceLastRequest = currentTime - this.lastRequestTime;

    if (timeSinceLastRequest < this.MIN_REQUEST_INTERVAL) {
      await new Promise(resolve => setTimeout(resolve, this.MIN_REQUEST_INTERVAL - timeSinceLastRequest));
    }

    while (this.activeRequests >= this.MAX_CONCURRENT_REQUESTS) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    this.activeRequests++;
    this.lastRequestTime = Date.now();
  }

  releaseRequest() {
    this.activeRequests--;
  }

  async apiCall(path, options) {
    try {
      await this.waitForRateLimit();

      const headers = {
        'Authorization': `Bearer ${this.accessToken}`,
        'x-api-key': 'ikch5oozb9xwdddk0t6kotjn',
        'Content-Type': ['post', 'put', 'patch'].includes(options.method) ? 'application/json' : undefined
      };
      if (!headers['Content-Type']) {
        delete headers['Content-Type'];
      }

      const res = await axios.request({
        url: `https://api.etsy.com/v3/application${path}`,
        method: options.method,
        headers: headers,
        data: !options.payload ? undefined : JSON.stringify(options.payload),
      });
      return res.data;
    } catch (err) {
      console.error('API call error:', err?.response?.data || err.message);
      throw err;
    } finally {
      this.releaseRequest();
    }
  }

  async getMe() {
    return this.apiCall('/users/me', { method: 'get' });
  }

  async getShop() {
    const { user_id, shop_id } = await this.getMe() || {};
    if (!shop_id) {
      throw new Error('Shop not found');
    }
    return this.apiCall('/shops/' + shop_id, { method: 'get' });
  }

  async getShopListings() {
    const { shop_id } = await this.getMe() || {};
    if (!shop_id) {
      throw new Error('Shop not found');
    }
    const data = await this.apiCall(`/shops/${shop_id}/listings`, { method: 'get' });
    return data.results;
  }

  async getListing({ shopId, listingId }) {
    let _shopId = shopId;
    if (!_shopId) {
      const shopInfo = await this.getMe() || {};
      _shopId = shopInfo?.shop_id;
    }
    if (!_shopId) {
      throw new Error('Shop not found');
    }
    try {
      const data = await this.apiCall(`/listings/${listingId}`, { method: 'get' });
      return data;
    } catch (error) {
      throw new Error(error?.response?.data?.error || error.message);
    }
  }

  async getShopShippingProfiles({ shopId }) {
    let _shopId = shopId;
    if (!_shopId) {
      const shopInfo = await this.getMe() || {};
      _shopId = shopInfo?.shop_id;
    }
    if (!_shopId) {
      throw new Error('Shop not found');
    }
    const data = await this.apiCall(`/shops/${_shopId}/shipping-profiles`, { method: 'get' });
    return data.results;
  }

  async getShopSection({ shopId }) {
    let _shopId = shopId;
    if (!_shopId) {
      const shopInfo = await this.getMe() || {};
      _shopId = shopInfo?.shop_id;
    }
    if (!_shopId) {
      throw new Error('Shop not found');
    }
    try {
      const data = await this.apiCall(`/shops/${_shopId}/sections`, { method: 'get' });
      return data;
    } catch (error) {
      throw new Error(error?.response?.data?.error || error.message);
    }
  }

  async getReceipts({ shopId, minCreated }) {
    let _shopId = shopId;
    if (!_shopId) {
      const shopInfo = await this.getMe() || {};
      _shopId = shopInfo?.shop_id;
    }
    if (!_shopId) {
      throw new Error('Shop not found');
    }
    try {
      const thirtyDaysAgo = Math.floor(Date.now() / 1000) - (30 * 24 * 60 * 60);
      const data = await this.apiCall(`/shops/${_shopId}/receipts?limit=100&min_created=${minCreated || thirtyDaysAgo}`, { method: 'get' });
      return data?.results;
    } catch (error) {
      throw new Error(error?.response?.data?.error || error.message);
    }
  }

  async getTaxonomyNodes({ shopId }) {
    let _shopId = shopId;
    if (!_shopId) {
      const shopInfo = await this.getMe() || {};
      _shopId = shopInfo?.shop_id;
    }
    if (!_shopId) {
      throw new Error('Shop not found');
    }
    try {
      const data = await this.apiCall(`/seller-taxonomy/nodes`, { method: 'get' });
      return data;
    } catch (error) {
      throw new Error(error?.response?.data?.error || error.message);
    }
  }
}

const storeData = {"etsyAccessToken":"*********.tFYC0rm7Df6zLSCI43BOSil0QlPALDLRucoLVJLdXbiRIpbGzIvbzECxB2uuTrI1qLUkCm_47TuTVmPk4-OEMbwklu","etsyRefreshToken":"*********.nKNvWdBVInYyb6d0nu1Kpfylu9VbnDKgXdxoqVRG9mk6emFB9lP4ge0VG3CXZD7JJcZ7LJlSI2MzxeccuFf3UPmtE-"}

const TEST_LISTING_ID = process.argv[2] || 4298325565;

async function testEtsyListingInfo() {
  console.log('🧪 Starting Etsy API Tests...\n');

  try {
    // Initialize Etsy helper
    const etsy = new EtsyTest(storeData.etsyAccessToken, storeData.etsyRefreshToken);
    console.log('✅ Etsy helper initialized');

    // Test 1: Get user and shop information
    console.log('\n📋 Test 1: Getting user and shop information...');
    try {
      const userInfo = await etsy.getMe();
      console.log('👤 User Info:', JSON.stringify(userInfo, null, 2));

      const shopInfo = await etsy.getShop();
      console.log('🏪 Shop Info:', JSON.stringify(shopInfo, null, 2));
    } catch (error) {
      console.error('❌ Error getting user/shop info:', error.message);
    }

    // Test 2: Get shop listings
    console.log('\n📋 Test 2: Getting shop listings...');
    try {
      const listings = await etsy.getShopListings();
      console.log(`📦 Found ${listings.length} listings:`);
      listings.forEach((listing, index) => {
        console.log(`  ${index + 1}. ID: ${listing.listing_id}, Title: "${listing.title}", Price: $${listing.price.amount / listing.price.divisor}`);
      });
    } catch (error) {
      console.error('❌ Error getting shop listings:', error.message);
    }

    // Test 3: Get specific listing by ID
    console.log('\n📋 Test 3: Getting specific listing information...');
    try {
      const listing = await etsy.getListing({ listingId: TEST_LISTING_ID });
      console.log('📄 Listing Details:', JSON.stringify(listing, null, 2));
    } catch (error) {
      console.error(`❌ Error getting listing ${TEST_LISTING_ID}:`, error.message);
    }

    // Test 4: Get shipping profiles
    console.log('\n📋 Test 4: Getting shipping profiles...');
    try {
      const shippingProfiles = await etsy.getShopShippingProfiles({});
      console.log('🚚 Shipping Profiles:', JSON.stringify(shippingProfiles, null, 2));
    } catch (error) {
      console.error('❌ Error getting shipping profiles:', error.message);
    }

    // Test 5: Get shop sections
    console.log('\n📋 Test 5: Getting shop sections...');
    try {
      const sections = await etsy.getShopSection({});
      console.log('📂 Shop Sections:', JSON.stringify(sections, null, 2));
    } catch (error) {
      console.error('❌ Error getting shop sections:', error.message);
    }

    // Test 6: Get recent receipts
    console.log('\n📋 Test 6: Getting recent receipts...');
    try {
      const receipts = await etsy.getReceipts({});
      console.log(`📋 Found ${receipts.length} recent receipts`);
      if (receipts.length > 0) {
        console.log('📄 First receipt:', JSON.stringify(receipts[0], null, 2));
      }
    } catch (error) {
      console.error('❌ Error getting receipts:', error.message);
    }

    // Test 7: Get taxonomy nodes
    console.log('\n📋 Test 7: Getting taxonomy nodes...');
    try {
      const taxonomy = await etsy.getTaxonomyNodes({});
      console.log('🏷️ Taxonomy Nodes (first 5):', JSON.stringify(taxonomy.slice(0, 5), null, 2));
    } catch (error) {
      console.error('❌ Error getting taxonomy nodes:', error.message);
    }

    console.log('\n✅ All tests completed!');

  } catch (error) {
    console.error('💥 Test failed with error:', error);
  }
}

// Main execution
async function main() {
  console.log('🚀 Etsy API Test Script\n');

  // Test with hardcoded tokens
  await testEtsyListingInfo();

  console.log('\n🏁 Test script finished');
  process.exit(0);
}

// Handle errors
process.on('unhandledRejection', (reason, promise) => {
  console.error('💥 Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

main().catch(console.error);
