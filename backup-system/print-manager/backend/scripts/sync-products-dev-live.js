// example: node scripts/sync-products-dev-live.js bgreseller CD%2022 dev2live

const axios = require('axios');


const env = {
  dev: {
    url: 'https://dev.bg-production.personify.tech',
    admin: {
      email: '<EMAIL>',
      password: process.argv[2],
    },
    token: '',
  },
  live: {
    url: 'https://bg-production.personify.tech',
    admin: {
      email: '<EMAIL>',
      password: process.argv[3],
    },
    token: '',
  }
};

const [source, target] = process.argv[4].split('2');


const login = async (envName) => {
  const envData = env[envName];
  const res = await axios.request({
    url: `${envData.url}/api/users/login`,
    method: 'post',
    headers: {
      'content-type': 'application/json'
    },
    data: JSON.stringify(envData.admin)
  });
  envData.token = res.data.data.token;
}

const start = async () => {
  await login('dev');
  await login('live');

  const sourceEnv = env[source];
  console.log('sourceEnv', sourceEnv.url)
  const targetEnv = env[target];

  let res = await axios.request({
    url: `${sourceEnv.url}/api/products`,
    method: 'get',
    headers: {
      'content-type': 'application/json',
      'authorization': `${sourceEnv.token}`
    }
  });

  const products = res.data.data.list;
  console.log('products', products); 

  for (let i=0; i<products.length; i++) {
    const p = products[i];
    console.log(`[${i+1}/${products.length}]`, p.name, p.previewData?.length || 0);

    const { id, name, description, image, galleries, previewData, tags, physicalWidth, physicalHeight, editorWidth, editorHeight,
      printAreas, availableForResellerIds, data, variations, price, packagingDescription, packagingImage, artboardUrl, dropletUrl } = p;

    res = await axios.request({
      url: `${targetEnv.url}/api/products`,
      method: 'post',
      headers: {
        'content-type': 'application/json',
        'authorization': `${targetEnv.token}`
      },
      data: JSON.stringify({
        id,
        previewData,
        name, description, image: !image ? undefined : image, galleries, tags: !tags ? undefined : tags, physicalWidth, physicalHeight,
        printAreas, availableForResellerIds, data, variations, price, packagingDescription, packagingImage, artboardUrl, dropletUrl,
        editorWidth, editorHeight,
      })
    });
    console.log('res', res.data.success, res.data.error);
  }

  console.log('DONE');
};
start();