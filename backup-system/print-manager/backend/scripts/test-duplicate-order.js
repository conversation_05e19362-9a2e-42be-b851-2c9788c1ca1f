const axios = require("axios")

const order1 = {
  "id": 5777821892832,
  "admin_graphql_api_id": "gid://shopify/Order/5777821892832",
  "app_id": 580111,
  "browser_ip": "2406:2d40:4159:2810:91b9:773c:a748:21e",
  "buyer_accepts_marketing": true,
  "cancel_reason": null,
  "cancelled_at": null,
  "cart_token": "2456342114d338b9904af9e48553435c",
  "checkout_id": 35100608299232,
  "checkout_token": "1a3b2837d46013cde896404c77803a77",
  "client_details": {
    "accept_language": "en-AU",
    "browser_height": null,
    "browser_ip": "2406:2d40:4159:2810:91b9:773c:a748:21e",
    "browser_width": null,
    "session_hash": null,
    "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/128.0.6613.98 Mobile/15E148 Safari/604.1"
  },
  "closed_at": null,
  "confirmation_number": "Y8GF0QIJ6",
  "confirmed": true,
  "contact_email": "<EMAIL>",
  "created_at": "2024-08-30T20:20:41+01:00",
  "currency": "GBP",
  "current_subtotal_price": "46.53",
  "current_subtotal_price_set": {
    "shop_money": {
      "amount": "46.53",
      "currency_code": "GBP"
    },
    "presentment_money": {
      "amount": "90.24",
      "currency_code": "AUD"
    }
  },
  "current_total_additional_fees_set": null,
  "current_total_discounts": "2.97",
  "current_total_discounts_set": {
    "shop_money": {
      "amount": "2.97",
      "currency_code": "GBP"
    },
    "presentment_money": {
      "amount": "5.76",
      "currency_code": "AUD"
    }
  },
  "current_total_duties_set": null,
  "current_total_price": "62.00",
  "current_total_price_set": {
    "shop_money": {
      "amount": "62.00",
      "currency_code": "GBP"
    },
    "presentment_money": {
      "amount": "120.24",
      "currency_code": "AUD"
    }
  },
  "current_total_tax": "0.00",
  "current_total_tax_set": {
    "shop_money": {
      "amount": "0.00",
      "currency_code": "GBP"
    },
    "presentment_money": {
      "amount": "0.00",
      "currency_code": "AUD"
    }
  },
  "customer_locale": "en-AU",
  "device_id": null,
  "discount_codes": [],
  "email": "<EMAIL>",
  "estimated_taxes": false,
  "financial_status": "paid",
  "fulfillment_status": null,
  "landing_site": "/products/pint-glass-delta-flag-keep-clear-of-me-i-am-manoeuvring-with-difficulty?shopify_email_activity_id=159914262752&syclid=cr91j89d8f4c73di7oj0&utm_campaign=emailmarketing_159914262752&utm_medium=email&utm_source=shopify_email",
  "landing_site_ref": null,
  "location_id": null,
  "merchant_of_record_app_id": null,
  "name": "#2166",
  "note": null,
  "note_attributes": [],
  "number": 1166,
  "order_number": 2166,
  "order_status_url": "https://www.greatharbourgifts.com/64237076704/orders/a7af6f2f50bb53999c223b92a50f4f24/authenticate?key=452c963542b9e3a197f6b95ea9fda4be&none=UgNdBFBDV1pXT1U",
  "original_total_additional_fees_set": null,
  "original_total_duties_set": null,
  "payment_gateway_names": [
    "shopify_payments"
  ],
  "phone": null,
  "po_number": null,
  "presentment_currency": "AUD",
  "processed_at": "2024-08-30T20:20:36+01:00",
  "reference": "a2d5ffbaffdcf2891cde6085a95c9b40",
  "referring_site": null,
  "source_identifier": "a2d5ffbaffdcf2891cde6085a95c9b40",
  "source_name": "web",
  "source_url": null,
  "subtotal_price": "46.53",
  "subtotal_price_set": {
    "shop_money": {
      "amount": "46.53",
      "currency_code": "GBP"
    },
    "presentment_money": {
      "amount": "90.24",
      "currency_code": "AUD"
    }
  },
  "tags": "",
  "tax_exempt": false,
  "tax_lines": [],
  "taxes_included": true,
  "test": false,
  "token": "a7af6f2f50bb53999c223b92a50f4f24",
  "total_discounts": "2.97",
  "total_discounts_set": {
    "shop_money": {
      "amount": "2.97",
      "currency_code": "GBP"
    },
    "presentment_money": {
      "amount": "5.76",
      "currency_code": "AUD"
    }
  },
  "total_line_items_price": "49.50",
  "total_line_items_price_set": {
    "shop_money": {
      "amount": "49.50",
      "currency_code": "GBP"
    },
    "presentment_money": {
      "amount": "96.00",
      "currency_code": "AUD"
    }
  },
  "total_outstanding": "0.00",
  "total_price": "62.00",
  "total_price_set": {
    "shop_money": {
      "amount": "62.00",
      "currency_code": "GBP"
    },
    "presentment_money": {
      "amount": "120.24",
      "currency_code": "AUD"
    }
  },
  "total_shipping_price_set": {
    "shop_money": {
      "amount": "15.47",
      "currency_code": "GBP"
    },
    "presentment_money": {
      "amount": "30.00",
      "currency_code": "AUD"
    }
  },
  "total_tax": "0.00",
  "total_tax_set": {
    "shop_money": {
      "amount": "0.00",
      "currency_code": "GBP"
    },
    "presentment_money": {
      "amount": "0.00",
      "currency_code": "AUD"
    }
  },
  "total_tip_received": "0.00",
  "total_weight": 0,
  "updated_at": "2024-08-30T20:20:44+01:00",
  "user_id": null,
  "billing_address": {
    "first_name": "Test order",
    "address1": "Capital Office, 124 City Road, London, EC1V 2NX",
    "phone": "7893929199",
    "city": "London",
    "zip": "EC1V 2NX",
    "province": "England",
    "country": "United Kingdom",
    "last_name": "Don't ship",
    "address2": "Ferguson House",
    "company": "Minh company 1",
    "latitude": 51.5266994,
    "longitude": -0.08822619999999999,
    "name": "Test order Don't ship",
    "country_code": "GB",
    "province_code": "ENG"
  },
  "customer": {
    "id": 8079691710747,
    "email": "<EMAIL>",
    "created_at": "2024-05-03T09:51:08+01:00",
    "updated_at": "2024-07-09T05:06:24+01:00",
    "first_name": "Dinh",
    "last_name": "Minh",
    "state": "disabled",
    "note": null,
    "verified_email": true,
    "multipass_identifier": null,
    "tax_exempt": false,
    "phone": null,
    "email_marketing_consent": {
      "state": "subscribed",
      "opt_in_level": "single_opt_in",
      "consent_updated_at": "2024-05-03T09:51:09+01:00"
    },
    "sms_marketing_consent": null,
    "tags": "",
    "currency": "GBP",
    "accepts_marketing": true,
    "accepts_marketing_updated_at": "2024-05-03T09:51:09+01:00",
    "marketing_opt_in_level": "single_opt_in",
    "tax_exemptions": [],
    "admin_graphql_api_id": "gid://shopify/Customer/8079691710747",
    "default_address": {
      "id": 10550199648539,
      "customer_id": 8079691710747,
      "first_name": "Test order",
      "last_name": "Don't ship",
      "company": "Minh company 1",
      "address1": "Capital Office, 124 City Road, London, EC1V 2NX",
      "address2": "Ferguson House",
      "city": "London",
      "province": "England",
      "country": "United Kingdom",
      "zip": "EC1V 2NX",
      "phone": "7893929199",
      "name": "Test order Don't ship",
      "province_code": "ENG",
      "country_code": "GB",
      "country_name": "United Kingdom",
      "default": true
    }
  },
  "discount_applications": [
    {
      "target_type": "line_item",
      "type": "automatic",
      "value": "6.0",
      "value_type": "percentage",
      "allocation_method": "across",
      "target_selection": "entitled",
      "title": "Pint glasses buy more, save more! (Discount)"
    }
  ],
  "fulfillments": [],
  "line_items": [
    {
      "id": 14289167220960,
      "admin_graphql_api_id": "gid://shopify/LineItem/14289167220960",
      "fulfillable_quantity": 3,
      "fulfillment_service": "bg-fulfillment",
      "fulfillment_status": null,
      "gift_card": false,
      "grams": 0,
      "name": "Pint Glass, (Delta flag) Keep clear of me; I am manoeuvring with difficulty",
      "price": "16.50",
      "price_set": {
        "shop_money": {
          "amount": "16.50",
          "currency_code": "GBP"
        },
        "presentment_money": {
          "amount": "32.00",
          "currency_code": "AUD"
        }
      },
      "product_exists": true,
      "product_id": 8658881544416,
      "properties": [
        {
          "name": "BG Product Number",
          "value": "915159964824"
        },
        {
          "name": "BG Product Variant Number",
          "value": "915159964824"
        },
        {
          "name": "BG Product Number",
          "value": "915159964824"
        },
        {
          "name": "BG Product Variant Number",
          "value": "915159964824"
        }
      ],
      "quantity": 3,
      "requires_shipping": true,
      "sku": "d-915159964824-915159964824",
      "taxable": false,
      "title": "Pint Glass, (Delta flag) Keep clear of me; I am manoeuvring with difficulty",
      "total_discount": "0.00",
      "total_discount_set": {
        "shop_money": {
          "amount": "0.00",
          "currency_code": "GBP"
        },
        "presentment_money": {
          "amount": "0.00",
          "currency_code": "AUD"
        }
      },
      "variant_id": 46164021903584,
      "variant_inventory_management": null,
      "variant_title": null,
      "vendor": "Great Harbour Gifts",
      "tax_lines": [],
      "duties": [],
      "discount_allocations": [
        {
          "amount": "2.97",
          "amount_set": {
            "shop_money": {
              "amount": "2.97",
              "currency_code": "GBP"
            },
            "presentment_money": {
              "amount": "5.76",
              "currency_code": "AUD"
            }
          },
          "discount_application_index": 0
        }
      ]
    }
  ],
  "payment_terms": null,
  "refunds": [],
  "shipping_address": {
    "first_name": "Test order",
    "address1": "Capital Office, 124 City Road, London, EC1V 2NX",
    "phone": "7893929199",
    "city": "London",
    "zip": "EC1V 2NX",
    "province": "England",
    "country": "United Kingdom",
    "last_name": "Don't ship",
    "address2": "Ferguson House",
    "company": "Minh company 1",
    "latitude": 51.5266994,
    "longitude": -0.08822619999999999,
    "name": "Test order Don't ship",
    "country_code": "GB",
    "province_code": "ENG"
  },
  "shipping_lines": [
    {
      "id": 4686635958496,
      "carrier_identifier": "650f1a14fa979ec5c74d063e968411d4",
      "code": "Standard International 7 to 11 working days",
      "discounted_price": "15.47",
      "discounted_price_set": {
        "shop_money": {
          "amount": "15.47",
          "currency_code": "GBP"
        },
        "presentment_money": {
          "amount": "30.00",
          "currency_code": "AUD"
        }
      },
      "phone": null,
      "price": "15.47",
      "price_set": {
        "shop_money": {
          "amount": "15.47",
          "currency_code": "GBP"
        },
        "presentment_money": {
          "amount": "30.00",
          "currency_code": "AUD"
        }
      },
      "requested_fulfillment_service_id": null,
      "source": "shopify",
      "title": "Standard International 7 to 11 working days",
      "tax_lines": [],
      "discount_allocations": []
    }
  ]
}

const MICRO_API_DOMAINS = "https://services.personify.tech";
const BACKEND_CMS_URL = "https://dev.bg-production.personify.tech";
const resellerId = "24922250924";

const triggerWebhook = async (order) => {
  const res = await axios.request({
    url: `${BACKEND_CMS_URL}/api/bg/shopify-webhook?clientId=${resellerId}&env=prod&clientName=Bottled Goose`,
    method: 'post',
    headers: {
      'content-type': 'application/json',
      'X-Auth-Token': "P8UeTt92HS98",
    },
    data: JSON.stringify(order)
  });
  return res.data.message;
}

const main = async () => {
  const res = await triggerWebhook({ ...order1, id: 6259711770949 });
  if (res !== "Order ID existed") console.error("Failed");
  else console.log("Passed");
}

main();
