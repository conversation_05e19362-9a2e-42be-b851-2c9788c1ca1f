const XLSX = require('xlsx');
const fs = require('fs');
const path = require('path');

const readPasswordProtectedXLSX = (filePath, password) => {
  try {
    console.log('Reading file:', filePath, password);
    // Read the encrypted workbook
    const workbook = XLSX.read(fs.readFileSync(filePath), {
      type: 'buffer',
      password: password
    });

    // Get first worksheet
    const firstSheet = workbook.Sheets[workbook.SheetNames[0]];

    // Convert to JSON
    const jsonData = XLSX.utils.sheet_to_json(firstSheet);

    return jsonData;
  } catch (error) {
    console.error('Error reading XLSX file:', error);
    throw error;
  }
};

const readAllXLSXFiles = (folderPath, password = "Extrasig@2025") => {
  try {
    // Read all files in the directory
    const files = fs.readdirSync(folderPath);

    // Filter for XLSX files
    const xlsxFiles = files.filter(file => file.toLowerCase().endsWith('.xlsx'));

    console.log(`Found ${xlsxFiles.length} XLSX files in ${folderPath}:`);
    xlsxFiles.forEach(file => console.log(`  - ${file}`));

    const allData = {};

    // Process each XLSX file
    xlsxFiles.forEach(file => {
      const filePath = path.join(folderPath, file);
      console.log(`\nProcessing: ${file}`);

      try {
        let data;
        if (password) {
          data = readPasswordProtectedXLSX(filePath, password);
        } else {
          // Read without password protection
          const workbook = XLSX.read(fs.readFileSync(filePath), {
            type: 'buffer'
          });
          const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
          data = XLSX.utils.sheet_to_json(firstSheet);
        }

        allData[file] = data;
        console.log(`  ✓ Successfully read ${data.length} rows from ${file}`);
      } catch (error) {
        console.error(`  ✗ Error reading ${file}:`, error.message);
        allData[file] = null;
      }
    });

    return allData;
  } catch (error) {
    console.error('Error reading directory:', error);
    throw error;
  }
};

const step1 = async () => {
  // Read all files from the orders-xlsx folder
  const ordersFolder = './scripts/orders-xlsx';
  console.log(`Reading all XLSX files from: ${ordersFolder}\n`);

  try {
    const allOrdersData = readAllXLSXFiles(ordersFolder);

    console.log('\n=== SUMMARY ===');
    Object.keys(allOrdersData).forEach(file => {
      const data = allOrdersData[file];
      if (data) {
        console.log(`${file}: ${data.length} orders`);
      } else {
        console.log(`${file}: Failed to read`);
      }
    });

    // Save all data to JSON file
    const outputFile = './scripts/imported-orders.json';
    try {
      fs.writeFileSync(outputFile, JSON.stringify(allOrdersData, null, 2));
      console.log(`\n✓ All data saved to: ${outputFile}`);
    } catch (error) {
      console.error(`\n✗ Error saving to JSON file:`, error.message);
    }

  } catch (error) {
    console.error('Failed to process orders:', error);
  }
}

const SIGG_PRODUCTS = {
  TROPICAL: {
    productId: 87482572566,
    designId: 352511632716,
  },
  WATERMELON: {
    productId: 97482572567,
    designId: 415030110499,
  },
  PEPPERMINT: {
    productId: 57482572563,
    designId: 50230316303,
  },
  SPEARMINT: {
    productId: 67482572564,
    designId: 5883715305,
  },
  BUBBLEMINT: {
    productId: 47482572562,
    designId: 864649332392,
  },
  STRAWBERRYLEMON: {
    productId: 77482572565,
    designId: 878959232515,
  },
}

const generateRawOrderData = ({
  first_name,
  last_name,
  preferred_name,
  flavor,
  street_address,
  city,
  zipcode,
  orderId,
}) => {
  const { productId, designId } = SIGG_PRODUCTS[flavor];
  if (!productId || !designId) {
    throw new Error(`Invalid flavor: ${flavor}`);
  }
  const defaultAddress = {
    id: 1,
    customer_id: 1,
    first_name: first_name || "",
    last_name: last_name || "",
    company: null,
    address1: street_address,
    address2: "",
    city: city,
    province: "",
    country: 'United Kingdom',
    zip: zipcode,
    phone: null,
    name: [first_name, last_name].filter(Boolean).join(' '),
    province_code: "",
    country_code: "GB",
    country_name: "United Kingdom",
    default: true,
  };
  return {
    id: orderId,
    created_at: moment().format('YYYY-MM-DDTHH:mm:ssZ'),
    currency: "GBP",
    contact_email: "",
    // @ts-ignore
    customer: {
      email: "",
      first_name: first_name || "",
      last_name: last_name || "",
      // @ts-ignore
      default_address: defaultAddress
    },
    billing_address: defaultAddress,
    shipping_address: defaultAddress,
    // @ts-ignore
    line_items: [{
      id: Number(`${orderId}1`),
      grams: 1,
      name: flavor,
      price: "0.00",
      price_set: {
        shop_money: {
          amount: "0.00",
          currency_code: "GBP"
        },
        presentment_money: {
          amount: "0.00",
          currency_code: "GBP"
        },
      },
      product_exists: true,
      product_id: productId,
      properties: designId ? [
        {
          name: "Design ID",
          value: designId
        }
      ] : [],
      quantity: 1,
      requires_shipping: false,
      sku: "",
      taxable: true,
      title: flavor,
      total_discount: "0.00",
      tax_lines: [],
      duties: [],
      discount_allocations: []
    }],
    name: `sigg-${orderId}`,
    order_number: orderId,
    current_subtotal_price: "0.00",
    current_total_price: "0.00",
    total_price: "0.00",
  }
}

const main = async () => {
  // step1();
}

main();
