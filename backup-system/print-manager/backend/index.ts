import "dotenv/config";
require('cross-fetch');
require('./console.log');
require('./src/config/firebase-admin');
import Server from "./server";

const port = 3000;
const server = new Server(port);
server.setupDatabase();

import * as UsersAPI from "./src/api/users";
import * as GeneralDataAPI from "./src/api/generate-data";
import * as PDFAPI from "./src/api/pdf";
import * as PrintJobAPI from "./src/api/print-jobs";
import * as ProductAPI from "./src/api/products";
import * as DesignAPI from "./src/api/design";
import * as ProductInstanceAPI from "./src/api/product-instances";
import * as OnlineStoreAPI from "./src/api/online-stores";
import * as TestAPI from "./src/api/hello-world";
import * as OrderAPI from "./src/api/order";
import * as ImageAPI from "./src/api/images";
import * as BGServiceAPI from "./src/api/bg-services";
import * as StickerAPI from "./src/api/sticker";
import * as PaymentAPI from "./src/api/payment";
import * as PackingSlipAPI from "./src/api/packing-slips";
import * as LogAPI from "./src/api/log";
import * as BGAPI from "./src/api/bg";
import * as SurveyAPI from "./src/api/survey";
import * as SettingsAPI from "./src/api/settings";
import * as ITLAPI from "./src/api/itl";

const apiArrays = [
  { name: 'user', apis: UsersAPI },
  { name: 'data', apis: GeneralDataAPI },
  { name: 'pdf', apis: PDFAPI },
  { name: 'printJob', apis: PrintJobAPI },
  { name: 'product', apis: ProductAPI },
  { name: 'design', apis: DesignAPI },
  { name: 'instance', apis: ProductInstanceAPI },
  { name: 'shop', apis: OnlineStoreAPI },
  { name: 'test', apis: TestAPI },
  { name: 'order', apis: OrderAPI },
  { name: 'image', apis: ImageAPI },
  { name: 'bgService', apis: BGServiceAPI },
  { name: 'sticker', apis: StickerAPI },
  { name: 'payment', apis: PaymentAPI },
  { name: 'packingSlip', apis: PackingSlipAPI },
  { name: 'log', apis: LogAPI },
  { name: 'survey', apis: SurveyAPI },
  { name: 'bg', apis: BGAPI },
  { name: 'settings', apis: SettingsAPI },
  { name: 'itl', apis: ITLAPI },
]

server.initAPIs(apiArrays);

// import { startCron } from './src/cron';
server.onDBReady().then(() => {
  console.log("DB READY");
  // MOVE CRON TO SEPARATE SERVICE
  // startCron();
});

import { genAPIClients  } from './genAPIClient';
if (process.env.GENERATE_API_CLIENT) {
  genAPIClients(apiArrays);
}

