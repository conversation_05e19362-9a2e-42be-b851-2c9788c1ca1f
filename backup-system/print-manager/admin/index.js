const prompts = require('prompts');
const axios = require('axios');

const args = process.argv;

const objectToQueryString = (obj) => {
  return Object.keys(obj).map((key) => {
    return encodeURIComponent(key) + '=' + encodeURIComponent(obj[key]);
  }).join('&')
}

const requestMicroApi = async (url, data, env = 'prod') => {
  const MICRO_SERVICES_HOST = env === 'dev' ? 'https://dev.bg-production.personify.tech' : 'https://bg-production.personify.tech';

  return axios({
    url: `${MICRO_SERVICES_HOST}${url}`,
    method: data.method || "get",
    headers: {
      ...(data.headers || {}),
      "Content-Type": "application/json",
      "X-Auth-Token": "P8UeTt92HS98",
      "env": env,
    },
    data: data.body,
  });
}

const requestLocalApi = async (url, data, env = 'prod') => {
  return axios({
    url: `http://localhost:3000${url}`,
    method: data.method || "get",
    headers: {
      ...(data.headers || {}),
      "Content-Type": "application/json",
      "env": env,
    },
    data: data.body,
  });
}

const rerunJob = async ({ jobId, pipelineId, thisJobOnly, force, env }) => {
  const res = await requestMicroApi(`/api/bg/pipeline/rerun-job`, {
    method: 'post',
    body: JSON.stringify({
      jobId, pipelineId, thisJobOnly, force
    }),
  }, env);
  return res.data;
}

const dispatchRoyalMail = async ({ orderId, force, env }) => {
  const res = await requestMicroApi(`/api/bg/trigger-dispatch-royalmail?orderId=${orderId}&force=${force}`, {
    method: 'post',
    body: JSON.stringify({
      force, orderId
    }),
  }, env);
  return res.data;
}

const getOrder = async (orderId, env) => {
  try {
    const params = {
      offset: 0,
      limit: 1,
      orderId
    }
    const res = await requestMicroApi(`/api/bg/listOrder?${objectToQueryString(params)}`, {}, env);
    let order = res.data.data.list[0];
    if (!order?.Pipelines?.length) {
      const resPipeline = await requestMicroApi(`/api/bg/pipeline/list?limit=10&offset=0&orderId=${orderId}`, {}, env);
      order = {
        ...order,
        Pipelines: resPipeline?.data.list
      }
    }
    return order;
  } catch (error) {
    console.error(error);
    return null;
  }
}

const reCalculateInvoice = async ({ orderId, forceUpdate }) => {
  try {
    const res = await requestLocalApi(`/api/order/recalculate-stripe-invoice`, {
      method: 'post',
      body: JSON.stringify({
        orderId,
        forceUpdate: forceUpdate ? true : false,
      })
    })
    return res
  } catch (error) {
    console.error(error)
  }
}

(async () => {
  switch (args[2]) {
    case "re-run": {
      let env = args[3];
      if (!env) {
        const res = await prompts({
          type: 'select',
          name: 'env',
          message: 'Environment: ',
          choices: ['prod', 'dev'].map(e => ({ title: e, value: e })),
        });
        env = res.env;
      }
      let pipelineId = args[4];
      if (!pipelineId) {
        const res = await prompts({
          type: 'text',
          name: 'pipelineId',
          message: 'Pipeline ID: ',
        });
        pipelineId = res.pipelineId;
      }
      let jobId = args[5];
      if (!jobId) {
        const res = await prompts({
          type: 'text',
          name: 'jobId',
          message: 'Job ID: ',
        });
        jobId = res.jobId;
      }
      let thisJobOnly = args[6];
      if (!thisJobOnly) {
        const res = await prompts({
          type: 'confirm',
          name: 'thisJobOnly',
          message: 'Rerun this job only? (default: true)',
          initial: true,
        });
        thisJobOnly = res.thisJobOnly;
      }
      let forceSkipCallback = args[7];
      if (!forceSkipCallback) {
        const res = await prompts({
          type: 'confirm',
          name: 'forceSkipCallback',
          message: 'Force skip callback? (default: false)',
          initial: false,
        });
        forceSkipCallback = res.forceSkipCallback;
      }
      if (!jobId || !pipelineId) {
        console.log('Please provide pipelineId, jobId, and type');
        return;
      }
      const res = await rerunJob({
        jobId,
        pipelineId,
        thisJobOnly,
        force: forceSkipCallback,
        env
      });
      console.log(res);
      break;
    }
    case "publish-rm": {
      let env = args[3];
      if (!env) {
        const res = await prompts({
          type: 'select',
          name: 'env',
          message: 'Environment: ',
          choices: ['prod', 'dev'].map(e => ({ title: e, value: e })),
        });
        env = res.env;
      }
      let orderId = args[4];
      if (!orderId) {
        const res = await prompts({
          type: 'text',
          name: 'orderId',
          message: 'Order ID: ',
        });
        orderId = res.orderId;
      }
      let force = args[5];
      if (!force) {
        const res = await prompts({
          type: 'confirm',
          name: 'force',
          message: 'Force to run (Skip pending publish RM job)?',
          initial: false,
        });
        force = res.force;
      }
      const res = await dispatchRoyalMail({
        orderId,
        force,
        env
      });
      console.log(res);
      break;
    }
    case "gen-artwork": {
      let env = args[3];
      if (!env) {
        const res = await prompts({
          type: 'select',
          name: 'env',
          message: 'Environment: ',
          choices: ['prod', 'dev'].map(e => ({ title: e, value: e })),
        });
        env = res.env;
      }
      let orderId = args[4];
      if (!orderId) {
        const res = await prompts({
          type: 'text',
          name: 'orderId',
          message: 'Order ID: ',
        });
        orderId = res.orderId;
      }
      const order = await getOrder(orderId, env);
      if (!order) {
        console.log('Order not found');
        return;
      }
      if (!order.Pipelines?.length) {
        console.log('Pipeline not found');
        return;
      }
      let jobId = null;
      let jobsObj = order.Pipelines[order.Pipelines.length - 1].Jobs;
      Object.keys(jobsObj).forEach(id => {
        const job = jobsObj[id];
        if (job.Title === "Generate PDF") {
          jobId = id;
        }
      });
      const res = await rerunJob({
        jobId,
        pipelineId: order.Pipelines[order.Pipelines.length - 1].Id,
        thisJobOnly: true,
        force: true,
        env
      });
      console.log(res);
      break;
    }
    case "re-calculate": {
      let orderId = args[3];
      if (!orderId) {
        const res = await prompts({
          type: 'text',
          name: 'orderId',
          message: 'Order ID: ',
        });
        orderId = res.orderId;
      }
      let forceUpdate = args[4];
      if (!forceUpdate) {
        const res = await prompts({
          type: 'confirm',
          name: 'forceUpdate',
          message: 'Force Update or just log different?',
          initial: false,
        });
        forceUpdate = res.forceUpdate;
      }
      const res = await reCalculateInvoice({ orderId, forceUpdate });
      const fs = require('fs');
      fs.writeFileSync(`re-calculate-invoice-${orderId}.json`, JSON.stringify(res.data, null, 2));
      console.log(`Results written to invoice-${orderId}.json`);
    }
    default:
      break;
  }
})();
