FROM lequanghuylc/c9sdk-pm2-ubuntu:latest

# WORKDIR /root/c9sdk

# CMD pm2 start server.js --name="c9sdk" -- -w / -l 0.0.0.0 -p 3399 -a c9sdk:${C9SDK_PASSWORD} && nginx -c /etc/nginx/nginx.conf && pm2 log 0
WORKDIR /root
COPY prepare_supervisord.sh .
# COPY initial_command.sh .
RUN chmod +x prepare_supervisord.sh
# RUN chmod +x initial_command.sh
CMD /root/prepare_supervisord.sh && echo "===\n" && printenv | while IFS='=' read -r k v; do printf 'export %s="%s"\n' "$k" "$(printf '%s' "$v" | sed 's/"/\\"/g')"; done > /env.sh && echo "===\n" && supervisord -c /etc/supervisor/conf.d/supervisord.conf

EXPOSE 8080