[supervisord]
nodaemon=true

[program:pm2]
command=pm2 start /root/c9sdk/server.js --name="c9sdk" -- -w / -l 0.0.0.0 -p 3399 -a c9sdk:$C9SDK_PASSWORD

[program:git-project-deployment]
command=/root/pm2-server-managers/detect-git-repo-deployment.sh
stdout_logfile=/root/pm2-server-managers/logfile.log
stderr_logfile=/root/pm2-server-managers/error.log

[program:nginx]
command=nginx -g "daemon off;"

[program:cron]
command=cron -f

[program:initial_command]
command=/root/initial_command.sh
stdout_logfile=/root/initial_command.log
stderr_logfile=/root/initial_command_error.log