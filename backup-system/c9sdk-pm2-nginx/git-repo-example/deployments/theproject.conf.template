server {
  listen 8080;
  server_name $DOMAIN_NAME;
  
  location / {
    root /var/www/cms;
    try_files $uri /index.html;
  }
  
  location /deployments {
    alias /var/www/deployments;
  }
  
  location /api {
    proxy_pass http://localhost:3000;
    client_max_body_size 100M;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header        Host $host;
    proxy_set_header        X-Real-IP $remote_addr;
    proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header        X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Host $host;
    proxy_set_header X-Forwarded-Port $server_port;
  }

}